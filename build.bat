@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 实验室管理系统构建和压缩脚本 (Windows版本)
REM Laboratory Management System Build and Compress Script (Windows)

echo 🚀 开始构建实验室管理系统...
echo 🚀 Starting Laboratory Management System build...

REM 清理之前的构建文件
echo 🧹 清理之前的构建文件...
if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ 已删除旧的 dist 目录
)

REM 删除之前的压缩文件
if exist "dist.zip" (
    del /q "dist.zip"
    echo ✅ 已删除旧的压缩文件
)

REM 执行构建
echo 📦 开始执行 npm run build...
call npm run build

REM 检查构建是否成功
if %errorlevel% equ 0 (
    echo ✅ 构建成功完成！

    REM 检查 dist 目录是否存在
    if exist "dist" (
        echo 📊 构建结果统计：
        echo    - 构建目录: %cd%\dist

        REM 创建压缩文件
        echo 🗜️  开始压缩 dist 目录...

        REM 使用PowerShell进行压缩
        powershell -command "Compress-Archive -Path 'dist\*' -DestinationPath 'dist.zip' -Force"

        if !errorlevel! equ 0 (
            echo ✅ 压缩完成！
            echo 📦 压缩文件信息：
            echo    - 文件名: dist.zip
            echo    - 文件路径: %cd%\dist.zip
        ) else (
            echo ❌ 压缩失败！
            exit /b 1
        )
    ) else (
        echo ❌ 构建失败：dist 目录不存在！
        exit /b 1
    )
) else (
    echo ❌ npm run build 执行失败！
    exit /b 1
)

echo.
echo 🎉 构建和压缩流程完成！
echo.
echo 📁 输出文件：
echo    - 构建目录: .\dist\
echo    - 压缩文件: .\dist.zip
echo.
echo 🚀 部署提示：
echo    1. 可以直接使用 dist\ 目录进行部署
echo    2. 或者上传压缩文件 dist.zip 到服务器后解压
echo.

pause