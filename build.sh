#!/bin/bash

# 实验室管理系统构建和压缩脚本
# Laboratory Management System Build and Compress Script

echo "🚀 开始构建实验室管理系统..."
echo "🚀 Starting Laboratory Management System build..."

# 清理之前的构建文件
echo "🧹 清理之前的构建文件..."
if [ -d "dist" ]; then
    rm -rf dist
    echo "✅ 已删除旧的 dist 目录"
fi

# 删除之前的压缩文件
if [ -f "dist.zip" ]; then
    rm -f dist.zip
    echo "✅ 已删除旧的压缩文件"
fi

# 执行构建
echo "📦 开始执行 npm run build..."
npm run build

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo "✅ 构建成功完成！"

    # 检查 dist 目录是否存在
    if [ -d "dist" ]; then
        echo "📊 构建结果统计："
        echo "   - 构建目录: $(pwd)/dist"
        echo "   - 文件数量: $(find dist -type f | wc -l)"
        echo "   - 目录大小: $(du -sh dist | cut -f1)"

        # 创建压缩文件
        echo "🗜️  开始压缩 dist 目录..."

        # 压缩文件
        zip -r "dist.zip" dist/ -x "*.DS_Store" "*/.*"

        if [ $? -eq 0 ]; then
            echo "✅ 压缩完成！"
            echo "📦 压缩文件信息："
            echo "   - 文件名: dist.zip"
            echo "   - 文件大小: $(du -sh "dist.zip" | cut -f1)"
            echo "   - 文件路径: $(pwd)/dist.zip"
        else
            echo "❌ 压缩失败！"
            exit 1
        fi
    else
        echo "❌ 构建失败：dist 目录不存在！"
        exit 1
    fi
else
    echo "❌ npm run build 执行失败！"
    exit 1
fi

echo ""
echo "🎉 构建和压缩流程完成！"
echo ""
echo "📁 输出文件："
echo "   - 构建目录: ./dist/"
echo "   - 压缩文件: ./dist.zip"
echo ""
echo "🚀 部署提示："
echo "   1. 可以直接使用 dist/ 目录进行部署"
echo "   2. 或者上传压缩文件 dist.zip 到服务器后解压"
echo ""