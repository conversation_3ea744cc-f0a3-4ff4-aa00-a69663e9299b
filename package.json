{"name": "laboratoryManagement", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@tinymce/tinymce-vue": "^3.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@wangeditor/plugin-upload-attachment": "^1.1.0", "amfe-flexible": "^2.2.1", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "codemirror": "^5.60.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "echarts": "^5.0.2", "element-ui": "^2.15.14", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "jsencrypt": "^3.3.2", "jspdf": "^3.0.0", "less": "^4.1.1", "less-loader": "^5.0.0", "lib-flexible": "^0.3.2", "lodash.clonedeep": "^4.5.0", "moment": "^2.29.4", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "popper.js": "^1.16.1", "postcss-plugin-px2rem": "^0.8.1", "postcss-px2rem": "^0.3.0", "postcss-pxtorem": "^6.0.0", "scss": "^0.2.4", "tinymce": "^5.0.12", "vant": "^2.12.10", "viewerjs": "^1.11.7", "vue": "^2.6.11", "vue-codemirror": "^4.0.6", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vue-table-with-tree-grid": "^0.2.4", "vue-video-player": "^5.0.2", "vue-waterfall2": "^1.10.1", "vuex": "^3.6.2", "vuex-persistedstate": "^4.0.0-beta.3", "webuploader": "^0.1.8", "xlsx": "^0.17.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.14.1", "sass": "1.26.8", "sass-loader": "8.0.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": false, "env": {"node": false}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}