<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业二维码生成器</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            padding: 40px;
        }

        .controls-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .preview-panel {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .color-input-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .color-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .color-input input[type="color"] {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .size-input-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .qr-preview {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #qrcode {
            border-radius: 10px;
            overflow: hidden;
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .download-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .generate-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .placeholder {
            color: #999;
            font-size: 1.1rem;
            text-align: center;
        }

        .advanced-options {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #e1e5e9;
        }

        .advanced-options h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .container {
                margin: 10px;
                border-radius: 15px;
            }
        }

        .info-card {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .range-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 5px;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 专业二维码生成器</h1>
            <p>高质量、可定制的二维码生成工具</p>
        </div>

        <div class="main-content">
            <div class="controls-panel">
                <div class="info-card">
                    💡 <strong>使用提示：</strong> 输入文本内容，自定义样式参数，即可生成高质量二维码
                </div>

                <div class="form-group">
                    <label for="text-input">📝 二维码内容</label>
                    <textarea id="text-input" placeholder="请输入要生成二维码的文本内容，支持网址、文字、联系方式等...">https://www.example.com</textarea>
                </div>

                <div class="form-group">
                    <label>🎨 颜色设置</label>
                    <div class="color-input-group">
                        <div class="color-input">
                            <input type="color" id="foreground-color" value="#000000">
                            <span>前景色</span>
                        </div>
                        <div class="color-input">
                            <input type="color" id="background-color" value="#ffffff">
                            <span>背景色</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>📏 尺寸设置</label>
                    <div class="size-input-group">
                        <div>
                            <label for="qr-size">二维码尺寸</label>
                            <input type="range" id="qr-size" min="200" max="800" value="300" step="50">
                            <div class="range-display">
                                <span>200px</span>
                                <span id="size-value">300px</span>
                                <span>800px</span>
                            </div>
                        </div>
                        <div>
                            <label for="margin-size">白边宽度</label>
                            <input type="range" id="margin-size" min="1" max="10" value="4" step="1">
                            <div class="range-display">
                                <span>1</span>
                                <span id="margin-value">4</span>
                                <span>10</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="advanced-options">
                    <h3>⚙️ 高级选项</h3>
                    <div class="form-group">
                        <label for="error-level">🛡️ 错误纠正级别</label>
                        <select id="error-level">
                            <option value="L">低 (L) - 约7%</option>
                            <option value="M" selected>中 (M) - 约15%</option>
                            <option value="Q">较高 (Q) - 约25%</option>
                            <option value="H">高 (H) - 约30%</option>
                        </select>
                    </div>
                </div>

                <button class="generate-btn" onclick="generateQR()">🚀 生成二维码</button>
            </div>

            <div class="preview-panel">
                <div class="qr-preview">
                    <div id="qrcode">
                        <div class="placeholder">
                            <p>📱 二维码预览区域</p>
                            <p>点击"生成二维码"按钮开始</p>
                        </div>
                    </div>
                </div>
                <button class="download-btn" id="download-btn" onclick="downloadQR()" disabled>
                    📥 下载二维码
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentQRDataURL = null;

        // 更新尺寸显示
        document.getElementById('qr-size').addEventListener('input', function() {
            document.getElementById('size-value').textContent = this.value + 'px';
        });

        document.getElementById('margin-size').addEventListener('input', function() {
            document.getElementById('margin-value').textContent = this.value;
        });

        // 生成二维码函数
        function generateQR() {
            const text = document.getElementById('text-input').value.trim();
            const foregroundColor = document.getElementById('foreground-color').value;
            const backgroundColor = document.getElementById('background-color').value;
            const size = parseInt(document.getElementById('qr-size').value);
            const margin = parseInt(document.getElementById('margin-size').value);
            const errorCorrectionLevel = document.getElementById('error-level').value;

            if (!text) {
                alert('请输入要生成二维码的内容！');
                return;
            }

            // 清空预览区域
            const qrcodeDiv = document.getElementById('qrcode');
            qrcodeDiv.innerHTML = '';

            // 生成二维码配置
            const options = {
                width: size,
                height: size,
                margin: margin,
                color: {
                    dark: foregroundColor,
                    light: backgroundColor
                },
                errorCorrectionLevel: errorCorrectionLevel,
                type: 'image/png',
                quality: 1,
                rendererOpts: {
                    quality: 1
                }
            };

            // 生成二维码
            QRCode.toCanvas(text, options, function (error, canvas) {
                if (error) {
                    console.error(error);
                    qrcodeDiv.innerHTML = '<div class="placeholder"><p>❌ 生成失败</p><p>请检查输入内容</p></div>';
                    return;
                }

                // 添加样式
                canvas.style.borderRadius = '10px';
                canvas.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.15)';

                // 显示二维码
                qrcodeDiv.appendChild(canvas);

                // 保存数据URL用于下载
                currentQRDataURL = canvas.toDataURL('image/png', 1.0);

                // 启用下载按钮
                document.getElementById('download-btn').disabled = false;
            });
        }

        // 下载二维码函数
        function downloadQR() {
            if (!currentQRDataURL) {
                alert('请先生成二维码！');
                return;
            }

            const link = document.createElement('a');
            link.download = `qrcode_${new Date().getTime()}.png`;
            link.href = currentQRDataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 实时预览功能
        function setupRealTimePreview() {
            const inputs = [
                'text-input', 'foreground-color', 'background-color',
                'qr-size', 'margin-size', 'error-level'
            ];

            inputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', debounce(generateQR, 500));
                }
            });
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 生成初始二维码
            generateQR();

            // 设置实时预览
            setupRealTimePreview();
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter 或 Cmd+Enter 生成二维码
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                generateQR();
            }

            // Ctrl+S 或 Cmd+S 下载二维码
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (!document.getElementById('download-btn').disabled) {
                    downloadQR();
                }
            }
        });
    </script>
</body>
</html>