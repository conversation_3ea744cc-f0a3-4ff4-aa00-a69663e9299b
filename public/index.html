<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- <html lang="en" style="font-size: 10vw"> -->
  <html lang="en">

  </html>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title> -->
  <title>湖州学院实验室</title>
  </script>
  <script
    type="text/javascript">document.write('<script src="./config/config.js?timestamp=' + new Date().getTime() + '" type="text/javascript" charset="utf-8"><\/script>')</script>
  <script type="text/javascript">
    // 创建一个加载iconFont的函数
    function loadIconFont () {
      if (window.g && window.g.iconFontUrl) {
        let linkElement = document.createElement('link');
        linkElement.rel = 'stylesheet';
        linkElement.href = window.g.iconFontUrl;
        document.head.appendChild(linkElement);
        let linkElement2 = document.createElement('script');
        linkElement2.src = window.g.iconFontUrl.replace(/\.css$/, ".js");
        document.head.appendChild(linkElement2);
      }
    }

    // 检查config是否已加载
    function checkConfig () {
      if (window.g) {
        loadIconFont();
      } else {
        // 如果config还没加载完成，稍后再检查
        setTimeout(checkConfig, 100);
      }
    }

    // 开始检查
    checkConfig();
  </script>
  <style>
  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app">

  </div>
  <!-- built files will be auto injected -->
</body>

</html>