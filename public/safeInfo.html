<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>实验室安全信息牌</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: "Microsoft YaHei", Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .safety-card {
      width: 1920px;
      border-radius: 0;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
      position: relative;
      overflow: hidden;
    }

    .header {
      color: #ffffff;
      background-color: #0063a4;
      padding: 30px 100px 30px 70px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .university-info {
      display: flex;
      align-items: center;
    }

    .info-img {
      width: 580px;
      height: 105px;
    }

    .university-logo {
      width: 100px;
      height: 100px;
      margin-right: 30px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .university-name {
      width: 450px;
      font-size: 48px;
      text-align: justify;
      text-align-last: justify;
    }

    .university-name-en {
      font-size: 35px;
      letter-spacing: 5px;
      text-align: justify;
      text-align-last: justify;
    }

    .title-section {
      width: 630px;
      text-align: center;
    }

    .main-title {
      width: 100%;
      font-size: 50px;
      margin: 0;
      letter-spacing: 25px;
    }

    .sub-title {
      font-size: 30px;
      letter-spacing: 4px;
      text-align: justify;
      text-align-last: justify;
    }

    .content-area {
      padding: 10px 55px;
      display: flex;
      flex-direction: column;
    }

    .info-table {
      display: flex;
      border: 1px solid #959595;
      border-bottom: none;
    }

    .info-cell {
      padding: 8px 12px;
      font-size: 30px;
      display: flex;
      align-items: center;
      color: #1f1f1f;
    }

    .info-label {
      width: 360px;
      min-height: 45px;
      background-color: rgba(255, 255, 255, 0.2);
      font-weight: bold;
      letter-spacing: 4px;
      border-right: 1px solid #959595;
    }

    .info-label-en {
      font-size: 25px;
      margin-left: 15px;
      background-color: rgba(255, 255, 255, 0.2);
      font-weight: 400;
      text-align: center;
      justify-content: center;
      letter-spacing: 1.5px;
    }

    .info-content {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      background-color: rgba(255, 255, 255, 0.9);
      color: #333;
      font-weight: bold;
    }

    .hazard-level {
      font-size: 36px;
      font-weight: bold;
      color: #ff3333;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .cautions-area {
      display: flex;
      margin-top: 10px;
      ;
      border: 1px solid #959595;
    }

    .cautions-section {
      width: 100%;
      color: #1f1f1f;
    }

    .cautions-title {
      height: 55px;
      background-color: #fef252;
      line-height: 55px;
      font-size: 30px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 10px;
      color: #333;
      letter-spacing: 2.5px;
      border-bottom: 1px solid #a7a48f;
    }

    .hazard-icons {
      padding: 10px 30px;
      min-height: 300px;
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    .hazard-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-width: 80px;
    }

    .hazard-icon {
      width: 100px;
      height: 100px;
      margin-bottom: 10px;
    }

    .hazard-icon-img {
      width: 100%;
      height: 100%;
    }

    .hazard-icon.flammable {
      background-color: #cc3232;
    }

    .hazard-icon.explosive {
      background-color: #ff8800;
    }

    .hazard-icon.fire {
      background-color: #ff4400;
    }

    .hazard-icon.no-smoking {
      background-color: #cc0000;
    }

    .hazard-icon.mask {
      background-color: #0088cc;
    }

    .hazard-text {
      font-size: 30px;
      color: #1f1f1f;
    }

    .protection-section-main {
      width: 350px;
      border-left: 1px solid #a7a48f;
    }

    .protection-title {
      font-size: 30px;
      font-weight: bold;
      color: #cc3232;
      margin-bottom: 20px;
      text-align: center;
      letter-spacing: 2px;

      span {
        font-size: 27px;
        font-weight: normal;
        letter-spacing: normal;
      }
    }

    .protection-item {
      width: 350px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      font-size: 24px;
      color: #cc3232;
    }

    .protection-check {
      font-weight: bold;
      margin-right: 16px;
      font-size: 35px;
    }

    .contact-footer {
      color: white;
      background-color: #0063a4;
      padding: 10px 80px;
      display: flex;
      font-size: 24px;
    }

    .contact-left,
    .contact-right {
      display: flex;
      flex-direction: column;
      gap: 3px;
      font-size: 28px;
    }

    .emergency-icon {
      width: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      font-size: 20px;
      margin-right: 80px;
    }

    .emergency-icon-img {
      width: 60px;
      height: 60px;
      margin-bottom: 5px;
    }

    .contact-section {
      display: flex;
      align-items: center;
    }
  </style>
</head>

<body>
  <div class="safety-card">
    <div class="header">
      <div class="university-info">
        <img class="info-img"
          src="https://img.alicdn.com/imgextra/i2/O1CN01AfBNQZ23wcEimKiYZ_!!6000000007320-2-tps-280-320.png" alt="">
        <!-- <div class="university-logo">
          <img src="https://img.alicdn.com/imgextra/i2/O1CN01AfBNQZ23wcEimKiYZ_!!6000000007320-2-tps-280-320.png" alt="">
        </div>
        <div>
          <div class="university-name">${deptName}</div>
          <div class="university-name-en">China Academy of Art</div>
        </div> -->
      </div>
      <div class="title-section">
        <div class="main-title">实验室安全信息牌</div>
        <div class="sub-title">Laboratory Safety Information Sheet</div>
      </div>
    </div>

    <div class="content-area">
      <!-- 第一行：实验室名称 -->
      <div class="info-table">
        <div class="info-cell info-label">
          <div>实验室名称</div>
          <div class="info-label-en">Lab Name</div>
        </div>
        <div class="info-cell info-content">${roomLabName}</div>
      </div>

      <!-- 第二行：房间号 -->
      <div class="info-table">
        <div class="info-cell info-label">
          <div>房间号</div>
          <div class="info-label-en">Room Number</div>
        </div>
        <div class="info-cell info-content">
          <#list roomList as room>${room.roomName} ${room.roomSn}</#list>
        </div>
      </div>

      <!-- 第三行：分类、危险等级 -->
      <div class="info-table">
        <div class="info-cell info-label">
          <div>分类</div>
          <div class="info-label-en">Classification</div>
        </div>
        <div class="info-cell info-content" style="width: 520px;border-right: 1px solid #959595;">机电类</div>
        <div class="info-cell info-label">
          <div>危险等级</div>
          <div class="info-label-en">Hazard Level</div>
        </div>
        <div class="info-cell info-content hazard-level">${hazardLevel}</div>
      </div>

      <!-- 第四行：负责人、安全员 -->
      <div class="info-table" style="border-bottom: 1px solid #959595;;">
        <div class="info-cell info-label">
          <div>负责人</div>
          <div class="info-label-en" style="margin-left: 3px;">Responsible Person</div>
        </div>
        <div class="info-cell info-content" style="width: 520px;border-right: 1px solid #959595;">${safetyManagerName}
        </div>
        <div class="info-cell info-label">
          <div>安全员</div>
          <div class="info-label-en">Safety Supervisor</div>
        </div>
        <div class="info-cell info-content">${safetyManagerName}</div>
      </div>

      <!-- 注意事项区域和灭火要点 -->
      <div class="cautions-area">
        <div class="cautions-section">
          <div class="cautions-title">注意事项和防护措施
            <span style="font-size: 27px;font-weight: normal;letter-spacing: normal;">Cautions and Protections
              Required</span>
          </div>
          <div class="hazard-icons">
            <#list hazardSourceList as hazardSource>
              <div class="hazard-item">
                <div class="hazard-icon">
                  <img src="${hazardSource.icon}" alt="">
                </div>
                <div class="hazard-text">${hazardSource.hazardName}</div>
              </div>
            </#list>
            <#list hazardProtectionList as hazardProtection>
              <div class="hazard-item">
                <div class="hazard-icon ">
                  <img src="${hazardProtection.icon}" alt="">
                </div>
                <div class="hazard-text">${hazardProtection.protectionName}</div>
              </div>
            </#list>
          </div>
        </div>
        <div class="protection-section-main">
          <div class="protection-title">灭火要点<br><span>Fire Extinguishment Points</span></div>
          <#list hazardProtectionMerge as hazardProtection>
            <div class="protection-item">
              <span class="protection-check">✓</span>
              <span style="font-weight: bold;">${hazardProtection.protectionKindName}</span>
            </div>
            <#list hazardProtection.protectionNameList as protectionName>
              <div class="protection-item">
                <span class="protection-check">✓</span>
                <span style="font-weight: bold;">${protectionName}</span>
              </div>
            </#list>
          </#list>
        </div>
      </div>
    </div>
    <div class="contact-footer">
      <div class="contact-section">
        <div class="emergency-icon">
          <img class="emergency-icon-img"
            src="https://img.alicdn.com/imgextra/i2/O1CN01AfBNQZ23wcEimKiYZ_!!6000000007320-2-tps-280-320.png" alt="">
          <div><strong>Emergency Contact Number</strong></div>
        </div>
      </div>
      <div class="contact-left">
        <div>${safetyManagerName} ${safetyManagerPhone}</div>
      </div>
      <div class="contact-right" style="margin-left: 150px;">
        <div>实验处 ( Office of Laboratory Management ) 67675454444</div>
        <div>紧急电话 ( Emergency phone number ) 火警119，急救120</div>
      </div>
    </div>
  </div>
</body>

</html>