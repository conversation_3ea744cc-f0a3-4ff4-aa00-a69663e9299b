<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import "babel-polyfill";
import VueRouter from "vue-router";
export default {
  name: "App",
  created() {
    // //在页面加载时读取sessionStorage里的状态信息
    // if (sessionStorage.getItem("isLogin")) {
    //   // this.$store.replaceState(
    //   //   Object.assign(
    //   //     {},
    //   //     this.$store.state,
    //   //     JSON.parse(sessionStorage.getItem("isLogin"))
    //   //   )
    //   // );
    //   // this.$store.user.isLogin
    // }
    // //在页面刷新时将vuex里的信息保存到sessionStorage里
    // window.addEventListener("beforeunload", () => {
    //   console.log("我在刷新，我把想存的值给存了起来")
    //   sessionStorage.setItem("isLogin", true);
    // });
  },
  methods: {
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
      );
      localStorage.setItem("ismobile", flag ? 1 : 0);
      let goUrl = flag ? 1 : 0;
      return goUrl;
    },
  },
  mounted() {
    let goUrl = this.isMobile();
    if (goUrl === 1) {
      //移动端地址
      // location = "mobile.html#/Mobileadmin";
    }
  },
};
</script>
