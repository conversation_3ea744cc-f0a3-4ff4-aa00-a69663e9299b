import request from "@admin/utils/request";

//危险源分类-分页
export function getkind(params) {
  return request({
    url: "/hazard/kind/page",
    method: "get",
    params,
  });
}

//危险源分类-新增
export function savekind(data) {
  return request({
    url: "/hazard/kind/save",
    method: "post",
    data,
  });
}

//危险源分类-编辑
export function updatekind(data) {
  return request({
    url: "/hazard/kind/update",
    method: "post",
    data,
  });
}

//危险源分类-删除
export function delkind(data) {
  return request({
    url: "/hazard/kind/delete",
    method: "post",
    data,
  });
}

//危险源分类-下拉框
export function listkind(params) {
  return request({
    url: "/hazard/kind/list",
    method: "get",
    params,
  });
}

//危险源-分页
export function getsource(params) {
  return request({
    url: "/hazard/source/page",
    method: "get",
    params,
  });
}

//危险源-新增
export function savesource(data) {
  return request({
    url: "/hazard/source/save",
    method: "post",
    data,
  });
}

//危险源-修改
export function updatesource(data) {
  return request({
    url: "/hazard/source/update",
    method: "post",
    data,
  });
}

//危险源-删除
export function delsource(data) {
  return request({
    url: "/hazard/source/delete",
    method: "post",
    data,
  });
}

//危险源-下拉框
export function listsource(params) {
  return request({
    url: "/hazard/source/list",
    method: "get",
    params,
  });
}

//危险源-图标上传
export function uploadsource(data) {
  return request({
    url: "/hazard/source/upload",
    method: "post",
    data,
  });
}

//安全防护分类-分页
export function getprotectionkind(params) {
  return request({
    url: "/hazard/protection/kind/page",
    method: "get",
    params,
  });
}

//安全防护分类-新增
export function saveprotectionkind(data) {
  return request({
    url: "/hazard/protection/kind/save",
    method: "post",
    data,
  });
}

//安全防护分类-修改
export function updateprotectionkind(data) {
  return request({
    url: "/hazard/protection/kind/update",
    method: "post",
    data,
  });
}

//安全防护分类-删除
export function delprotectionkind(data) {
  return request({
    url: "/hazard/protection/kind/delete",
    method: "post",
    data,
  });
}

//安全防护分类-下拉框
export function listprotectionkind(params) {
  return request({
    url: "/hazard/protection/kind/list",
    method: "get",
    params,
  });
}

//安全防护-分页
export function getprotection(params) {
  return request({
    url: "/hazard/protection/page",
    method: "get",
    params,
  });
}

//安全防护-新增
export function saveprotection(data) {
  return request({
    url: "/hazard/protection/save",
    method: "post",
    data,
  });
}

//安全防护-修改
export function updateprotection(data) {
  return request({
    url: "/hazard/protection/update",
    method: "post",
    data,
  });
}

//安全防护-删除
export function delprotection(data) {
  return request({
    url: "/hazard/protection/delete",
    method: "post",
    data,
  });
}

//安全防护-下拉框
export function listprotection(params) {
  return request({
    url: "/hazard/protection/list",
    method: "get",
    params,
  });
}

//安全防护-图标上传
export function uploadprotection(data) {
  return request({
    url: "/hazard/protection/upload",
    method: "post",
    data,
  });
}

//评估等级-分页
export function getlevel(params) {
  return request({
    url: "/hazard/level/page",
    method: "get",
    params,
  });
}

//评估等级-下拉框
export function listlevel(params) {
  return request({
    url: "/hazard/level/list",
    method: "get",
    params,
  });
}

//评估等级-修改
export function updatelevel(data) {
  return request({
    url: "/hazard/level/update",
    method: "post",
    data,
  });
}

//实验室绑定危险源
export function saverec(data) {
  return request({
    url: "/hazard/lab/rec/save",
    method: "post",
    data,
  });
}

//实验室修改绑定危险源
export function updaterec(data) {
  return request({
    url: "/hazard/lab/rec/update",
    method: "post",
    data,
  });
}

//实验室查询危险源详情
export function getrecdetail(params) {
  return request({
    url: "/hazard/lab/rec/detail",
    method: "get",
    params,
  });
}

//实验室绑定安全防护
export function saveprotectionrec(data) {
  return request({
    url: "/hazard/protection/lab/rec/save",
    method: "post",
    data,
  });
}

//实验室修改绑定安全防护
export function updateprotectionrec(data) {
  return request({
    url: "/hazard/protection/lab/rec/update",
    method: "post",
    data,
  });
}

//实验室清楚所有安全防护
export function delprotectionrec(data) {
  return request({
    url: "/hazard/protection/lab/rec/delete",
    method: "post",
    data,
  });
}

//实验室查询安全防护详情
export function getrecprotectiondetail(params) {
  return request({
    url: "/hazard/protection/lab/rec/detail",
    method: "get",
    params,
  });
}

//危险源关联安全防护
export function savesourceprotect(data) {
  return request({
    url: "/hazard/protection/relation/save-or-update",
    method: "post",
    data,
  });
}

//实验室查询安全防护详情
export function getsourceprotectdetail(params) {
  return request({
    url: "/hazard/protection/relation/detail",
    method: "get",
    params,
  });
}
