import request from "@admin/utils/request";

//模版列表-获取模版分页
export function gettemplates(params) {
  return request({
    url: "/api/home/<USER>/preview",
    method: "get",
    params,
  });
}
//模版列表-获取模版详情
export function gettemplatedetail(params) {
  return request({
    url: "/room/lab/template/detail",
    method: "get",
    params,
  });
}
//模版列表-添加模版
export function savetemplate(data) {
  return request({
    url: "/room/lab/template/save",
    method: "post",
    data,
  });
}
//模版列表-更新模版
export function updatetemplate(data) {
  return request({
    url: "/room/lab/template/update",
    method: "post",
    data,
  });
}
//模版列表-删除模版
export function deltemplate(data) {
  return request({
    url: "/room/lab/template/delete",
    method: "post",
    data,
  });
}
//模版列表-预览模版
export function previewtemplate(params) {
  return request({
    url: "/room/lab/template/preview",
    method: "get",
    params,
  });
}
//模版列表-设置安全准入默认模版
export function defaulttemplate(data) {
  return request({
    url: "/room/lab/template/default",
    method: "post",
    data,
  });
}
//模版列表-校验模版内容
export function validatetemplate(data) {
  return request({
    url: "/room/lab/template/validate",
    method: "post",
    data,
  });
}
//模版列表-获取模版变量
export function gettemplatefield(params) {
  return request({
    url: "/room/lab/template/field",
    method: "get",
    params,
  });
}
//模版列表-图片上传
export function uploadimg(data) {
  return request({
    url: "/fileUpload/img",
    method: "post",
    data,
  });
}

//模版绑定-获取绑定列表
export function gettemplatebind(params) {
  return request({
    url: "/room/lab/template-bind/page",
    method: "get",
    params,
  });
}
//模版绑定-新增绑定
export function savebind(data) {
  return request({
    url: "/room/lab/template-bind/save",
    method: "post",
    data,
  });
}
//模版绑定-删除绑定
export function delbind(data) {
  return request({
    url: "/room/lab/template-bind/delete",
    method: "post",
    data,
  });
}
//安全信息牌-获取下载内容
export function downloadinfo(params) {
  return request({
    url: "/room/lab/information-board/download",
    method: "get",
    params,
  });
}