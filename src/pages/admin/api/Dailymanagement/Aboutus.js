import request from '@index/utils/request';

//获取关于我们
export function getAboutUs() {
    return request({
        url: '/sysInfo/getAboutUs',
        method: 'get',
    })
}

//更新关于我们
export function updateAboutUs(data) {
    return request({
        url: '/sysInfo/updateAboutUs',
        method: 'post',
        data
    })
}

//获取使用帮助
export function getUsingHelp() {
    return request({
        url: '/sysInfo/getUsingHelp',
        method: 'get',
    })
}

//更新使用帮助
export function updateUsingHelp(data) {
    return request({
        url: '/sysInfo/updateUsingHelp',
        method: 'post',
        data
    })
}

//系统信息列表
export function getsysInfolist(params) {
    return request({
        url: '/sysInfo/list',
        method: 'get',
        params
    })
}

//系统信息列表-更新
export function updatesysInfolist(data) {
    return request({
        url: '/sysInfo/update',
        method: 'post',
        data
    })
}

//系统信息列表-获取
export function getmanagesysInfo(params) {
    return request({
        url: '/sysInfo/manage',
        method: 'get',
        params
    })
}

//系统信息列表-详情
export function getsysInfodetail(params) {
    return request({
        url: '/sysInfo/detail',
        method: 'get',
        params
    })
}




