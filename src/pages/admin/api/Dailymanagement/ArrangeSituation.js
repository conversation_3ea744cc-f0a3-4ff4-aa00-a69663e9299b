import request from '@admin/utils/request';
import qs from 'qs';

//管理员排课列表
export function getReserveList(yearTermId, schoolClassName, courseName, search, planWeekly,festival, deptName,roomName,campusName,pageNum, pageSize,orderKey,orderModel,startWeek,endWeek) {
    return request({
        url: '/manager/getReserveList',
        method: 'get',
        params: { yearTermId, schoolClassName, courseName, search, planWeekly ,festival,deptName,roomName,campusName,pageNum, pageSize,orderKey,orderModel,startWeek,endWeek}
    })
}


//实验项目批量删除
export function DelReserveList(data) {
    return request({
        url: '/testPlan/deleteList',
        method: 'post',
        data
    })
}

//实验项目修改
export function editReserveList(data) {
    return request({
        url: '/testPlan/update',
        method: 'post',
        data
    })
}

//实验室下拉框
export function getallroom(key,num) {
    return request({
        url: '/room/getAll',
        method: 'get',
        params:{key,num}
    })
}


//查看/调课
export function changearrange(testPlanId, pageNum, pageSize, orderKey, orderModel) {
    return request({
        url: '/manager/checkCourse',
        method: 'get',
        params: { testPlanId, pageNum, pageSize, orderKey, orderModel }
    })
}


//批量操作调课
export function editarrange(data) {
    return request({
        url: '/reserve/updateList',
        method: 'post',
        data
    })
}

//批量删除教学时间
// export function delListarrange(data) {
//     return request({
//         url: '/reserve/deleteList',
//         method: 'post',
//         data
//     })
// }
export function delListarrange(data) {
    return request({
        url: '/reserve/deleteBatch',
        method: 'post',
        data
    })
}

//“查看/调课”中的编辑修改
export function editarrangeSingle(data) {
    return request({
        url: '/reserve/update',
        method: 'post',
        data
    })
}


//检查冲突
export function checkConflicts(data,pageNum,pageSize) {
    return request({
        // url: '/manager/checkConflicts?pageNum='+pageNum+'&pageSize='+pageSize+'',
        url: '/manager/checkConflicts',
        method: 'post',
        data
    })
}

//导入实验项目表格-通用
export function importTestPlan(data) {
    return request({
        url: '/manager/fileSaveManager',
        method: 'post',
        data
    })
}

//导入实验项目表格-浙工大
export function importTestPlan_zjut(data) {
    return request({
        url: '/testPlan/importTestPlan/zjut',
        method: 'post',
        data
    })
}

//导出表格
export function exportExcel() {
    return request({
        url: '/manager/exportExcel',
        method: 'get',
    })
}

//根据教学时间查询符合的实验室
export function Searchallowroom(yearTermId,teachingTime) {
    return request({
        url: '/reserve/getRoomListByTime',
        method: 'get',
        params:{yearTermId,teachingTime}
    })
}

//检查冲突后确认排课
export function AddReserve(data) {
    return request({
        url: '/reserve',
        method: 'post',
        data
    })
}

//批量排课-保存草稿
export function SaveDraft(data) {
    return request({
        url: '/manager/saveDeft',
        method: 'post',
        data
    })
}

//批量排课-获取草稿
export function GetDraft(data) {
    return request({
        url: '/manager/getDraft?accNo=1',
        method: 'get',
        data
    })
}

//批量排课-清除草稿
export function DelDraft(data) {
    return request({
        url: '/manager/deleteDraft?accNo=1',
        method: 'post',
        data
    })
}

//实验项目下拉框
export function getallTestPlan() {
    return request({
        url: '/testPlan/getAllTestPlan',
        method: 'get',
    })
}

//实验项目下拉框
export function getallTestItem(testPlanId) {
    return request({
        url: '/testPlan/getTestItemByTestPlanId',
        method: 'get',
		params:{testPlanId}
    })
}

//直接排课-无需选择实验计划
export function directArrangementCourse(data) {
    return request({
        url: '/reserve/directArrangementCourse',
        method: 'post',
		data
    })
}

