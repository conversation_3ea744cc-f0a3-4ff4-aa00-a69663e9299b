import request from "@admin/utils/request";

//教学排课
export function getReserve(
  beginDate,
  endDate,
  search,
  roomName,
  schoolClassName,
  status,
  pageNum,
  pageSize,
  orderModel,
  orderKey
) {
  return request({
    url: "/manager/getTeacherReserveStatus",
    method: "get",
    params: {
      beginDate,
      endDate,
      search,
      roomName,
      schoolClassName,
      status,
      pageNum,
      pageSize,
      orderModel,
      orderKey,
    },
  });
}
//个人预约
export function getStudentReserve(
  beginDate,
  endDate,
  search,
  devName,
  status,
  pageNum,
  pageSize,
  orderRule,
  orderItems
) {
  return request({
    url: "/manager/getFreeReserveStatus",
    method: "get",
    params: {
      beginDate,
      endDate,
      search,
      devName,
      status,
      pageNum,
      pageSize,
      orderRule,
      orderItems,
    },
  });
}

//删除预约
export function delReserve(data) {
  return request({
    url: "/reserve/delete",
    method: "post",
    data,
  });
}

//删除预约-批量删除
export function delReserveBatch(data) {
  return request({
    url: "/reserve/deleteBatch",
    method: "post",
    data,
  });
}

export function advanceReserve(data) {
  return request({
    url: "/manager/advanceReserve",
    method: "post",
    data,
  });
}

//修改预约
export function editReserve(data) {
  return request({
    url: "/reserve/update",
    method: "post",
    data,
  });
}

//设备下拉框
export function getAlldev(key, num) {
  return request({
    url: "/device/getAllDev",
    method: "get",
    params: { key, num },
  });
}

//上课下拉框
// export function getAllClass(beginTime,endTime) {
//     return request({
//         url: '/schoolClass/getClassByTime',
//         method: 'get',
//         params: { beginTime,endTime}
//     })
// }

export function getAllClass() {
  return request({
    url: "/schoolClass/getAll",
    method: "get",
  });
}

//实验室预约状况
export function getTeacherFreeReserveStatus(
  beginDate,
  endDate,
  search,
  roomName,
  status,
  pageNum,
  pageSize
) {
  return request({
    url: "/manager/getTeacherFreeReserveStatus",
    method: "get",
    params: { beginDate, endDate, search, roomName, status, pageNum, pageSize },
  });
}

//预约审核接口
export function reserveReview(data) {
  return request({
    url: "/manager/reserveReview",
    method: "post",
    data,
  });
}

//获取小组成员
export function getGroupMember(uuid) {
  return request({
    url: "/reserve/getGroupMember",
    method: "get",
    params: { uuid },
  });
}

export function getactivityyorder(
  beginDate,
  endDate,
  search,
  roomName,
  status,
  pageNum,
  pageSize
) {
  return request({
    url: "/manager/getActivityReserveStatus",
    method: "get",
    params: { beginDate, endDate, search, roomName, status, pageNum, pageSize },
  });
}

//添加违约操作
export function saveByResv(data) {
  return request({
    url: "/credit/rec/saveByResv",
    method: "post",
    data,
  });
}

//添加
export function tagNotClass(data) {
  return request({
    url: "/manager/tagNotClass",
    method: "post",
    data,
  });
}

//管理员占用实验室
export function adminOccupy(data) {
  return request({
    url: "/reserve/adminOccupy",
    method: "post",
    data,
  });
}

//教学排课状况-延迟下课操作
export function delayEnd(data) {
  return request({
    url: "/reserve/delayEnd",
    method: "post",
    data,
  });
}

//教学排课状况-查看教师信息
export function getTeacherInfo(params) {
  return request({
    url: "/manager/getTeacherInfo",
    method: "get",
    params,
  });
}

//管理员占用-添加占用
export function addadminOccupy(data) {
  return request({
    url: "/reserve/adminOccupy",
    method: "post",
    data,
  });
}

//管理员占用-获取占用列表
export function getadminOccupy(params) {
  return request({
    url: "/manager/getAdminOccupyReserveStatus",
    method: "get",
    params,
  });
}

//自由预约-列表
export function getaccesspage(params) {
  return request({
    url: "/reserve/access/page",
    method: "get",
    params,
  });
}

//自由预约-编辑
export function updateaccess(data) {
  return request({
    url: "/reserve/access/update",
    method: "post",
    data
  });
}

//自由预约-删除
export function delaccess(data) {
  return request({
    url: "/reserve/access/delete",
    method: "post",
    data
  });
}

//自由预约-提前结束
export function endaccess(data) {
  return request({
    url: "/reserve/access/end",
    method: "post",
    data
  });
}


//大型仪器预约状况
export function getDevLargeReserveStatus(params) {
  return request({
    url: "/manager/getDevLargeReserveStatus",
    method: "get",
    params
  });
}

