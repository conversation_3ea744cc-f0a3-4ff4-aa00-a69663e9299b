import request from '@admin/utils/request';

//获取当天排课日历表
// export function getDaycourses() {
//     return request({
//         url: '/manager/getTodayReserveList',
//         method: 'get',
//     })
// }

//获取当天排课日历表（以实验室为主）---管理端
export function getDaycourses(params) {
    return request({
        url: '/manager/getTodayReserveListByRoomCopy',
        method: 'get',
        params
    })
}

//获取当天排课日历表（以实验室为主）---用户端
export function getDaycoursesTeacher(params) {
    return request({
        url: '/api/reserve/toDayCourse',
        method: 'get',
        params
    })
}


// export function getDaycourses(roomFloor, buildingNo, teachingWeek, week) {
//     return request({
//         url: '/manager/getTodayReserveListByRoom',
//         method: 'get',
//         params: { roomFloor, buildingNo, teachingWeek, week }
//     })
// }

//获取当周排课日历表（以周几为主）
export function getWeekcourses(beginDate, endDate, roomIds, teachingWeek, yearTermId) {
    return request({
        url: '/manager/getReserveListByDate',
        method: 'get',
        params: { beginDate, endDate, roomIds, teachingWeek, yearTermId }
    })
}

//获取当前星期和周次
export function getWeek() {
    return request({
        url: 'yearTerm/getWeekByDate',
        method: 'get',
    })
}

//获取周次对应时间范围
export function getWeekTime(yearTermId, week) {
    return request({
        url: '/yearTerm/getWeekDate',
        method: 'get',
        params: { yearTermId, week }
    })
}


//楼层平面图接口-获取当前实验室的当前课程信息
export function getRoomCourse(floorNo, roomSn) {
    return request({
        url: '/room/getNowReserveRoom',
        method: 'get',
        params: { floorNo, roomSn }
    })
}
//楼层平面图接口-获取楼层所有实验室
export function getRooms(floorNo, roomFloor) {
    return request({
        url: '/room/getNowRoomFloor',
        method: 'get',
        params: { floorNo, roomFloor }
    })
}

//根据教师获取课程相关信息
export function getCourseByTeacher(params) {
    return request({
        url: '/testPlan/getCourseByTeacher',
        method: 'get',
        params
    })
}

//获取教师下拉框
export function getAccNo(key, num) {
    return request({
        url: '/account',
        method: 'get',
        params: { key, num }
    })
}

//直接排课
export function toDayArrangementCourse(data) {
    return request({
        url: '/reserve/toDayArrangementCourse',
        method: 'post',
        data
    })
}

//修改排课
export function updateArrangementCourse(data) {
    return request({
        url: '/reserve/updateArrangementCourse',
        method: 'post',
        data
    })
}

//获取可用实验室列表
export function getAvailableRoom(params) {
    return request({
        url: '/manager/getAvailableRoom',
        method: 'get',
        params
    })
}

//获取课程预约详情-主要用于获取该预约是否为两个实验室
export function courseDetail(params) {
    return request({
        url: '/reserve/courseDetail',
        method: 'get',
        params
    })
}
