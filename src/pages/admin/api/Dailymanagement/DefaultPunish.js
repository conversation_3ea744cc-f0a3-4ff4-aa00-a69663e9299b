import request from '@admin/utils/request';

// 获取违约状况数据表格
export function getCreditRec(beginDate,endDate,logonName,deptName,className,roomName,devName,creditKindId,status,pageNum,pageSize,orderItems,orderRule) {
    return request({
        url: '/credit/rec/page',
        method: 'get',
        params:{beginDate,endDate,logonName,deptName,className,roomName,devName,creditKindId,status,pageNum,pageSize,orderItems,orderRule}
    })
}

// 添加违约
export function postCreditRec(data) {
    return request({
        url: '/credit/rec/save',
        method: 'post',
        data
    })
}

// 获取违约类型下拉框
export function getCreditKind() {
    return request({
        url: '/credit/kind/getAll',
        method: 'get',
    })
}

// 获取信用规则下拉框
export function getCreditRule() {
    return request({
        url: '/credit/rule/getAll',
        method: 'get',
    })
}

// 取消违约  TODOTEST
export function cancel(data) {
    return request({
        url: '/credit/rec/cancel',
        method: 'post',
        data
    })
}




// 处罚状况数据表格
export function getPunish(params) {
  return request({ url: '/credit/punish/rec/page',
        method: 'get',
        params: params
    })
}

// 管理员处罚指定用户
export function postCreditRecPunish(data) {
    return request({
        url: '/credit/punish/rec/punish',
        method: 'post',
        data
    })
}

// 管理员重置所有用户积分
export function postCreditRecReset(data) {
    return request({
        url: '/credit/punish/rec/rest',
        method: 'post',
        data
    })
}

// 取消处罚
export function cancelpunish(data) {
    return request({
        url: '/credit/punish/rec/cancel',
        method: 'post',
        data
    })
}

// 查询设备类型
export function getDevKind() {
    return request({
        url: '/devKind/getDevKind',
        method: 'get',
        params: {}
    })
}

// 查询所有子系统
export function getSubSystemKind() {
  return request({
    url: '/credit/sub-system/dict',
    method: 'get'
  })
}
