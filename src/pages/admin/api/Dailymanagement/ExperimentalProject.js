import request from '@admin/utils/request';

//获取实验项目列表
export function getTestPlanList(yearTermId,roomName,schoolClassId,courseId,teacherAccNo,teacherAccName,pageNum,pageSize) {
    return request({
        url: '/testPlan/getTestPlanList',
        method: 'get',
        params:{yearTermId,roomName,schoolClassId,courseId,teacherAccNo,teacherAccName,pageNum,pageSize}
    })
}


//获取实验项目详情列表
export function getPlanDetail(testPlanId,pageNum,pageSize,orderModel,orderKey) {
    return request({
        url: '/testPlan/getTestPlanByIdToManager',
        method: 'get',
        params:{testPlanId,pageNum,pageSize,orderModel,orderKey}
    })
}

