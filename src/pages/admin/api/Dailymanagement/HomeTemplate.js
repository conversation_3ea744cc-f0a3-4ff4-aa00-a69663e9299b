import request from "@admin/utils/request";


// 文件列表分页查询
export function getFile(params) {
  return request({
    url: '/home/<USER>/page',
    method: 'get',
    params
  })
}
// 模版删除
export function templateDetail(params) {
  return request({
    url: '/home/<USER>/detail',
    method: 'get',
    params
  })
}
// 模版新增
export function templateAdd(data) {
  return request({
    url: '/home/<USER>/save',
    method: 'post',
    data
  })
}
// 模版编辑
export function templateDelete(data) {
  return request({
    url: '/home/<USER>/delete',
    method: 'post',
    data
  })
}
// 模版更新
export function templateEdit(data) {
  return request({
    url: '/home/<USER>/update',
    method: 'post',
    data
  })
}
// 模版预览校验
export function templateValidate(data) {
  return request({
    url: '/home/<USER>/validate',
    method: 'post',
    data
  })
}
// 预览模版
export function templatePreview(params) {
  return request({
    url: '/home/<USER>/preview',
    method: 'get',
    params
  })
}
// 设置首页默认模版
export function templateDefault(data) {
  return request({
    url: '/home/<USER>/default',
    method: 'post',
    data
  })
}
// 模版图片上传接口
export function templateUpload(data) {
  return request({
    url: '/home/<USER>/upload',
    method: 'post',
    data
  })
}
// 模版类型
export function templateKind(params) {
  return request({
    url: '/home/<USER>/template-kind',
    method: 'get',
    params
  })
}
// 添加首页模版绑定关系
export function templateKindSave(data) {
  return request({
    url: '/home/<USER>/save',
    method: 'post',
    data
  })
}
// 分页获取首页模版绑定列表
export function templateKindPage(params) {
  return request({
    url: '/home/<USER>/page',
    method: 'get',
    params
  })
}
// 批量删除首页模版绑定关系
export function templateKindDelete(data) {
  return request({
    url: '/home/<USER>/delete',
    method: 'post',
    data
  })
}
