import request from '@admin/utils/request';

//上网监控-监控模式下拉框
export function getAll_library(key,num) {
    return request({
        url: '/internet/library/getAll',
        method: 'get',
        params:{key,num}
    })
}

//上网监控-监控模式下拉框2-去掉了全部禁止和全部允许
export function getAll_library2(key,num) {
    return request({
        url: '/internet/library/getCustomAll',
        method: 'get',
        params:{key,num}
    })
}

//上网监控-系统设置列表数据获取
export function getmonitor(pageNum,pageSize,internetLibraryName,internetLibraryKind,orderItems,orderRule) {
    return request({
        url: '/sys/internet/monitor',
        method: 'get',
        params:{pageNum,pageSize,internetLibraryName,internetLibraryKind,orderItems,orderRule}
    })
}
//上网监控-新增系统配置
export function addmonitor(data) {
    return request({
        url: '/sys/internet/monitor',
        method: 'post',
        data
    })
}
//上网监控-修改系统配置
export function editmonitor(data) {
    return request({
        url: '/sys/internet/monitor/update',
        method: 'post',
        data
    })
}
//上网监控-删除系统配置
export function delmonitor(data) {
    return request({
        url: '/sys/internet/monitor/delete',
        method: 'post',
        data
    })
}

//上网监控-地址库列表数据获取
export function getlibrary(pageNum,pageSize,internetLibraryName,orderItems,orderRule) {
    return request({
        url: '/internet/library',
        method: 'get',
        params:{pageNum,pageSize,internetLibraryName,orderItems,orderRule}
    })
}
//上网监控-新增地址库
export function addlibrary(data) {
    return request({
        url: '/internet/library',
        method: 'post',
        data
    })
}
//上网监控-修改网络库
export function editlibrary(data) {
    return request({
        url: '/internet/library/update',
        method: 'post',
        data
    })
}
//上网监控-删除网络库
export function dellibrary(data) {
    return request({
        url: '/internet/library/delete',
        method: 'post',
        data
    })
}


//上网监控-网络地址获取
export function getaddress(pageNum,pageSize,internetAddress,internetLibraryName,internetLibraryKind,orderItems,orderRule) {
    return request({
        url: '/internet/address',
        method: 'get',
        params:{pageNum,pageSize,internetAddress,internetLibraryName,internetLibraryKind,orderItems,orderRule}
    })
}
//上网监控-新增地址库
export function addaddress(data) {
    return request({
        url: '/internet/address',
        method: 'post',
        data
    })
}
//上网监控-修改网络库
export function editaddress(data) {
    return request({
        url: '/internet/address/update',
        method: 'post',
        data
    })
}
//上网监控-删除网络库
export function deladdress(data) {
    return request({
        url: '/internet/address/delete',
        method: 'post',
        data
    })
}

//上网监控-获取上网监控信息
export function getInternetMonitorInfo(params) {
    return request({
        url: '/device/getInternetMonitorInfo',
        method: 'get',
        params
    })
}

//上网监控-设置临时监控模式
export function updateInternetMonitor(data) {
    return request({
        url: '/device/updateInternetMonitor',
        method: 'post',
        data
    })
}

//上网监控-获取上网日志
export function getInternetlog(params) {
    return request({
        url: '/internet/log',
        method: 'get',
        params
    })
}