import request from "@admin/utils/request";

//获取banner列表
export function getbanner (params) {
  return request({
    url: "/banner/image/page",
    method: "get",
    params,
  });
}

//新增banner
export function addbanner (data) {
  return request({
    url: "/banner/image/save",
    method: "post",
    data,
  });
}

//修改banner
export function editbanner (data) {
  return request({
    url: "/banner/image/edit",
    method: "post",
    data,
  });
}

//删除banner
export function delbanner (data) {
  return request({
    url: "/banner/image/delete",
    method: "post",
    data,
  });
}

//上传banner
export function uploadbanner (data) {
  return request({
    url: "/banner/image/upload",
    method: "post",
    data,
  });
}

//banner分类
export function categorybanner (params) {
  return request({
    url: "/banner/image/category/list",
    method: "get",
    params,
  });
}

//按分类获取banner图片列表
export function imagebanner (params) {
  return request({
    url: "/banner/image/list",
    method: "get",
    params,
  });
}
