import request from '@admin/utils/request';

// 获取应用分类列表
export function categoryList (params) {
  return request({
    url: '/home/<USER>/page',
    method: 'get',
    params
  })
}
// 入参字段说明
// 字段	类型	必填	含义	含义
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	orderNum, categoryId
// orderRule	String	否	排序规则（, 号分割）	asc
// categoryName	String	否	分类名称	实验室
// categoryVisible	Integer	否	是否启用 0 - 禁用，1 - 启用	1

// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	分类ID
// └─ uuid	String	唯一值
// └─ categoryName	String	分类名称
// └─ categoryIcon	String	分类图标类名或标识
// └─ categorySvg	String	分类SVG图标代码或路径
// └─ categoryVisible	Integer	是否显示 0 - 否, 1 - 是
// └─ orderNum	Integer	排序权重
// └─ memo	String	备注
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

// 新增应用分类
export function categorySave (data) {
  return request({
    url: "/home/<USER>/save",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// categoryName	String	是	分类名称
// categoryIcon	String	否	分类图标类名或标识
// categorySvg	String	否	分类SVG图标代码或路径
// categoryVisible	Integer	是	是否显示 0 - 否, 1 - 是
// orderNum	Integer	是	排序权重
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Integer	分类id

// 更新应用分类
export function categoryUpdate (data) {
  return request({
    url: "/home/<USER>/update",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	唯一值
// categoryName	String	是	分类名称
// categoryIcon	String	否	分类图标类名或标识
// categorySvg	String	否	分类SVG图标代码或路径
// categoryVisible	Integer	是	是否显示 0 - 否, 1 - 是
// orderNum	Integer	是	排序权重
// memo	String	否	备注

// 删除应用分类
export function categoryDelete (data) {
  return request({
    url: "/home/<USER>/delete",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值

// 应用分类icon上传
export function uploadimg (data) {
  return request({
    url: "/fileUpload/img",
    method: "post",
    data,
  });
}