import request from '@admin/utils/request';

// 应用分类下拉框
export function categorySelect (params) {
  return request({
    url: '/home/<USER>/select',
    method: 'get',
    params
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// key	String	否	关键字	实验室
// num	Integer	否	数量，默认10，传0时表示查询所有

// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	分类ID
// └─ uuid	String	唯一值
// └─ categoryName	String	分类名称
// └─ categoryIcon	String	分类图标类名或标识
// └─ categorySvg	String	分类SVG图标代码或路径
// └─ orderNum	Integer	排序权重

// 子应用分页列表
export function subAppList (params) {
  return request({
    url: '/home/<USER>/page',
    method: 'get',
    params
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	orderNum, appId
// orderRule	String	否	排序规则（, 号分割）	asc
// appName	String	否	子应用名称	实验室基本信息
// categoryUuid	String	否	子应用分类uuid	1909166948307046400
// categoryName	String	否	子应用分类名称	安全
// appEnabled	Integer	否	是否启用 0 - 禁用，1 - 启用	1
// pcUrl	String	否	pc端地址	baidu.com
// // mobileUrl	String	否	移动端地址	mobile.baidu.com
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ appId	Integer	应用ID
// └─ uuid	String	唯一值
// └─ appName	String	应用名称
// └─ appCode	String	应用编码
// └─ categoryId	Integer	所属分类
// └─ categoryName	String	所属分类名称
// └─ appIcon	String	应用图标类名或标识
// └─ appSvg	String	应用SVG图标代码或路径
// └─ pcUrl	String	PC端链接地址
// └─ mobileUrl	String	移动端链接地址（空则共用PC地址）
// └─ orderNum	Integer	排序权重
// └─ appEnabled	Integer	是否启用 0 - 否，1 - 是
// └─ memo	String	备注
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

// 新增子应用
export function subAppSave (data) {
  return request({
    url: "/home/<USER>/save",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// appName	String	是	应用名称
// appCode	String	是	应用编码
// categoryId	Integer	是	所属分类
// appIcon	String	否	应用图标类名或标识
// appSvg	String	否	应用SVG图标代码或路径
// pcUrl	String	是	PC端链接地址
// mobileUrl	String	否	移动端链接地址（空则共用PC地址）
// orderNum	Integer	是	排序权重
// appEnabled	Integer	是	是否启用 0 - 否，1 - 是
// memo	String	否	备注

// 更新子应用
export function subAppUpdate (data) {
  return request({
    url: "/home/<USER>/update",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	唯一值
// appName	String	是	应用名称
// appCode	String	是	应用编码
// categoryId	Integer	是	所属分类
// appIcon	String	否	应用图标类名或标识
// appSvg	String	否	应用SVG图标代码或路径
// pcUrl	String	是	PC端链接地址
// mobileUrl	String	否	移动端链接地址（空则共用PC地址）
// orderNum	Integer	是	排序权重
// appEnabled	Integer	是	是否启用 0 - 否，1 - 是
// memo	String	否	备注

// 删除子应用
export function subAppDelete (data) {
  return request({
    url: "/home/<USER>/delete",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
