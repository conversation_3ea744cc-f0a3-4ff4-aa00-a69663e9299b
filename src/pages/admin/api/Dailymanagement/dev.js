import request from '@admin/utils/request';

//获取上机记录
export function getdevRes(beginTime,endTime,search,mac,devName,pageNum,pageSize) {
    return request({
        url: '/devRec',
        method: 'get',
        params:{beginTime,endTime,search,mac,devName,pageNum,pageSize}
    })
}

//上机管理模块接口
//-获取上机信息
export function getOnBoardInfo(params) {
    return request({
        url: '/device/getOnBoardInfo',
        method: 'get',
        params
    })
}
//命令下发
export function commandIssued(data) {
    return request({
        url: '/device/commandIssued',
        method: 'post',
        data
    })
}
//重置网卡
export function resetmac(data) {
    return request({
        url: '/device/resetMac',
        method: 'post',
        data
    })
}

// 提前结束预约(2021/01/25: lishihao 修改)
export  function overReserveNew(params) {
	return request({
	    url: '/manage/advanceReserve',
	    method: 'get',
	    params
	})
}
// 获取区域
export  function getRoom(params) {
	return request({
	    url: '/room/getRoom',
	    method: 'get',
	    params
	})
}
// 获取类型
export  function getKind(params) {
	return request({
	    url: '/devKind/getDevKind',
	    method: 'get',
	    params
	})
}

//座位使用状况
export  function getuseState(params) {
	return request({
	    url: '/device/useState',
	    method: 'get',
	    params
	})
}

// 查询用户信息
export  function getAccnoInfo(params) {
	return request({
	    url: '/account/getInfo',
	    method: 'get',
	    params
	})
}

//座位使用记录
export  function getSeatUseRecord(params) {
	return request({
	    url: '/getOnBoardRecord',
	    method: 'get',
	    params
	})
}

// 部门管理
// 查询
export  function getAccDept(params) {
	return request({
	    url: '/accDept',
	    method: 'get',
	    params
	})
}


//电控和设备绑定关系接口
//获取表格数据
export function getAllByEcu(roomName,devName) {
	return request({
	    url: '/daily/dev/getAllByEcu',
	    method: 'get',
	    params:{roomName,devName}
	})
}
//上电操作
export function ecuDevPowerOn(data) {
	return request({
	    url: '/daily/dev/ecuDevPowerOn',
	    method: 'post',
	    data
	})
}

//断电操作
export function ecuDevPowerOFF(data) {
	return request({
	    url: '/daily/dev/ecuDevPowerOff',
	    method: 'post',
	    data
	})
}