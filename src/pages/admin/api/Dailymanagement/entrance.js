import request from '@admin/utils/request';

//实验室类型下拉框
export function getRoomType () {
  return request({
    url: '/room/getRoomKindAll',
    method: 'get',
  })
}

//获取实验室列表
export function getRoom (roomName, roomKindId, floorNo, openState, pageNum, pageSize) {
  return request({
    url: '/room',
    method: 'get',
    params: { roomName, roomKindId, floorNo, openState, pageNum, pageSize }
  })
}


//刷卡记录
export function getDoorCard (beginDate, endDate, roomName, peopleSearch, cardMode, pageNum, pageSize, orderItems, orderRule) {
  return request({
    url: '/doorCardRec',
    method: 'get',
    params: { beginDate, endDate, roomName, peopleSearch, cardMode, pageNum, pageSize, orderItems, orderRule }
  })
}

//远程开门
export function openDoor (data) {
  return request({
    url: '/doorCtrl/remoteDoorOpen',
    method: 'post',
    data
  })
}
//远程关门
export function closeDoor (data) {
  return request({
    url: '/doorCtrl/remoteDoorClose',
    method: 'post',
    data
  })
}
//远程重启
export function resetDoor (data) {
  return request({
    url: '/doorCtrl/remote-reboot',
    method: 'post',
    data
  })
}

//获取实验室当前预约信息
export function getCurrentReserve (params) {
  return request({
    url: '/daily/room/getCurrentReserve',
    method: 'get',
    params
  })
}

//获取实验室使用状况
export function getrommusedata (pageNum, pageSize, roomSn, roomName, buildingName, campusName, roomKindName, useStatus) {
  return request({
    url: '/daily/room',
    method: 'get',
    params: { pageNum, pageSize, roomSn, roomName, buildingName, campusName, roomKindName, useStatus }
  })
}




//实验室分配-楼宇信息
export function getbuildings () {
  return request({
    url: '/room/distribution/building',
    method: 'get'
  })
}

//实验室分配-楼宇信息
export function getfloors (uuid) {
  return request({
    url: '/room/distribution/floor',
    method: 'get',
    params: { uuid }
  })
}

//实验室分配-楼层下的实验室
export function getrooms (uuid) {
  return request({
    url: '/building/floor/room',
    method: 'get',
    params: { uuid }
  })
}

//实验室分配-楼层图上传获取路径
export function labUpload (data) {
  return request({
    url: '/fileUpload/img',
    method: 'post',
    data
  })
}

//实验室分配-更新对应楼层的楼层图
export function updateImage (data) {
  return request({
    url: '/building/floor/updateImage',
    method: 'post',
    data
  })
}

//实验室分配-更新对应楼层信息
export function updateImageInfo (data) {
  return request({
    url: '/building/floor/updateImageInfo',
    method: 'post',
    data
  })
}

//实验室分配-保存实验室分配关系
export function updateroomdistribution (data) {
  return request({
    url: '/room/distribution',
    method: 'post',
    data
  })
}

//实验室分配-保存实验室分配关系-批量
export function updateBatchroomdistribution (data) {
  return request({
    url: '/room/distribution/updateBath',
    method: 'post',
    data
  })
}

//实验室分配-解除实验室分配关系
export function deleteroomdistribution (data) {
  return request({
    url: '/room/distribution/delete',
    method: 'post',
    data
  })
}

//实验室分配-解除实验组分配关系
export function deleteroomdistributionBatch (data) {
  return request({
    url: '/room/distribution/deleteBatch',
    method: 'post',
    data
  })
}

//实验室分配-获取科研实验的实验室分配
export function getresearchRoom (params) {
  return request({
    url: '/room/distribution/research',
    method: 'get',
    params
  })
}

//实验室分配-获取用户的实验室分配
export function getuserRoom (params) {
  return request({
    url: '/room/distribution/user',
    method: 'get',
    params
  })
}

//实验室分配-获取实验室分配情况
export function getdistributionRoom (params) {
  return request({
    url: '/room/distribution/room',
    method: 'get',
    params
  })
}

//实验室分配-获取楼层下实验室分组列表
export function getRoomTeam (uuid) {
  return request({
    url: '/building/floor/roomKind',
    method: 'get',
    params: { uuid }
  })
}

//实验室分配-实验室实验中心分配课题组
export function saveByRoomKind (data) {
  return request({
    url: '/room/distribution/saveByRoomKind',
    method: 'post',
    data
  })
}
