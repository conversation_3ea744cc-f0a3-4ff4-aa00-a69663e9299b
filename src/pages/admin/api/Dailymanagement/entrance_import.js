import request from "@admin/utils/request_import";
//导出刷卡记录
export function importFile(data) {
  return request({
    url: "/doorCardRec/exportExcel",
    method: "post",
    responseType: "blob", // 设置响应数据类型为 blob
    data,
  });
}

//导出资产表格
export function exportAssets(data) {
  return request({
    url: "/device/exportAssets",
    method: "post",
    responseType: "blob", // 设置响应数据类型为 blob
    data,
  });
}

//实验室分配-导出实验室分配情况
export function importFile_allocationlab(data) {
  return request({
    url: "/room/distribution/exportExcel",
    method: "post",
    responseType: "blob", // 设置响应数据类型为 blob
    data,
  });
}
