import request from '@admin/utils/request';

// 获取友情链接列表
export function friendLinkList (params) {
  return request({
    url: '/home/<USER>/page',
    method: 'get',
    params
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	orderNum, sid
// orderRule	String	否	排序规则（, 号分割）	asc
// linkName	String	否	友情链接名称	百度
// linkUrl	String	否	友情链接URL	baidu
// linkVisible	Integer	否	是否显示 0 - 否, 1 - 是	1

// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ sid	Integer	主键ID
// └─ uuid	String	唯一值
// └─ linkName	String	链接名称
// └─ linkUrl	String	链接地址
// └─ linkIcon	String	链接图标类名或标识
// └─ linkSvg	String	链接SVG图标代码或路径
// └─ linkVisible	Integer	是否显示 0 - 否, 1 - 是
// └─ orderNum	Integer	排序权重
// └─ memo	String	备注
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

// 新增友情链接
export function friendLinkSave (data) {
  return request({
    url: "/home/<USER>/save",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// linkName	String	是	链接名称
// linkUrl	String	是	链接地址
// linkIcon	String	否	链接图标类名或标识
// linkSvg	String	否	链接SVG图标代码或路径
// linkVisible	Integer	是	是否显示 0 - 否, 1 - 是
// orderNum	Integer	是	排序权重
// memo	String	否	备注

// 更新友情链接
export function friendLinkUpdate (data) {
  return request({
    url: "/home/<USER>/update",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	唯一值
// linkName	String	是	链接名称
// linkUrl	String	是	链接地址
// linkIcon	String	否	链接图标类名或标识
// linkSvg	String	否	链接SVG图标代码或路径
// linkVisible	Integer	是	是否显示 0 - 否, 1 - 是
// orderNum	Integer	是	排序权重
// memo	String	否

// 删除友情链接
export function friendLinkDelete (data) {
  return request({
    url: "/home/<USER>/delete",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值