import request from "@admin/utils/request";
// 菜单树形结构
export function menuTree () {
  return request({
    url: "/home/<USER>/tree",
    method: "get",
  });
}
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ menuId	Integer	菜单id
// └─ uuid	String	唯一值
// └─ menuParentId	Integer	父级id
// └─ menuName	String	菜单名称
// └─ menuAbbreviation	String	菜单简称
// └─ menuIcon	String	菜单图标类名或标识
// └─ menuSvg	String	菜单SVG图标代码或路径
// └─ coverImage	String	菜单封面
// └─ imageUrl	String	背景图片地址
// └─ kind	Integer	类型：1 - 导航菜单, 2 - 超链接, 3 - 富文本, 4 - 文章列表, 5 - 下载
// └─ submenuStyle	Integer	下级菜单展示方式，仅当 kind = 1 时：1 - 下级菜单平铺, 2 - 下级菜单为超链接列表 ；kind = 4时：1 - 列表展示 2 - 并列展示，其它无效
// └─ hyperlinks	String	超链接，类型为2时需要
// └─ property	Integer	属性（预留扩展字段）
// └─ orderNum	Integer	排序
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ showHome	boolean	是否展示到首页
// └─ homeOrderNum	Integer	展示到首页的顺序
// └─ children	List	子菜单
//     └─ menuId	Integer	菜单id
//     └─ uuid	String	唯一值
//     └─ menuParentId	Integer	父级id
//     └─ menuName	String	菜单名称
//     └─ menuAbbreviation	String	菜单简称
//     └─ menuIcon	String	菜单图标类名或标识
//     └─ menuSvg	String	菜单SVG图标代码或路径
//     └─ coverImage	String	菜单封面
//     └─ imageUrl	String	背景图片地址
//     └─ kind	Integer	类型：1 - 导航菜单, 2 - 超链接, 3 - 富文本, 4 - 文章列表, 5 - 下载
//     └─ submenuStyle	Integer	下级菜单展示方式，仅当 kind = 1 时：1 - 下级菜单平铺, 2 - 下级菜单为超链接列表 ；kind = 4时：1 - 列表展示 2 - 并列展示，其它无效
//     └─ hyperlinks	String	超链接，类型为2时需要
//     └─ property	Integer	属性（预留扩展字段）
//     └─ orderNum	Integer	排序
//     └─ showHome	boolean	是否展示到首页
//     └─ homeOrderNum	Integer	展示到首页的顺序

//新增菜单
export function menuSave (data) {
  return request({
    url: "/home/<USER>/save",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// menuParentId	Integer	是	父级id
// menuName	String	是	菜单名称
// menuAbbreviation	String	是	菜单简称
// menuIcon	String	否	菜单图标类名或标识
// menuSvg	String	否	菜单SVG图标代码或路径
// coverImage	String	否	菜单封面
// imageUrl	String	否	背景图片地址
// kind	Integer	是	类型：1 - 导航菜单, 2 - 超链接, 3 - 富文本, 4 - 文章列表, 5 - 下载
// submenuStyle	Integer	否	下级菜单展示方式，仅当 kind = 1 时：1 - 下级菜单平铺, 2 - 下级菜单为超链接列表 ；kind = 4时：1 - 列表展示 2 - 并列展示，其它无效
// hyperlinks	String	否	超链接，类型为2时需要
// property	Integer	否	属性（预留扩展字段）
// orderNum	Integer	是	排序
// memo	String	否	备注
// showHome	boolean	否	是否展示到首页
// homeOrderNum	Integer	否	展示到首页的顺序
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//更新菜单
export function menuUpdate (data) {
  return request({
    url: "/home/<USER>/update",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	菜单唯一值
// menuParentId	Integer	是	父级id
// menuName	String	是	菜单名称
// menuAbbreviation	String	是	菜单简称
// menuIcon	String	否	菜单图标类名或标识
// menuSvg	String	否	菜单SVG图标代码或路径
// coverImage	String	否	菜单封面
// imageUrl	String	否	背景图片地址
// kind	Integer	是	类型：1 - 导航菜单, 2 - 超链接, 3 - 富文本, 4 - 文章列表, 5 - 下载
// submenuStyle	Integer	否	下级菜单展示方式，仅当 kind = 1 时：1 - 下级菜单平铺, 2 - 下级菜单为超链接列表 ；kind = 4时：1 - 列表展示 2 - 并列展示，其它无效
// hyperlinks	String	否	超链接，类型为2时需要
// property	Integer	否	属性（预留扩展字段）
// orderNum	Integer	是	排序
// showHome	boolean	否	是否展示到首页
// homeOrderNum	Integer	否	展示到首页的顺序
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//删除菜单
export function menuDelete (data) {
  return request({
    url: "/home/<USER>/delete",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//菜单类型字典
export function menuKindDict () {
  return request({
    url: "/home/<USER>/kind-dict",
    method: "get",
  });
}
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ label	String	字典标签
// └─ value	String	字典值

//获取菜单详情
export function menuDetail (params) {
  return request({
    url: "/home/<USER>/detail",
    method: "get",
    params,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// uuid	String	是	菜单唯一值	1927556964091564032
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	HomeMenuVO
// └─ menuId	Integer	菜单id
// └─ uuid	String	唯一值
// └─ menuParentId	Integer	父级id
// └─ menuName	String	菜单名称
// └─ menuAbbreviation	String	菜单简称
// └─ menuIcon	String	菜单图标类名或标识
// └─ menuSvg	String	菜单SVG图标代码或路径
// └─ coverImage	String	菜单封面
// └─ imageUrl	String	背景图片地址
// └─ kind	Integer	类型：1-导航菜单,2-超链接,3-富文本,4-文章列表,5-下载
// └─ submenuStyle	Integer	下级菜单展示方式（仅当 kind=1 时有效）：1-普通导航无点击,2-下级菜单平铺,3-下级菜单为超链接列表
// └─ hyperlinks	String	超链接，类型为2时需要
// └─ property	Integer	属性（预留扩展字段）
// └─ orderNum	Integer	排序
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ showHome	boolean	是否展示到首页
// └─ homeOrderNum	Integer	展示到首页的顺序

//获取富文本菜单详细
export function menuRichTextDetail (params) {
  return request({
    url: "/home/<USER>/rich-text-detail",
    method: "get",
    params,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	HomeMenuRichTextVO
// └─ menu	HomeMenuVO	菜单信息
//     └─ menuId	Integer	菜单id
//     └─ uuid	String	唯一值
//     └─ menuParentId	Integer	父级id
//     └─ menuName	String	菜单名称
//     └─ menuAbbreviation	String	菜单简称
//     └─ menuIcon	String	菜单图标类名或标识
//     └─ menuSvg	String	菜单SVG图标代码或路径
//     └─ coverImage	String	菜单封面
//     └─ imageUrl	String	背景图片地址
//     └─ kind	Integer	类型：1 - 导航菜单, 2 - 超链接, 3 - 富文本, 4 - 文章列表, 5 - 下载
//     └─ submenuStyle	Integer	下级菜单展示方式（仅当 kind = 1 时有效）：1 - 普通导航无点击, 2 - 下级菜单平铺, 3 - 下级菜单为超链接列表
//     └─ hyperlinks	String	超链接，类型为2时需要
//     └─ property	Integer	属性（预留扩展字段）
//     └─ orderNum	Integer	排序
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ showHome	boolean	是否展示到首页
//     └─ homeOrderNum	Integer	展示到首页的顺序
// └─ article	HomeMenuArticleVO	文章信息
//     └─ menuArticleId	Integer	菜单文章ID
//     └─ uuid	String	唯一标识符
//     └─ menuId	Integer	关联菜单ID（对应home_menu.menu_id）
//     └─ menuName	String	菜单名称
//     └─ title	String	文章标题
//     └─ coverImage	String	封面
//     └─ content	String	文章内容
//     └─ articleSummary	String	文章摘要
//     └─ tags	String	文章标签，多个标签用逗号分隔
//     └─ publishStatus	Integer	发布状态：1 - 草稿，2 - 已发布
//     └─ publishDate	LocalDateTime	发布时间（可预设未来时间）
//     └─ kind	Integer	文章类型（预留扩展）
//     └─ property	Integer	属性标识（预留扩展）
//     └─ viewCount	Integer	浏览次数
//     └─ createdBy	Integer	创建人ID（关联用户表）
//     └─ createdByLogonName	String	创建人学工号
//     └─ createdByTrueName	String	创建人姓名
//     └─ memo	String	备注信息
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	最后修改时间

//新增富文本菜单
export function menuSaveRichText (data) {
  return request({
    url: "/home/<USER>/save-rich-text",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// menu	HomeMenuSaveDTO	是	菜单信息
// └─ menuParentId	Integer	是	父级id
// └─ menuName	String	是	菜单名称
// └─ menuAbbreviation	String	是	菜单简称
// └─ menuIcon	String	否	菜单图标类名或标识
// └─ menuSvg	String	否	菜单SVG图标代码或路径
// └─ coverImage	String	否	菜单封面
// └─ imageUrl	String	否	背景图片地址
// └─ kind	Integer	是	类型：3 - 富文本
// └─ hyperlinks	String	否	超链接，类型为2时需要
// └─ property	Integer	否	属性（预留扩展字段）
// └─ orderNum	Integer	是	排序
// └─ memo	String	否	备注
// article	HomeMenuArticleSaveDTO	是	文章信息
// └─ menuId	Integer	是	关联菜单ID	新增时默认传0
// └─ title	String	是	文章标题
// └─ coverImage	String	否	封面
// └─ content	String	否	文章内容
// └─ tags	String	否	文章标签，多个标签用逗号分隔
// └─ publishStatus	Integer	是	发布状态：1 - 草稿，2 - 已发布
// └─ publishDate	LocalDateTime	是	发布时间（可预设未来时间）格式：yyyy - MM - dd HH: mm: ss
// └─ kind	Integer	否	文章类型（预留扩展）
// └─ property	Integer	否	属性标识（预留扩展）
// └─ memo	String	否	备注
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Integer

//更新富文本菜单
export function menuUpdateRichText (data) {
  return request({
    url: "/home/<USER>/update-rich-text",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// menu	HomeMenuSaveDTO	是	菜单信息
// └─ uuid	String	是	菜单唯一值
// └─ menuParentId	Integer	是	父级id
// └─ menuName	String	是	菜单名称
// └─ menuAbbreviation	String	是	菜单简称
// └─ menuIcon	String	否	菜单图标类名或标识
// └─ menuSvg	String	否	菜单SVG图标代码或路径
// └─ coverImage	String	否	菜单封面
// └─ imageUrl	String	否	背景图片地址
// └─ kind	Integer	是	类型：1 - 导航菜单, 2 - 超链接, 3 - 富文本, 4 - 文章列表, 5 - 下载
// └─ submenuStyle	Integer	否	下级菜单展示方式（仅当 kind = 1 时有效）：1 - 普通导航无点击, 2 - 下级菜单平铺, 3 - 下级菜单为超链接列表
// └─ hyperlinks	String	否	超链接，类型为2时需要
// └─ property	Integer	否	属性（预留扩展字段）
// └─ orderNum	Integer	是	排序
// └─ memo	String	否	备注
// └─ showHome	boolean	否	是否展示到首页
// └─ homeOrderNum	Integer	否	展示到首页的顺序
// article	HomeMenuArticleSaveDTO	是	文章信息
// └─ uuid	String	是	唯一标识符
// └─ menuId	Integer	是	关联菜单ID（对应home_menu.menu_id）
// └─ title	String	是	文章标题
// └─ coverImage	String	否	封面
// └─ content	String	否	文章内容
// └─ tags	String	否	文章标签，多个标签用逗号分隔
// └─ publishStatus	Integer	是	发布状态：1 - 草稿，2 - 已发布
// └─ publishDate	LocalDateTime	是	发布时间（可预设未来时间）格式：yyyy - MM - dd HH: mm: ss
// └─ kind	Integer	否	文章类型（预留扩展）
// └─ property	Integer	否	属性标识（预留扩展）
// └─ memo	String	否	备注
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//删除富文本菜单
export function menuDeleteRichText (data) {
  return request({
    url: "/home/<USER>/delete-rich-text",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//菜单文章列表
export function menuArticleList (params
) {
  return request({
    url: "/home/<USER>/page",
    method: "get",
    params,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	publishDate, menuArticleId
// orderRule	String	否	排序规则（, 号分割）	desc
// beginDate	String	否	发布开始时间	2025-05 - 28
// endDate	String	否	发布结束时间	2025-05 - 30
// title	String	否	标题	测试
// tags	String	否	标签	光明
// menuUuid	String	否	菜单uuid	1927568210740252672
// menuName	String	否	菜单名称	实验
// publishStatus	Integer	否	发布状态：1 - 草稿，2 - 已发布	1
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ menuArticleId	Integer	菜单文章ID
// └─ uuid	String	唯一标识符
// └─ menuId	Integer	关联菜单ID（对应home_menu.menu_id）
// └─ menuName	String	菜单名称
// └─ title	String	文章标题
// └─ coverImage	String	封面
// └─ articleSummary	String	文章摘要
// └─ tags	String	文章标签，多个标签用逗号分隔
// └─ publishStatus	Integer	发布状态：1 - 草稿，2 - 已发布
// └─ publishDate	LocalDateTime	发布时间（可预设未来时间）
// └─ kind	Integer	文章类型（预留扩展）
// └─ property	Integer	属性标识（预留扩展）
// └─ viewCount	Integer	浏览次数
// └─ createdBy	Integer	创建人ID（关联用户表）
// └─ createdByLogonName	String	创建人学工号
// └─ createdByTrueName	String	创建人姓名
// └─ memo	String	备注信息
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	最后修改时间

//新增菜单文章
export function menuArticleSave (data) {
  return request({
    url: "/home/<USER>/save",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// menuId	Integer	是	关联菜单ID（对应home_menu.menu_id）	必须为kind = 4的菜单id
// title	String	是	文章标题
// coverImage	String	否	封面
// content	String	否	文章内容
// tags	String	否	文章标签，多个标签用逗号分隔
// publishStatus	Integer	是	发布状态：1 - 草稿，2 - 已发布
// publishDate	LocalDateTime	是	发布时间（可预设未来时间）格式：yyyy - MM - dd HH: mm: ss
// kind	Integer	否	文章类型（预留扩展）
// property	Integer	否	属性标识（预留扩展）
// memo	String	否	备注信息
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//更新菜单文章
export function menuArticleUpdate (data) {
  return request({
    url: "/home/<USER>/update",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一标识符
// menuId	Integer	是	关联菜单ID（对应home_menu.menu_id）	必须为kind = 4的菜单id
// title	String	是	文章标题
// coverImage	String	否	封面
// content	String	否	文章内容
// tags	String	否	文章标签，多个标签用逗号分隔
// publishStatus	Integer	是	发布状态：1 - 草稿，2 - 已发布
// publishDate	LocalDateTime	是	发布时间（可预设未来时间）格式：yyyy - MM - dd HH: mm: ss
// └─ date	LocalDate	否
//     └─ year	int	否
//     └─ month	short	否
//     └─ day	short	否
// └─ time	LocalTime	否
//     └─ hour	byte	否
//     └─ minute	byte	否
//     └─ second	byte	否
//     └─ nano	int	否
// kind	Integer	否	文章类型（预留扩展）
// property	Integer	否	属性标识（预留扩展）
// memo	String	否	备注信息
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

//删除菜单文章
export function menuArticleDelete (data) {
  return request({
    url: "/home/<USER>/delete",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息

// 菜单文章详情
export function menuArticleDetail (params) {
  return request({
    url: "/home/<USER>/detail",
    method: "get",
    params,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// uuid	String	是	uuid唯一值	1927594570527412224
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	HomeMenuArticleVO
// └─ menuArticleId	Integer	菜单文章ID
// └─ uuid	String	唯一标识符
// └─ menuId	Integer	关联菜单ID（对应home_menu.menu_id）
// └─ menuName	String	菜单名称
// └─ title	String	文章标题
// └─ coverImage	String	封面
// └─ content	String	文章内容
// └─ articleSummary	String	文章摘要
// └─ tags	String	文章标签，多个标签用逗号分隔
// └─ publishStatus	Integer	发布状态：1 - 草稿，2 - 已发布
// └─ publishDate	LocalDateTime	发布时间（可预设未来时间）
// └─ kind	Integer	文章类型（预留扩展）
// └─ property	Integer	属性标识（预留扩展）
// └─ viewCount	Integer	浏览次数
// └─ createdBy	Integer	创建人ID（关联用户表）
// └─ createdByLogonName	String	创建人学工号
// └─ createdByTrueName	String	创建人姓名
// └─ memo	String	备注信息
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	最后修改时间

//菜单下载内容列表
export function menuDownLoadList (params) {
  return request({
    url: "/home/<USER>/page",
    method: "get",
    params,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	publishDate, downloadId
// orderRule	String	否	排序规则（, 号分割）	desc
// beginDate	String	否	发布开始时间	2025-05 - 28
// endDate	String	否	发布结束时间	2025-05 - 30
// title	String	否	标题	实验室
// tags	String	否	标签	光明
// menuUuid	String	否	菜单uuid
// menuName	String	否	菜单名称	测试
// publishStatus	Integer	否	发布状态：1 - 草稿，2 - 已发布
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ downloadId	Integer	下载项ID
// └─ uuid	String	唯一值
// └─ menuId	Integer	关联菜单ID
// └─ menuName	String	菜单名称
// └─ title	String	用户可见标题
// └─ subtitle	String	副标题
// └─ tags	String	逗号分隔标签
// └─ attachmentUrl	String	文件地址
// └─ fileName	String	原始文件名（含扩展名）
// └─ fileSize	Long	文件大小(字节)
// └─ fileType	String	文件扩展名
// └─ publishStatus	Integer	1-草稿，2-发布
// └─ publishDate	LocalDateTime	发布时间（可预设未来时间）
// └─ createdBy	Integer	创建人ID
// └─ createdByLogonName	String	创建人学工号
// └─ createdByTrueName	String	创建人姓名
// └─ memo	String	备注信息
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

//新增菜单下载内容
export function menuDownLoadSave (data) {
  return request({
    url: "/home/<USER>/save",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// menuId	Integer	是	关联菜单ID	必须为kind = 5的菜单id
// title	String	是	用户可见标题
// subtitle	String	否	副标题
// tags	String	否	逗号分隔标签
// attachmentUrl	String	是	文件地址
// publishStatus	Integer	是	1 - 草稿，2 - 发布
// publishDate	LocalDateTime	是	发布时间（可预设未来时间）格式：yyyy - MM - dd HH: mm: ss
// memo	String	否	备注信息
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息

//更新菜单下载内容
export function menuDownLoadUpdate (data) {
  return request({
    url: "/home/<USER>/update",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// menuId	Integer	是	关联菜单ID	必须为kind = 5的菜单id
// title	String	是	用户可见标题
// subtitle	String	否	副标题
// tags	String	否	逗号分隔标签
// attachmentUrl	String	是	文件地址
// publishStatus	Integer	是	1 - 草稿，2 - 发布
// publishDate	LocalDateTime	是	发布时间（可预设未来时间）格式：yyyy - MM - dd HH: mm: ss
// memo	String	否	备注信息
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
//删除菜单下载内容
export function menuDownLoadDelete (data) {
  return request({
    url: "/home/<USER>/delete",
    method: "post",
    data,
  });
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息

//菜单文章的图片上传
export function uploadmenu (data) {
  return request({
    url: "/fileUpload/img",
    method: "post",
    data,
  });
}
//菜单下载的文件上传
export function uploadFile (data) {
  return request({
    url: "/fileUpload/file",
    method: "post",
    data,
  });
}
//上传视频
export function uploadvideo (data) {
  return request({
    url: "/video/upload",
    method: "post",
    data,
  });
}