import request from '@admin/utils/request';

//获取新闻列表
export function getNewsOrNotice (params) {
  return request({
    url: '/news',
    method: 'get',
    params
  })
}
//获取新闻列表
export function getNews (pageNum, pageSize, kind, status, title, orderKey, orderModel) {
  return request({
    url: '/news',
    method: 'get',
    params: { pageNum, pageSize, kind, status, title, orderKey, orderModel }
  })
}

//获取新闻详情
export function getNewsDetail (params) {
  return request({
    url: '/news/detail',
    method: 'get',
    params
  })
}

//删除新闻
export function delNews (data) {
  return request({
    url: '/news/delete',
    method: 'post',
    data
  })
}

//新增新闻
export function addNews (data) {
  return request({
    url: '/news',
    method: 'post',
    data
  })
}

//修改新闻
export function editNews (data) {
  return request({
    url: '/news/update',
    method: 'post',
    data
  })
}


//新增图片
export function addimg (data) {
  return request({
    url: '/news/upload',
    method: 'post',
    data
  })
}

//删除图片
export function delimg (data) {
  return request({
    url: '/news/deleteUploadImage',
    method: 'post',
    data
  })
}

//获取图片
export function getimgs (data) {
  return request({
    url: '/news/getUploadImage',
    method: 'get',
    data
  })
}
//设置置顶新闻
export function setTopNew (data) {
  return request({
    url: "/news/setTop",
    method: "post",
    data,
  })
}

//新闻封面上传
export function uploadimg (data) {
  return request({
    url: "/news/upload",
    method: "post",
    data,
  });
}