import request from "@admin/utils/request";

//班级下拉框
export function getclass(key, num, yearTermId) {
  return request({
    url: "/schoolClass/getAll",
    method: "get",
    params: { key, num, yearTermId },
  });
}

//获取课程班列表
export function getschoolClass(schoolClassName, yearTermId, pageNum, pageSize) {
  return request({
    url: "/schoolClass",
    method: "get",
    params: { schoolClassName, yearTermId, pageNum, pageSize },
  });
}

//编辑课程班
export function editschoolClass(data) {
  return request({
    url: "/schoolClass/update",
    method: "post",
    data,
  });
}

//新增课程班
export function addschoolClass(data) {
  return request({
    url: "/schoolClass",
    method: "post",
    data,
  });
}

//删除课程班
export function delschoolClass(data) {
  return request({
    url: "/schoolClass/delete",
    method: "post",
    data,
  });
}

//课程班成员获取
export function getschoolClassMember(
  classId,
  search,
  pageNum,
  pageSize,
  testPlanId
) {
  return request({
    url: "/schoolClassMember",
    method: "get",
    params: { classId, search, pageNum, pageSize, testPlanId },
  });
}

//课程班成员获取
export function editschoolClassMember(data) {
  return request({
    url: "/schoolClassMember/update",
    method: "post",
    data,
  });
}

//课程班成员获取
export function delschoolClassMember(data) {
  return request({
    url: "/schoolClassMember/delete",
    method: "post",
    data,
  });
}

//课程班成员获取
export function addschoolClassMember(data) {
  return request({
    url: "/schoolClassMember",
    method: "post",
    data,
  });
}

//导入课程班
export function importClass(data) {
  return request({
    url: "/schoolClass/fileSave",
    method: "post",
    data,
  });
}

//导入成员
export function importMember(data) {
  return request({
    url: "/schoolClassMember/fileSave",
    method: "post",
    data,
  });
}

//新增班级成员
export function saveschoolClassMember(data) {
  return request({
    url: "/schoolClassMember/saveByClassId",
    method: "post",
    data,
  });
}

//获取出勤状况
export function getattendance(params) {
  return request({
    url: "/reserve/attendance",
    method: "get",
    params
  });
}
