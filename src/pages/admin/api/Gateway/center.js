import request from "@admin/utils/request";

//获取实验中心
export function getCenter(params) {
  return request({
    url: "/room/center/home/<USER>",
    method: "get",
    params,
  });
}

//新增实验中心
export function saveCenter(data) {
  return request({
    url: "/room/center/home/<USER>",
    method: "post",
    data,
  });
}
//批量新增实验中心
export function saveBatchCenter(data) {
  return request({
    url: "/room/center/home/<USER>",
    method: "post",
    data,
  });
}

//删除实验中心
export function delCenter(data) {
  return request({
    url: "/room/center/home/<USER>",
    method: "post",
    data,
  });
}
//编辑实验中心
export function updateCenter(data) {
  return request({
    url: "/room/center/home/<USER>",
    method: "post",
    data,
  });
}
