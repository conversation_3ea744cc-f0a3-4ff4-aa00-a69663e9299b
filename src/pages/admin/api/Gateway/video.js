import request from "@admin/utils/request";

//获取视频列表
export function getvideos(params) {
  return request({
    url: "/video/home/<USER>",
    method: "get",
    params,
  });
}

//关联视频
export function bindvideos(data) {
  return request({
    url: "/video/home/<USER>",
    method: "post",
    data,
  });
}

//新增首页视频
export function addvideo(data) {
  return request({
    url: "/video/home/<USER>",
    method: "post",
    data,
  });
}

//删除首页视频
export function deletevideo(data) {
  return request({
    url: "/video/home/<USER>",
    method: "post",
    data,
  });
}

//视频下拉框
export function getvideoselect(params) {
  return request({
    url: "/video/list",
    method: "get",
    params,
  });
}
