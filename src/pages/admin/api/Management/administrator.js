import request from '@admin/utils/request';

export function getManager(pageNum,pageSize,search,manRoleId,orderKey,orderModel) {
  return request({
    url: '/manager/page',
    method: 'get',
    params:{pageNum,pageSize,search,manRoleId,orderKey,orderModel}
  })
}

export function addManager(data) {
  return request({
    url: '/manager/save',
    method: 'post',
    data
  })
}

export function updateManager(data) {
  return request({
    url: '/manager/update',
    method: 'post',
    data
  })
}

export function delManager(data) {
  return request({
    url: '/manager/delete',
    method: 'post',
    data
  })
}

export function getAccNo(key,num,type) {
  return request({
    url: '/account',
    method: 'get',
    params:{key,num,type}
  })
}

