import request from "@admin/utils/request";

//获取菜单列表
export function getFunction (data) {
  return request({
    url: "/function/list",
    method: "get",
    data,
  });
}
//权限分配
export function getAppMenuList () {
  return request({
    url: "/function/list-user",
    method: "get",
  });
}
//权限分配
export function getAppmenuTree () {
  return request({
    url: "/function/list-user",
    method: "get",
  });
}

export function getAllmenu (data) {
  return request({
    url: "/function/getAllMenu",
    method: "get",
    data,
  });
}

//添加菜单
export function addFunction (data) {
  return request({
    url: "/function/save",
    method: "post",
    data,
  });
}

//删除菜单
export function delFunction (data) {
  return request({
    url: "/function/delete",
    method: "post",
    data,
  });
}

//编辑菜单
export function updateFunction (data) {
  return request({
    url: "/function/update",
    method: "post",
    data,
  });
}

export function searchFunction (data) {
  return request({
    url: "/function/queryFunByRoleID",
    method: "get",
    data,
  });
}

export function getFnByroleId (roleId) {
  return request({
    url: "/function/queryFunByRoleID",
    method: "get",
    params: { roleId },
  });
}

//应用列表
export function getApplication (params) {
  return request({
    url: "/sysApplication/page",
    method: "get",
    params,
  });
}
//新增应用
export function saveApplication (data) {
  return request({
    url: "/sysApplication/save",
    method: "post",
    data,
  });
}
//更新应用
export function updateApplication (data) {
  return request({
    url: "/sysApplication/update",
    method: "post",
    data,
  });
}
//删除应用
export function deleteApplication (data) {
  return request({
    url: "/sysApplication/delete",
    method: "post",
    data,
  });
}
//开启应用
export function updateActive (data) {
  return request({
    url: "/sysApplication/updateActive",
    method: "post",
    data,
  });
}
//应用下拉框
export function getApplicationlist (params) {
  return request({
    url: "/sysApplication/list",
    method: "get",
    params,
  });
}
