import request from '@admin/utils/request';

export function getroleMan(pageNum,pageSize,roleName,orderKey,orderModel) {
  return request({
    url: '/roleMan',
    method: 'get',
    params:{pageNum,pageSize,roleName,orderKey,orderModel}
  })
}

export function getRoleData(roleUuid) {
  return request({
    url: '/roleData/getLabRoleData',
    method: 'get',
    params:{roleUuid}
  })
}

export function getRoleData_campus(roleUuid) {
  return request({
    url: '/roleData',
    method: 'get',
    params:{roleUuid}
  })
}

export function editRoleData(data) {
  return request({
    url: '/roleData/update',
    method: 'post',
    data
  })
}
// export function getroleMan(data) {
//   return request({
//     url: '/roleMan',
//     method: 'get',
//     params: {data}
//   })
// }

export function updateRole(data) {
  return request({
    url: '/roleMan/update',
    method: 'post',
    data
  })
}

export function delRole(data) {
  return request({
    url: '/roleMan/delete',
    method: 'post',
	data
  })
}

export function addRole(data) {
  return request({
    url: '/roleMan',
    method: 'post',
    data
  })
}

export function getAllrole(data) {
  return request({
    url: '/roleMan/getAll',
    method: 'get',
    data
  })
}

