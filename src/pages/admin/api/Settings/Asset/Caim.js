import request from "@admin/utils/request";

//获取认领列表
export function getClaimpage(params) {
  return request({
    url: "/device/claim/page",
    method: "get",
    params,
  });
}

//导入表格
export function importclaim(data) {
  return request({
    url: "/device/claim/import",
    method: "post",
    data,
  });
}

//资产确认
export function confirmclaim(data) {
  return request({
    url: "/device/claim/confirm",
    method: "post",
    data,
  });
}

// 获取实验中心带房间下拉框
export function getRoomKindSelect(params) {
  return request({
    url: "/room/getRoomKindSelect",
    method: "get",
    params
  });
}

//大型仪器资产认领
export function confirmlarge(data) {
  return request({
    url: "/device/claim/confirm-large",
    method: "post",
    data
  });
}
