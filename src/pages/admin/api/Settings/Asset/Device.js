import request from "@admin/utils/request";

//获取设备
export function getDevice(
  pageNum,
  pageSize,
  tagName,
  devName,
  devSn,
  roomName,
  deptName,
  devSource,
  directionOfUse,
  purchaseStartDate,
  purchaseEndDate,
  devKindName,
  ctrlMode,
  orderItems,
  orderRule,
  devProp
) {
  return request({
    url: "/device",
    method: "get",
    params: {
      pageNum,
      pageSize,
      tagName,
      devName,
      devSn,
      roomName,
      deptName,
      devSource,
      directionOfUse,
      purchaseStartDate,
      purchaseEndDate,
      devKindName,
      ctrlMode,
      orderItems,
      orderRule,
      devProp,
    },
  });
}

//单个添加设备
export function addDevice(data) {
  return request({
    url: "/device",
    method: "post",
    data,
  });
}
//批量添加设备
export function addManyDevice(data) {
  return request({
    url: "/device/createDevs",
    method: "post",
    data,
  });
}

//编辑设备
export function editDevice(data) {
  return request({
    url: "/device/update",
    method: "post",
    data,
  });
}

//删除设备
export function delDevice(data) {
  return request({
    url: "/device/delete",
    method: "post",
    data,
  });
}

//批量删除设备
export function delDeviceMany(data) {
  return request({
    url: "/device/batchDelete",
    method: "post",
    data,
  });
}

//批量设置设备控制方式
export function delDeviceCtrlMode(data) {
  return request({
    url: "/device/batchUpdateCtrlMode",
    method: "post",
    data,
  });
}

//设备下拉框
export function getAlldev(key, num) {
  return request({
    url: "/device/getAllDev",
    method: "get",
    params: { key, num },
  });
}

//获取实验室的最大设备编号
export function getMaxDevSn(roomId) {
  return request({
    url: "/device/getMaxDevSn",
    method: "get",
    params: { roomId },
  });
}

//导入资产/设备表格----药高院
export function importBariddAssets(data) {
  return request({
    url: "/device/importBariddAssets",
    method: "post",
    data,
  });
}

//导入资产/设备表格----通用
export function importMedicineSchoolAssets(data) {
  return request({
    url: "/device/importMedicineSchoolAssets",
    method: "post",
    data,
  });
}

//导入资产表格
export function importdevice(data) {
  return request({
    url: "/device/import",
    method: "post",
    data,
  });
}

//批量修改开放状态
export function batchUpdateOpenState(data) {
  return request({
    url: "/device/batchUpdateOpenState",
    method: "post",
    data,
  });
}
