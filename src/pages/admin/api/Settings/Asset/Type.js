import request from '@admin/utils/request';

export function getTypes(devKindName, pageNum, pageSize) {
	return request({
		url: '/device/getDevKindList',
		method: 'get',
		params: {
			devKindName,
			pageNum,
			pageSize
		}
	})
}

export function addType(data) {
	return request({
		url: '/device/saveDevKind',
		method: 'post',
		data
	})
}

export function editType(data) {
	return request({
		url: '/device/updateDevKind',
		method: 'post',
		data
	})
}


export function delType(data) {
	return request({
		url: '/device/deleteDevKind',
		method: 'post',
		data
	})
}




//获取设备类型下拉框的数据
// export function getAllType(devKindName) {
// 	return request({
// 		url: '/device/getDevKindList?devKindName=' + devKindName + '',
// 		method: 'get',
// 	})
// }
export function getAllType(devKindName) {
	return request({
		url: '/device/getApiDevKinAll?devKindName=' + devKindName + '',
		method: 'get',
	})
}
