import request from '@admin/utils/request';

export function getBuilding(pageNum, pageSize, buildingName, campusName,orderKey,orderModel) {
    return request({
        url: '/building',
        method: 'get',
        params: { pageNum, pageSize, buildingName, campusName,orderKey,orderModel}
    })
}

export function addBuilding(data) {
    return request({
        url: '/building',
        method: 'post',
        data
    })
}

export function editBuilding(data) {
    return request({
        url: '/building/update',
        method: 'post',
        data
    })
}


export function delBuilding(data) {
    return request({
        url: '/building/delete',
        method: 'post',
		data
    })
}


//获取所有校区数据下拉框
export function getAllCampus(key,num) {
    return request({
        url: '/campus/getAll',
        method: 'get',
        params:{key,num}
    })
}



//获取所有楼宇数据下拉框
export function getAllbuilding(key,num) {
    return request({
        url: '/building/getAll',
        method: 'get',
        params:{key,num}
    })
}