import request from '@admin/utils/request';

//获取收费标准
export function getrules(pageNum,pageSize,orderItems,orderRule) {
    return request({
        url: '/fee/rule',
        method: 'get',
        params: {pageNum,pageSize,orderItems,orderRule}
    })
}

//新增收费标准
export function addrules(data) {
    return request({
        url: '/fee/rule',
        method: 'post',
        data
    })
}

//修改收费标准
export function editrules(data) {
    return request({
        url: '/fee/rule/update',
        method: 'post',
        data
    })
}


//删除收费标准
export function delrules(data) {
    return request({
        url: '/fee/rule/delete',
        method: 'post',
        data
    })
}

//账单记录
export function getrec(pageNum,pageSize,beginDate,endDate,logonName,deptName,className,roomName,devName,status,orderItems,orderRule) {
    return request({
        url: '/fee/rec',
        method: 'get',
        params:{pageNum,pageSize,beginDate,endDate,logonName,deptName,className,roomName,devName,status,orderItems,orderRule}
    })
}