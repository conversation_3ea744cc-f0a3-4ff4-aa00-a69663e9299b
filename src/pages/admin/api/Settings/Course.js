import request from '@admin/utils/request';

//获取课程列表(一对多)
export function getCourse(deptName, courseName, courseSn, pageNum, pageSize, orderKey, orderModel,courseId,deptId) {
    return request({
        url: '/course',
        method: 'get',
        params: { deptName, courseName, courseSn, pageNum, pageSize, orderKey, orderModel,courseId,deptId}
    })
}

//获取课程列表(一对一)
export function getSignCourse(deptName, courseName, courseSn, pageNum, pageSize){
	return request({
	    url: '/course/getCourseAndTestItem',
	    method: 'get',
	    params: { deptName, courseName, courseSn, pageNum, pageSize}
	})
}

// 修改课程（一对一）
export function editSignCourse(data) {
    return request({
        url: '/course/updateCourseAndTestItem',
        method: 'post',
        data
    })
}

// 新增课程（一对一）
export function addSignCourse(data) {
    return request({
        url: '/course/addCourseAndTestItem',
        method: 'post',
        data
    })
}

//导入课程表格
export function importCourse(data) {
    return request({
        url: '/course/fileSave',
        method: 'post',
        data
    })
}

//修改课程
export function editCourse(data) {
    return request({
        url: '/course/update',
        method: 'post',
        data
    })
}

//修改实验项目
export function edittestPlan(data) {
    return request({
        url: '/testPlan/updateTestItem',
        method: 'post',
        data
    })
}


//新增课程
export function addCourse(data) {
    return request({
        url: '/course',
        method: 'post',
        data
    })
}

//新增实验项目
export function addtestPlan(data) {
    return request({
        url: '/testPlan/saveTestItem',
        method: 'post',
        data
    })
}


//删除课程
export function deleteCourse(data) {
    return request({
        url: '/course/delete',
        method: 'post',
        data
    })
}

//删除实验项目
export function deletetestPlan(data) {
    return request({
        url: '/testPlan/deleteTestItem',
        method: 'post',
        data
    })
}


//获取课程下拉框
export function getAllCourse(key,num) {
    return request({
        url: '/course/getAll',
        method: 'get',
		params:{key,num}
    })
}


//获取精品课程数据
export function getexcellentcourse() {
    return request({
        url: '/course/getExcellentCourse',
        method: 'get',
    })
}

//上传精品课程图片
export function uploadcourse(data) {
    return request({
        url: '/course/upload',
        method: 'post',
        data
    })
}

//设置精品课程
export function setcourse(data) {
    return request({
        url: '/course/setExcellentCourse',
        method: 'post',
        data
    })
}