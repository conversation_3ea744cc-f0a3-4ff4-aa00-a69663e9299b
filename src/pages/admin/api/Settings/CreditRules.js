import request from '@admin/utils/request';

//获取信用积分数据
export function getCreditScore(pageNum, pageSize,orderItems,orderRule) {
    return request({
        url: '/credit/score/page',
        method: 'get',
        params: { pageNum, pageSize,orderItems,orderRule}
    })
}

//编辑信用积分
export function editCreditScore(data) {
    return request({
        url: '/credit/score/update',
        method: 'post',
        data
    })
}


//获取信用规则数据
export function getCreRule(pageNum, pageSize,orderItems,orderRule) {
    return request({
        url: '/credit/rule/page',
        method: 'get',
        params: { pageNum, pageSize,orderItems,orderRule}
    })
}

//新增信用规则
export function addCreRule(data) {
    return request({
        url: '/credit/rule/save',
        method: 'post',
        data
    })
}

//修改信用规则
export function editCreRule(data) {
    return request({
        url: '/credit/rule/update',
        method: 'post',
        data
    })
}

//删除信用规则
export function delCreRule(data) {
    return request({
        url: '/credit/rule/delete',
        method: 'post',
        data
    })
}

//获取类型数据
export function getCreditKind(pageNum, pageSize,orderItems,orderRule) {
    return request({
        url: '/credit/kind/page',
        method: 'get',
        params: { pageNum, pageSize,orderItems,orderRule}
    })
}

//新增信用类型
export function addCreditKind(data) {
    return request({
        url: '/credit/kind/save',
        method: 'post',
        data
    })
}

//编辑信用类型
export function editCreditKind(data) {
    return request({
        url: '/credit/kind/update',
        method: 'post',
        data
    })
}

//编辑信用类型
export function delCreditKind(data) {
    return request({
        url: '/credit/kind/delete',
        method: 'post',
        data
    })
}

// 查询所有子系统
export function getSubSystemKind() {
  return request({
    url: '/credit/sub-system/dict',
    method: 'get'
  })
}

// 查询信用类型字典
export function getCreditKindDict() {
  return request({
    url: '/credit/kind/dict',
    method: 'get'
  })
}

