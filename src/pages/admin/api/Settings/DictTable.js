import request from '@admin/utils/request';

//字典表的增删改查
export function getDict(pageNum, pageSize, codeType) {
    return request({
        url: '/codingTable',
        method: 'get',
        params: { pageNum, pageSize, codeType}
    })
}

export function addDict(data) {
    return request({
        url: '/codingTable',
        method: 'post',
        data
    })
}

export function editDict(data) {
    return request({
        url: '/codingTable/update',
        method: 'post',
        data
    })
}

export function delDict(data) {
    return request({
        url: '/codingTable/delete',
        method: 'post',
        data
    })
}

//获取所有树形结构
export function getDictAll(codeType) {
    return request({
        url: '/codingTable/getAll',
        method: 'get',
        params: {codeType}
    })
}

//获取字典表类型
export function getTypeAll() {
    return request({
        url: '/codingTable/getType',
        method: 'get',
    })
}

//获取预约主题
export function getDictTitle(codeType, codeValue) {
    return request({
        url: '/codingTable/testName',
        method: 'get',
        params: { codeType, codeValue}
    })
}

//获取系统配置项
export function getsysconfig(key) {
    return request({
        url: '/sysConfig',
        method: 'get',
        params: {key}
    })
}

//修改系统配置项
export function editsysconfig(data) {
    return request({
        url: '/sysConfig',
        method: 'post',
        data
    })
}

//上传图片-通用
export function uploadimg(data) {
    return request({
        url: '/fileUpload/img',
        method: 'post',
        data
    })
}