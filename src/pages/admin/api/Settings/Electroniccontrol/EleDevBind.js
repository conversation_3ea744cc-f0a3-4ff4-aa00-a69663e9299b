import request from '@admin/utils/request';

//首页数据加载接口
export function getAllDev(roomName, devName) {
    return request({
        url: '/ecu/ctrl/dev/getAllDev',
        method: 'get',
        params: { roomName, devName }
    })
}

//查看已绑定的数据
export function getBindCtrls(pageNum, pageSize, dataId,dataType) {
    return request({
        url: '/ecu/ctrl/dev/getBindCtrl',
        method: 'get',
        params: { pageNum, pageSize, dataId,dataType }
    })
}

//更新绑定
export function updateBindRelation(data) {
    return request({
        url: '/ecu/ctrl/dev/updateBindRelation',
        method: 'post',
        data
    })
}

//菜单树-绑定网关
export function getTreeByData(dataId, dataType) {
    return request({
        url: '/ecu/ctrl/dev/getTreeByData',
        method: 'get',
        params: { dataId, dataType }
    })
}