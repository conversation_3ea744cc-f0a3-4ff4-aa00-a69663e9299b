import request from '@admin/utils/request';

//查询电控程序
export function getprogram(pageNum, pageSize, ecuProgramSn, ecuProgramName,ecuProgramIp,orderItems,orderRule) {
    return request({
        url: '/ecu/program',
        method: 'get',
        params: {pageNum, pageSize, ecuProgramSn, ecuProgramName,ecuProgramIp,orderItems,orderRule}
    })
}

//保存电控程序
export function saveprogram(data){
    return request({
        url: '/ecu/program',
        method: 'post',
        data
    })
}

//修改电控程序
export function updateprogram(data){
    return request({
        url: '/ecu/program/update',
        method: 'post',
        data
    })
}

//删除电控程序
export function delprogram(data){
    return request({
        url: '/ecu/program/delete',
        method: 'post',
        data
    })
}

//电控程序的网关列表
export function getgateways(pageNum,pageSize,ecuProgramUuid,orderItems,orderRule){
    return request({
        url: '/ecu/program/gateway',
        method: 'get',
        params:{pageNum,pageSize,ecuProgramUuid,orderItems,orderRule}
    })
}

//绑定网关和电控程序
export function bindgateway(data){
    return request({
        url: '/ecu/program/gateway',
        method: 'post',
        data
    })
}

//解除绑定网关和电控程序
export function unbindgateway(data){
    return request({
        url: '/ecu/program/gateway/delete',
        method: 'post',
        data
    })
}

//根据程序获取对应网关
export function getprogramgateway(ecuProgramUuid){
    return request({
        url: '/ecu/program/gateway/getAll',
        method: 'get',
        params:{ecuProgramUuid}
    })
}

//获取所有程序
export function getprogramgs(key,num){
    return request({
        url: '/ecu/program/getAll',
        method: 'get',
        params:{key,num}
    })
}

