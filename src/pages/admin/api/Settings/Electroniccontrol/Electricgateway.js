import request from '@admin/utils/request';

//查询网关
export function getGateway(pageNum, pageSize, ecuGatewaySn, ecuGatewayName,ecuGatewayBrand,ecuGatewayIp,ecuProgramName,orderItems,orderRule) {
    return request({
        url: '/ecu/gateway',
        method: 'get',
        params: {pageNum, pageSize, ecuGatewaySn, ecuGatewayName,ecuGatewayBrand,ecuGatewayIp,ecuProgramName,orderItems,orderRule}
    })
}

//保存网关
export function saveGateway(data){
    return request({
        url: '/ecu/gateway',
        method: 'post',
        data
    })
}

//修改网关
export function updateGateway(data){
    return request({
        url: '/ecu/gateway/update',
        method: 'post',
        data
    })
}

//删除网关
export function delGateway(data){
    return request({
        url: '/ecu/gateway/delete',
        method: 'post',
        data
    })
}

//查询所有网关
export function getallGateway(key,num){
    return request({  
        url: '/ecu/gateway/getAll',
        method: 'get',
        params: {key, num}
    })
}