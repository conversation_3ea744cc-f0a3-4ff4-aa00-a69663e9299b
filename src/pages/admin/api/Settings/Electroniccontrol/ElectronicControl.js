import request from '@admin/utils/request';

//查询电控控制器
export function getctrls(pageNum, pageSize, ecuCtrlSn, ecuCtrlName,kind,orderItems,orderRule) {
    return request({
        url: '/ecu/ctrl',
        method: 'get',
        params: {pageNum, pageSize, ecuCtrlSn, ecuCtrlName,kind,orderItems,orderRule}
    })
}

//保存电控控制器
export function savectrls(data){
    return request({
        url: '/ecu/ctrl',
        method: 'post',
        data
    })
}

//修改电控控制器
export function updatectrls(data){
    return request({
        url: '/ecu/ctrl/update',
        method: 'post',
        data
    })
}

//删除电控控制器
export function delctrls(data){
    return request({
        url: '/ecu/ctrl/delete',
        method: 'post',
        data
    })
}

//查看控制器与设备的绑定关系
export function getBindDev(pageNum, pageSize, ecuCtrlUuid) {
    return request({
        url: '/ecu/ctrl/getBindDev',
        method: 'get',
        params: {pageNum, pageSize, ecuCtrlUuid}
    })
}