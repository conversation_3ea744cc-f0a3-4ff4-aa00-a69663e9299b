import request from '@admin/utils/request';

export function getdoorCtrlSta(pageNum, pageSize, dcsName,orderModel,orderKey) {
    return request({
        url: '/doorCtrlStation',
        method: 'get',
        params: { pageNum, pageSize, dcsName,orderModel,orderKey }
    })
}

export function adddoorCtrlSta(data) {
    return request({
        url: '/doorCtrlStation',
        method: 'post',
        data
    })
}

export function editdoorCtrlSta(data) {
    return request({
        url: '/doorCtrlStation/update',
        method: 'post',
        data
    })
}


export function deldoorCtrlSta(data) {
    return request({
        url: '/doorCtrlStation/delete',
        method: 'post',
		data
    })
}
