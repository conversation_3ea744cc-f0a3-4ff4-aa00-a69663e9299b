import request from '@admin/utils/request';

export function getdoorCtrl(pageNum, pageSize, ctrlName,ctrlSn,dcsName,roomName,ctrlStatus) {
    return request({
        url: '/doorCtrl/page',
        method: 'get',
        params: { pageNum, pageSize, ctrlName,ctrlSn,dcsName,roomName,ctrlStatus}
    })
}

export function adddoorCtrl(data) {
    return request({
        url: '/doorCtrl/save',
        method: 'post',
        data
    })
}

export function editdoorCtrl(data) {
    return request({
        url: '/doorCtrl/update',
        method: 'post',
        data
    })
}


export function deldoorCtrl(data) {
    return request({
        url: '/doorCtrl/delete',
        method: 'post',
		data
    })
}
