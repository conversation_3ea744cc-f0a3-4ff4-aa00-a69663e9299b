import request from '@admin/utils/request';

//获取楼层信息
export function getFloor(pageNum, pageSize, floorName, buildingName, deptName, orderRule, orderItems) {
    return request({
        url: '/building/floor',
        method: 'get',
        params: { pageNum, pageSize, floorName, buildingName, deptName, orderRule, orderItems }
    })
}

export function addFloor(data) {
    return request({
        url: '/building/floor',
        method: 'post',
        data
    })
}

export function editFloor(data) {
    return request({
        url: '/building/floor/update',
        method: 'post',
        data
    })
}

export function delFloor(data) {
    return request({
        url: '/building/floor/delete',
        method: 'post',
		data
    })
}