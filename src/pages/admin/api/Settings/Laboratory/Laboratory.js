import request from "@admin/utils/request";

//实验室-列表
export function getLaboratory(params) {
  return request({
    url: "/room",
    method: "get",
    params,
  });
}

//实验室-新增
export function addLaboratory(data) {
  return request({
    url: "/room",
    method: "post",
    data,
  });
}

//实验室-编辑
export function editLaboratory(data) {
  return request({
    url: "/room/update",
    method: "post",
    data,
  });
}

//实验室-删除
export function delLaboratory(data) {
  return request({
    url: "/room/delete",
    method: "post",
    data,
  });
}

//实验室-获取实验室最大楼层
export function getBuildMaxFloor(uuid) {
  return request({
    url: "/building/getBuildMaxFloor",
    method: "get",
    params: { uuid },
  });
}

//免预约人员列表-有实验室
export function getnoorder(uuid, pageNum, pageSize, search) {
  return request({
    url: "/roomUserMember/getRoomUserByRoom",
    method: "get",
    params: { uuid, pageNum, pageSize, search },
  });
}

//新增免预约人员-有实验室
export function addnoorder(data) {
  return request({
    url: "/roomUserMember",
    method: "post",
    data,
  });
}

//删除免预约人员-有实验室
export function delnoorder(data) {
  return request({
    url: "/roomUserMember/delete",
    method: "post",
    data,
  });
}

export function importlMember(data) {
  return request({
    url: "/roomUserMember/fileSaveByRoom",
    method: "POST",
    data,
  });
}

//免预约人员列表-所有的免预约人员名单
export function getDetailMember(trueName, roomName, pageNum, pageSize) {
  return request({
    url: "/roomUserMember",
    method: "get",
    params: { trueName, roomName, pageNum, pageSize },
  });
}

//新增免预约人员-无实验室
export function addmember(data) {
  return request({
    url: "/roomUserMember/saveSpecialMember",
    method: "post",
    data,
  });
}

//新增免预约人员-批量新增实验室
export function addMoremember(data) {
  return request({
    url: "/roomUserMember/saveWithRoomIds",
    method: "post",
    data,
  });
}

//修改免预约人员-无实验室
export function editmember(data) {
  return request({
    url: "/roomUserMember/updateSpecialMember",
    method: "post",
    data,
  });
}

//修改免预约人员-有实验室
export function editnoorder(data) {
  return request({
    url: "/roomUserMember/update",
    method: "post",
    data,
  });
}

//删除免预约人员-无实验室
export function delmember(data) {
  return request({
    url: "/roomUserMember/deleteSpecialMember",
    method: "post",
    data,
  });
}

//获取实验室名称下拉框的数据--用户端
export function getAllLaboratory(key, num, groupFlag) {
  return request({
    url: "/room/getAll",
    method: "get",
    params: { key, num, groupFlag },
  });
}

//获取所有楼宇下拉框的数据
export function getAllBuilding(params) {
  return request({
    url: "/building/getAll",
    method: "get",
    params,
  });
}

//获取实验室类型名称下拉框的数据
export function getAllType(key, num) {
  return request({
    url: "/room/getRoomKindAll",
    method: "get",
    params: { key, num },
  });
}

//用户端首页的实验室环境
export function getRoomMessage() {
  return request({
    url: "/room/getRoomMessage",
    method: "get",
  });
}

//用户端首页的实验室环境
export function uploadimg(data) {
  return request({
    url: "/photoManager/upload",
    method: "post",
    data,
  });
}

//获取实验室详情
export function getroomdetail(params) {
  return request({
    url: "/room/detail",
    method: "get",
    params,
  });
}

//获取实验室绑定的电控控制器
export function getEcuCtrlList(params) {
  return request({
    url: "/room/getEcuCtrlList",
    method: "get",
    params,
  });
}

//房间实验室-列表
export function getRoomlab(params) {
  return request({
    url: "/room/lab/page",
    method: "get",
    params,
  });
}

//房间实验室-新增
export function addRoomlab(data) {
  return request({
    url: "/room/lab/save",
    method: "post",
    data,
  });
}

//房间实验室-修改
export function editRoomlab(data) {
  return request({
    url: "/room/lab/update",
    method: "post",
    data,
  });
}

//房间实验室-删除
export function delRoomlab(data) {
  return request({
    url: "/room/lab/delete",
    method: "post",
    data,
  });
}

//获取所有房间实验室
export function getAllRoomlab(params) {
  return request({
    url: "/room/lab/getAll",
    method: "get",
    params,
  });
}

//免预约人员-复制
export function saveBatch(data) {
  return request({
    url: "/roomUserMember/saveBatch",
    method: "post",
    data,
  });
}

//房间实验室管理员列表
export function getroomlabmanager(params) {
  return request({
    url: "/room/lab/manager/page",
    method: "get",
    params,
  });
}

//房间实验室管理员-新增
export function saveroomlabmanager(data) {
  return request({
    url: "/room/lab/manager/save",
    method: "post",
    data,
  });
}

//房间实验室管理员-批量删除
export function deleteBatchmanager(data) {
  return request({
    url: "/room/lab/manager/deleteBatch",
    method: "post",
    data,
  });
}

//获取当前用户管理的实验室列表
export function getRoomLabList(data) {
  return request({
    url: "/room/lab/manager/getRoomLabList",
    method: "post",
    data,
  });
}

//房间实验室-视频列表
export function getVideo(data) {
  return request({
    url: "/room/lab/manager/getRoomLabList",
    method: "post",
    data,
  });
}

//房间实验室-视频上传
export function addVideo(data) {
  return request({
    url: "/room/lab/manager/getRoomLabList",
    method: "post",
    data,
  });
}

//获取实验室绑定的视频列表
export function getroomlabvideo(params) {
  return request({
    url: "/room/lab/video-page",
    method: "get",
    params,
  });
}

//新增实验室列表
export function addroomlabvideo(data) {
  return request({
    url: "/room/lab/save-video",
    method: "post",
    data,
  });
}

//批量修改管控模式
export function batchmode(data) {
  return request({
    url: "/room/batch-man-mode",
    method: "post",
    data,
  });
}

//新增负责人
export function saveperson(data) {
  return request({
    url: "/room/lab/responsible/person/save",
    method: "post",
    data,
  });
}

//获取负责人详情
export function getpersondetail(params) {
  return request({
    url: "/room/lab/responsible/person/detail",
    method: "get",
    params,
  });
}

//获取负责人详情
export function updatekind(data) {
  return request({
    url: "/room/lab/responsible/person/kind/update",
    method: "post",
    data,
  });
}

//批量修改房间开放状态
export function batchUpdateOpenState(data) {
  return request({
    url: "/room/batchUpdateOpenState",
    method: "post",
    data,
  });
}