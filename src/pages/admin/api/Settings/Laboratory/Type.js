import request from "@admin/utils/request";

export function getType(
  roomKindName,
  pageNum,
  pageSize,
  orderItems,
  orderRule
) {
  return request({
    url: "/room/getRoomKindList",
    method: "get",
    params: { roomKindName, pageNum, pageSize, orderItems, orderRule },
  });
}

export function addType(data) {
  return request({
    url: "/room/saveRoomKind",
    method: "post",
    data,
  });
}

export function editType(data) {
  return request({
    url: "/room/updateRoomKind",
    method: "post",
    data,
  });
}

export function delType(data) {
  return request({
    url: "/room/deleteRoomKind",
    method: "post",
    data,
  });
}

export function saveperson(data) {
  return request({
    url: "/room/center/responsible/person/save",
    method: "post",
    data,
  });
}

export function getpersondetail(params) {
  return request({
    url: "/room/center/responsible/person/detail",
    method: "get",
    params,
  });
}
