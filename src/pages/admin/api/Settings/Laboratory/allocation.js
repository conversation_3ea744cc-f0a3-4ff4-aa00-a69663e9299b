import request from '@admin/utils/request';

export function getallocation(pageNum, pageSize, roomConfigName) {
    return request({
        url: '/room/getRoomConfigList',
        method: 'get',
        params: { pageNum, pageSize, roomConfigName }
    })
}


export function addallocation(data) {
    return request({
        url: '/room/saveRoomConfig',
        method: 'post',
        data
    })
}

export function editallocation(data) {
    return request({
        url: '/room/updateRoomConfig',
        method: 'post',
        data
    })
}


export function delallocation(data) {
    return request({
        url: '/room/deleteRoomConfig',
        method: 'post',
		data
    })
}

//获取实验室配置下拉框
export function getallocationAll(key,num) {
    return request({
        url: '/room/getRoomConfigAll',
        method: 'get',
        params:{key,num}
    })
}