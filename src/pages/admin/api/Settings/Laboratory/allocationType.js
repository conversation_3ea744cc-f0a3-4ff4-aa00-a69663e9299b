import request from '@admin/utils/request';

export function getallocationTypes(pageNum, pageSize, search) {
    return request({
        url: '/room/config-kind',
        method: 'get',
        params: { pageNum, pageSize, search }
    })
}


export function addallocationType(data) {
    return request({
        url: '/room/config-kind',
        method: 'post',
        data
    })
}

export function editallocationType(data) {
    return request({
        url: '/room/config-kind/update',
        method: 'post',
        data
    })
}


export function delallocationType(data) {
    return request({
        url: '/room/config-kind/delete',
        method: 'post',
		data
    })
}

//获取实验室配置分类下拉框
export function getallocationTypeAll(key,num) {
    return request({
        url: '/room/config-kind/getAll',
        method: 'get',
        params:{key,num}
    })
}

//获取实验室配置分类树结构
export function getallocationTypeTree() {
    return request({
        url: '/room/config-kind/getTree',
        method: 'get',
    })
}