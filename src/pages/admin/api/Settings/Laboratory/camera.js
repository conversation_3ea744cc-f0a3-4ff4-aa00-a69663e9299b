import request from '@admin/utils/request';

export function getdatas(pageNum, pageSize,vcrName,vcrBrand,cameraBrand,cameraName,roomName) {
  return request({
    url: '/video/camera',
    method: 'get',
    params:{pageNum, pageSize,vcrName,vcrBrand,cameraBrand,cameraName,roomName}
  })
}

export function addcamera(data) {
  return request({
    url: '/video/camera',
    method: 'post',
    data
  })
}

export function editcamera(data) {
  return request({
    url: '/video/camera/update',
    method: 'post',
    data
  })
}

export function delcamera(data) {
  return request({
    url: '/video/camera/delete',
    method: 'post',
    data
  })
}
