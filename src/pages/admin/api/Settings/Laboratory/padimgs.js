import request from '@admin/utils/request';

export function getPhoto() {
    return request({
        url: '/photoManager',
        method: 'get',
    })
}


export function delPhoto(data) {
    return request({
        url: '/photoManager/delete',
        method: 'post',
		data
    })
}


export function addPhoto(data) {
    return request({
        url: '/photoManager',
        method: 'post',
        data
    })
}

export function editPhoto(data) {
    return request({
        url: '/photoManager/update',
        method: 'post',
        data
    })
}


//pad屏相册提交图片
export function addimg(data) {
  return request({
    url: '/photoManager/upload',
    method: 'post',
    data
  })
}