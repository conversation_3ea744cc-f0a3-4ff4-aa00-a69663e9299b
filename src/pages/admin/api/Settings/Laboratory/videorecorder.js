import request from '@admin/utils/request';

export function getdatas(pageNum, pageSize,vcrName,vcrBrand,vcrIp) {
  return request({
    url: '/video/vcr',
    method: 'get',
    params:{pageNum, pageSize,vcrName,vcrBrand,vcrIp}
  })
}

export function addvcr(data) {
  return request({
    url: '/video/vcr',
    method: 'post',
    data
  })
}

export function editvcr(data) {
  return request({
    url: '/video/vcr/update',
    method: 'post',
    data
  })
}

export function delvcr(data) {
  return request({
    url: '/video/vcr/delete',
    method: 'post',
    data
  })
}

export function getallvcr(key,num) {
  return request({
    url: '/video/vcr/getAll',
    method: 'get',
    params:{key,num}
  })
}

export function getPublicKey(data) {
  return request({
    url: '/common/getPublicKey',
    method: 'get',
    data
  })
}