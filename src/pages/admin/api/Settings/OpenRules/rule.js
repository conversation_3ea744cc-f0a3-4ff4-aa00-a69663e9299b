import request from "@admin/utils/request";

export function getopenRule(pageNum, pageSize) {
  return request({
    url: "/openRule",
    method: "get",
    params: { pageNum, pageSize },
  });
}

export function addopenRule(data) {
  return request({
    url: "/openRule",
    method: "post",
    data,
  });
}

export function editopenRule(data) {
  return request({
    url: "/openRule/update",
    method: "post",
    data,
  });
}

export function delopenRule(data) {
  return request({
    url: "/openRule/delete",
    method: "post",
    data,
  });
}

export function getopenRuleAll() {
  return request({
    url: "/openRule/getAll",
    method: "get",
  });
}

//保存特殊日期开放时间
export function saveopenRuleTime(data) {
  return request({
    url: "/openRuleTime/save",
    method: "post",
    data
  });
}

//保存特殊日期开放时间
export function getopenRuleTimelist(params) {
  return request({
    url: "/openRuleTime/list",
    method: "get",
    params
  });
}
