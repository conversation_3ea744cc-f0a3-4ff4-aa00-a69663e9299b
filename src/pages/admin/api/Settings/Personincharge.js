import request from '@admin/utils/request';

//获取楼层信息
export function getprincipal(pageNum, pageSize, deptName, search, orderRule, orderItems) {
    return request({
        url: '/principal',
        method: 'get',
        params: { pageNum, pageSize, deptName, search, orderRule, orderItems }
    })
}

export function addprincipal(data) {
    return request({
        url: '/principal',
        method: 'post',
        data
    })
}

export function editprincipal(data) {
    return request({
        url: '/principal/update',
        method: 'post',
        data
    })
}

export function delprincipal(data) {
    return request({
        url: '/principal/delete',
        method: 'post',
        data
    })
}