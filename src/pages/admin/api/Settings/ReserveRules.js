import request from '@admin/utils/request';

export function getRule(pageNum, pageSize) {
    return request({
        url: '/resvRule',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

export function addRule(data) {
    return request({
        url: '/resvRule',
        method: 'post',
        data
    })
}

export function editRule(data) {
    return request({
        url: '/resvRule/update',
        method: 'post',
        data
    })
}


export function delRule(data) {
    return request({
        url: '/resvRule/delete',
        method: 'post',
		data
    })
}

export function getkinds() {
    return request({
        url: '/resvRule/kind',
        method: 'get',
    })
}

export function getaccessMember(pageNum,pageSize,ruleUuid,logonName,trueName) {
    return request({
        url: '/resvRule/accessMember',
        method: 'get',
		params:{pageNum,pageSize,ruleUuid,logonName,trueName}
    })
}

export function delaccessMember(data) {
    return request({
        url: '/resvRule/deleteAccessMember',
        method: 'post',
		data
    })
}

export function addaccessMember(data) {
	return request({
	    url: '/resvRule/saveAccessMember',
	    method: 'post',
		data
	})
}

export function getaccessMemberlist(ident,key,num) {
    return request({
        url: '/resvRule/getAccountList',
        method: 'get',
		params:{ident,key,num}
    })
}

//获取特殊人员组列表
export function getGroups(kind,groupName,pageNum,pageSize) {
    return request({
        url: '/groups',
        method: 'get',
		params:{kind,groupName,pageNum,pageSize}
    })
}


//新增特殊人员组
export function addGroups(data){
	return request({
	    url: '/groups',
	    method: 'post',
		data
	})
}

//删除特殊人员组
export function delGroups(data){
	return request({
	    url: '/groups/delete',
	    method: 'post',
		data
	})
}

//编辑特殊人员组
export function editGroups(data){
	return request({
	    url: '/groups/update',
	    method: 'post',
		data
	})
}




//特殊人员组成员列表
export function getgroupmember(pageNum,pageSize,kind,groupId){
	return request({
	    url: '/groupMember',
	    method: 'get',
		params:{pageNum,pageSize,kind,groupId}
	})
}

//添加特殊人员组成员
export function addgroupmember(data){
	return request({
	    url: '/groupMember',
	    method: 'post',
		data
	})
}

//批量添加特殊人员组成员
export function addmanygroupmember(data){
	return request({
	    url: '/groupMember/saveBatch',
	    method: 'post',
		data
	})
}


//删除特殊人员组成员
export function delgroupmember(data){
	return request({
	    url: '/groupMember/delete',
	    method: 'post',
		data
	})
}

//批量删除特殊人员组成员
export function delmanygroupmember(data){
	return request({
	    url: '/groupMember/deleteBatch',
	    method: 'post',
		data
	})
}

//获取特殊人员组下拉列表
export function getgroupmemberSelect(key,num,kind){
	return request({
	    url: '/groups/getAll',
	    method: 'get',
		params:{key,num,kind}
	})
}