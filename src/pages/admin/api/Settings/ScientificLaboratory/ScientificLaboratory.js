import request from '@admin/utils/request';

export function getresearchtest(pageNum, pageSize, researchTestSn, researchTestName,researchTestTypeCode,researchTestLevel,holderName,leaderName,deptName,beginDate,endDate,orderItems,orderRule) {
    return request({
        url: '/research/test',
        method: 'get',
        params: {pageNum, pageSize, researchTestSn, researchTestName,researchTestTypeCode,researchTestLevel,holderName,leaderName,deptName,beginDate,endDate,orderItems,orderRule}
    })
}

export function addresearchtest(data) {
    return request({
        url: '/research/test',
        method: 'post',
        data
    })
}

export function editresearchtest(data) {
    return request({
        url: '/research/test/update',
        method: 'post',
        data
    })
}


export function delresearchtest(data) {
    return request({
        url: '/research/test/delete',
        method: 'post',
		data
    })
}

//获取科研实验下拉框数据
export function getAllresearch(key,num) {
    return request({
        url: '/research/test/getAll',
        method: 'get',
		params:{key,num}
    })
}

export function getroomdistribution(roomId) {
    return request({
        url: '/room/distribution/detail',
        method: 'get',
		params:{roomId}
    })
}

export function getresearchtestmember(pageNum, pageSize, uuid, kind,memberName,orderItems,orderRule) {
    return request({
        url: '/research/test/member',
        method: 'get',
        params: {pageNum, pageSize, uuid, kind,memberName,orderItems,orderRule}
    })
}