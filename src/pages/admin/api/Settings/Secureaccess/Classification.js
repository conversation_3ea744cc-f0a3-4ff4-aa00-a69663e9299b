import request from '@admin/utils/request';

//获取分类
export function getkind(params) {
	return request({
		url: '/safety/kind/page',
		method: 'get',
		params
	})
}

//获取分类详情
export function getkinddetail(params) {
	return request({
		url: '/safety/kind/detail',
		method: 'get',
		params
	})
}

//新增分类
export function addkind(data) {
	return request({
		url: '/safety/kind/save',
		method: 'post',
		data
	})
}

//编辑分类
export function editkind(data) {
	return request({
		url: '/safety/kind/update',
		method: 'post',
		data
	})
}

//删除分类
export function delkind(data) {
	return request({
		url: '/safety/kind/delete',
		method: 'post',
		data
	})
}

//获取所有分类
export function getAllkind(params) {
	return request({
		url: '/safety/kind/getAll',
		method: 'get',
		params
	})
}


