import request from '@admin/utils/request';

//获取试卷
export function getexampage(params) {
	return request({
		url: '/safety/exam/page',
		method: 'get',
		params
	})
}

//获取试卷详情
export function getexampagedetail(params) {
	return request({
		url: '/safety/exam/detail',
		method: 'get',
		params
	})
}

//保存试卷
export function saveexampage(data) {
	return request({
		url: '/safety/exam/save',
		method: 'post',
		data
	})
}

//更新试卷
export function updateexampage(data) {
	return request({
		url: '/safety/exam/update',
		method: 'post',
		data
	})
}

//删除试卷
export function delexampage(data) {
	return request({
		url: '/safety/exam/delete',
		method: 'post',
		data
	})
}

//获取全部试卷
export function getexamAll(params) {
	return request({
		url: '/safety/exam/getAll',
		method: 'get',
		params
	})
}

//根据题型获取分类下拉框
export function getQuestionKind(params) {
	return request({
		url: '/safety/kind/getQuestionKind',
		method: 'get',
		params
	})
}

//根据题型和分类获取题目列表
export function getAllBySafetyKindId(params) {
	return request({
		url: '/safety/question/getAllBySafetyKindId',
		method: 'get',
		params
	})
}