import request from "@admin/utils/request";

//获取文章
export function getarticle(params) {
  return request({
    url: "/safety/article/page",
    method: "get",
    params,
  });
}

//获取文章详情
export function getarticledetail(params) {
  return request({
    url: "/safety/article/detail",
    method: "get",
    params,
  });
}

//新增文章
export function addarticle(data) {
  return request({
    url: "/safety/article/save",
    method: "post",
    data,
  });
}

//更新文章
export function updatearticle(data) {
  return request({
    url: "/safety/article/update",
    method: "post",
    data,
  });
}

//删除文章
export function delarticle(data) {
  return request({
    url: "/safety/article/delete",
    method: "post",
    data,
  });
}

//上传视频
export function uploadvideo(data) {
  return request({
    url: "/fileUpload/video",
    method: "post",
    data,
  });
}

//获取题库
export function getquestion(params) {
  return request({
    url: "/safety/question/page",
    method: "get",
    params,
  });
}

//获取试题详情
export function getquestiondetail(params) {
  return request({
    url: "/safety/question/detail",
    method: "get",
    params,
  });
}

//新增试题
export function addquestion(data) {
  return request({
    url: "/safety/question/save",
    method: "post",
    data,
  });
}

//修改试题
export function updatequestion(data) {
  return request({
    url: "/safety/question/update",
    method: "post",
    data,
  });
}

//删除试题
export function delquestion(data) {
  return request({
    url: "/safety/question/delete",
    method: "post",
    data,
  });
}

//导入试题
export function importquestion(data) {
  return request({
    url: "/safety/question/import",
    method: "post",
    data,
  });
}

//批量删除试题
export function deleteBatch(data) {
  return request({
    url: "/safety/question/deleteBatch",
    method: "post",
    data,
  });
}

//批量修改分类
export function editBatch(data) {
  return request({
    url: "/safety/question/batch-update",
    method: "post",
    data,
  });
}

//查看试题相同题目
export function getSamequestion(params) {
  return request({
    url: "/safety/question/same-question",
    method: "get",
    params,
  });
}
