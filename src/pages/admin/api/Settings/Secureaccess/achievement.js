import request from "@admin/utils/request";

//获取考试列表
export function getexamresult(params) {
  return request({
    url: "/safety/exam/result/page",
    method: "get",
    params,
  });
}

//获取考试详情
export function getexamdetail(params) {
  return request({
    url: "/safety/exam/result/detail",
    method: "get",
    params,
  });
}

//重新授权
export function restauth(data) {
  return request({
    url: "/safety/exam/result/rest-auth",
    method: "post",
    data,
  });
}
