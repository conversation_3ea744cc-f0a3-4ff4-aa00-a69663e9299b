import request from '@admin/utils/request';

//获取学期列表
export function getYearTerms(pageNum,pageSize) {
    return request({
        url: '/yearTerm/getYearTerm',
        method: 'get',
        params:{pageNum,pageSize}
    })
}

//新增学期列表
export function addYearTerms(data) {
    return request({
        url: '/yearTerm/saveYearTerm',
        method: 'post',
        data
    })
}

//修改学期列表
export function editYearTerms(data) {
    return request({
        url: '/yearTerm/updateYearTerm',
        method: 'post',
        data
    })
}

//删除学期列表
export function deleteYearTerms(data) {
    return request({
        url: '/yearTerm/deleteYearTerm',
        method: 'post',
        data
    })
}

export function getAllYearTerm() {
    return request({
        url: '/yearTerm/getAll',
        method: 'get',
    })
}