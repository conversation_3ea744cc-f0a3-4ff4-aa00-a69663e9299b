import request from '@admin/utils/request';

//获取作息列表
export function getWorkSchedule(pageNum,pageSize) {
    return request({
        url: '/yearTerm/getClassTime',
        method: 'get',
        params:{pageNum,pageSize}
    })
}

//新增学期列表
export function addWorkSchedule(data) {
    return request({
        url: '/yearTerm/saveClassTime',
        method: 'post',
        data
    })
}

//修改学期列表
export function editWorkSchedule(data) {
    return request({
        url: '/yearTerm/updateClassTime',
        method: 'post',
        data
    })
}

//删除学期列表
export function deleteWorkSchedule(data) {
    return request({
        url: '/yearTerm/deleteClassTime',
        method: 'post',
        data
    })
}