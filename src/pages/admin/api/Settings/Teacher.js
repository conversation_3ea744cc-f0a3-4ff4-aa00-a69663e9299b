import request from '@admin/utils/request';

export function getTeacher(pageNum, pageSize, search) {
    return request({
        url: '/teacher',
        method: 'get',
        params: { pageNum, pageSize, search }
    })
}

export function addTeacher(data) {
    return request({
        url: '/teacher',
        method: 'post',
        data
    })
}

export function editTeacher(data) {
    return request({
        url: '/teacher/update',
        method: 'post',
        data
    })
}


export function delTeacher(data) {
    return request({
        url: '/teacher/delete',
        method: 'post',
		data
    })
}

export function getAllCount(key,num) {
    return request({
        url: '/account',
        method: 'get',
        params:{key,num}
    })
}

export function getAllTeacher(key,num) {
    return request({
        url: '/teacher/getAll',
        method: 'get',
        params:{key,num}
    })
}

//导入教师表格
export function importTeacher(data) {
    return request({
        url: '/teacher/import',
        method: 'post',
        data
    })
}