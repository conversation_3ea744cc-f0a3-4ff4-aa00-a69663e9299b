import request from '@admin/utils/request';

// export function getAccount(pageNum,page) {
//   return request({
//     url: '/account/get?pageNum='+page+'&pageSize='+pageNum+'',
//     method: 'get',
//   })
// }
export function getAccount(pageNum,pageSize,logonName,cardNo,kind,ident,className,deptName,handPhone,email,localstatus,search,orderKey,orderModel) {
  return request({
    url: '/account/get',
    method: 'get',
    params: {pageNum,pageSize,logonName,cardNo,kind,ident,className,deptName,handPhone,email,localstatus,search,orderKey,orderModel}
  })
}

export function updateStatus(data) {
  return request({
    url: '/account/updateStatus',
    method: 'post',
    data
  })
}

export function delAccount(data) {
  return request({
    url: '/account/delete',
    method: 'post',
	data
  })
}

export function addAccount(data) {
  return request({
    url: '/account',
    method: 'post',
    data
  })
}

export function editAccount(data) {
  return request({
    url: '/account/update',
    method: 'post',
    data
  })
}

export function editPassword(data) {
  return request({
    url: '/account/reset',
    method: 'post',
    data
  })
}

export function editSubsidyAbout(data) {
  return request({
    url: '/account/updateSubsidyAbout',
    method: 'post',
    data
  })
}



//获逻辑班下拉框的数据
export function getAllClass() {
  return request({
    url: '/schoolClass/getAll',
    method: 'get',
  })
}

//同步账户
export function sysAccount() {
  return request({
    url: '/account/sysAccount',
    method: 'get',
  })
}

//导入补助
export function importSubsidy(data) {
  return request({
      url: '/account/importSubsidy',
      method: 'post',
      data
  })
}

//导入机时
export function importFreeTime(data) {
  return request({
      url: '/account/importFreeTime',
      method: 'post',
      data
  })
}


//根据通配符表达式修改补助相关信息
export function updateSubsidyAboutByWildcard(data) {
  return request({
      url: '/account/updateSubsidyAboutByWildcard',
      method: 'post',
      data
  })
}

//根据通配符表达式查询账户数量
export function countByWildcard(params) {
  return request({
      url: '/account/countByWildcard',
      method: 'get',
      params
  })
}