import request from '@admin/utils/request';

export function getAccClass(pageNum,pageSize,className,classKind,orderKey,orderModel) {
  return request({
    url: '/accClass',
    method: 'get',
    params:{pageNum,pageSize,className,classKind,orderKey,orderModel}
  })
}

export function addAccClass(data) {
  return request({
    url: '/accClass',
    method: 'post',
    data
  })
}

export function editAccClass(data) {
  return request({
    url: '/accClass/update',
    method: 'post',
    data
  })
}

export function delAccClass(data) {
  return request({
    url: '/accClass/delete',
    method: 'post',
	data
  })
}

//获取班级下拉框的数据
export function getAllClass(key,num,yearTermId) {
  return request({
    url: '/accClass/getAll',
    method: 'get',
    params:{key,num,yearTermId}
  })
}


