import request from '@admin/utils/request';

export function getAccDept(pageNum, pageSize, deptName, kind, orderKey, orderModel) {
  return request({
    url: '/accDept',
    method: 'get',
    params: { pageNum, pageSize, deptName, kind, orderKey, orderModel }
  })
}

export function addAccDept(data) {
  return request({
    url: '/accDept',
    method: 'post',
    data
  })
}

export function editAccDept(data) {
  return request({
    url: '/accDept/update',
    method: 'post',
    data
  })
}


export function delAccDept(data) {
  return request({
    url: '/accDept/delete',
    method: 'post',
    data
  })
}


//获取部门下拉框的数据
export function getAllDept(key, num) {
  return request({
    url: '/accDept/getAll',
    method: 'get',
    params:{key, num}
  })
}

//获取部门管理员
export function getmanager(params) {
  return request({
    url: '/accDept/manager/page',
    method: 'get',
    params
  })
}

//新增管理员
export function savemanager(data) {
  return request({
    url: '/accDept/manager/save',
    method: 'post',
    data
  })
}

//批量删除管理员
export function deleteBatchmanager(data) {
  return request({
    url: '/accDept/manager/deleteBatch',
    method: 'post',
    data
  })
}
