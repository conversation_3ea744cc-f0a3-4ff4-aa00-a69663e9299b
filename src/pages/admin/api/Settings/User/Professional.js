import request from '@admin/utils/request';

//获取部门表格数据
export function getMajor(pageNum, pageSize, majorName, orderKey, orderModel) {
	return request({
		url: '/accMajor',
		method: 'get',
		params: {
			pageNum,
			pageSize,
			majorName,
			orderKey,
			orderModel
		}
	})
}

//获取部门下拉框数据
export function getAllMajor(key,num) {
	return request({
		url: '/accMajor/getAll',
		method: 'get',
		params:{key,num}
	})
}

//新增部门
export function addMajor(data) {
	return request({
		url: '/accMajor',
		method: 'post',
		data
	})
}

//编辑部门
export function editMajor(data) {
	return request({
	  url: '/accMajor/update',
	  method: 'post',
	  data
	})
}

//删除部门
export function delMajor(data) {
	return request({
	  url: '/accMajor/delete',
	  method: 'post',
	  data
	})
}

//导入专业表格
export function importMajor(data) {
    return request({
        url: '/accMajor/fileSave',
        method: 'post',
        data
    })
}
