import request from '@admin/utils/request';

export function getCampus(pageNum,pageSize) {
  return request({
    url: '/campus',
    method: 'get',
    params:{
      pageNum,pageSize
    }
  })
}

export function getallCampus(params) {
  return request({
    url: '/campus/getAll',
    method: 'get',
    params
  })
}

export function addCampus(data) {
  return request({
    url: '/campus',
    method: 'post',
    data
  })
}

export function editCampus(data) {
  return request({
    url: '/campus/update',
    method: 'post',
    data
  })
}


export function delCampus(data) {
  return request({
    url: '/campus/delete',
    method: 'post',
	data
  })
}

