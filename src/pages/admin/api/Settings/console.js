import request from '@admin/utils/request';

export function getConsole(pageNum, pageSize, consoleName, kind,orderItems,orderRule) {
    return request({
        url: '/console',
        method: 'get',
        params: { pageNum, pageSize, consoleName, kind,orderItems,orderRule}
    })
}

export function addConsole(data) {
    return request({
        url: '/console',
        method: 'post',
        data
    })
}

export function editConsole(data) {
    return request({
        url: '/console/update',
        method: 'post',
        data
    })
}


export function delConsole(data) {
    return request({
        url: '/console/delete',
        method: 'post',
		data
    })
}

// 获取楼宇树
export function getLabArea(consoleUuid) {
    return request({
        url: '/building/roomTree',
        method: 'get',
        params:{consoleUuid}
    })
}