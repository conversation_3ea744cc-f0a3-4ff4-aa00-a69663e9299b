import request from '@admin/utils/request';

export function getgrid(pageNum, pageSize, gridName, search,orderItems,orderRule) {
    return request({
        url: '/grid',
        method: 'get',
        params: { pageNum, pageSize, gridName, search,orderItems,orderRule}
    })
}

export function addgrid(data) {
    return request({
        url: '/grid',
        method: 'post',
        data
    })
}

export function editgrid(data) {
    return request({
        url: '/grid/update',
        method: 'post',
        data
    })
}


export function delgrid(data) {
    return request({
        url: '/grid/delete',
        method: 'post',
		data
    })
}