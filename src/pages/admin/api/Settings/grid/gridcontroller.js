import request from '@admin/utils/request';

export function getgridrelation(pageNum, pageSize, gridId, dataType,dataName,orderItems,orderRule) {
    return request({
        url: '/grid/relation',
        method: 'get',
        params: {pageNum, pageSize, gridId, dataType,dataName,orderItems,orderRule}
    })
}

export function addgridrelation(data) {
    return request({
        url: '/grid/relation',
        method: 'post',
        data
    })
}


export function delgridrelation(data) {
    return request({
        url: '/grid/relation/delete',
        method: 'post',
		data
    })
}