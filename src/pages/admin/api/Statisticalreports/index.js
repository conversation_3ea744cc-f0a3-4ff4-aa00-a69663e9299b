import request from '@admin/utils/request';

//国别码下拉框数据
export function getCountrycode(key,num) {
    return request({
        url: '/worldRegionCode/getAll',
        method: 'get',
        params: {key,num}
    })
}

//教学实验项目表
export function getExperimentProject(pageNum, pageSize,testItemName,yearTermId,orderItems,orderRule) {
    return request({
        url: '/statistics/testItem',
        method: 'get',
        params: { pageNum, pageSize,testItemName,yearTermId,orderItems,orderRule}
    })
}

//课程使用情况
export function getCourseUse(pageNum, pageSize,courseSn,courseName,deptName,beginDate,endDate,orderItems,orderRule) {
    return request({
        url: '/statistics/course/experiment',
        method: 'get',
        params: { pageNum, pageSize,courseSn,courseName,deptName,beginDate,endDate,orderItems,orderRule}
    })
}

//实验室使用情况
export function getLabUse(pageNum, pageSize,roomSn,roomName,roomKindName,beginDate,endDate,orderItems,orderRule) {
    return request({
        url: '/statistics/room/experiment',
        method: 'get',
        params: {pageNum, pageSize,roomSn,roomName,roomKindName,beginDate,endDate,orderItems,orderRule}
    })
}

//教师使用情况
export function getTeacherUse(pageNum, pageSize,logonName,trueName,deptName,orderItems,orderRule) {
    return request({
        url: '/statistics/teacher/experiment',
        method: 'get',
        params: { pageNum, pageSize,logonName,trueName,deptName,orderItems,orderRule}
    })
}

//设备使用情况
export function getDevUse(pageNum, pageSize,devSn,devName,roomName,buildingName,devKindName,orderItems,orderRule) {
    return request({
        url: '/statistics/device/experiment',
        method: 'get',
        params: { pageNum, pageSize,devSn,devName,roomName,buildingName,devKindName,orderItems,orderRule}
    })
}

//开课学院使用情况
export function getClassesUse(pageNum, pageSize,deptSn,deptName,orderItems,orderRule) {
    return request({
        url: '/statistics/dept/experiment',
        method: 'get',
        params: { pageNum, pageSize,deptSn,deptName,orderItems,orderRule}
    })
}

//管理员占用情况统计
export function adminoccupy(params) {
    return request({
        url: '/statistics/admin-occupy',
        method: 'get',
        params
    })
}

//实验室上机情况统计
export function getroomcomputer(params) {
    return request({
        url: '/statistics/room/computer',
        method: 'get',
        params
    })
}

//已支付上机费用
export function getfeepaid(params) {
    return request({
        url: '/statistics/fee/paid',
        method: 'get',
        params
    })
}

//专任实验室人员
export function getLabPeople(pageNum, pageSize) {
    return request({
        url: '/building',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

//实验室基本情况
export function getLabBasicStatus(pageNum, pageSize) {
    return request({
        url: '/building',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

//实验室经费情况
export function getLabFunding(pageNum, pageSize) {
    return request({
        url: '/building',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

//教学科研仪器设备
export function getTeachingeQuipment(pageNum, pageSize,devSn,devName,deptName,devSource,directionOfUse,purchaseStartDate,purchaseEndDate,orderItems,orderRule,priceCeiling,priceFloor) {
    return request({
        url: '/statistics/device/scientific-research',
        method: 'get',
        params: {pageNum, pageSize,devSn,devName,deptName,devSource,directionOfUse,purchaseStartDate,purchaseEndDate,orderItems,orderRule,priceCeiling,priceFloor}
    })
}

//教学科研仪器设备增减变动
export function getTeachingeQuipmentAdd(pageNum, pageSize) {
    return request({
        url: '/statistics/device/change',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

//高等实验室综合信息一
export function getHigherLab1(pageNum, pageSize) {
    return request({
        url: '/building',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

//高等实验室综合信息二
export function getHigherLab2(pageNum, pageSize) {
    return request({
        url: '/statistics/dept/room',
        method: 'get',
        params: { pageNum, pageSize}
    })
}

//综合查询
export function getsynthesis(pageNum, pageSize,queryFields,beginDate,endDate,teacher,testItemName,schoolClassName,roomName,courseName,teacherDeptName,courseDeptName,orderItems,orderRule) {
    return request({
        url: '/statistics/synthesis/teaching',
        method: 'get',
        params: {pageNum, pageSize,queryFields,beginDate,endDate,teacher,testItemName,schoolClassName,roomName,courseName,teacherDeptName,courseDeptName,orderItems,orderRule}
    })
}
//综合查询-教学班课程学时
export function getsynthesis_other(params) {
    return request({
        url: '/statistics/synthesis/teaching',
        method: 'get',
        params
    })
}

//上机情况对比综合查询
export function getsynthesiscomputer(params) {
    return request({
        url: '/statistics/synthesis/computer',
        method: 'get',
        params
    })
}

//上机情况对比综合查询-学院
export function getdeptComputer(params) {
    return request({
        url: '/statistics/synthesis/deptComputer',
        method: 'get',
        params
    })
}

//课程学院下拉框（列表中的数据）
export function getDeptAll(params) {
    return request({
        url: '/testPlan/getDeptAll',
        method: 'get',
        params
    })
}

//学院实验项目人时数统计
export function gettestPlanItem(params) {
    return request({
        url: '/statistics/testPlanItem/page',
        method: 'get',
        params
    })
}

//设备使用情况综合统计
export function getsynthesisdev(params) {
    return request({
        url: '/statistics/synthesis/dev',
        method: 'get',
        params
    })
}