import request from "@index/utils/request";

//获取终端列表
export function getTerminalList (params) {
  return request({
    url: "/iot/terminal/getTerminalList",
    method: "get",
    params,
  });
}

// 获取终端与设备的绑定信息列表
export function getBindTerminal (params) {
  return request({
    url: "/iot/iotTerminalDevRelation/getBindTerminal",
    method: "get",
    params,
  });
}

// 更新或保存设备绑定终端信息
export function saveOrUpdateBindTerminal (data) {
  return request({
    url: "/iot/iotTerminalDevRelation/saveOrUpdateBindTerminal",
    method: "post",
    data,
  });
}

//更新终端
export function updateterminal (params) {
  return request({
    url: "/iot/terminal/update",
    method: "get",
    params
  });
}

// 获取实验室下的终端列表
export function getLaboratoryTerminalList (params) {
  return request({
    url: "/iot/terminal/lab-terminal-list",
    method: "get",
    params
  });
}
// 获取实验室下的终端列表状态
export function getLaboratoryTerminalStatusList (params) {
  return request({
    url: "/iot/terminal/terminal-status-list",
    method: "get",
    params
  });
}
// 获取实验室下的设备列表
export function getLaboratoryTerminalDeviceList (params) {
  return request({
    url: "/iot/terminal/dev-terminal-list",
    method: "get",
    params
  });
}
export function getDoorCtrlList (params) {
  return request({
    url: "/doorCtrl/list",
    method: "get",
    params
  });
}
// 终端控制
// 更新或保存设备绑定终端信息
export function setTermianControlStatus (data) {
  return request({
    url: "/iot/terminal/control",
    method: "post",
    data,
  });
}
