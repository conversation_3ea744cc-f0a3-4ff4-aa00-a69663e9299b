import request from "@admin/utils/request";

//待办列表
export function gettasklist(params) {
  return request({
    url: "/api/task/list",
    method: "get",
    params,
  });
}

//已办列表
export function getfinishtasklist(params) {
  return request({
    url: "/api/task/finishList",
    method: "get",
    params,
  });
}

//抄送列表
export function getcopyProcess(params) {
  return request({
    url: "/api/task/copyProcess",
    method: "get",
    params,
  });
}

//审核通过
export function approval(data) {
  return request({
    url: "/api/task/approval",
    method: "post",
    data,
  });
}

//审核拒绝
export function reject(data) {
  return request({
    url: "/api/task/reject",
    method: "post",
    data,
  });
}

//抄送已读
export function readCopy(data) {
  return request({
    url: "/api/task/readCopy",
    method: "post",
    data,
  });
}

//任务详情
export function gettaskdetail(params) {
  return request({
    url: "/api/task/detail/" + params,
    method: "get",
  });
}

//表单详情
export function getdetailByResv(params) {
  return request({
    url: "/api/task/detailByResv/" + params,
    method: "get",
  });
}

//代办详情
export function getpendingdetail(params) {
  return request({
    url: "/api/task/pending/detail",
    method: "get",
    params,
  });
}
