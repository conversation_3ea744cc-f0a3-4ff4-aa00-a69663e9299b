import request from "@admin/utils/request";

//获取仪器列表
export function getDevLargeReport(params) {
  return request({
    url: "/devLarge/getDevLargeReport",
    method: "get",
    params,
  });
}

//仪器上报
export function addreoprt(data) {
  return request({
    url: "/devLarge/devLargeReport",
    method: "post",
    data,
  });
}

//仪器更新和修改
export function updatedevlarge(data) {
  return request({
    url: "/devLarge/update",
    method: "post",
    data,
  });
}

//根据仪器uuid获取服务记录
export function getDevLargeRec(params) {
  return request({
    url: "/devLargeRec/getDevLargeRec",
    method: "get",
    params,
  });
}

//获取上报记录列表
export function getPagedevReportRec(params) {
  return request({
    url: "/devReportRec/getPage",
    method: "get",
    params,
  });
}

//服务记录上报
export function addreoprtrec(data) {
  return request({
    url: "/devLargeRec/devLargeRecReport",
    method: "post",
    data,
  });
}

//服务记录新增
export function savereoprtrec(data) {
  return request({
    url: "/devLargeRec/save",
    method: "post",
    data,
  });
}

//获取设备详情
export function getDetail(params) {
  return request({
    url: "/device/getDetail",
    method: "get",
    params,
  });
}

//大型仪器下拉框
export function getDevLarge(params) {
  return request({
    url: "/devLarge/getDevLarge",
    method: "get",
    params,
  });
}

//服务记录删除
export function deldevLargeRec(data) {
  return request({
    url: "/devLargeRec/delete",
    method: "post",
    data,
  });
}
