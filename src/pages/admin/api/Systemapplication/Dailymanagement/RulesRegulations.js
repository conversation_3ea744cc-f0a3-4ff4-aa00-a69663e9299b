import request from '@index/utils/request';

//获取规章制度
export function getregulation(params) {
    return request({
        url: '/regulation/page',
        method: 'get',
        params
    })
}

//删除规章制度
export function delregulation(data) {
    return request({
        url: '/regulation/delete',
        method: 'post',
        data
    })
}

//新增规章制度
export function saveregulation(data) {
    return request({
        url: '/regulation/save',
        method: 'post',
        data
    })
}

//修改规章制度
export function editregulation(data) {
    return request({
        url: '/regulation/update',
        method: 'post',
        data
    })
}


//获取规章制度
export function getregulationdetail(params) {
    return request({
        url: '/regulation/detail',
        method: 'get',
        params
    })
}
