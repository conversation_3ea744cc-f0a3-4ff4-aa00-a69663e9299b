import request from "@admin/utils/request";

//存包柜的记录查询
export function getRentalUse(params) {
  return request({
    url: "/rental/cabinet-rec/getPage",
    method: "get",
    params,
  });
}

//存包柜管理列表
export function getRental(params) {
  return request({
    url: "/rental/cabinet/page",
    method: "get",
    params,
  });
}

//租借柜保存
export function addRental(data) {
  return request({
    url: "/rental/cabinet/save",
    method: "post",
    data,
  });
}

//租借柜修改
export function updateRental(data) {
  return request({
    url: "/rental/cabinet/update",
    method: "post",
    data,
  });
}

//租借柜删除
export function deleteRental(data) {
  return request({
    url: "/rental/cabinet/delete",
    method: "post",
    data,
  });
}
