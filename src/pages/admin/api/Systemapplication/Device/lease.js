
import request from '@admin/utils/request';

//获取租借列表
export function getLeaseList(params) {
    return request({
        url: '/borrow/order/page',
        method: 'get',
        params
    })
}

//获取租借详情
export function getLeaseDetail(params) {
    return request({
        url: '/borrow/order/detail',
        method: 'get',
        params
    })
}

//
// export function addgroup(data) {
//     return request({
//         url: '/patrol/group/save',
//         method: 'post',
//         data
//     })
// }
