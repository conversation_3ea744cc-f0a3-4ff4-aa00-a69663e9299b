import request from '@admin/utils/request';

//获取巡检组
export function getgroup(params) {
    return request({
        url: '/patrol/group/page',
        method: 'get',
        params
    })
}

//添加巡检组
export function addgroup(data) {
    return request({
        url: '/patrol/group/save',
        method: 'post',
        data
    })
}

//修改巡检组
export function updategroup(data) {
    return request({
        url: '/patrol/group/update',
        method: 'post',
        data
    })
}

//删除巡检组
export function delgroup(data) {
    return request({
        url: '/patrol/group/delete',
        method: 'post',
        data
    })
}

//获取所有巡检组
export function getAllgroup(params) {
    return request({
        url: '/patrol/group/getAll',
        method: 'get',
        params
    })
}

//获取巡检组成员
export function getgroupmember(params) {
    return request({
        url: '/patrol/group/user/member/page',
        method: 'get',
        params
    })
}

//新增巡检组成员
export function addgroupmember(data) {
    return request({
        url: '/patrol/group/user/member/save',
        method: 'post',
        data
    })
}

//删除巡检组成员
export function delgroupmember(data) {
    return request({
        url: '/patrol/group/user/member/delete',
        method: 'post',
        data
    })
}

//批量删除巡检组成员
export function delgroupmemberBatch(data) {
    return request({
        url: '/patrol/group/user/member/deleteBatch',
        method: 'post',
        data
    })
}