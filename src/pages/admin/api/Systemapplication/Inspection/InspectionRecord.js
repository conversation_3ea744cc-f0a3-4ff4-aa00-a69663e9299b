import request from '@admin/utils/request';

//分页获取巡检任务列表
export function getinspectiontask(params) {
    return request({
        url: '/patrol/inspection/task/page',
        method: 'get',
        params
    })
}

//添加巡检任务
export function saveinspectiontask(data) {
    return request({
        url: '/patrol/inspection/task/save',
        method: 'post',
        data
    })
}

//修改巡检任务
export function updateinspectiontask(data) {
    return request({
        url: '/patrol/inspection/task/update',
        method: 'post',
        data
    })
}

//删除巡检任务
export function deleteinspectiontask(data) {
    return request({
        url: '/patrol/inspection/task/delete',
        method: 'post',
        data
    })
}

//获取巡检任务详情
export function getinspectiontaskdetail(params) {
    return request({
        url: '/patrol/inspection/task/detail',
        method: 'get',
        params
    })
}

//获取巡检任务巡检项详情
export function gettaskdetail(params) {
    return request({
        url: '/patrol/task/detail/page',
        method: 'get',
        params
    })
}

//执行任务
export function executetask(data) {
    return request({
        url: '/patrol/inspection/task/execute',
        method: 'post',
        data
    })
}

//异常整改列表
export function getpatrolexception(params) {
    return request({
        url: '/patrol/exception/rec/page',
        method: 'get',
        params
    })
}

//异常整改详情
export function getpatrolexceptiondetail(params) {
    return request({
        url: '/patrol/exception/rec/detail',
        method: 'get',
        params
    })
}