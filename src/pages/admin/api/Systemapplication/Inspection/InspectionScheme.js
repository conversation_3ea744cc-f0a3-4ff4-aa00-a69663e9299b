import request from '@admin/utils/request'

// 分页获取巡检计划列表
export function getinspectionplan(params) {
  return request({
    url: '/patrol/inspection/plan/page',
    method: 'get',
    params
  })
}

// 添加巡检计划
export function saveinspectionplan(data) {
  return request({
    url: '/patrol/inspection/plan/save',
    method: 'post',
    data
  })
}

// 新增自检计划
export function saveinspectionplansaveselfcheck(data) {
  return request({
    url: '/patrol/inspection/plan/save-self-check',
    method: 'post',
    data
  })
}

// 修改巡检计划
export function updateinspectionplan(data) {
  return request({
    url: '/patrol/inspection/plan/update',
    method: 'post',
    data
  })
}

// 删除巡检计划
export function deleteinspectionplan(data) {
  return request({
    url: '/patrol/inspection/plan/delete',
    method: 'post',
    data
  })
}

// 获取巡检计划详情
export function getinspectionplandetail(params) {
  return request({
    url: '/patrol/inspection/plan/detail',
    method: 'get',
    params
  })
}

// 获取巡检计划任务列表
export function getinspectionplantask(params) {
  return request({
    url: '/patrol/inspection/plan/taskPage',
    method: 'get',
    params
  })
}

// 新增巡检计划任务
export function saveinspectionplantask(data) {
  return request({
    url: '/patrol/inspection/plan/taskSave',
    method: 'post',
    data
  })
}

// 修改巡检计划任务
export function updateinspectionplantask(data) {
  return request({
    url: '/patrol/inspection/plan/taskUpdate',
    method: 'post',
    data
  })
}

// 删除巡检计划任务
export function delinspectionplantask(data) {
  return request({
    url: '/patrol/inspection/plan/taskDelete',
    method: 'post',
    data
  })
}

// 获取巡检任务详情
export function gettaskDetail(params) {
  return request({
    url: '/patrol/inspection/plan/taskDetail',
    method: 'post',
    params
  })
}

// 执行计划
export function executeplan(data) {
  return request({
    url: '/patrol/inspection/plan/execute',
    method: 'post',
    data
  })
}

// 终止计划
export function cancelplan(data) {
  return request({
    url: '/patrol/inspection/plan/cancel',
    method: 'post',
    data
  })
}

// 实验中心菜单树
export function getroomcentertree(params) {
  return request({
    url: '/room/centre/tree',
    method: 'get',
    params
  })
}

// 校区楼宇菜单树
export function getroomtree(params) {
  return request({
    url: '/room/tree',
    method: 'get',
    params
  })
}
// 获取巡检计划任务完成状态
export function taskStatus(params) {
  return request({
    url: '/patrol/inspection/plan/task-status',
    method: 'get',
    params
  })
}
