import request from '@admin/utils/request';

//获取列表
export function getpatrolroomPage(params) {
    return request({
        url: '/patrol/room/manager/region',
        method: 'get',
        params
    })
}

//获取列表-房间负责人
export function getpatrolroommanagerPage(params) {
    return request({
        url: '/patrol/room/manager/page',
        method: 'get',
        params
    })
}

//新增负责人
export function addpatrolroommanager(data) {
    return request({
        url: '/patrol/room/manager/save',
        method: 'post',
        data
    })
}

//设置主要负责人
export function addpatrolroommanagerleader(data) {
    return request({
        url: '/patrol/room/manager/leader',
        method: 'post',
        data
    })
}

//删除负责人
export function delpatrolroommanager(data) {
    return request({
        url: '/patrol/room/manager/delete',
        method: 'post',
        data
    })
}