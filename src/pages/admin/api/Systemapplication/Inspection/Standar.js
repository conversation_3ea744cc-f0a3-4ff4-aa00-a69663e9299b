import request from '@admin/utils/request';

//获取列表-检查项目
export function getpatrolinspectionitem(params) {
    return request({
        url: '/patrol/inspection/item/page',
        method: 'get',
        params
    })
}

//新增-检查项目
export function addpatrolinspectionitem(data) {
    return request({
        url: '/patrol/inspection/item/save',
        method: 'post',
        data
    })
}

//编辑-检查项目
export function updatepatrolinspectionitem(data) {
    return request({
        url: '/patrol/inspection/item/update',
        method: 'post',
        data
    })
}

//删除-检查项目
export function delpatrolinspectionitem(data) {
    return request({
        url: '/patrol/inspection/item/delete',
        method: 'post',
        data
    })
}

//导入-检查项目
export function importpatrolinspectionitem(data) {
    return request({
        url: '/patrol/inspection/item/import',
        method: 'post',
        data
    })
}


//获取列表-检查表
export function getpatrolinspectionform(params) {
    return request({
        url: '/patrol/inspection/form/page',
        method: 'get',
        params
    })
}

//新增-检查表
export function addpatrolinspectionform(data) {
    return request({
        url: '/patrol/inspection/form/save',
        method: 'post',
        data
    })
}

//编辑-检查表
export function updatepatrolinspectionform(data) {
    return request({
        url: '/patrol/inspection/form/update',
        method: 'post',
        data
    })
}

//删除-检查表
export function delpatrolinspectionform(data) {
    return request({
        url: '/patrol/inspection/form/delete',
        method: 'post',
        data
    })
}


//获取检查表下的检查项目
export function getpatrolinspectionformitemPage(params) {
    return request({
        url: '/patrol/inspection/form/itemPage',
        method: 'get',
        params
    })
}

//获取检查表下的检查详情
export function getpatrolinspectionformdetail(params) {
    return request({
        url: '/patrol/inspection/form/detail',
        method: 'get',
        params
    })
}


//检查表下拉框
export function getpatrolinspectionformAll(params) {
    return request({
        url: '/patrol/inspection/form/getAll',
        method: 'get',
        params
    })
}


//获取列表-检查分类
export function getinspectioncategory(params) {
    return request({
        url: '/patrol/inspection/category/page',
        method: 'get',
        params
    })
}

//新增-检查分类
export function addinspectioncategory(data) {
    return request({
        url: '/patrol/inspection/category/save',
        method: 'post',
        data
    })
}

//编辑-检查分类
export function updateinspectioncategory(data) {
    return request({
        url: '/patrol/inspection/category/update',
        method: 'post',
        data
    })
}

//删除-检查分类
export function delinspectioncategory(data) {
    return request({
        url: '/patrol/inspection/category/delete',
        method: 'post',
        data
    })
}

//检查分类下拉框
export function getinspectioncategoryAll(params) {
    return request({
        url: '/patrol/inspection/category/getAll',
        method: 'get',
        params
    })
}

//检查分类菜单树
export function getcategoryTree() {
    return request({
        url: '/patrol/inspection/category/getTree',
        method: 'get',
    })
}



