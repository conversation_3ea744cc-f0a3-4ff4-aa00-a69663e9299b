import request from "@admin/utils/request";

//分页获取巡检计划列表
export function getlinkagegetPage(params) {
  return request({
    url: "/iot/scene/linkage/getPage",
    method: "get",
    params,
  });
}

//新建联动-触发对象-实验室树
export function getroomtree(params) {
  return request({
    url: "/room/tree",
    method: "get",
    params,
  });
}

//新建联动-触发对象-设备树
export function getdevicetree(params) {
  return request({
    url: "/device/tree",
    method: "get",
    params,
  });
}

//新建联动-触发对象-获取设备和实验室下面的终端
export function getBindTerminal(params) {
  return request({
    url: "/iot/iotTerminalDevRelation/getBindTerminal",
    method: "get",
    params,
  });
}

//新建联动-触发条件-条件内的属性
export function getAllBusiness(params) {
  return request({
    url: "/iot/terminal/getAllBusiness",
    method: "get",
    params,
  });
}

//新建联动-出发条件-选择开放时间段
export function getOpenRuleData(params) {
  return request({
    url: "/openRuleTime/getOpenRuleData",
    method: "get",
    params,
  });
}

//新建联动-触发条件-选择开放时间段
export function addscenelinkage(data) {
  return request({
    url: "/iot/scene/linkage/save",
    method: "post",
    data,
  });
}

//修改联动
export function updatescenelinkage(data) {
  return request({
    url: "/iot/scene/linkage/update",
    method: "post",
    data
  });
}

//删除联动
export function dellinkage(data) {
  return request({
    url: "/iot/scene/linkage/delete",
    method: "post",
    data
  });
}


//场景联动-日志
export function getlinkagelog(params) {
  return request({
    url: "/iot/scene/linkage/log/getPage",
    method: "get",
    params,
  });
}

//日志管理-相关接口
export function getloglist(params) {
  return request({
    url: "/iot/iotThresholdAlarmLog/getPage",
    method: "get",
    params
  });
}

//特殊地点树
export function getspecialPlacetree(params) {
  return request({
    url: "/specialPlace/tree",
    method: "get",
    params,
  });
}
