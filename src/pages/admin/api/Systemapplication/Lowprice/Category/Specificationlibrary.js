import request from "@admin/utils/request";

//列表-检查规格
export function getspecification(params) {
  return request({
    url: "/lowValue/specification/page",
    method: "get",
    params
  });
}

//新增-检查规格
export function addspecification(data) {
  return request({
    url: "/lowValue/specification/save",
    method: "post",
    data,
  });
}

//编辑-检查规格
export function updatespecification(data) {
  return request({
    url: "/lowValue/specification/update",
    method: "post",
    data,
  });
}

//删除-检查规格
export function delspecification(data) {
  return request({
    url: "/lowValue/specification/delete",
    method: "post",
    data,
  });
}

//修改-检查规格状态
export function updateStatus(data) {
  return request({
    url: "/lowValue/specification/updateStatus",
    method: "post",
    data,
  });
}

//下拉框-检查规格状态
export function getAllspecification(data) {
  return request({
    url: "/lowValue/specification/getAll",
    method: "get",
    data,
  });
}
