import request from '@admin/utils/request';

//新增-检查分类
export function addlowValue(data) {
    return request({
        url: '/lowValue/Kind/save',
        method: 'post',
        data
    })
}

//编辑-检查分类
export function updatelowValue(data) {
    return request({
        url: '/lowValue/Kind/update',
        method: 'post',
        data
    })
}

//删除-检查分类
export function dellowValue(data) {
    return request({
        url: '/lowValue/Kind/delete',
        method: 'post',
        data
    })
}

//检查分类菜单树
export function getlowValueTree() {
    return request({
        url: '/lowValue/Kind/tree',
        method: 'get',
    })
}