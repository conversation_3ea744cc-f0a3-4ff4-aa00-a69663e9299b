import request from "@admin/utils/request";

//列表-商品
export function getconsumable(params) {
  return request({
    url: "/low/value/consumable/page",
    method: "get",
    params,
  });
}

//新增-商品
export function addconsumable(data) {
  return request({
    url: "/low/value/consumable/save",
    method: "post",
    data,
  });
}

//编辑-商品
export function updateconsumable(data) {
  return request({
    url: "/low/value/consumable/update",
    method: "post",
    data,
  });
}

//删除-商品
export function delconsumable(data) {
  return request({
    url: "/low/value/consumable/delete",
    method: "post",
    data,
  });
}
