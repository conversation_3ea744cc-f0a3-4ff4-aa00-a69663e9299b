import request from "@admin/utils/request";

//列表-入库单
export function getwarehousingEntrypage(params) {
  return request({
    url: "/low/value/warehousingEntry/page",
    method: "get",
    params,
  });
}

//新增-入库
export function addwarehousingEntry(data) {
  return request({
    url: "/low/value/warehousingEntry/save",
    method: "post",
    data,
  });
}

//修改-入库
export function updatewarehousingEntry(data) {
  return request({
    url: "/low/value/warehousingEntry/update",
    method: "post",
    data,
  });
}

//删除-入库
export function delwarehousingEntry(data) {
  return request({
    url: "/low/value/warehousingEntry/delete",
    method: "post",
    data,
  });
}

//提交-入库
export function submitwarehousingEntry(data) {
  return request({
    url: "/low/value/warehousingEntry/submit",
    method: "post",
    data,
  });
}

//入库单详情
export function getwarehousingEntrydetail(params) {
  return request({
    url: "/low/value/warehousingEntry/detail",
    method: "get",
    params,
  });
}

//列表=商品信息
export function getwarehousingGoods(params) {
  return request({
    url: "/low/value/warehousingGoods/page",
    method: "get",
    params,
  });
}

//提交入库单商品信息
export function submitwarehousingGoods(data) {
  return request({
    url: "/low/value/warehousingGoods/submit",
    method: "post",
    data
  });
}
