import request from "@admin/utils/request";

//新增退回单
export function savereturnOrder(data) {
  return request({
    url: "/low/value/returnOrder/save",
    method: "post",
    data,
  });
}

//修改退回单
export function updatereturnOrder(data) {
  return request({
    url: "/low/value/returnOrder/update",
    method: "post",
    data,
  });
}

//列表-退回单
export function getreturnOrderpage(params) {
  return request({
    url: "/low/value/returnOrder/page",
    method: "get",
    params,
  });
}

//提交退回单
export function submitreturnOrder(data) {
  return request({
    url: "/low/value/returnOrder/submit",
    method: "post",
    data,
  });
}

//列表-退回单详情
export function getreturnOrderdetail(params) {
  return request({
    url: "/low/value/returnOrder/detail",
    method: "get",
    params,
  });
}

//列表-退回商品列表
export function getreturnGoods(params) {
  return request({
    url: "/low/value/returnGoods/page",
    method: "get",
    params,
  });
}
