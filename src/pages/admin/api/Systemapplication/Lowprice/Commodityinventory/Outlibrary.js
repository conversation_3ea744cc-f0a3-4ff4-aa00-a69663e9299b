import request from "@admin/utils/request";

//列表-出库单
export function getoutboundDeliveryOrder(params) {
  return request({
    url: "/low/value/outboundDeliveryOrder/page",
    method: "get",
    params,
  });
}

//新增-出库
export function addoutboundDeliveryOrder(data) {
  return request({
    url: "/low/value/outboundDeliveryOrder/save",
    method: "post",
    data,
  });
}

//修改-出库
export function updateoutboundDeliveryOrder(data) {
  return request({
    url: "/low/value/outboundDeliveryOrder/update",
    method: "post",
    data,
  });
}

//删除-出库
export function deloutboundDeliveryOrder(data) {
  return request({
    url: "/low/value/outboundDeliveryOrder/delete",
    method: "post",
    data,
  });
}

//提交-出库商品
export function submitoutboundGoods(data) {
  return request({
    url: "/low/value/outboundGoods/submit",
    method: "post",
    data,
  });
}

//出库单详情
export function getoutboundDeliveryOrderdetail(params) {
  return request({
    url: "/low/value/outboundDeliveryOrder/detail",
    method: "get",
    params,
  });
}

//列表=商品信息
export function getoutboundGoods(params) {
  return request({
    url: "/low/value/outboundGoods/page",
    method: "get",
    params,
  });
}

//商品树
export function getinventorytree(params) {
  return request({
    url: "/low/value/inventory/tree",
    method: "get",
    params,
  });
}

//提交-出库单
export function submitoutboundDeliveryOrder(data) {
  return request({
    url: "/low/value/outboundDeliveryOrder/submit",
    method: "post",
    data,
  });
}
