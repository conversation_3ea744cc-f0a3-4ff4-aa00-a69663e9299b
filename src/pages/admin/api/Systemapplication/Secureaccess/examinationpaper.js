import request from "@admin/utils/request";

//获取绑定树
export function getbindtree(params) {
  return request({
    url: "/safety/exam/bind/tree",
    method: "get",
    params
  });
}

//获取实验室绑定关系
export function getbindroom(params) {
  return request({
    url: "/safety/exam/bind/room",
    method: "get",
    params
  });
}

//绑定试卷
export function confirmbind(data) {
  return request({
    url: "/safety/exam/bind/confirm",
    method: "post",
    data,
  });
}

//安全证书列表
export function getpermissionpage(params) {
  return request({
    url: "/safety/permission/page",
    method: "get",
    params
  });
}

//新增安全证书
export function savepermission(data) {
  return request({
    url: "/safety/permission/save",
    method: "post",
    data,
  });
}

//删除安全证书
export function delpermission(data) {
  return request({
    url: "/safety/permission/delete",
    method: "post",
    data,
  });
}
