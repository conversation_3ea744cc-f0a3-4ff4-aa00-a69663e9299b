import request from '@admin/utils/request';

//当前-采集
export function collectdetail() {
  return request({
    url: '/report/sj7/detail/collect',
    method: 'get',
  })
}

//当前-列表
export function getdetailpage(params) {
    return request({
      url: '/report/sj7/detail/page',
      method: 'get',
      params
    })
}

//当前-编辑
export function updatedetail(data) {
    return request({
      url: '/report/sj7/detail/update',
      method: 'post',
      data
    })
}

//当前-新增
export function adddetail(data) {
  return request({
    url: '/report/sj7/detail/save',
    method: 'post',
    data
  })
}

//当前-新增
export function impordetail(data) {
  return request({
    url: '/report/sj7/detail/import',
    method: 'post',
    data
  })
}

//当前-归档
export function headerarchive(data) {
    return request({
      url: '/report/sj7/header/archive',
      method: 'post',
      data
    })
}

//当前-判断当前学期是否已归档
export function isArchive() {
  return request({
    url: "/report/sj7/header/isArchive",
    method: "get",
  });
}




//历史-列表
export function getheaderpage(params) {
  return request({
    url: '/report/sj7/header/page',
    method: 'get',
    params
  })
}


//历史-查看详情
export function getdetailhistorypage(params) {
    return request({
      url: '/report/sj7/detail/history/page',
      method: 'get',
      params
    })
}

