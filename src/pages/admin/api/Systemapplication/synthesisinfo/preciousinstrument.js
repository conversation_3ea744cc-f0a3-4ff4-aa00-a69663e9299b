import request from "@admin/utils/request";

//实验室-列表
export function getroompage(params) {
  return request({
    url: "/report/sj3/detail/room/page",
    method: "get",
    params,
  });
}

//实验室-采集
export function getroomcollect(params) {
  return request({
    url: "/report/sj3/detail/room/collect",
    method: "get",
    params,
  });
}

//实验室-上报操作
export function reportroom() {
  return request({
    url: "/report/sj3/detail/room/report",
    method: "post",
  });
}

//学院-列表
export function getdeptpage(params) {
  return request({
    url: "/report/sj3/detail/dept/page",
    method: "get",
    params,
  });
}

//学院-上报
export function deptreport(data) {
  return request({
    url: "/report/sj3/detail/dept/report",
    method: "post",
    data,
  });
}

//学院-新增
export function deptsave(data) {
  return request({
    url: "/report/sj3/detail/save",
    method: "post",
    data,
  });
}

//学院-更新
export function deptupdate(data) {
  return request({
    url: "/report/sj3/detail/update",
    method: "post",
    data,
  });
}

//学院-删除
export function deldept(data) {
  return request({
    url: "/report/sj3/detail/delete",
    method: "post",
    data,
  });
}

//学院-批量删除
export function delBatchdept(data) {
  return request({
    url: "/report/sj3/detail/deleteBatch",
    method: "post",
    data,
  });
}

//学院-获取当前管理下的学院
export function getDeptList(params) {
  return request({
    url: "/report/sj3/detail/dept/list",
    method: "get",
    params,
  });
}
//以上都是当前界面的接口

//统筹-列表
export function getdetailpage(params) {
  return request({
    url: "/report/sj3/detail/page",
    method: "get",
    params,
  });
}

//统筹-归档
export function archiveheader(data) {
  return request({
    url: "/report/sj3/header/archive",
    method: "post",
    data,
  });
}

//统筹-左侧学院列表
export function getdeptlist(params) {
  return request({
    url: "/report/sj3/detail/dept/list",
    method: "get",
    params,
  });
}

//统筹-判断当前学期是否已归档
export function isArchive() {
  return request({
    url: "/report/sj3/header/isArchive",
    method: "get",
  });
}
//以上都是统筹界面的接口

//历史-列表
export function getheaderpage(params) {
  return request({
    url: "/report/sj3/header/page",
    method: "get",
    params,
  });
}

//历史-详情
export function gethistorypagedetail(params) {
  return request({
    url: "/report/sj3/detail/history/page",
    method: "get",
    params,
  });
}

//历史-导出
export function exporthistory(params) {
  return request({
    url: "/report/sj3/detail/history/export",
    method: "get",
    params,
  });
}
//以上都是历史界面的接口

//三个阶段-导入表格
export function importall(data) {
  return request({
    url: "/report/sj3/detail/import",
    method: "post",
    data,
  });
}
