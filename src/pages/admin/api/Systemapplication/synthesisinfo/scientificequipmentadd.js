import request from '@admin/utils/request';

//当前-采集
export function collectdetail(data) {
  return request({
    url: '/report/sj2/detail/collect',
    method: 'post',
    data
  })
}

//当前-列表
export function getdetailpage(params) {
    return request({
      url: '/report/sj2/detail/page',
      method: 'get',
      params
    })
}

//当前-编辑
export function updatedetail(data) {
    return request({
      url: '/report/sj2/detail/update',
      method: 'post',
      data
    })
}

//当前-归档
export function headerarchive(data) {
    return request({
      url: '/report/sj2/header/archive',
      method: 'post',
      data
    })
}

//当前-导入
export function importdetail(data) {
  return request({
    url: '/report/sj2/header/archive',
    method: 'post',
    data
  })
}

//当前-判断当前学期是否已归档
export function isArchive() {
  return request({
    url: '/report/sj2/header/isArchive',
    method: 'get',
  })
}




//历史-列表
export function getheaderpage(params) {
  return request({
    url: '/report/sj2/header/page',
    method: 'get',
    params
  })
}


//历史-查看详情
export function getdetailhistorypage(params) {
    return request({
      url: '/report/sj2/detail/history/page',
      method: 'get',
      params
    })
}

