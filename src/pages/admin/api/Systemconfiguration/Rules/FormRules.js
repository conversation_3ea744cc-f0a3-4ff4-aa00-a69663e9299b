import request from '@index/utils/request'

// 分页
export function getform(params) {
  return request({
    url: '/rule/form/page',
    method: 'get',
    params
  })
}

// 新增
export function addform(data) {
  return request({
    url: '/rule/form/add',
    method: 'post',
    data
  })
}

// 编辑
export function editform(data) {
  return request({
    url: '/rule/form/edit',
    method: 'post',
    data
  })
}

// 删除
export function delform(data) {
  return request({
    url: '/rule/form/delete',
    method: 'post',
    data
  })
}

// 详情
export function detailform(params) {
  return request({
    url: '/rule/form/detail',
    method: 'get',
    params
  })
}

// 获取所有表单id下拉框
export function getFormIdDataDropdown(params) {
  return request({
    url: '/workflow/define/dropdown',
    method: 'get',
    params
  })
}

// 获取表单uuid下拉框
export function getFormUuidDropdown(params) {
  return request({
    url: '/workflow/define/nodeFormDropdown',
    method: 'get',
    params
  })
}

// 获取外借绑定的表单
export function getBorrowForm() {
  return request({
    url: '/borrow/form/detail',
    method: 'get'
  })
}

// 绑定外借表单
export function bindBorrowForm(data) {
  return request({
    url: '/borrow/form/bind',
    method: 'post',
    data
  })
}
