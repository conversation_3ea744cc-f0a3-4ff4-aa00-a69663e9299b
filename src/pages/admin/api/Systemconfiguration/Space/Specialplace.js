import request from "@admin/utils/request";

//获取特殊地点列表
export function getlist(params) {
  return request({
    url: "/specialPlace/list",
    method: "get",
    params,
  });
}

//新增特殊地点
export function save(data) {
  return request({
    url: "/specialPlace/save",
    method: "post",
    data,
  });
}

//编辑特殊地点
export function update(data) {
  return request({
    url: "/specialPlace/update",
    method: "post",
    data,
  });
}

//删除特殊地点
export function del(data) {
  return request({
    url: "/specialPlace/delete",
    method: "post",
    data,
  });
}

//特殊地点树
export function getspecialPlacetree(params) {
  return request({
    url: "/specialPlace/tree",
    method: "get",
    params,
  });
}
