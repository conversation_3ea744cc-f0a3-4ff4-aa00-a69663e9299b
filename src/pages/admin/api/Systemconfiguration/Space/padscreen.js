import request from "@admin/utils/request";

//获取实验室负责人列表
export function getpersondetail(params) {
  return request({
    url: "/room/responsible/person/detail",
    method: "get",
    params,
  });
}

//新增实验室负责人
export function saveperson(data) {
  return request({
    url: "/room/responsible/person/save",
    method: "post",
    data,
  });
}

//获取实验室规则制度
export function getregulationdetail(params) {
  return request({
    url: "/room/regulation/detail",
    method: "get",
    params,
  });
}

//新增/更新实验室-规则制度
export function saveregulation(data) {
  return request({
    url: "/room/regulation/save",
    method: "post",
    data,
  });
}

//获取规章制度
export function getregulationdetailcontent(params) {
  return request({
    url: "/regulation/detail",
    method: "get",
    params,
  });
}
