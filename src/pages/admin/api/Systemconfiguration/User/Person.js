import request from "@index/utils/request";

//获取负责人列表
export function getperson(params) {
  return request({
    url: "/responsible/person/page",
    method: "get",
    params,
  });
}

//新增负责人
export function saveperson(data) {
  return request({
    url: "/responsible/person/save",
    method: "post",
    data
  });
}

//修改负责人
export function updateperson(data) {
  return request({
    url: "/responsible/person/update",
    method: "post",
    data
  });
}

//删除负责人
export function delperson(data) {
  return request({
    url: "/responsible/person/delete",
    method: "post",
    data
  });
}

//上传负责人照片
export function uploadpersonimg(data) {
  return request({
    url: "/responsible/person/upload",
    method: "post",
    data
  });
}
