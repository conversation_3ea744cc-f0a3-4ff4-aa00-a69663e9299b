import request from "@index/utils/request";

//分组列表
export function getcategory(params) {
  return request({
    url: "/workflow/category/getPage",
    method: "get",
    params,
  });
}

//新增分组
export function addcategory(params) {
  return request({
    url: "/workflow/category/add",
    method: "get",
    params,
  });
}

//修改分组
export function editcategory(params) {
  return request({
    url: "/workflow/category/edit",
    method: "get",
    params,
  });
}

//获取属性数组
export function gettimepage(params) {
  return request({
    url: "/workflow/category/property",
    method: "get",
    params,
  });
}

//表单下拉框----获取id
export function getdropdownid(params) {
  return request({
    url: "/workflow/define/dropdown",
    method: "get",
    params,
  });
}

//表单下拉框----获取uuid
export function getdropdownuuid(params) {
  return request({
    url: "/workflow/define/nodeFormDropdown",
    method: "get",
    params,
  });
}
