import request from '@index/utils/request';

//获取学年列表
export function gettimepage(params) {
    return request({
        url: '/report/time/page',
        method: 'get',
        params
    })
}

//新增学年
export function savetime(data) {
    return request({
        url: '/report/time/save',
        method: 'post',
        data
    })
}

//修改学年
export function updatetime(data) {
    return request({
        url: '/report/time/update',
        method: 'post',
        data
    })
}

//删除学年
export function deltime(data) {
    return request({
        url: '/report/time/delete',
        method: 'post',
        data
    })
}

//发布学年
export function publishtime(data) {
    return request({
        url: '/report/time/publish',
        method: 'post',
        data
    })
}

//获取当前学年
export function getcurrentTime(params) {
    return request({
        url: '/report/time/current',
        method: 'get',
        params
    })
}

//获取所有学年
export function getalltime(params) {
    return request({
        url: '/report/time/getAll',
        method: 'get',
        params
    })
}