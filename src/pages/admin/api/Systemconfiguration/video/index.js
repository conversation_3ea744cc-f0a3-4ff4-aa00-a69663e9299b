import request from "@index/utils/request";

//分类列表
export function getcategory(params) {
  return request({
    url: "/video/category/page",
    method: "get",
    params,
  });
}

//分类树
export function getcategorytree(params) {
  return request({
    url: "/video/category/tree",
    method: "get",
    params,
  });
}

//新增分类
export function savecategory(data) {
  return request({
    url: "/video/category/save",
    method: "post",
    data,
  });
}

//修改分类
export function editcategory(data) {
  return request({
    url: "/video/category/update",
    method: "post",
    data,
  });
}

//删除分类
export function delcategory(data) {
  return request({
    url: "/video/category/delete",
    method: "post",
    data,
  });
}

//视频列表
export function getvideo(params) {
  return request({
    url: "/video/page",
    method: "get",
    params,
  });
}

//新增视频
export function savevideo(data) {
  return request({
    url: "/video/save",
    method: "post",
    data,
  });
}

//修改视频
export function editvideo(data) {
  return request({
    url: "/video/update",
    method: "post",
    data,
  });
}

//删除视频
export function delvideo(data) {
  return request({
    url: "/video/delete",
    method: "post",
    data,
  });
}


//上传视频
export function uploadvideo(data) {
  return request({
    url: "/video/upload",
    method: "post",
    data,
  });
}

//视频绑定列表
export function getrelationvideo(params) {
  return request({
    url: "/video/relation/page",
    method: "get",
    params,
  });
}

//新增视频绑定关系
export function saverelationvideo(data) {
  return request({
    url: "/video/relation/save",
    method: "post",
    data,
  });
}

//删除视频绑定关系
export function delrelationvideo(data) {
  return request({
    url: "/video/relation/delete",
    method: "post",
    data,
  });
}
