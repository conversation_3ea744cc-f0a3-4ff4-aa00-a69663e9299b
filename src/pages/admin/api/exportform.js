import request from "@admin/utils/request_import";

//教学开放预约-排课情况-已排课程导出
export function export_arrangecourse(params) {
  return request({
    url: "/manager/exportExcel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-预约状况-已排课程导出
export function export_teachingcourse(params) {
  return request({
    url: "/manager/arrangementCourse/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-教学实验项目
export function export_experimentproject(params) {
  return request({
    url: "/statistics/testItem/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-教学科研仪器设备
export function export_teachingequipment(params) {
  return request({
    url: "/statistics/device/scientific-research/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-课程使用
export function export_courseuse(params) {
  return request({
    url: "/statistics/course/experiment/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-开课学院使用
export function export_classesuse(params) {
  return request({
    url: "/statistics/dept/experiment/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-教师使用
export function export_teacheruse(params) {
  return request({
    url: "/statistics/teacher/experiment/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-已支付上机费用
export function export_boardingpaid(params) {
  return request({
    url: "/statistics/fee/paid/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//教学开放预约-统计报表-设备使用
export function export_devuse(params) {
  return request({
    url: "/statistics/device/experiment/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学科研仪器设备-实验室
export function export_sj1_room(params) {
  return request({
    url: "/report/sj1/detail/room/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学科研仪器设备-学院
export function export_sj1_dept(params) {
  return request({
    url: "report/sj1/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学科研仪器设备-统筹
export function export_sj1_report(params) {
  return request({
    url: "report/sj1/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学科研仪器设备-历史
export function export_sj1_history(params) {
  return request({
    url: "report/sj1/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学科研仪器设备增减变动-当前
export function export_sj2_now(params) {
  return request({
    url: "report/sj2/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学科研仪器设备-历史
export function export_sj2_history(params) {
  return request({
    url: "report/sj2/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-贵重仪器设备-实验室
export function export_sj3_room(params) {
  return request({
    url: "/report/sj3/detail/room/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-贵重仪器设备-学院
export function export_sj3_dept(params) {
  return request({
    url: "/report/sj3/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-贵重仪器设备-统筹
export function export_sj3_report(params) {
  return request({
    url: "/report/sj3/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-贵重仪器设备-统筹
export function export_sj3_history(params) {
  return request({
    url: "/report/sj3/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学实验项目-实验室
export function export_sj4_room(params) {
  return request({
    url: "/report/sj4/detail/room/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学实验项目-学院
export function export_sj4_dept(params) {
  return request({
    url: "/report/sj4/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学实验项目-统筹
export function export_sj4_report(params) {
  return request({
    url: "/report/sj4/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-教学实验项目-历史
export function export_sj4_history(params) {
  return request({
    url: "/report/sj4/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-专任实验室人员-实验室
export function export_sj5_room(params) {
  return request({
    url: "/report/sj5/detail/room/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-专任实验室人员-学院
export function export_sj5_dept(params) {
  return request({
    url: "/report/sj5/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-专任实验室人员-统筹
export function export_sj5_report(params) {
  return request({
    url: "/report/sj5/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-专任实验室人员-历史
export function export_sj5_history(params) {
  return request({
    url: "/report/sj5/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-房间基本情况-实验室
export function export_sj6_room(params) {
  return request({
    url: "/report/sj6/detail/room/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-房间基本情况-学院
export function export_sj6_dept(params) {
  return request({
    url: "/report/sj6/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-实验室基本情况-实验室
export function export_sj6_room_lab(params) {
  return request({
    url: "/report/sj6/lab/detail/room/lab/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-实验室基本情况-学院
export function export_sj6_dept_lab(params) {
  return request({
    url: "/report/sj6/lab/detail/dept/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-实验室基本情况-统筹
export function export_sj6_report(params) {
  return request({
    url: "/report/sj6/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-实验室基本情况-历史
export function export_sj6_history(params) {
  return request({
    url: "/report/sj6/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-实验室经费情况-当前
export function export_sj7_now(params) {
  return request({
    url: "/report/sj7/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-实验室经费情况-历史
export function export_sj7_history(params) {
  return request({
    url: "/report/sj7/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-高等实验室综合信息表-当前
export function export_sz1_now(params) {
  return request({
    url: "/report/sz1/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-高等实验室综合信息表-历史
export function export_sz1_history(params) {
  return request({
    url: "/report/sz1/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-高等实验室综合信息表二-当前
export function export_sz2_now(params) {
  return request({
    url: "/report/sz2/detail/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-高等实验室综合信息表二-历史
export function export_sz2_history(params) {
  return request({
    url: "/report/sz2/detail/history/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-低值易耗品-商品库存-商品列表
export function export_consumable(params) {
  return request({
    url: "/low/value/consumable/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-低值易耗品-商品库存-入库商品明细
export function export_warehousingGoods(params) {
  return request({
    url: "/low/value/warehousingGoods/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合信息-低值易耗品-商品库存-出库商品明细
export function export_outboundGoods(params) {
  return request({
    url: "/low/value/outboundGoods/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合统计-排课记录综合查询
export function export_synthesisteaching(params) {
  return request({
    url: "/statistics/synthesis/teaching/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合统计-上机情况对比综合查询
export function export_synthesiscomputer(params) {
  return request({
    url: "/statistics/synthesis/computer/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合统计-实验室使用
export function export_roomexperiment(params) {
  return request({
    url: "/statistics/room/experiment/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//综合统计-实验室上机使用
export function export_roomcomputer(params) {
  return request({
    url: "/statistics/room/computer/export-excel",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//预约状况-教学排课-出勤状况导出
export function export_reserveattendance(params) {
  return request({
    url: "/reserve/attendance/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//大型仪器管理-仪器管理
export function export_devLarge(params) {
  return request({
    url: "/devLarge/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//大型仪器管理-服务记录
export function export_devLargeRec(params) {
  return request({
    url: "/devLargeRec/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//安全巡检-巡检记录
export function export_task(params) {
  return request({
    url: "/patrol/inspection/task/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//安全巡检-异常记录
export function export_rec(params) {
  return request({
    url: "/patrol/exception/rec/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//租借柜导出
export function export_cabinetrec(params) {
  return request({
    url: "/rental/cabinet-rec/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//安全准入-试题导出
export function export_question(params) {
  return request({
    url: "/safety/question/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//安全准入-成绩管理-导出
export function export_exame(params) {
  return request({
    url: "/safety/exam/result/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//实验室管理-房间-导出
export function export_room(params) {
  return request({
    url: "/room/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//安全准入管理-数据统计-证书导出
export function export_permission(params) {
  return request({
    url: "/statistics/safety/permission/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}

//安全准入管理-数据统计-成绩导出
export function export_examresult(params) {
  return request({
    url: "/statistics/safety/exam/result/export",
    method: "get",
    responseType: "blob", // 设置响应数据类型为 blob
    params,
  });
}
