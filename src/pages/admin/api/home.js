import request from '@admin/utils/request';

//楼宇设备数量统计信息
export function getbuildingdatas() {
    return request({
      url: '/home/<USER>/building',
      method: 'get',
    })
}

//楼层设备数量统计信息
export function getfloordatas() {
  return request({
    url: '/home/<USER>/floor',
    method: 'get',
  })
}

//实验室设备数量统计信息
export function getroomdevdatas() {
    return request({
      url: '/home/<USER>/room',
      method: 'get',
    })
}

//设备类型统计信息
export function getdevKind() {
    return request({
      url: '/home/<USER>/kind',
      method: 'get',
    })
}

//楼宇实验室分配情况
export function getdistributionbuilding() {
    return request({
      url: '/home/<USER>/distribution/building',
      method: 'get',
    })
}

//楼层实验室分配情况
export function getdistributionfloor() {
    return request({
      url: '/home/<USER>/distribution/floor',
      method: 'get',
    })
}

//科研实验实验室分配情况
export function getdistributionresearch() {
    return request({
      url: '/home/<USER>/distribution/research',
      method: 'get',
    })
}

//用户实验室分配情况
export function getdistributionuser() {
    return request({
      url: '/home/<USER>/distribution/user',
      method: 'get',
    })
}


//文件上传-通用
export function Uploadfile(data) {
  return request({
    url: '/fileUpload/file',
    method: 'post',
    data
  })
}


//实验室菜单树-通用
export function getroomtree() {
  return request({
    url: '/room/tree',
    method: 'get'
  })
}