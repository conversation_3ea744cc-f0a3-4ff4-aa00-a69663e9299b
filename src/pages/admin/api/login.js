import request from '@admin/utils/request';

export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}

//获取较全的用户信息
export function getalluserinfo() {
  return request({
    url: '/login/getLogonInfo',
    method: 'get',
  })
}

//获取统一身份认证登录地址
export function getAuthAddress(params) {
  return request({
    url: '/login/getAuthAddress',
    method: 'get',
    params
  })
}

//统一身份认证登录
export function authLogin(data) {
  return request({
    url: '/login/authLogin',
    method: 'post',
    data
  })
}

//统一身份认证登录
export function logout(data) {
  return request({
    url: '/login/logout',
    method: 'post',
    data
  })
}