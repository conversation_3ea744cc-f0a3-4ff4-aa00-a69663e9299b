<template>
  <!-- 图表展示 -->
  <div class="chart-wrap" ref="charRef"></div>
</template>

<script>
import echarts from "echarts";
export default {
  name: "BarCharts",
  props: {
    data: {
      type: Array,
      required: true,
    },
    // 类型: 1 实验室 2 课程 4开课学院 8 教师
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      mEcharts: null,
    };
  },
  watch: {
    data: {
      handler(nv) {
        if (nv) {
          const option = this.getOption(nv);
          this.init(option);
        }
      },
    },
  },
  mounted() {
    this.mEcharts = echarts.init(this.$refs.charRef);
  },
  beforeDestroy() {
    if (this.mEcharts) {
      this.mEcharts.dispose();
    }
  },
  methods: {
    init(option) {
      this.mEcharts && this.mEcharts.setOption(option);
    },
    getOption(data) {
      const colors = ["#5470C6", "#91CC75", "#EE6666", "#cc9975"];
      let xData = [];
      let testHourList = [];
      let yA = [
            {
              type: "value",
              name: "实验学时数",
              position: "left",
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: colors[3],
                },
              },
              axisLabel: {
                formatter: "{value} ",
              },
            },
          ];;
      let ser = [
            {
              name: "实验学时数",
              type: "bar",
              yAxisIndex: 0,
              data: testHourList,
              itemStyle: {
                color: colors[3],
              },
            },
          ];
      let legendData = ["实验学时"];
      data.forEach((dt) => {
        if (this.type === 1) {
          // 个人使用排行榜
          xData.push(dt.roomName);
          legendData = ["实验学时", "课外时长", "课外人数"];
          let extracurricularTimeList = [];
          let extracurricularUserNumList = [];
          extracurricularTimeList.push(dt.extracurricularTime);
          extracurricularUserNumList.push(dt.extracurricularUserNum);
          yA = [
            {
              type: "value",
              name: "实验学时数",
              position: "left",
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: colors[3],
                },
              },
              axisLabel: {
                formatter: "{value} ",
              },
            },
            // 时长
            {
              type: "value",
              name: "课外时长",
              position: "left",
              alignTicks: true,
              offset: 60,
              axisLine: {
                show: true,
                lineStyle: {
                  color: colors[1],
                },
              },
              axisLabel: {
                formatter: "{value} min",
              },
            },
            // 预约使用量
            {
              type: "value",
              name: "课外人数",
              position: "right",
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: colors[0],
                },
              },
              axisLabel: {
                formatter: "{value} 人",
              },
            },
          ];
          ser = [
            {
              name: "实验学时数",
              type: "bar",
              yAxisIndex: 0,
              data: testHourList,
              itemStyle: {
                color: colors[3],
              },
            },
            {
              name: "课外时长",
              type: "bar",
              yAxisIndex: 1,
              data: extracurricularTimeList,
              itemStyle: {
                color: colors[1],
              },
            },
            {
              name: "课外人数",
              type: "bar",
              yAxisIndex: 2,
              data: extracurricularUserNumList,
              itemStyle: {
                color: colors[0],
              },
            },
          ];
        } else if (this.type === 2) {
          xData.push(dt.courseName);
        } else if (this.type === 4) {
          xData.push(dt.deptName);
        } else if(this.type === 8){
          xData.push(dt.trueName);
        }
        testHourList.push(dt.testHour);
      });
      return {
        color: colors,
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        grid: {
          right: "15%",
          left: "15%",
          bottom: "25%",
        },
        toolbox: {
          feature: {
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        dataZoom: {
          type: "inside",
          xAxisIndex: [0],
          startValue: 0,
          endValue: 19,
        },
        legend: {
          data: legendData,
        },
        dataZoom: [
          {
            type: "slider",
            startValue: 0,
            endValue: 20,
            bottom: "21%",
            showDataShadow: false, //是否显示数据阴影 默认auto
            showDetail: false, //即拖拽时候是否显示详细数值信息 默认true
            height: 20,
            // 不显示控制手柄
            handleSize: 0,
            handleStyle: {
              borderColor: "#cacaca",
              borderWidth: "1",
              shadowBlur: 2,
              background: "#ddd",
              shadowColor: "#ddd",
            },
            realtime: false,
          },
          //下面这个属性是里面拖到
          {
            type: "inside",
            show: true,
            xAxisIndex: [0],
            startValue: 0, //默认为1
            endValue: 20,
            zoomLock: true,
          },
        ],
        xAxis: [
          {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: xData,
            axisLabel: {
              interval: 0,
              rotate: 60,
            },
          },
        ],
        yAxis: yA,
        series: ser,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-wrap {
  width: 100%;
  height: 650px;
}
</style>