<template>
  <!-- 图表展示 -->
  <div>
    <div class="chart-wrap" id="chartid"></div>
  </div>
</template>

<script>
import { uniquearr, sortBy } from "@admin/utils/index.js";
export default {
  name: "BarCharts",
  props: {
    data: {
      type: Array,
      required: true,
    },
    // 类型: 1 实验室 2 课程 4开课学院 8 教师
    type: {
      type: Number,
      default: 1,
    },
    queryFields: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      mEcharts: null,
    };
  },
  watch: {
    data: {
      handler(nv) {
        if (nv) {
          this.getOption(nv);
        }
      },
    },
  },
  mounted() {
    this.getOption(this.data);
  },
  beforeDestroy() {
    if (this.mEcharts) {
      this.mEcharts.dispose();
    }
  },
  methods: {
    init(option) {
      this.mEcharts && this.mEcharts.setOption(option);
    },
    getOption(data) {
      const colors = ["#5470C6", "#91CC75", "#EE6666", "#cc9975"];
      let testHourList = [],
        extracurricularTimeList = [],
        json = {
          civilYear: [],
          yearTerm: [],
          campus: [],
          room: [],
        };
      let yA = [
        {
          type: "value",
          name: "实验学时数",
          position: "left",
          alignTicks: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: colors[3],
            },
          },
          axisLabel: {
            formatter: "{value} ",
          },
        },
      ];
      let ser = [
        {
          name: "实验学时数",
          type: "bar",
          data: testHourList,
          itemStyle: {
            color: colors[0],
          },
        },
      ];
      let legendData = ["实验学时"];
      data.forEach((dt) => {
        this.queryFields.map((item, index) => {
          if (item === "civilYear") {
            json.civilYear.push(dt.civilYear);
          }
          if (item === "yearTerm") {
            json.yearTerm.push(dt.yearTermName);
          }
          if (item === "campus") {
            json.campus.push(dt.campusName);
          }
          if (item === "room") {
            json.room.push(dt.roomName);
          }
        });
        legendData = ["实验学时数"];
        extracurricularTimeList.push(dt.extracurricularTime);
        testHourList.push(dt.testHour);
        yA = [
          {
            type: "value",
            name: "实验学时数",
            position: "left",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[0],
              },
            },
            axisLabel: {
              formatter: "{value} ",
            },
          },
        ];
        ser = [
          {
            name: "实验学时数",
            type: "bar",
            data: testHourList,
            itemStyle: {
              color: colors[0],
            },
          },
        ];
      });
      let XData = [],
        arr = [],
        yearTermarr = [];
      if (json["yearTerm"].length != 0) {
        yearTermarr = uniquearr(json.yearTerm);
      }
      // console.log(yearTermarr);
      if (json["room"].length != 0) {
        json["room"].map((item, index) => {
          let string;
          yearTermarr.map((v, k) => {
            string = item + "-" + v;
            XData.push(string);
          });
        });
      }
      // this.queryFields.map((item, index) => {
      //   if (json[item].length != 0) {
      //     json["yearTerm"].map((v, k) => {
      //       json["room"].map((a, b) => {
      //         drr[v] = v + "-" + a;
      //       });
      //     });
      //   }
      // });
      // console.log(XData);
      // console.log(json);
      // 基于准备好的dom，初始化echarts实例
      let myChart = this.$echarts.init(document.getElementById("chartid"));
      // 绘制图表
      myChart.setOption({
        color: colors,
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        grid: {
          right: "10%",
          left: "5%",
          bottom: "20%",
        },
        toolbox: {
          feature: {
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        legend: {
          data: legendData,
        },
        dataZoom: [
          {
            type: "slider", //给x轴设置滚动条
            show: true, //flase直接隐藏图形
            xAxisIndex: [0],
            bottom: "10%",
            height: 20,
            showDetail: false,
            startValue: 0, //滚动条的起始位置
            endValue: 12, //滚动条的截止位置（按比例分割你的柱状图x轴长度）
          },
          {
            type: "inside", //设置鼠标滚轮缩放
            show: true,
            xAxisIndex: [0],
            startValue: 0,
            endValue: 9,
          },
        ],
        xAxis: [
          {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: XData,
            axisLabel: {
              interval: 0,
              rotate: 30,
            },
            // name: xName,
          },
        ],
        yAxis: yA,
        series: ser,
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-wrap {
  width: 100%;
  height: 650px;
}
</style>
