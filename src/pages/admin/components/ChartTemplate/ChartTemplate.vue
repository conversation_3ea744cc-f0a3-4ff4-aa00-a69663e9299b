<!--
  实验室、课程、开课学院、教师统计图表
-->
<template>
  <div class="rank">
    <div class="search">
      <el-form :model="searchInfo" width="100%" label-width="100px" class="itemBox">
        <el-row :gutter="20" v-if="chartType == 1">
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="实验室编号" prop="roomSn">
              <el-input
                v-model="searchInfo.roomSn"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="实验室名称" prop="devSn">
              <el-input
                v-model="searchInfo.roomName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item :label="'所属' + ExpCenterName" prop="devSn">
              <el-input
                v-model="searchInfo.roomKindName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="12" :lg="8" :xl="6">
            <el-form-item label="日期">
              <el-date-picker
                v-model="searchInfo.date"
                type="daterange"

                style="width: 100%"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :clearable="true"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-else-if="chartType == 2">
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="课程编号" prop="courseSn">
              <el-input
                v-model="searchInfo.courseSn"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="课程名称" prop="courseName">
              <el-input
                v-model="searchInfo.courseName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="开课学院" prop="deptName">
              <el-input
                v-model="searchInfo.deptName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="12" :lg="8" :xl="6">
            <el-form-item label="日期">
              <el-date-picker
                v-model="searchInfo.date"
                type="daterange"

                style="width: 100%"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :clearable="true"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-else-if="chartType == 4">
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="学院编号" prop="deptSn">
              <el-input
                v-model="searchInfo.deptSn"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="学院名称" prop="deptName">
              <el-input
                v-model="searchInfo.deptName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-else-if="chartType == 8">
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="教师工号" prop="logonName">
              <el-input
                v-model="searchInfo.logonName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="教师姓名" prop="trueName">
              <el-input
                v-model="searchInfo.trueName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
            <el-form-item label="教师所属学院" prop="deptName">
              <el-input
                v-model="searchInfo.deptName"

                clearable
                placeholder="请输入"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="text-align: center">
          <el-button
            type="primary"
            icon="el-icon-search"

            class="smallbtn"
            @click="handleSearch"
            >搜索</el-button
          >
        </el-row>
      </el-form>
    </div>

    <!-- 结果区域 -->
    <div class="res-con">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChartTemplate",
  components: {},
  props: {
    // 哪个的图表 1 实验室 2 课程 4 开课学院 8 教师
    chartType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      searchInfo: {
        date: [],
        roomSn: "",
        roomName: "",
        roomKindName: "",
        // 课程编号
        courseSn: "",
        // 课程名称
        courseName: "",
        // 开课学院
        deptName: "",
        // 学院编号
        deptSn: "",
        // 学院名称
        deptName: "",
        // 教师工号
        logonName: "",
        // 教师姓名
        trueName: "",
        // 教师所属学院
        deptName: "",
      },
      pageInfo: {
        page: -1, // 当前页
        pageNum: 1, // 每页数量
      },
      // 实验中心名称个性化
      ExpCenterName: window.g.ExpCenterName,
    };
  },
  computed: {},
  created() {},
  watch: {},
  methods: {
    handleSearch() {
      // 搜索
      this.$emit("search", this.searchInfo, this.pageInfo);
    },
  },
};
</script>

<style scoped>
.el-date-editor {
  width: 100%;
}
</style>
