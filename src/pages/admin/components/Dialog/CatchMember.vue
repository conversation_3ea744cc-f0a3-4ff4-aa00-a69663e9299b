<template>
  <div>
    <el-dialog
      :title="catchmemberTitle"
      :visible.sync="catchmemberShow"
      append-to-body
      :close-on-click-modal="false"
      :before-close="handleclose_catchmember"
      width="1200px"
      top="0%"
    >
      <el-form label-width="110px">
        <el-row>
          <el-col :span="8">
            <el-form-item :label="AccountName" label-width="100px" prop="accNo">
              <el-input
                v-model="accNo"
                placeholder="请输入工号或者姓名关键词查询选择"
                clearable

              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button
              type="primary"
              icon="el-icon-search"

              class="smallbtn"
              style="margin-left: 20px"
              @click="searchMember"
              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <div>
        <el-dropdown
          trigger="click"
          class="import"
          szie="small"
          @command="handleClassMember"
          v-allow="'schoolClassMember:save'"
        >
          <span class="el-dropdown-link">
            <i class="el-icon-plus icon"></i>
            添加成员
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="singlemember">单个添加成员</el-dropdown-item>
            <el-dropdown-item command="importmember">
              <el-upload
                action
                ref="upload"
                class="upload-demo fl"
                accept=".xls,.xlsx"
                :http-request="sumbitUpload"
                :show-file-list="false"
                :before-upload="beforeUpload"
              >
                <el-button type="text" style="color: #606266; font-size: 14px"
                  >导入成员表</el-button
                >
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item command="classmemadd">添加班级成员</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <a href="./template_member.xlsx" style="margin-left: 20px">成员表模板下载</a>
      </div>
      <el-table
        :data="MemberTable"
        stripe
        border
        style="widht: 100%; margin-top: 2%"
        class="marginTop"
        v-loading="catchmemberloading"
      >
        <el-table-column
          type="index"
          width="65px"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column prop="trueName" label="姓名" align="center"></el-table-column>
        <el-table-column prop="logonName" label="学工号" align="center"></el-table-column>
        <el-table-column prop="className" label="班级" align="center"></el-table-column>
        <el-table-column prop="deptName" label="学院" align="center"></el-table-column>
        <el-table-column prop="handPhone" label="手机" align="center"></el-table-column>
        <el-table-column prop="email" label="邮箱" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-delete"
              style="color: red"
              @click="delMember(scope.row)"
              v-allow="'schoolClassMember:delete'"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页开始 -->
      <div class="paging">
        <Paging :pageCount="membercount" @pagebar="changeMember"></Paging>
      </div>
      <!-- 分页结束 -->
    </el-dialog>
    <!-- 单个添加成员弹窗开始 -->
    <AddSinglemember
      @getschoolClassMembers="getschoolClassMembers"
      @handleclose="handleclose"
      :catchsinglememberShow="catchsinglememberShow"
      :MemberTable="MemberTable"
      :classId="classId"
    >
    </AddSinglemember>
    <!-- 单个添加成员弹窗结束 -->
    <!-- 添加班级成员弹窗开始 -->
    <Classmember
      :addclassmemberShow="addclassmemberShow"
      :classId="classId"
      @handleclose="handleclose_class"
      @getschoolClassMembers="getschoolClassMembers"
    ></Classmember>
    <!-- 添加班级成员弹窗结束 -->
    <!-- 导入课程班失败结果-->
    <el-dialog
      title="导入失败数据"
      :visible.sync="failimportshow"
      append-to-body
      :close-on-click-modal="false"
      width="600px"
    >
      <el-table :data="FailTable" stripe border style="width: 100%" class="marginTop">
        <el-table-column
          type="index"
          width="65px"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column prop="logonName" label="学工号" align="center"></el-table-column>
        <el-table-column prop="trueName" label="姓名" align="center"></el-table-column>
        <el-table-column
          prop="errorMsg"
          label="失败原因"
          align="center"
        ></el-table-column>
      </el-table>
    </el-dialog>
    <!-- 导入课程班失败结果-->
  </div>
</template>
<script>
import {
  getschoolClassMember,
  delschoolClassMember,
  importMember,
} from "@admin/api/Dailymanagement/program";
import { getAccClass } from "@admin/api/Settings/User/Class.js";
import { getAccount } from "@admin/api/Settings/User/Account.js";
import Paging from "@admin/components/pagebar/Paging.vue";
import AddSinglemember from "@admin/components/Dialog/Components/AddSinglemember.vue";
import Classmember from "@admin/components/Dialog/Components/Classmember.vue";
export default {
  data() {
    return {
      AccountName: window.g.AccountName,
      failimportshow: false,
      FailTable: [],
      catchsinglememberShow: false,
      MemberTable: [],
      catchmemberloading: false,
      accNo: "",
      MemberpageData: {
        page: 10,
        pageNum: 1,
      },
      membercount: 0,
      pageCount_addclassmember: 0,
      singlemember_loading: false,
      addclassmemberShow: false,
      selectaddclassmembers: [],
      addclassmember_tabledata: [],
      pageCount_singlemember: 0,
      currentpage_singlemember: 0,
      pageData_singlemember: {
        page: 10,
        pageNum: 1,
      },
      pageData_addclassmember: {
        page: 10,
        pageNum: 1,
      },
    };
  },
  props: ["catchmemberShow", "classId", "yearTermId", "catchmemberTitle"],
  methods: {
    handleclose_catchmember(done) {
      this.$emit("handleclose_catchmember", false);
      done(true);
    },
    handleclose(data) {
      this.catchsinglememberShow = data;
    },
    handleclose_class(data) {
      this.addclassmemberShow = data;
    },
    //输入学工号查询班级名称
    searchMember() {
      this.MemberpageData.pageNum = 1;
      this.getschoolClassMembers();
    },
    //查看班级成员
    getschoolClassMembers() {
      this.catchmemberloading = true;
      getschoolClassMember(
        this.classId,
        this.accNo,
        this.MemberpageData.pageNum,
        this.MemberpageData.page
      ).then((res) => {
        this.catchmemberloading = false;
        if (res.code == 0) {
          this.MemberTable = res.data;
          console.log(this.MemberTable);
          this.membercount = res.count;
        }
      });
    },
    changeMember(pageData) {
      this.MemberpageData = pageData;
      // 页面刷新
      this.getschoolClassMembers();
    },
    //删除班级成员
    delMember(row) {
      this.$confirm("此操作将永久删除该成员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delschoolClassMember(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.getschoolClassMembers();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //查看成员弹窗的选择下拉框
    handleClassMember(command) {
      if (command == "singlemember") {
        this.catchsinglememberShow = true;
        // this.singlememberdata();
      } else if (command == "importmember") {
      } else if (command == "classmemadd") {
        this.addclassmemberShow = true;
        this.selectaddclassmembers = [];
        this.addclassmemberdata();
        this.$refs.addclassmemberTable.clearSelection();
      }
    },
    // 单个添加成员-表格初始数据
    singlememberdata() {
      this.singlemember_loading = true;
      if (this.selectaccNo == "") {
        this.selectaccNo = this.singlemember_searchForm.accNo;
      }
      getAccount(
        this.pageData_singlemember.pageNum,
        this.pageData_singlemember.page,
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        this.selectaccNo
      ).then((res) => {
        this.singlemember_loading = false;
        this.selectaccNo = "";
        if (res.code == 0) {
          res.data.map((item, index) => {
            res.data[index].isadd = false;
            this.MemberTable.map((v, k) => {
              if (item.accNo == v.accNo) {
                res.data[index].isadd = true;
              }
            });
          });
          this.singlemember_tabledata = res.data;
          this.pageCount_singlemember = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 班级添加成员-表格初始化
    addclassmemberdata() {
      this.addclassmember_loading = true;
      getAccClass(
        this.pageData_addclassmember.pageNum,
        this.pageData_addclassmember.page,
        this.addclassmember_Form.classId,
        "",
        "",
        ""
      ).then((res) => {
        this.addclassmember_loading = false;
        if (res.code == 0) {
          this.addclassmember_tabledata = res.data;
          this.pageCount_addclassmember = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //上传文件
    sumbitUpload(item) {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let fd = new FormData();
      fd.append("file", item.file);
      fd.append("yearTermId", this.yearTermId);
      fd.append("schoolClassId", this.classId);
      importMember(fd)
        .then((res) => {
          loading.close();
          if (res.code == 0) {
            this.$message.success("上传成功");
            this.yearTermshow = false;
            this.addmemberShow = false;
            this.yearTermId = "";
            this.initTable();
          } else {
            this.$message({
              message: "上传失败",
              type: "error",
              duration: "5000",
            });
            this.failimportshow = true;
            this.yearTermshow = false;
            this.addmemberShow = false;
            this.yearTermId = "";
            if (res.data.failList) {
              res.data.failList.map((item) => {
                item.schoolClassName = this.classname;
              });
              this.FailTable = res.data.failList;
            }
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    //上传文件类型限制
    beforeUpload(file) {
      // const isLt40M = file.size / 1024 / 1024 < 40;
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "xls";
      const extension2 = testmsg === "xlsx";
      if (!extension && !extension2) {
        this.$message({
          message: "导入表格类型只能是xls或者xlsx！",
          type: "error",
          duration: "5000",
        });
        return false;
      }
    },
  },
  created() {
    // console.log(this.addclassmemberShow)
    // this.getschoolClassMembers();
  },
  components: {
    Paging,
    AddSinglemember,
    Classmember,
  },
  watch: {
    catchmemberShow: {
      handler(v) {
        this.catchsinglememberShow = false;
        if (v) {
          this.getschoolClassMembers();
        }
      },
    },
  },
};
</script>
