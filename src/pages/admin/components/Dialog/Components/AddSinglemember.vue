<template>
  <el-dialog
    title="单个添加成员"
    :visible.sync="catchsinglememberShow"
    append-to-body
    :close-on-click-modal="false"
    :before-close="handleclose"
    top="0%"
    width="1200px"
  >
    <div class="search" style="padding: 15px">
      <el-form
        :model="singlemember_searchForm"
        width="100%"
        label-width="70px"
        class="itemBox"
      >
        <el-row :gutter="10">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <el-form-item :label="AccountName" label-width="110px">
              <el-autocomplete
                class="inline-input"
                v-model="singlemember_searchForm.accNo"
                :fetch-suggestions="accNoquerySearch"
                clearable

                placeholder="请输入教师工号或者姓名"
                style="width: 100%"
                @change="HandleaccNo"
                @select="HandleselectaccNo"
              ></el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="singlemember_searchBtn"
              class="smallbtn"

              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 表格开始 -->
    <el-table
      :data="singlemember_tabledata"
      stripe
      border
      style="width: 100%"
      class="marginTop"
      v-loading="singlemember_loading"
    >
      <el-table-column prop="logonName" label="登录名" align="center"></el-table-column>
      <el-table-column prop="trueName" label="姓名" align="center"></el-table-column>
      <el-table-column
        prop="kind"
        label="类型"
        :formatter="kindFormat"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="className"
        :label="classname + '名称'"
        align="center"
      ></el-table-column>
      <el-table-column prop="deptName" label="学院名称" align="center"></el-table-column>
      <el-table-column
        prop="ident"
        label="身份"
        :formatter="identFormat"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="sex"
        label="性别"
        :formatter="sexFormat"
        align="center"
      ></el-table-column>
      <el-table-column prop="handPhone" label="手机" align="center"></el-table-column>
      <el-table-column prop="email" label="邮箱" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-user"
            @click="singlemember_editBtn(scope.row)"
            v-if="!scope.row.isadd"
            >添加</el-button
          >
          <el-button type="text" icon="el-icon-user" disabled v-if="scope.row.isadd"
            >已添加</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页开始-->
    <div class="paging">
      <Paging
        :pageCount="pageCount_singlemember"
        @pagebar="changePage_singlemember"
        :currentpage="currentpage_singlemember"
      ></Paging>
    </div>
    <!-- 分页结束-->
  </el-dialog>
</template>
<script>
import {
  addschoolClassMember,
  getschoolClassMember,
} from "@admin/api/Dailymanagement/program";
import { getAccNo } from "@admin/api/Management/administrator";
import { getAccount } from "@admin/api/Settings/User/Account.js";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      AccountName: window.g.AccountName,
      singlemember_searchForm: {
        accNo: "",
      },
      singlemember_tabledata: [],
      singlemember_loading: false,
      pageData_singlemember: {
        page: 10,
        pageNum: 1,
      },
      //存放当前需要导入成员表的班级
      classname: "",
      pageCount_singlemember: "",
      currentpage_singlemember: "",
      addForm: {
        accNo: "",
        schoolClassId: "",
      }, //添加新成员
      accNo: "",
      MemberTable: [],
    };
  },
  props: ["catchsinglememberShow", "classId"],
  methods: {
    handleclose(done) {
      this.$emit("handleclose", false);
      this.$emit("getschoolClassMembers", true);
      done(true);
    },
    accNoquerySearch(queryString, cb) {
      let arr = [];
      getAccNo(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.accNoTrueName,
              value: item.accNoTrueName,
            });
          });
          cb(arr);
        }
      });
    },
    // 单个添加成员-表格初始数据
    singlememberdata() {
      this.singlemember_loading = true;
      if (this.selectaccNo == "") {
        this.selectaccNo = this.singlemember_searchForm.accNo;
      }
      getAccount(
        this.pageData_singlemember.pageNum,
        this.pageData_singlemember.page,
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        this.selectaccNo
      ).then((res) => {
        this.singlemember_loading = false;
        this.selectaccNo = "";
        if (res.code == 0) {
          res.data.map((item, index) => {
            res.data[index].isadd = false;
            this.MemberTable.map((v, k) => {
              if (item.accNo == v.accNo) {
                res.data[index].isadd = true;
              }
            });
          });
          this.singlemember_tabledata = res.data;
          this.pageCount_singlemember = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //单个添加成员弹窗-分页
    changePage_singlemember(pageData) {
      this.pageData_singlemember = pageData;
      this.currentpage_singlemember = pageData.pageNum;
      // 页面刷新
      this.singlememberdata();
    },
    singlemember_editBtn(data) {
      this.$confirm("此操作将添加该成员, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.addForm.accNo = data.accNo;
          this.addForm.schoolClassId = this.classId;
          console.log(this.addForm);
          this.addMemberbtn();
        })
        .catch(() => {
          this.addForm.accNo = data.accNo;
          this.addForm.schoolClassId = this.classId;
          console.log(this.addForm);
          this.addMemberbtn();
          // this.$message({
          //     type: "info",
          //     message: "已取消添加",
          // });
        });
    },
    //添加成员弹窗的新增按钮
    addMemberbtn() {
      addschoolClassMember(this.addForm).then((res) => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.getschoolClassMembers();
          // this.$emit("getschoolClassMembers",true);
          setTimeout(() => {
            this.singlememberdata();
          }, 2000);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    getschoolClassMembers() {
      // this.catchmemberloading = true;
      getschoolClassMember(this.classId, this.accNo, 1, 1000).then((res) => {
        // this.catchmemberloading = false;
        if (res.code == 0) {
          this.MemberTable = res.data;
          console.log(this.MemberTable);
          // this.membercount = res.count;
        }
      });
    },
    singlemember_searchBtn() {
      addschoolClassMember(this.addForm).then((res) => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.getschoolClassMembers();
          // this.$emit("getschoolClassMembers",true);
          setTimeout(() => {
            this.singlememberdata();
          }, 2000);
        } else {
          this.$message.error(res.message);
        }
      });
    },
    HandleaccNo(data) {
      if (this.singlemember_searchForm.accNo == "") {
        this.singlemember_searchForm.accNo = data;
      }
    },
    HandleselectaccNo(data) {
      let text = data.label;
      this.selectaccNo = text.split("(")[1].split(")")[0];
    },
    singlemember_searchBtn() {
      this.singlememberdata();
    },
    sortChange() {
      this.singlememberdata();
    },
    // 表格 kind 数据格式
    kindFormat(row, col) {
      if (row.kind == 1) {
        return "一卡通账户";
      } else if (row.kind == 2) {
        return "本地账户";
      } else {
        return "未知";
      }
    },
    // 表格 身份数据格式化
    identFormat(row, col) {
      var str = "";
      switch (row.ident) {
        case 256:
          str = "学生";
          break;
        case 512:
          str = "教师";
          break;
      }
      return str;
    },
    // 表格 性别数据格式化
    sexFormat(row, col) {
      var str = "";
      switch (row.sex) {
        case 1:
          str = "男";
          break;
        case 2:
          str = "女";
          break;
        case 3:
          str = "保密";
          break;
      }
      return str;
    },
  },
  created() {},
  components: {
    Paging,
  },
  watch: {
    catchsinglememberShow: {
      handler(v) {
        if (v) {
          console.log(v);
          // this.catchsinglememberShow = true;
          this.getschoolClassMembers();
          setTimeout(() => {
            this.singlememberdata();
          }, 2000);
        }
      },
    },
  },
};
</script>
