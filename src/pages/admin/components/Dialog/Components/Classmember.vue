<template>
    <el-dialog title="添加班级成员" :visible.sync="addclassmemberShow" append-to-body :close-on-click-modal="false" width="1200px" top="0%" :before-close="handleclose">
        <div class="search" style="padding: 15px">
            <el-form :model="addclassmember_Form" width="100%" class="itemBox">
                <el-row :gutter="10">
                    <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
                        <el-form-item label="班级" label-width="60px">
                            <el-autocomplete class="inline-input" v-model="addclassmember_Form.classId"
                                :fetch-suggestions="ClassquerySearch" clearable
                                :placeholder="'请输入' + classname + '名称'" style="width: 100%"></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
                        <el-button type="primary" icon="el-icon-search" @click="addclassmember_searchBtn"
                            class="smallbtn"  style="margin-left: 20px">搜索</el-button>
                    </el-col>
                </el-row>
                <el-row>
                    <el-button type="primary" icon="el-icon-plus" @click="addclassmember_manyaddBtn" class="smallbtn"
                        >批量添加</el-button>
                </el-row>
            </el-form>
        </div>
        <!-- 表格开始 -->
        <el-table :data="addclassmember_tabledata" stripe border style="width: 100%" class="marginTop"
            v-loading="addclassmember_loading" @selection-change="handleSelectionChange" :row-key="getRowKeys"
            ref="addclassmemberTable">
            <el-table-column type="selection" min-width="50px" align="center"
                :reserve-selection="true"></el-table-column>
            <el-table-column sortable="custom" prop="classSn" label="班级编号" align="center"></el-table-column>
            <el-table-column prop="className" :label="classname + '名称'" align="center"></el-table-column>
            <el-table-column prop="deptName" label="学院名称" align="center"></el-table-column>
            <el-table-column prop="classKind" label="班级类型" align="center">
                <template slot-scope="scope">
                    <span v-if="scope.row.classKind == '1'">一卡通</span>
                    <span v-if="scope.row.classKind == '2'">本地</span>
                </template>
            </el-table-column>
            <el-table-column prop="memo" label="备注" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-plus" @click="addclassmember_editBtn(scope.row)">添加</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页开始-->
        <div class="paging">
            <Paging :pageCount="pageCount_addclassmember" @pagebar="changePage_addclassmember"
                :currentpage="currentpage_addclassmember"></Paging>
        </div>
        <!-- 分页结束-->
    </el-dialog>
</template>
<script>
import { saveschoolClassMember } from "@admin/api/Dailymanagement/program.js";
import { getAllClass, getAccClass } from "@admin/api/Settings/User/Class.js";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
    data() {
        return {
            pageCount_addclassmember: 0,
            addclassmember_loading: false,
            addclassmember_tabledata: [],
            addclassmember_Form: {
                classId: ""
            },
            classname: "",
            pageData_addclassmember: {
                page: 10,
                pageNum: 1,
            },
            addclassmember_Form: {
                classId: "",
            },
            selectaddclassmembers: [],
            currentpage_addclassmember: ""
        }
    },
    props: ['addclassmemberShow', 'classId'],
    methods: {
        handleclose(done) {
            this.$emit("handleclose", false);
            this.$emit("getschoolClassMembers", true);
            done(true);
        },
        changePage_addclassmember(pageData) {
            this.pageData_addclassmember = pageData;
            this.currentpage_addclassmember = pageData.pageNum;
            // 页面刷新
            this.addclassmemberdata();
        },
        //班级添加成员-添加
        addclassmember_editBtn(data) {
            this.$confirm("此操作将添加该班级, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let arr = [];
                    arr.push(data.classId);
                    let json = {
                        schoolClassId: this.classId,
                        classIds: arr,
                    };
                    saveschoolClassMember(json).then((res) => {
                        if (res.code == 0) {
                            // this.CatchMember();
                            this.$emit("getschoolClassMembers",true)
                            this.addclassmemberShow = false;
                            this.$message.success(res.message);
                        } else {
                            this.$message({
                                message: res.message,
                                type: "error",
                                duration: "5000",
                            });
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消添加",
                    });
                });
        },
        handleSelectionChange(val) {
            this.selectaddclassmembers = [];
            val.map((item, index) => {
                this.selectaddclassmembers.push(item.classId);
            });
        },
        //查看成员
        CatchMember(row) {
            this.catchmemberShow = true;
            if (row) {
                this.classId = row.schoolClassId;
                this.classname = row.schoolClassName;
                this.yearTermId = row.yearTermId;
            }
            this.accNo = "";
            // this.getschoolClassMembers();
        },
        //班级添加成员-批量添加
        addclassmember_manyaddBtn() {
            if (this.selectaddclassmembers.length == 0) {
                this.$message.error("请勾选班级");
            } else {
                let json = {
                    schoolClassId: this.classId,
                    classIds: this.selectaddclassmembers,
                };
                saveschoolClassMember(json).then((res) => {
                    this.selectaddclassmembers = [];
                    this.$refs.addclassmemberTable.clearSelection();
                    if (res.code == 0) {
                        this.$emit("getschoolClassMembers",true)
                        this.addclassmemberShow = false;
                        this.$message.success(res.message);
                    } else {
                        this.$message({
                            message: res.message,
                            type: "error",
                            duration: "5000",
                        });
                    }
                });
            }
        },
        // 班级添加成员-搜索
        addclassmember_searchBtn() {
            this.addclassmemberdata();
        },
        //班级下拉查询
        ClassquerySearch(queryString, cb) {
            let arr = [];
            getAllClass(queryString, 20).then((res) => {
                if (res.code == 0) {
                    res.data.forEach((item) => {
                        arr.push({
                            label: item.classId,
                            value: item.className,
                        });
                    });
                    cb(arr);
                }
            });
        },
        // 班级添加成员-表格初始化
        addclassmemberdata() {
            this.addclassmember_loading = true;
            getAccClass(
                this.pageData_addclassmember.pageNum,
                this.pageData_addclassmember.page,
                this.addclassmember_Form.classId,
                "",
                "",
                ""
            ).then((res) => {
                this.addclassmember_loading = false;
                if (res.code == 0) {
                    this.addclassmember_tabledata = res.data;
                    this.pageCount_addclassmember = res.count;
                } else {
                    this.$message({
                        message: res.message,
                        type: "error",
                        duration: "5000",
                    });
                }
            });
        },
        getRowKeys(row) {
            return row.uuid;
        },
    },
    components: {
        Paging
    },
    watch: {
        addclassmemberShow: {
            handler(v) {
                if (v) {
                    this.addclassmemberdata();
                    this.selectaddclassmembers = [];
                    this.$refs.addclassmemberTable.clearSelection();
                }
            }
        }
    }
}
</script>