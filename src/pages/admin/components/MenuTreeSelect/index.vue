<template>
  <div class="menu-tree-select" @click="visible = true">
    <el-popover placement="bottom-start" trigger="click" :width="popoverWidth" v-model="visible">
      <div v-if="showTopLevelOption" class="top-level-option" :class="{ 'is-selected': value === 0 }" @click="selectTopLevel">
        <span class="top-level-label">顶级菜单</span>
        <el-tag type="success" size="mini" style="margin-left: 5px">导航菜单</el-tag>
      </div>
      <el-tree
        ref="menuTree"
        :data="menuTreeData"
        :props="treeProps"
        node-key="menuId"
        :highlight-current="true"
        :expand-on-click-node="false"
        :default-expand-all="defaultExpandAll"
        :check-strictly="true"
        @node-click="handleMenuNodeClick"
        :render-content="renderTreeNode"
      ></el-tree>
      <div slot="reference" class="el-input el-input--small" style="width: 100%">
        <el-input :placeholder="placeholder" readonly :disabled="disabled" :value="selectedName" />
        <span class="el-input__suffix" v-if="clearable && selectedName">
          <span class="el-input__suffix-inner">
            <i class="el-input__icon el-icon-circle-close el-input__clear" @click.stop="clear"></i>
          </span>
        </span>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { menuTree } from "@admin/api/Dailymanagement/menu";

export default {
  name: "MenuTreeSelect",
  props: {
    // 可选择的节点类型，传入一个数字或数组
    selectableKind: {
      type: [Number, Array],
      default: 1,
    },
    // 自定义节点标签
    kindLabels: {
      type: Object,
      default: () => ({
        5: {
          text: "下载菜单",
        },
        4: {
          text: "文章列表",
        },
        3: {
          text: "富文本",
        },
        2: {
          text: "超链接",
        },
        1: {
          text: "导航菜单",
        },
      }),
    },
    // 选中的值
    value: {
      type: [String, Number],
      default: "",
    },
    // 选中的名称
    selectedNodeName: {
      type: String,
      default: "",
    },
    // 占位符
    placeholder: {
      type: String,
      default: "请选择菜单",
    },
    // 输入框大小
    size: {
      type: String,
      default: "small",
    },
    // 弹出框宽度
    popoverWidth: {
      type: Number,
      default: 320,
    },
    // 是否默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    // 提示文本
    notSelectableTip: {
      type: String,
      default: "只能选择指定类型的菜单节点",
    },
    // 是否显示清除按钮
    clearable: {
      type: Boolean,
      default: true,
    },
    // 是否显示顶级菜单选项在树的顶部
    showTopLevelOption: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      menuTreeData: [],
      treeProps: {
        label: "menuName",
        children: "children",
      },
      selectedName: this.value === 0 ? "顶级菜单" : this.selectedNodeName,
    };
  },
  watch: {
    selectedNodeName(val) {
      if (this.value !== 0) {
        this.selectedName = val;
      }
    },
    value(val) {
      // 如果选择了顶级菜单
      if (val === 0) {
        this.selectedName = "顶级菜单";
        if (this.$refs.menuTree) {
          this.$refs.menuTree.setCurrentKey(null);
        }
        return;
      }

      // 如果外部传入的value变化，重新设置当前选中的节点
      this.$nextTick(() => {
        if (this.$refs.menuTree && val) {
          this.$refs.menuTree.setCurrentKey(val);
        }
      });
    },
  },
  created() {
    this.getMenuTreeData();

    // 初始化时，如果value为0，设置为顶级菜单
    if (this.value === 0) {
      this.selectedName = "顶级菜单";
    }
  },
  methods: {
    // 获取菜单树数据
    getMenuTreeData() {
      menuTree().then((res) => {
        if (res.code === 0) {
          // 过滤并处理菜单树，构建包含所有层级但只有最终层级为指定kind的菜单树
          const filterMenuTree = (menus) => {
            if (!menus || !menus.length) return [];

            const selectableKinds = Array.isArray(this.selectableKind) ? this.selectableKind : [this.selectableKind];

            return menus
              .filter((menu) => {
                // 保留指定kind的最终节点或者有子节点的菜单
                return selectableKinds.includes(menu.kind) || (menu.children && menu.children.length > 0);
              })
              .map((menu) => {
                const newMenu = { ...menu };
                if (menu.children && menu.children.length > 0) {
                  newMenu.children = filterMenuTree(menu.children);
                  // 如果过滤后没有子节点，且当前节点不是可选择的kind，则不保留该节点
                  if (newMenu.children.length === 0 && !selectableKinds.includes(menu.kind)) {
                    return null;
                  }
                }
                return newMenu;
              })
              .filter(Boolean); // 移除空节点
          };

          this.menuTreeData = filterMenuTree(res.data);
        }
      });
    },

    // 自定义节点渲染
    renderTreeNode(h, { node }) {
      const selectableKinds = Array.isArray(this.selectableKind) ? this.selectableKind : [this.selectableKind];

      const isSelectable = selectableKinds.includes(node.data.kind);
      const className = isSelectable ? "default" : "default not-selectable";

      // 添加提示信息
      const tooltip = isSelectable ? "" : this.notSelectableTip;

      // 获取节点标签信息
      let kindInfo = this.kindLabels[node.data.kind] || {
        text: `菜单类型${node.data.kind}`,
        type: "info",
      };

      // 根据节点是否可选择来动态确定标签类型
      kindInfo = {
        ...kindInfo,
        type: isSelectable ? "success" : "info",
      };

      const content = (
        <span class={className} title={tooltip}>
          <span>{node.label}</span>
          <el-tag type={kindInfo.type} size="mini" style="margin-left: 5px">
            {kindInfo.text}
          </el-tag>
        </span>
      );
      return content;
    },

    // 菜单树相关方法
    handleMenuNodeClick(node) {
      const selectableKinds = Array.isArray(this.selectableKind) ? this.selectableKind : [this.selectableKind];

      if (selectableKinds.includes(node.kind)) {
        this.selectedName = node.label || node.menuName;
        this.visible = false;

        // 设置当前选中节点
        this.$nextTick(() => {
          if (this.$refs.menuTree) {
            this.$refs.menuTree.setCurrentKey(node.menuId);
          }
        });

        // 向父组件传递选中的值和名称
        this.$emit("input", node.menuId);
        this.$emit("update:selectedNodeName", this.selectedName);
        this.$emit("select", node);
      } else {
        this.$notify.warning({
          title: "警告",
          message: this.notSelectableTip,
        });
      }
    },

    // 选择顶级菜单
    selectTopLevel() {
      this.selectedName = "顶级菜单";
      this.visible = false;

      // 清除树中的选中状态
      if (this.$refs.menuTree) {
        this.$refs.menuTree.setCurrentKey(null);
      }

      // 向父组件传递选中的值和名称
      this.$emit("input", 0);
      this.$emit("update:selectedNodeName", this.selectedName);
      this.$emit("select", { menuId: 0, menuName: "顶级菜单", kind: 1 });
    },

    // 清空选择
    clear() {
      this.selectedName = "";
      this.$emit("input", "");
      this.$emit("update:selectedNodeName", "");
      if (this.$refs.menuTree) {
        this.$refs.menuTree.setCurrentKey(null);
      }
    },
  },
};
</script>

<style scoped lang="less">
// 菜单树样式
/deep/ .el-tree {
  max-height: 300px;
  overflow-y: auto;
}
/deep/ .el-tree-node__content {
  height: 32px;
}
/deep/ .el-tree-node__label {
  font-size: 14px;
}
/deep/ .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f1f7fe;
  color: #409eff;
}
/deep/ .default {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 10px;
}
/deep/ .not-selectable {
  color: #999;
  cursor: not-allowed;
}

// 顶级菜单选项样式
.top-level-option {
  width: 100%;
  padding: 6px 10px 6px 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    background-color: #f5f7fa;
  }

  &.is-selected {
    background-color: #f1f7fe;
    color: #409eff;
  }

  .top-level-label {
    font-size: 14px;
  }
}
</style>
