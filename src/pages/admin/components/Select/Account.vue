<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    placeholder="请输入工号或者姓名关键词查询选择"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getAccNo } from "@admin/api/Management/administrator.js";
export default {
  data() {
    return {
      name: "",
    };
  },
  props: ["accNo"],
  created() {},
  methods: {
    handleSelect(data) {
      this.$emit("getName", data.value);
      this.$emit("getlogonName", data.logonName);
      this.$emit("getaccNo", data.accNo);
    },
    handleChange(data) {
      this.$emit("getName", data.value);
      this.$emit("getlogonName", data.logonName);
      this.$emit("getaccNo", data.accNo);
    },
    handleClear() {
      this.$emit("getName", "");
      this.$emit("getlogonName", "");
      this.$emit("getaccNo", "");
    },
    handleBlur() {
      //   this.selectname = name;
      //   this.$emit("getName", this.selectname);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getAccNo(queryString, 500).then((res) => {
        if (res.code === 0) {
          res.data.forEach((item) => {
            arr.push({
              value: item.accNoTrueName,
              logonName: item.logonName,
              accNo: item.accNo,
            });
          });
          cb(arr);
        }
      });
    },
  },
};
</script>

<style></style>
