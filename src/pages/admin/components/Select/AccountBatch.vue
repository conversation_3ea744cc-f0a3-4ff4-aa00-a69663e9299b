<template>
  <el-select
    class="inline-input"
    v-model="name"
    :remote-method="querySearch"
    clearable
    placeholder="请输入工号或者姓名关键词查询选择"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    filterable
    style="width: 100%"
    multiple
    remote
    :loading="loading"
  >
    <el-option
      v-for="item in options"
      :key="item.uuid"
      :label="item.accNoTrueName"
      :value="item.accNo"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getAccNo } from "@admin/api/Management/administrator.js";
export default {
  data() {
    return {
      name: [],
      options: [],
      selectname: "",
      loading: false,
    };
  },
  props: ["accNo","close"],
  created() {},
  methods: {
    handleChange(data) {
      this.$emit("getaccNo", data);
    },
    handleClear() {
      this.$emit("getaccNo", "");
    },
    handleBlur() {
      // this.querySearch("");
      //   this.selectname = name;
      this.$emit("getName", this.name);
    },
    querySearch(queryString) {
      this.loading = true;
      getAccNo(queryString, 500).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.options = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
  watch: {
    accNo: {
      handler(val) {
        // this.name = val;
      },
      immediate: true,
    },
    close: {
      handler(val) {
        console.log(val);
        this.name = [];
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
