<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    placeholder="请输入楼宇名称关键词查询选择"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getAllbuilding } from "@admin/api/Settings/Building.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
      selectTruename: "",
      options: [],
      deptname: window.g.DeptName,
    };
  },
  props: ["getdeptid"],
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label;
      this.selectTruename = data.value;
      this.$emit("getbuildingName", this.selectname);
      this.$emit("getbuildingTrueName", data.value);
      this.$emit("getbuildingUuid", data.uuid);
      this.$emit("getbuildingId", data.label);
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      if (this.selectTruename == "") {
        this.selectTruename = data;
      }
      this.$emit("getbuildingName", this.selectname);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getbuildingName", this.selectname);
      this.$emit("getbuildingTrueName", "");
    },
    handleBlur() {
      this.selectTruename = this.name;
      this.$emit("getbuildingTrueName", this.selectTruename);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getAllbuilding(queryString, 500).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.buildingId,
              value: item.buildingName,
              uuid: item.uuid,
            });
            this.options.push({
              label: item.buildingId,
              value: item.buildingName,
              uuid: item.uuid,
            });
          });
          cb(arr);
        }
      });
    },
  },
  watch: {
    getdeptid: {
      handler(val) {
        console.log(val);
        this.querySearch("");
        var arr = [];
        setTimeout(() => {
          this.options.map((item, index) => {
            if (val == item.label) {
              arr.push(item.value);
            }
          });
          this.name = arr[0];
        }, 1000);
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
