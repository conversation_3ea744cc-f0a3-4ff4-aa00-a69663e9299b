<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    placeholder="请输入校区关键词查询选择"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getAllCampus } from "@admin/api/Settings/Building.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
    };
  },
  props: {},
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label;
      this.$emit("getCampusName", this.selectname);
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      this.$emit("getCampusName", this.selectname);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getCampusName", this.selectname);
    },
    handleBlur() {
      //   this.selectname = name;
      //   this.$emit("getName", this.selectname);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getAllCampus(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.campusName,
              value: item.campusName,
            });
          });
          cb(arr);
        }
      });
    },
  },
};
</script>

<style></style>
