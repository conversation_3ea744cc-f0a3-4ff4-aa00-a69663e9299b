<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    :placeholder="'请输入' + Classofstudy + '关键词查询选择'"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getclass } from "@admin/api/Dailymanagement/program";
export default {
  data() {
    return {
      name: "",
      selectname: "",
      Classofstudy: window.g.Classofstudy,
    };
  },
  props: ["yearTermId"],
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label;
      this.selectnumber = data.schoolClassNum;
      this.$emit("getClassName", this.selectname);
      this.$emit("getClassNumber", this.selectnumber);
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      this.$emit("getClassName", this.selectname);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getClassName", this.selectname);
    },
    handleBlur() {
      this.$emit("getClassName", this.name);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getclass(queryString, 500, this.yearTermId).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.schoolClassName,
              value: item.schoolClassName,
              schoolClassNum: item.schoolClassNum,
            });
          });
          cb(arr);
        }
      });
    },
  },
};
</script>

<style></style>
