<template>
	<el-autocomplete
		class="inline-input"
		v-model="name"
		:fetch-suggestions="querySearch"
		clearable
		placeholder="请输入国别码关键词查询选择"
		@select="handleSelect"
		@change="handleChange"
		@clear="handleClear"
		@blur="handleBlur"

		style="width:100%"
	></el-autocomplete>
</template>

<script>
import { getCountrycode } from '@admin/api/Statisticalreports/index.js';
export default {
	data() {
		return {
			name: '',
			selectname: '',
			options: []
		};
	},
	props: ['getcountrycode'],
	created() {},
	methods: {
		handleSelect(data) {
			this.selectname = data.label;
			this.$emit('getId', this.selectname);
		},
		handleChange(data) {
			if (this.selectname == '') {
				this.selectname = data;
			}
			this.$emit('getId', this.selectname);
		},
		handleClear() {
			this.selectname = '';
			this.$emit('getId', this.selectname);
		},
		handleBlur() {
			//   this.selectname = name;
			//   this.$emit("getName", this.selectname);
		},
		querySearch(queryString, cb) {
			//   this.$emit("getName", this.name);
			let arr = [];
			getCountrycode(queryString, 20).then(res => {
				if (res.code == 0) {
					res.data.forEach(item => {
						arr.push({
							label: item.arabicNumberCode,
							value: item.chineseFullName
						});
						this.options.push({
							label: item.arabicNumberCode,
							value: item.chineseFullName
						});
					});
					cb(arr);
				}
			});
		}
	},
	watch: {
		getcountrycode: {
			handler(val) {
				console.log("val===="+val);
				this.querySearch('');
				var arr = [];
				setTimeout(() => {
					this.options.map((item, index) => {
						if ((val == item.label)) {
							arr.push(item.value);
						}
					});
					this.name = arr[0]
				}, 1000);
			},
			immediate: true
		}
	}
};
</script>

<style></style>
