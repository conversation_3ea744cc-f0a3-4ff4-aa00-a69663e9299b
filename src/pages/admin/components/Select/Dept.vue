<template>
  <el-autocomplete
    v-model="name"
    class="inline-input"
    :fetch-suggestions="querySearch"
    clearable
    :placeholder="'请输入' + deptname + '名称关键词查询选择'"

    style="width: 100%"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"
  />
</template>

<script>
import { getAllDept } from '@admin/api/Settings/User/Department'
export default {
  props: ['getdeptid', 'deptName'],
  data() {
    return {
      name: '',
      selectname: '',
      selectTruename: '',
      options: [],
      deptname: window.g.DeptName
    }
  },
  watch: {
    getdeptid: {
      handler(val) {
        this.querySearch('')
        var arr = []
        setTimeout(() => {
          this.options.map((item, index) => {
            if (val === item.label) {
              arr.push(item.value)
            }
          })
          this.name = arr[0]
        }, 1000)
      },
      immediate: true
    },
    deptName: {
      handler(val) {
        this.name = val
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label
      this.selectTruename = data.value
      this.$emit('getDeptName', this.selectname)
      this.$emit('getDeptTrueName', data.value)
    },
    handleChange(data) {
      console.log(data)
      if (this.selectname === '') {
        this.selectname = data
      }
      if (this.selectTruename === '') {
        this.selectTruename = data
      }
      this.$emit('getDeptName', this.selectname)
    },
    handleClear() {
      this.selectname = ''
      this.$emit('getDeptName', this.selectname)
      this.$emit('getDeptTrueName', '')
    },
    handleBlur() {
      this.selectTruename = this.name
      this.$emit('getDeptTrueName', this.selectTruename)
    },
    querySearch(queryString, cb) {
      const arr = []
      getAllDept(queryString, 100).then((res) => {
        if (res.code === 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.deptId,
              value: item.deptName
            })
            this.options.push({
              label: item.deptId,
              value: item.deptName
            })
          })
          if (typeof cb === 'function') {
            cb(arr)
          }
        }
      })
    }
  }
}
</script>

<style></style>
