<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    :placeholder="'请输入单位名称关键词查询选择'"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getDeptList } from "@admin/api/Systemapplication/synthesisinfo/scientificequipment.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
      selectTruename: "",
      options: [],
      deptname: window.g.DeptName,
    };
  },
  props: ["getdeptid", "deptName"],
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label;
      this.selectTruename = data.value;
      this.$emit("getDeptName", this.selectname);
      this.$emit("getDeptTrueName", data.value);
    },
    handleChange(data) {
      console.log(data);
      if (this.selectname == "") {
        this.selectname = data;
      }
      if (this.selectTruename == "") {
        this.selectTruename = data;
      }
      this.$emit("getDeptName", this.selectname);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getDeptName", this.selectname);
      this.$emit("getDeptTrueName", "");
    },
    handleBlur() {
      this.selectTruename = this.name;
      this.$emit("getDeptTrueName", this.selectTruename);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getDeptList(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.deptId,
              value: item.deptName,
            });
            this.options.push({
              label: item.deptId,
              value: item.deptName,
            });
          });
          cb(arr);
        }
      });
    },
  },
  watch: {
    getdeptid: {
      handler(val) {
        this.querySearch("");
        var arr = [];
        setTimeout(() => {
          this.options.map((item, index) => {
            if (val == item.label) {
              arr.push(item.value);
            }
          });
          this.name = arr[0];
        }, 1000);
      },
      immediate: true,
    },
    deptName: {
      handler(val) {
        this.name = val;
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
