<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    placeholder="'请输入检查表关键词查询选择'"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getpatrolinspectionformAll } from "@admin/api/Systemapplication/Inspection/Standar.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
      selectTruename: "",
      options: [],
      deptname: window.g.DeptName,
    };
  },
  props: ["getformtid", "formName"],
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label;
      this.selectTruename = data.value;
      this.$emit("getformId", this.selectname);
      this.$emit("getformName", data.value);
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      if (this.selectTruename == "") {
        this.selectTruename = data;
      }
      this.$emit("getformId", this.selectname);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getformId", this.selectname);
      this.$emit("getformName", "");
    },
    handleBlur() {
      this.selectTruename = this.name;
      this.$emit("getformName", this.selectTruename);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      let json = {
        key: queryString,
        num: 10,
      };
      getpatrolinspectionformAll(json).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.patrolInspectionFormId,
              value: item.patrolInspectionFormName,
            });
            this.options.push({
              label: item.patrolInspectionFormId,
              value: item.patrolInspectionFormName,
            });
          });
          cb(arr);
        }
      });
    },
  },
  watch: {
    getformtid: {
      handler(val) {
        this.querySearch("");
        var arr = [];
        setTimeout(() => {
          this.options.map((item, index) => {
            if (val == item.label) {
              arr.push(item.value);
            }
          });
          this.name = arr[0];
        }, 1000);
      },
      immediate: true,
    },
    formName: {
      handler(val) {
        this.name = val;
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
