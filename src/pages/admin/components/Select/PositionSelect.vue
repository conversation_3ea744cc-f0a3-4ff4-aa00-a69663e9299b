<template>
  <el-cascader
    v-model="value"
    :options="Options"
    @change="handleChange"
    style="width: 100%"
    clearable

  ></el-cascader>
  <!-- <el-select v-model="value" placeholder="请选择" class="publicselect"  style="width: 100%" @change="handleChange" @select="handleSelect" clearable>
		<el-option v-for="(item, index) in Options" :key="index" :label="item.label" :value="item.value"></el-option>
	</el-select> -->
</template>

<script>
import { getType, getTypeAll } from "@index/api/Arrange/Arrange";
export default {
  data() {
    return {
      Options: [],
      value: "",
    };
  },
  props: ["position"],
  created() {
    this.getdata();
  },
  methods: {
    handleSelect(data) {},
    handleChange(data) {
      let id = data[1];
      this.$emit("getId", id);
    },
    getdata() {
      getType().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            if (item.name == "专业技术职务") {
              getTypeAll(item.id).then((v) => {
                if (v.code == 0) {
                  v.data.map((a, index) => {
                    let json = {
                      label: a.name,
                      value: a.codeValue,
                    };
                    if (a.children) {
                      json.children = [];
                      a.children.map((b, c) => {
                        json.children.push({
                          label: b.name,
                          value: b.codeValue,
                        });
                      });
                    }
                    this.Options.push(json);
                  });
                }
                // v.data.forEach(b => {
                // 	this.Options.push({
                // 		label: b.name,
                // 		value: b.id
                // 	});
                // });
              });
            }
          });
        }
      });
    },
  },
  watch: {
    position: {
      handler(val) {
        this.value = val;
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
