<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    placeholder="请输入实验室关键词查询选择"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getAllLaboratory } from "@admin/api/Settings/Laboratory/Laboratory.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
      options: [],
    };
  },
  props: ["getroomid", "roomName", "getroomSn"],
  created() {},
  methods: {
    handleSelect(data) {
      let text = data.label;
      this.selectname = text.split("(")[0];
      this.$emit("getRoomName", this.selectname);
      this.$emit("getRoomId", data.roomId);
      this.$emit("getroomSn", data.roomSn);
      this.$emit("getroomTrueName", data.roomTrueName);
      this.$emit("getroomUuid", data.uuid);
      console.log(data.uuid)
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      this.$emit("getRoomName", this.selectname);
      this.$emit("getroomTrueName", data.roomTrueName);
      this.$emit("getRoomId", data.roomId);
      this.$emit("getroomSn", data.roomSn);
      this.$emit("getroomUuid", data.uuid);
      console.log(data.uuid)
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getRoomName", this.selectname);
      this.$emit("getroomTrueName", "");
      this.$emit("getRoomId", "");
      this.$emit("getroomSn", "");
      this.$emit("getroomUuid", "");
    },
    handleBlur() {
      //   this.selectname = name;
      this.$emit("getroomTrueName", this.name);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      getAllLaboratory(queryString, 5000).then((res) => {
        if (res.code == 0) {
          let arr = [];
          res.data.forEach((item) => {
            arr.push({
              label: item.roomName,
              value: item.roomName,
              roomTrueName: item.roomTrueName,
              roomId: item.roomId,
              roomSn: item.roomSn,
              uuid: item.uuid,
            });
          });
          this.options = arr;
          if (cb !== undefined){
            cb(arr);
          }
        }
      });
    },
  },
  watch: {
    getroomid: {
      handler(val) {
        this.querySearch("");
        var arr = [];
        setTimeout(() => {
          this.options.map((item, index) => {
            if (val == item.roomId) {
              arr.push(item.value);
            }
          });
          this.name = arr[0];
        }, 1500);
      },
      immediate: true,
    },
    roomName: {
      handler(val) {
        this.name = val;
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
