<template>
  <el-select
    class="inline-input"
    v-model="name"
    :remote-method="querySearch"
    clearable
    placeholder="请输入关键词查询选择"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    filterable
    style="width: 100%"
    :multiple="multipleflag"
    remote
    :loading="loading"
  >
    <el-option
      v-for="(item, index) in options"
      :key="index"
      :label="item.roomName"
      :value="item.roomId"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getAllLaboratory } from "@admin/api/Settings/Laboratory/Laboratory.js";
export default {
  data() {
    return {
      name: [],
      options: [],
      selectname: "",
      loading: false,
    };
  },
  props: ["groupFlag", "roomId", "multipleflag"],
  created() {},
  methods: {
    handleChange(data) {
      this.$emit("getroomId", data);
      let arr = [];
      if (this.groupFlag == 1) {
        this.options.map((item, index) => {
          if (data == item.label) {
            arr.push(item.label);
          }
        });
      } else {
        this.options.map((item, index) => {
          data.map((v, k) => {
            if (item.label == v) {
              arr.push(item.label);
            }
          });
        });
      }
      this.$emit("getroomName", arr);
    },
    handleClear() {
      this.$emit("getroomId", "");
      this.$emit("getroomName", "");
    },
    handleBlur() {
      this.$emit("getName", this.name);
    },
    querySearch(queryString) {
      this.loading = true;
      this.options = [];
      getAllLaboratory(queryString, 1000, this.groupFlag).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.options = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
  watch: {
    roomId: {
      handler(val) {
        this.querySearch();
        setTimeout(() => {
          if (val) {
            if (val.length == 0) {
              this.name = [];
            } else {
              this.name = val;
            }
          }
        }, 1000);
      },
      immediate: true,
    },
    groupFlag: {
      handler(val) {
        this.querySearch();
      },
    },
  },
};
</script>

<style></style>
