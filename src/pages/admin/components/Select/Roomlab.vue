<template>
  <el-select
    class="inline-input"
    v-model="name"
    :remote-method="querySearch"
    clearable
    placeholder="请输入关键词查询选择"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    filterable
    style="width: 100%"
    remote
    :loading="loading"
    :multiple="multiple"
  >
    <el-option
      v-for="(item, index) in options"
      :key="index"
      :label="item.roomLabName"
      :value="item.roomLabId"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getAllRoomlab } from "@admin/api/Settings/Laboratory/Laboratory.js";
export default {
  data() {
    return {
      name: "",
      options: [],
      selectname: "",
      loading: false,
    };
  },
  props: ["roomlabeId", "multiple"],
  created() {},
  methods: {
    handleChange(data) {
      this.$emit("getroomlab", data);
      let uuids = [];
      this.options.map((item, index) => {
        data.map((v, k) => {
          if (item.roomLabId == v) {
            uuids.push(item.uuid);
          }
        });
      });
      this.$emit("getroomUuid", uuids);
    },
    handleClear() {
      this.$emit("getroomlab", "");
    },
    handleBlur() {
      this.$emit("getroomlab", this.name);
    },
    querySearch(queryString) {
      this.loading = true;
      let json = {
        key: queryString,
        num: 20,
      };
      getAllRoomlab(json).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.options = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
  watch: {
    roomlabeId: {
      handler(val) {
        this.querySearch("");
        setTimeout(() => {
          this.name = val;
        }, 1000);
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
