<template>
  <el-select
    v-model="value"
    placeholder="请选择"
    class="publicselect"

    style="width: 100%"
    clearable
    @change="handleChange"
    @select="handleSelect"
    @clear="handleClear"
  >
    <el-option
      v-for="(item, index) in Options"
      :key="index"
      :label="item.yearTermName"
      :value="item.yearTermId"
    />
  </el-select>
</template>

<script>
import { getAllYearTerm } from '@admin/api/Settings/Semester/TermManagement'
export default {
  props: ['yearTermId'],
  data() {
    return {
      Options: [],
      value: ''
    }
  },
  watch: {
    yearTermId: {
      handler(v) {
        this.value = v
      }
    }
  },
  created() {
    this.getdata()
  },
  methods: {
    handleSelect(data) {
      console.log(data)
    },
    handleChange(data) {
      console.log(data)
      this.$emit('getId', data)
      this.Options.map((item, index) => {
        if (item.yearTermId == data) {
          const begin = item.beginDate.toString()
          const end = item.endDate.toString()
          const json = {
            beginDate:
              begin.slice(0, 4) +
              '-' +
              begin.slice(4, 6) +
              '-' +
              begin.slice(6, 8),
            endDate:
              end.slice(0, 4) + '-' + end.slice(4, 6) + '-' + end.slice(6, 8)
          }
          this.$emit('getDate', json)
        }
      })
    },
    handleClear() {
      this.$emit('getDate', '')
    },
    getdata() {
      getAllYearTerm().then((res) => {
        if (res.code == 0) {
          this.Options = res.data
          this.$emit('getSemsterOptions', this.Options)
        }
      })
    }
  }
}
</script>

<style></style>
