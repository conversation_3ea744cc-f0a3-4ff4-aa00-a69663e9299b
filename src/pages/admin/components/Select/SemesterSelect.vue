<template>
  <el-select v-model="value" placeholder="请选择" class="publicselect"  style="width: 100%"
    @change="handleChange" @select="handleSelect" clearable>
    <el-option v-for="(item, index) in Options" :key="index" :label="item.label" :value="item.value"></el-option>
  </el-select>
</template>

<script>
import { getAllYearTerm } from "@index/api/Arrange/Arrange";
export default {
  data() {
    return {
      Options: [],
      value: "",
    };
  },
  props: {},
  created() {
    this.getdata();
  },
  methods: {
    handleSelect(data) { },
    handleChange(data) {
      this.$emit("getId", data);
      this.Options.map((item, index) => {
        if (item.value == data) {
          let begin = item.beginDate.toString();
          let end = item.endDate.toString();
          let json = {
            beginDate:
              begin.slice(0, 4) + "-" + begin.slice(4, 6) + "-" + begin.slice(6, 8),
            endDate: end.slice(0, 4) + "-" + end.slice(4, 6) + "-" + end.slice(6, 8),
          };
          this.$emit("getDate", json);
        }
      });
    },
    getdata() {
      getAllYearTerm().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.Options.push({
              label: item.yearTermName,
              value: item.yearTermId,
              beginDate: item.beginDate,
              endDate: item.endDate,
            });
          });
        }
      });
    }
  }
}
</script>
<style></style>
