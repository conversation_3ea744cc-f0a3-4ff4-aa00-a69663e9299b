<template>
	<el-select v-model="value" placeholder="请选择" class="publicselect"  style="width: 100%" @change="handleChange" @select="handleSelect" clearable>
		<el-option v-for="(item, index) in Options" :key="index" :label="item.label" :value="item.value"></el-option>
	</el-select>
</template>

<script>
import { getType, getTypeAll } from '@index/api/Arrange/Arrange';
export default {
	data() {
		return {
			Options: [],
			value:"",
		};
	},
	props: ['getstatuscode'],
	created() {
		this.getdata();
	},
	methods: {
		handleSelect(data) {
		},
		handleChange(data) {
		  this.$emit("getId", data);
		},
		getdata() {
			getType().then(res => {
				if (res.code == 0) {
					res.data.forEach(item => {
						if (item.name == '设备现状码') {
							getTypeAll(item.id).then(v => {
								v.data.forEach(b => {
									this.Options.push({
										label: b.name,
										value: b.codeValue
									});
								});
							});
						}
					});
				}
			});
		}
	},
	watch:{
		getstatuscode:{
			handler(val){
				console.log(val);
				this.value = val;
			},
			immediate: true
		}
	}
};
</script>

<style></style>
