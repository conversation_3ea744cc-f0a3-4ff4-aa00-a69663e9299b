<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    placeholder="请输入工号或者姓名关键词查询选择"
    @select="handleSelect"
    @change="handleChange"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getAllTeacher } from "@admin/api/Settings/Teacher.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
    };
  },
  props: ['accNo'],
  created() {},
  methods: {
    handleSelect(data) {
      let text = data.label;
      this.$emit("getName", text);
      this.$emit("getlogonName", data.logonName);
      this.$emit("getaccNo", data.accNo);
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      this.$emit("getName", this.selectname);
      this.$emit("getlogonName", data);
      this.$emit("getaccNo", data.accNo);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getlogonName", "");
      this.$emit("getName", this.selectname);
      this.$emit("getaccNo", "");
    },
    handleBlur() {
      //   this.selectname = name;
      //   this.$emit("getName", this.selectname);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getAllTeacher(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.trueName,
              value: item.accNoTrueName,
              accNo:item.accNo,
              logonName:item.logonName
            });
          });
          cb(arr);
        }
      });
    },
  },
  watch: {
    accNo:{
      handler(val) {
        this.name = val;
        console.log(this.name);
      },
      immediate: true
    }
  },
};
</script>

<style></style>
