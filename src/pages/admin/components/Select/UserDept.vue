<template>
  <div>
    <el-select
      v-model="name"

      placeholder="请选择"
      style="width: 100%"
      multiple
      clearable
      filterable
      @change="handleChange"
    >
      <el-option
        v-for="(item, index) in deptOptions"
        :key="index"
        :label="item.deptName"
        :value="item.deptId"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import { getDeptList } from "@admin/api/Systemapplication/synthesisinfo/scientificequipment.js";
export default {
  data() {
    return {
      name: [],
      selectname: "",
      selectTruename: "",
      deptOptions: [],
      deptname: window.g.DeptName,
    };
  },
  props: ["getdeptid"],
  created() {},
  methods: {
    handleChange(data) {
      this.$emit("getDeptName", data.deptname);
      this.$emit("getDeptId", this.name);
    },
    getdata() {
      getDeptList("", 200).then((res) => {
        if (res.code == 0) {
          this.deptOptions = res.data;
        }
      });
    },
  },
  created(){
    this.getdata();
  },
  watch: {
    getdeptid: {
      handler(val) {
        this.name = val;
      },
      immediate: true,
    }
  },
};
</script>

<style></style>
