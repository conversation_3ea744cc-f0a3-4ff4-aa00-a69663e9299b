<template>
  <el-cascader
    v-model="value"
    :options="Options"
    @change="handleChange"
    style="width: 100%"
    clearable

    placeholder="请选择实验室配置"
  ></el-cascader>
  <!-- <el-select v-model="value" placeholder="请选择" class="publicselect"  style="width: 100%" @change="handleChange" @select="handleSelect" clearable>
		<el-option v-for="(item, index) in Options" :key="index" :label="item.label" :value="item.value"></el-option>
	</el-select> -->
</template>

<script>
import { getallocationTypeTree } from "@admin/api/Settings/Laboratory/allocationType.js";
export default {
  data() {
    return {
      Options: [],
      value: "",
    };
  },
  props: {},
  created() {
    this.getdata();
  },
  methods: {
    handleSelect(data) {},
    handleClear(data) {
      if (!data) {
        this.$emit("getId", "");
      }
    },
    handleChange(data) {
      if (data) {
        this.$emit("getId", data);
      } else {
        this.$emit("getId", "");
      }
    },
    getdata() {
      getallocationTypeTree().then((res) => {
        if (res.code == 0) {
          res.data.map((item, index) => {
            let json = {
              label: item.roomConfigKindName,
              value: item.roomConfigKindId,
            };
            if (item.roomConfigList) {
              json.children = [];
              item.roomConfigList.map((v, k) => {
                json.children.push({
                  label: v.roomConfigName,
                  value: v.roomConfigId,
                });
              });
            }
            this.Options.push(json);
          });
        }
      });
    },
  },
};
</script>

<style></style>
