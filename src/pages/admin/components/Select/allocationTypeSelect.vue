<template>
  <el-select
    v-model="value"
    placeholder="请选择"
    class="publicselect"

    style="width: 100%"
    @change="handleChange"
    @select="handleSelect"
    clearable
  >
    <el-option
      v-for="(item, index) in Options"
      :key="index"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
import { getallocationTypeAll } from "@admin/api/Settings/Laboratory/allocationType";
export default {
  data() {
    return {
      Options: [{
        label: "暂无",
        value:0
      }],
      value: "",
    };
  },
  props: ['getcategory'],
  methods: {
    getdata() {
      getallocationTypeAll().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item, index) => {
            this.Options.push({
              label: item.roomConfigKindName,
              value: item.roomConfigKindId,
            });
          });
        }
      });
    },
    handleSelect(data) {},
    handleChange(data) {
      this.$emit("getId", data);
    },
  },
  created() {
    this.getdata();
  },
  watch: {
		getcategory:{
			handler(val){
				console.log(val);
				this.value = val;
			},
			immediate: true
		}
	}
};
</script>
<style></style>