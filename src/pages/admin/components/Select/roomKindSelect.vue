<template>
  <el-autocomplete
    class="inline-input"
    v-model="name"
    :fetch-suggestions="querySearch"
    clearable
    :placeholder="'请输入'+ExpCenterName+'关键词查询选择'"
    @select="handleSelect"
    @clear="handleClear"
    @blur="handleBlur"

    style="width: 100%"
  ></el-autocomplete>
</template>

<script>
import { getAllType } from "@admin/api/Settings/Laboratory/Laboratory.js";
export default {
  data() {
    return {
      name: "",
      selectname: "",
      options: [],
      // 实验中心名称个性化
      ExpCenterName:window.g.ExpCenterName,
    };
  },
  props: ["getroomkindid"],
  created() {},
  methods: {
    handleSelect(data) {
      this.selectname = data.label;
      this.$emit("getDeptName", this.selectname);
    },
    handleChange(data) {
      if (this.selectname == "") {
        this.selectname = data;
      }
      console.log(data)
      this.$emit("getDeptName", this.selectname);
    },
    handleClear() {
      this.selectname = "";
      this.$emit("getDeptName", this.selectname);
    },
    handleBlur(data) {
      // if(!this.selectname){
      //   this.$emit("getName", "");
      // }
      //   this.selectname = name;
      //   this.$emit("getName", this.selectname);
    },
    querySearch(queryString, cb) {
      //   this.$emit("getName", this.name);
      let arr = [];
      getAllType(queryString, 500).then((res) => {
        if (res.code == 0) {
          if (res.data) {
            res.data.forEach((item) => {
              arr.push({
                label: item.roomKindId,
                value: item.roomKindName,
              });
              this.options.push({
                label: item.roomKindId,
                value: item.roomKindName,
              });
            });
            cb(arr);
          }else{
            this.$emit("getDeptName", "");
          }
        }
      });
    },
  },
  watch: {
    getroomkindid: {
      handler(val) {
        this.querySearch("");
        var arr = [];
        setTimeout(() => {
          this.options.map((item, index) => {
            if (val == item.label) {
              arr.push(item.value);
            }
          });
          this.name = arr[0];
        }, 1000);
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
