<template>
  <el-select
    v-model="value"
    placeholder="请选择"
    class="publicselect"

    style="width: 100%"
    @change="handleChange"
    @select="handleSelect"
    clearable
  >
    <el-option
      v-for="(item, index) in Options"
      :key="index"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  data() {
    return {
      Options: [],
      value: "",
    };
  },
  props: ["getroomkind"],
  created() {
    this.getdata();
  },
  methods: {
    handleSelect(data) {},
    handleChange(data) {
      this.$emit("getId", data);
    },
    getdata() {
      // 简化版，只提供一个选项
      this.Options = [
        {
          label: "实验室",
          value: "lab",
        }
      ];
    },
  },
  watch: {
    getroomkind: {
      handler(val) {
        this.value = val;
      },
      immediate: true,
    },
  },
};
</script>

<style></style>
