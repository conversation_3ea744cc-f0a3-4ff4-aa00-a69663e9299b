<template>
  <div class="dynamic-table">
    <el-table
      :data="tableData"
      border
      stripe
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <!-- 动态生成列 -->
      <el-table-column
        v-for="(column, index) in columns"
        :key="index"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :sortable="column.sortable || false"
        :formatter="column.formatter"
      >
        <!-- 自定义列内容 -->
        <template v-if="column.slotName" v-slot="{ row }">
          <slot :name="column.slotName" :row="row"></slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="showPagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.currentPage"
      :page-sizes="pagination.pageSizes"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: "TablePagetion",
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => [],
    },
    // 列配置
    columns: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every((item) => {
          return item.prop && item.label;
        });
      },
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => ({
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 20, 50, 100],
        total: 0,
      }),
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    handleSizeChange(val) {
      this.$emit("page-size-change", val);
    },
    handleCurrentChange(val) {
      this.$emit("current-page-change", val);
    },
    handleSortChange({ column, prop, order }) {
      this.$emit("sort-change", { column, prop, order });
    },
  },
};
</script>

<style scoped>
.dynamic-table {
  margin-top: 20px;
}
.el-pagination {
  margin-top: 15px;
  text-align: right;
}
</style>
