<template>
  <div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="data.pageNum"
      :page-sizes="[10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
      :page-size="10"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageCount"
    />
    <!-- <i class="el-icon-refresh-right" @click="refersh"></i> -->
  </div>
</template>
<script>
export default {
  props: {
    pageCount: {
      type: Number,
      required: false,
      default: 0,
    },
    currentPage: {
      type: Number,
      required: false,
      default: 1,
    },
  },
  data() {
    return {
      data: {
        pageNum: 1,
        page: 10,
        orderKey: "",
        orderModel: "",
      },
    };
  },
  watch: {
    currentPage: {
      handler(val) {
        this.pageNum = val;
      },
    },
  },
  created() {
    if (!this.currentPage) {
      this.pageNum = 1;
    }
  },
  methods: {
    handleSizeChange(val) {
      //   业务逻辑 当触发事件改变了值 修改data中的值 = val 然后触发绑在子组件上的父组件的事件 将值传过去
      //   在父组件的事件里面 将得到的值修改到data中 然后调用接口函数重新获取数据
      this.data.page = val;
      this.data.pageSize = val; // 部分页面使用pageSize参数作兼容处理
      this.$emit("pagebar", this.data);
    },
    handleCurrentChange(val) {
      this.data.pageNum = val;
      this.$emit("pagebar", this.data);
    },
    refersh() {
      this.$emit("pagebar", this.data);
    },
  },
};
</script>
<style lang="scss" scoped>
.el-pagination button,
.el-pagination span:not([class*="suffix"]) {
  font-size: 12px;
}
.el-pagination {
  float: right;
  margin-top: 1%;
}
.el-icon-refresh-right {
  float: right;
  margin-top: 4.2%;
  font-size: 23px;
  font-weight: lighter;
  margin-right: 1%;
  color: #606266;
  cursor: pointer;
}
</style>
