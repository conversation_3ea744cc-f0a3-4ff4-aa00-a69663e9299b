<template>
  <div class="tinymce-editor">
    <editor
      v-model="myValue"
      :init="init"
      :disabled="disabled"
      @onClick="onClick"
      :key="tinymceFlag"
    >
    </editor>
  </div>
</template>
<script>
import { uploadimg } from "@admin/api/Settings/Laboratory/Laboratory.js";
import tinymce from "tinymce/tinymce";
import "tinymce/themes/silver/theme";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/icons/default/icons";
// import "tinymce/models/dom/model";
import "tinymce/plugins/print";
import "tinymce/plugins/preview";
import "tinymce/plugins/searchreplace";
import "tinymce/plugins/autolink";
import "tinymce/plugins/directionality";
import "tinymce/plugins/visualblocks";
import "tinymce/plugins/visualchars";
import "tinymce/plugins/fullscreen";
import "tinymce/plugins/image";
import "tinymce/plugins/link";
import "tinymce/plugins/media";
import "tinymce/plugins/template";
import "tinymce/plugins/code";
import "tinymce/plugins/codesample";
import "tinymce/plugins/table";
import "tinymce/plugins/charmap";
import "tinymce/plugins/hr";
// import "tinymce/plugins/pagebreak";
import "tinymce/plugins/nonbreaking";
import "tinymce/plugins/anchor";
import "tinymce/plugins/insertdatetime";
import "tinymce/plugins/advlist";
import "tinymce/plugins/lists";
import "tinymce/plugins/wordcount";
import "tinymce/plugins/imagetools";
import "tinymce/plugins/textpattern";
import "tinymce/plugins/help";
// import "tinymce/plugins/emoticons";
import "tinymce/plugins/autosave";
// import "tinymce/plugins/bdmap";
// import "tinymce/plugins/indent2em";
import "tinymce/plugins/autoresize";
// import "tinymce/plugins/formatpainter";
// import "tinymce/plugins/axupimgs";
import "../../../../../public/tinymce/plugins/kityformula-editor/plugin.min.js";
import "../../../../../public/tinymce/plugins/importword/plugin.min.js";
import "../../../../../public/tinymce/skins/content/default/content.css";
import { uploadvideo } from "@admin/api/Settings/Secureaccess/Onlinelearning";
export default {
  components: {
    Editor,
  },
  props: {
    //传入一个value，使组件支持v-model绑定
    value: {
      type: String,
      default: "",
    },
    tinymceFlag: {
      type: Number,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    plugins: {
      type: [String, Array],
      // default: "lists image media table textcolor wordcount contextmenu",
      default:
        "print preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap hr  nonbreaking anchor insertdatetime advlist lists wordcount imagetools textpattern help emoticons autosave  autoresize kityformula-editor importword",
    },
    fontsize_formats: {
      type: String,
      default: "12px 14px 16px 18px 24px 36px 48px 56px 72px",
    },
    toolbar: {
      type: [String, Array],
      // default: "undo redo |  formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image media table | removeformat",
      default:
        "code undo redo restoredraft | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat | table image media charmap hr  insertdatetime print preview | fullscreen | bdmap indent2em lineheight formatpainter axupimgs | kityformula-editor importword",
    },
    fontFormats: {
      type: String,
      default:
        "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;宋体=simsun, serif;苹果苹方=PingFang SC, Microsoft YaHei, sans- serif;Arial=arial, helvetica, sans- serif;Arial Black=arial black, avant garde;Book Antiqua=book antiqua, palatino;Comic Sans MS=comic sans ms, sans- serif;Courier New = courier new, courier;Georgia = georgia, palatino;Helvetica = helvetica;Symbol = symbol;Tahoma = tahoma, arial, helvetica, sans - serif;Terminal = terminal, monaco;Times New Roman = times new roman, times;Verdana = verdana, geneva",
    },
  },
  data() {
    return {
      ApiUrl:window.g.ApiUrl,
      //初始化配置
      init: {
        language_url: "./tinymce/zh_CN.js",
        language: "zh_CN",
        height: 650,
        skin_url: "./tinymce/skins/ui/oxide",
        plugins: this.plugins,
        toolbar: this.toolbar,
        // branding: false,
        // menubar: true,
        // quickbars_selection_toolbar:
        //   "removeformat | bold italic underline strikethrough | fontsizeselect forecolor backcolor",
        font_formats: this.fontFormats,
        fontsize_formats: this.fontsize_formats,
        //此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
        //如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
        images_upload_handler: (blobInfo, success, failure, progress) => {
          let fd = new FormData();
          fd.append("file", blobInfo.blob(), blobInfo.filename());
          uploadimg(fd).then((res) => {
            if (res.code === 0) {
              success(this.ApiUrl + res.data);
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
          // const img = "data:image/jpeg;base64," + blobInfo.base64();
          // success(img);
        },
        file_picker_types: "media",
        media_live_embeds: true,
        file_picker_callback: (callback, value, meta) => {
          if (meta.filetype === "media") {
            const input = document.createElement("input");
            input.setAttribute("type", "file");
            const that = this; // 为 Vue 构造函数中的 this，指向 Vue 实例对象
            input.onchange = async function () {
              const file = this.files[0]; // 为 HTMLInputElement 构造函数中的 this，指向 input 实例对象
              const isValid = await that.validateVideo(file);

              if (isValid) {
                const loading = that.$loading({
                  lock: true,
                  text: "Loading",
                  spinner: "el-icon-loading",
                  background: "rgba(0, 0, 0, 0.7)",
                });
                that.uploadFile(file, "video");
                setTimeout(() => {
                  loading.close();
                  if (that.videomsg.url != "") {
                    callback(that.location + "lab/" + that.videomsg.url);
                  }
                  // console.log(that.location + "lab/" + that.videomsg.url);
                }, 2000);
                // const { url } = await that.uploadFile(file, "video");
                // callback(url);
              } else {
                callback();
              }
            };

            input.click();
          }
        },
        // video_template_callback: (data) => {
        //   console.log(data);
        //   let source = that.location + "lab/" + data.source1;
        //   console.log(source);
        //   return `<video width="745" height="420" controls="controls" src=${data.source} />`;
        // },
      },
      myValue: "",
      videomsg: {
        url: "",
        name: "",
      },
      location: window.location.protocol + "//" + window.location.host + "/",
    };
  },
  mounted() {
    tinymce.init({});
    // this.myValue = "";
  },
  created() {
    // tinymce.init({});
  },
  methods: {
    //添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events
    //需要什么事件可以自己增加
    onClick(e) {
      this.$emit("onClick", e, tinymce);
    },
    //可以添加一些自己的自定义事件，如清空内容
    clear() {
      this.myValue = "";
    },
    //校验视频格式和内存大小
    validateVideo(file) {
      const isMP4 = file.type === "video/mp4";
      const isLt3M = file.size / 1024 / 1024 < 5;

      if (!isMP4) {
        this.$message.error("上传视频必须为 MP4 格式！");

        return false;
      }

      if (!isLt3M) {
        this.$message.error("上传视频大小限制 5M 以内！");

        return false;
      }

      const duration = this.getVideoDuration(file);
      if (duration > 60) {
        this.$message.error("上传视频时长不能超过 60 秒！");

        return false;
      }

      return true;
    },
    //获取视频源
    getVideoDuration(file) {
      return new Promise((resolve) => {
        const videoElement = document.createElement("video");
        videoElement.src = URL.createObjectURL(file);

        videoElement.addEventListener("loadedmetadata", () => {
          resolve(videoElement.duration);
        });
      });
    },
    //上传视频
    uploadFile(file, folder = "images") {
      const formData = new FormData();
      formData.append("file", file);
      uploadvideo(formData).then((res) => {
        if (res.code == 0) {
          this.videomsg.url = res.data;
          this.videomsg.name = file.name;
          // return {
          //   url: res.data,
          //   name: file.name,
          // };
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
  },
  watch: {
    value: {
      handler(newValue) {
        this.myValue = newValue;
        console.log(this.myValue);
      },
      deep: true,
      immediate: true,
    },
    myValue(newValue) {
      this.$emit("input", newValue);
    },
  },
};
</script>
<style>
.tox-tinymce-aux {
  z-index: 3000 !important;
}
.el-loading-mask {
  z-index: 4000 !important;
}
</style>
