<template>
  <div class="fixed-header">
    <div class="navbar">
      <hamburger
        :is-active="sidebar.opened"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />

      <breadcrumb class="breadcrumb-container" />

      <div class="right-menu">
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <!-- <img :src="avatar+'?imageView2/1/w/80/h/80'" class="user-avatar"> -->
            <!-- <img
              src="https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif"
              class="user-avatar"
            /> -->
            <p class="name">{{ logonName }}</p>
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <!-- <router-link to="/"><el-dropdown-item>修改密码</el-dropdown-item></router-link> -->
            <el-dropdown-item @click.native="backsite">前往预约端</el-dropdown-item>
            <!-- <el-dropdown-item @click.native="gotourl">前往安全培训和准入管理端</el-dropdown-item> -->
            <el-dropdown-item divided @click.native="logout"
              ><span style="display: block">退出登录</span></el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@admin/components/Breadcrumb";
import Hamburger from "@admin/components/Hamburger";
import store from "@admin/store";
import { loginType } from "@admin/api/user";

export default {
  data() {
    return {
      ApiUrl:window.g.ApiUrl,
      logonName: store.getters.name,
    };
  },
  components: {
    Breadcrumb,
    Hamburger,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar"]),
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    gotourl() {
      if (window.g.onlinetest_adminUrl) {
        window.open(window.g.onlinetest_adminUrl + "?flag=admin");
      }
    },
    //统一认证退出登录
    logout() {
      loginType().then((res) => {
        if (res.code == 0) {
          if (res.data == 2) {
            let a = window.location.href;
            let href = a.split("/")[0] + this.ApiUrl + "/login/logout";
            window.location.href = href;
          } else {
            this.$store
              .dispatch("user/logout")
              .then(() => {
                this.$router.push(`/login?redirect=${this.$route.fullPath}`);
              })
              .catch(() => {});
          }
        }
      });
    },
    //返回预约端
    backsite() {
      // let a = window.location.href;
      // let href = a.split('/')[0];
      window.location.href = "index.html";
    },
    //系统退出登录
    // async logout() {
    //   await this.$store.dispatch("user/logout");
    //   this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    // },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    padding-right: 20px;
    box-sizing: border-box;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      cursor: pointer;
      .avatar-wrapper {
        margin-top: 10px;
        position: relative;
        .name {
          float: left;
          line-height: 32px;
          margin-left: 10px;
        }
        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          float: left;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 12px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
