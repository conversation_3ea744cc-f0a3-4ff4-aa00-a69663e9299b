<template>
  <div class="fixed-header">
    <div class="navbar">
      <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in AppList" :key="index + 'list'" :name="item.applicationSn" :label="item.applicationName">
          {{ item.applicationName }}
        </el-tab-pane>
        <!-- <el-tab-pane label="系统配置" name="first">系统配置</el-tab-pane>
        <el-tab-pane label="系统应用" name="second">系统应用</el-tab-pane>
        <el-tab-pane label="综合统计" name="third">综合统计</el-tab-pane> -->
      </el-tabs>

      <!-- <breadcrumb class="breadcrumb-container" /> -->

      <div class="right-menu">
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <!-- <img :src="avatar+'?imageView2/1/w/80/h/80'" class="user-avatar"> -->
            <!-- <img
              src="https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif"
              class="user-avatar"
            /> -->
            <div class="name">{{ logonName }}</div>
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <!-- <router-link to="/"
              ><el-dropdown-item>修改密码</el-dropdown-item></router-link
            > -->
            <el-dropdown-item @click.native="backsite">前往预约端</el-dropdown-item>
            <!-- <el-dropdown-item @click.native="gotourl"
              >前往安全培训和准入管理端</el-dropdown-item
            > -->
            <el-dropdown-item divided @click.native="logout"><span style="display: block">退出登录</span></el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@admin/components/Breadcrumb";
import Hamburger from "@admin/components/Hamburger";
import store from "@admin/store";
import { loginType, getAppList } from "@admin/api/user";
import { logout } from "@admin/api/login";
import Layout from "@admin/layout";

export default {
  data() {
    return {
      logonName: store.getters.name,
      activeName: "",
      tabRoutes: [],
      AppList: [],
    };
  },
  components: {
    Breadcrumb,
    Hamburger,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar"]),
  },
  methods: {
    //获取应用数据
    getAppList() {
      getAppList().then((res) => {
        if (res.code == 0) {
          // 临时仅开启系统应用和系统配置
          this.AppList = res.data.filter((item) => {
            return item.applicationName === "系统应用" || item.applicationName === "系统配置";
          });
          this.activeName = sessionStorage.getItem("activeName", this.activeName);
          if (!this.activeName) {
            let activearr = [];
            this.AppList.map((item, index) => {
              if (item.active == 1) {
                activearr.push(item.applicationSn);
              }
            });
            if (activearr.length != 0) {
              this.activeName = activearr[0];
              this.$store.dispatch("permission/generateRoutes", this.activeName).then((res) => {
                // this.$router.addRoutes(res);
              });
            } else {
              this.activeName = res.data[0].applicationSn;
            }
            sessionStorage.setItem("activeName", this.activeName);
          }
          // console.log("this.activeName===" + this.activeName);
        }
      });
    },
    handleClick() {
      this.$store.dispatch("permission/generateRoutes", this.activeName).then((res) => {
        console.log(res);
        // this.$router.addRoutes(res);
      });
      sessionStorage.setItem("activeName", this.activeName);
      // setTimeout(() => {
      //   this.$router.go(0);
      // }, 100);
      // console.log(accessRoutes);
      // let baseroute = this.$store.getters.tabRoutes.slice(0, 4);
      // // let arr = this.$store.getters.tabRoutes;
      // let firstarr = [],
      //   secondarr = [],
      //   thirdarr = [],
      //   allarr = [];
      // this.tabRoutes.map((item, index) => {
      //   if (item.system == "Systemconfiguration") {
      //     firstarr.push(item);
      //   }
      //   if (item.system == "Systemapplication") {
      //     secondarr.push(item);
      //   }
      //   if (item.system == "Comprehensivestatistics") {
      //     thirdarr.push(item);
      //   }
      // });
      // if (this.activeName == "first") {
      //   allarr = baseroute.concat(firstarr);
      // } else if (this.activeName == "second") {
      //   allarr = baseroute.concat(secondarr);
      // } else if (this.activeName == "third") {
      //   allarr = baseroute.concat(thirdarr);
      // }
      // console.log(allarr);
      // this.$set(this.$store.dispatch("app/setTabRoutes", allarr));
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    gotourl() {
      if (window.g.onlinetest_adminUrl) {
        window.open(window.g.onlinetest_adminUrl + "?flag=admin");
      }
    },
    //统一认证退出登录
    logout() {
      let json = {
        callbackPage: window.location.protocol + "//" + window.location.host + window.g.thirdurl + "/admin.html",
      };
      logout(json).then((res) => {
        if (res.code == 0) {
          if (res.data.isThirdLogout == 1) {
            window.location.href = res.data.thirdLogoutUrl;
          } else {
            this.$store
              .dispatch("user/logout")
              .then(() => {
                this.$router.go(0);
                // this.$router.replace(`/login?redirect=${this.$route.fullPath}`);
              })
              .catch(() => {
                this.$router.go(0);
                // this.$router.replace(`/login?redirect=${this.$route.fullPath}`);
              });
          }
        } else {
          this.$message.error(res.message);
        }
      });
      // this.$store
      //   .dispatch("user/logout")
      //   .then(() => {
      //     this.$router.push(`/login?redirect=${this.$route.fullPath}`);
      //   })
      //   .catch(() => { });

      // loginType().then((res) => {
      //   if (res.code == 0) {
      //     if (res.data == 2) {
      //       let a = window.location.href;
      //       let href = a.split("/")[0] + "/lab/login/logout";
      //       window.location.href = href;
      //     } else {
      //       this.$store
      //         .dispatch("user/logout")
      //         .then(() => {
      //           this.$router.push(`/login?redirect=${this.$route.fullPath}`);
      //         })
      //         .catch(() => {});
      //     }
      //   }
      // });
    },
    //返回预约端
    backsite() {
      let a = window.location.href;
      if (a.indexOf("back") != -1) {
        window.location.href = "labplatform.html";
      } else {
        window.location.href = "index.html";
      }
    },
    //系统退出登录
    // async logout() {
    //   await this.$store.dispatch("user/logout");
    //   this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    // },
  },
  created() {
    this.getAppList(); //获取当前应用
    // this.tabRoutes = this.$store.getters.tabRoutes;
    // setTimeout(() => {
    //   this.handleClick();
    // }, 1000);
  },
};
</script>
<style lang="less" scoped>
@media screen and (min-width: 1920px) {
  /deep/ .el-tabs {
    width: 85%;
  }
}
@media screen and (max-width: 1920px) and (min-width: 1680px) {
  /deep/ .el-tabs {
    width: 80%;
  }
}
@media screen and (max-width: 1680px) and (min-width: 1600px) {
  /deep/ .el-tabs {
    width: 75%;
  }
}
@media screen and (max-width: 1600px) and (min-width: 1366px) {
  /deep/ .el-tabs {
    width: 70%;
  }
}
@media screen and (max-width: 1366px) and (min-width: 1280px) {
  /deep/ .el-tabs {
    width: 65%;
  }
}
@media screen and (max-width: 1280px) and (min-width: 795px) {
  /deep/ .el-tabs {
    width: 60%;
  }
}
@media screen and (max-width: 795px) {
  /deep/ .el-tabs {
    width: 55%;
  }
}
/deep/ .el-tabs {
  width: 80%;
  height: 100%;
  float: left;
}

/deep/ .el-tabs__nav-wrap::after {
  position: static !important;
}

/deep/ .el-tabs__nav-scroll {
  margin-top: 5px;
}

/deep/ .el-tabs__item {
  color: #e0e6ed;
  font-size: 16px;
}

/deep/ .el-tabs__header {
  width: 100%;
  padding: 0;
  position: relative;
  // padding-left: 60px;
  box-sizing: border-box;
}
</style>
<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #304156;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    padding-right: 20px;
    box-sizing: border-box;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
      cursor: pointer;
      height: 100%;
      .avatar-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        height: 100%;
        .name {
          margin-left: 10px;
          color: white;
        }

        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          float: left;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          top: 50%;
          right: -20px;
          transform: translateY(-50%);
          font-size: 12px;
          color: white;
        }
      }
    }
  }
}
</style>
