<template>
  <div>
    <!-- <logo v-if="showLogo" :collapse="isCollapse" /> -->
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <!-- <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        /> -->
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
        <!-- <sidebar-item
          v-for="route in tabRoutes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        /> -->
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import Logo from './Logo'
import SidebarItem from "./SidebarItem";
import variables from "@admin/styles/variables.scss";
import store from "@admin/store";

export default {
  data() {
    return {
      // tabRoutes: [],
    };
  },
  components: { SidebarItem },
  created() {
    // setTimeout(() => {
    //   this.tabRoutes = this.$store.getters.tabRoutes;
    //   console.log(this.tabRoutes);
    // }, 1000);
    // console.log(this.$store.getters);
    // console.log(this.permission_routes);
    // console.log(this.routes);
  },
  computed: {
    ...mapGetters(["sidebar", "permission_routes"]),
    routes() {
      return this.$router.options.routes;
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
};
</script>
