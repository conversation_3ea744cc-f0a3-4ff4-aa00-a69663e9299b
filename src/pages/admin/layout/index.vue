<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
      </div>
      <div :class="{ 'fixed-header2': fixedHeader }">
        <!-- <navbar /> -->
        <NavTab />
      </div>

      <app-main />
      <div class="footer">
        <p class="fl">当前版本:{{ version }}</p>
        <!-- <p class="fl" style="margin-left: 30px;">{{AdminversionTitle}}</p> -->
      </div>
    </div>
  </div>
</template>

<script>
import { Navbar, NavTab, Sidebar, AppMain, Footer } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";

export default {
  name: "Layout",
  data() {
    return {
      version: window.g.version,
      // AdminversionTitle:window.g.AdminversionTitle
    };
  },
  components: {
    Navbar,
    NavTab,
    Sidebar,
    AppMain,
    Footer,
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    device() {
      return this.$store.state.app.device;
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@admin/styles/mixin.scss";
@import "@admin/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 300;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}
.fixed-header2 {
  position: fixed;
  top: 50px;
  right: 0;
  z-index: 300;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}

.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 50px;
  z-index: 1000;
  line-height: 50px;
  background: rgba(31 45 61 / 80%);
  padding-left: 2%;
  color: white;
  font-size: 14px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  box-shadow: 1px -3px 17px 0 rgb(0 0 0 / 10%);
}
</style>
