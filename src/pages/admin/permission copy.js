import router, { resetRouter } from './router/index'
import store from './store/index'
import axios from 'axios'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // 一个进度条的插件
import 'nprogress/nprogress.css' // progress bar style
import getPageTitle from '@admin/utils/get-page-title'

import { getFunction } from '@admin/api/Management/management'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] //  免登陆白名单
const that = this;
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()


  // 设置页面标题
  document.title = getPageTitle(to.meta.title)

  // 确定用户是否已登录
  const haslogin = store.state.user.isLogin
  await store.dispatch('user/judgelogin')
  if (haslogin) {
    if (!haslogin) {
      if (whiteList.indexOf(to.path) !== -1) {
        next()
      } else {
        // router.go(0);
        // next(`/login?redirect=${to.path}`)
        // router.push("/login")
        await store.dispatch('user/resetToken')
        NProgress.done()
      }
    } else {
      if (to.path === '/login') {
        // 如果已登录，则重定向到主页
        next({ path: '/' })
        NProgress.done()
      } else {
        const bool = store.getters.permission_routes && store.getters.permission_routes.length > 0;
        if (bool) {
          next()
        } else {
          try {
            if (store.state.user.roles) {
              const roles = store.state.user.roles;
              store.dispatch('permission/generateRoutes', sessionStorage.getItem("activeName"), roles).then((accessRoutes) => {
                router.addRoutes(accessRoutes);
                if (whiteList.indexOf(to.path) !== -1) {
                  next({ ...to, replace: true })
                } else {
                  next()
                }
              })
            } else {
              if (whiteList.indexOf(to.path) !== -1) {
                next()
              } else {
                await store.dispatch('user/resetToken')
                // next(`/login?redirect=${to.path}`)
                NProgress.done()
              }
            }
          } catch (error) {
            // // remove token and go to login page to re-login
            // await store.dispatch('user/resetToken')
            // Message.error(error || 'Has Error')
            // next(`/login?redirect=${to.path}`)
            // NProgress.done()
            if (whiteList.indexOf(to.path) !== -1) {
              next()
            } else {
              await store.dispatch('user/resetToken')
              // next(`/login?redirect=${to.path}`)
              NProgress.done()
            }
          }
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      await store.dispatch('user/resetToken')
      // next(`/login?redirect=${to.path}`)
      NProgress.done()
      // router.push("/login")
      // NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})


// 设置响应拦截
axios.interceptors.response.use(res => {
  if (res.data.code === 500) {
    store.commit('setIsLogin', false);
    sessionStorage.setItem("isLogin", false);
    next(`/login?redirect=${to.path}`)
    NProgress.done()
    return res
  }
  return res
}, error => { })