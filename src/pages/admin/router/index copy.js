import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)
/* Layout */
import Layout from '@admin/layout'

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@admin/views/login/index'),
    hidden: true
  },
  {
    path: '/thirdlogin',
    component: () => import('@admin/views/thirdlogin/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@admin/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'home',
        component: () => import('@admin/views/home/<USER>'),
        meta: {
          title: '主页',
          icon: 'el-icon-s-home'
        }
      }
    ]
  }
]

// export const router = new Router({
//     routes: constantRoutes.concat(errorRoutes)
// })

// 异步挂载的路由
// 动态需要根据权限加载的路由表
// 系统配置应用：Systemconfiguration  //系统应用：Systemapplication //综合统计：Comprehensivestatistics
export const asyncRoutes = [
  // 系统配置应用 开始
  {
    path: '/Systemconfiguration/User',
    name: 'User',
    component: Layout,
    system: 'Systemconfiguration',
    children: [
      {
        path: 'User',
        name: 'User',
        component: () => import('@admin/views/Systemconfiguration/User/index'),
        meta: {
          title: '人员组织架构'
        }
      }
    ]
  },
  {
    path: '/Systemconfiguration/SysConfig',
    name: 'SysConfig',
    component: Layout,
    system: 'Systemconfiguration',
    children: [
      {
        path: 'SysConfig',
        name: 'SysConfig',
        component: () =>
          import('@admin/views/Systemconfiguration/SysConfig/index'),
        meta: {
          title: '系统配置项管理'
        }
      }
    ]
  },
  {
    path: '/Systemconfiguration/DictTable',
    name: 'DictTable',
    component: Layout,
    system: 'Systemconfiguration',
    children: [
      {
        path: 'DictTable',
        name: 'DictTable',
        component: () =>
          import('@admin/views/Systemconfiguration/DictTable/index'),
        meta: {
          title: '字典管理'
        }
      }
    ]
  },
  {
    path: '/Systemconfiguration/Management',
    component: Layout,
    name: 'Management',
    system: 'Systemconfiguration',
    meta: {
      title: '权限管理'
    }, // 页面需要的权限
    children: [
      {
        path: 'application',
        name: 'application',
        component: () =>
          import(
            '@admin/views/Systemconfiguration/Management/application/index'
          ),
        meta: {
          title: '应用管理'
        }
      },
      {
        path: 'menu',
        name: 'Menu',
        component: () =>
          import('@admin/views/Systemconfiguration/Management/menu/index'),
        meta: {
          title: '菜单管理',
          role: ['staadmin', 'super_editor'],
          options: []
        } // 页面需要的权限
      },
      {
        path: 'role',
        name: 'role',
        component: () =>
          import('@admin/views/Systemconfiguration/Management/role/index'),
        meta: {
          title: '角色管理',
          role: ['staadmin', 'super_editor'],
          options: []
        } // 页面需要的权限
      },
      {
        path: 'administrator',
        name: 'administrator',
        component: () =>
          import(
            '@admin/views/Systemconfiguration/Management/administrator/index'
          ),
        meta: {
          title: '管理员管理',
          role: ['staadmin', 'super_editor'],
          options: []
        } // 页面需要的权限
      }
    ]
  },
  {
    path: '/Systemconfiguration/Customform',
    component: Layout,
    name: 'Customform',
    system: 'Systemconfiguration',
    meta: {
      title: '自定义表单'
    }, // 页面需要的权限
    children: [
      {
        path: 'form',
        name: 'form',
        component: () =>
          import('@admin/views/Systemconfiguration/Customform/form/index'),
        meta: {
          title: '表单管理'
        }
      },
      // {
      //   path: "waitform",
      //   name: "waitform",
      //   component: () =>
      //     import("@admin/views/Systemconfiguration/Customform/waitform/index"),
      //   meta: {
      //     title: "我的待办",
      //   },
      // },
      {
        path: 'formgroup',
        name: 'formgroup',
        component: () =>
          import('@admin/views/Systemconfiguration/Customform/formgroup/index'),
        meta: {
          title: '表单分组'
        }
      }
    ]
  },
  // 系统配置应用 结束
  // 系统应用 开始
  // 门户管理
  {
    path: '/Systemapplication/Gateway',
    name: 'Gateway',
    component: Layout,
    system: 'Systemapplication',
    meta: {
      title: '门户管理'
    },
    children: [
      {
        path: 'Banner',
        name: 'Banner',
        component: () => import('@admin/views/Systemapplication/Gateway/Banner/index'),
        meta: {
          title: 'Banner图管理'
        }
      },
      {
        path: 'News',
        name: 'News',
        component: () => import('@admin/views/Systemapplication/Gateway/News/index'),
        meta: {
          title: '新闻管理'
        }
      },
      {
        path: 'Video',
        name: 'Video',
        component: () => import('@admin/views/Systemapplication/Gateway/Video/index'),
        meta: {
          title: '视频管理'
        }
      },
      {
        path: 'Labcenter',
        name: 'Labcenter',
        component: () => import('@admin/views/Systemapplication/Gateway/Labcenter/index'),
        meta: {
          title: '实验中心管理'
        }
      }
    ]
  },
  // 系统应用 结束
]

export const errorRoutes = [
  // 404 页面必须放置在最后一个页面
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

// 创建路由
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    // base: '/Lab/',
    // mode: 'history',

    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes.concat(asyncRoutes).concat(errorRoutes)
    // routes: constantRoutes.concat(errorRoutes)
  })

export const router = createRouter()

// 重置路由
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
