import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)
/* Layout */
import Layout from '@admin/layout'

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@admin/views/login/index'),
    hidden: true
  },
  {
    path: '/thirdlogin',
    component: () => import('@admin/views/thirdlogin/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@admin/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'home',
        component: () => import('@admin/views/home/<USER>'),
        meta: {
          title: '主页',
          icon: 'el-icon-s-home'
        }
      }
    ]
  }
]

// export const router = new Router({
//     routes: constantRoutes.concat(errorRoutes)
// })

// 异步挂载的路由
// 动态需要根据权限加载的路由表
// 系统配置应用：Systemconfiguration  //系统应用：Systemapplication //综合统计：Comprehensivestatistics
export const asyncRoutes = [
  // 系统配置应用 开始
  {
    path: '/Systemconfiguration/User',
    name: 'User',
    component: Layout,
    system: 'Systemconfiguration',
    children: [
      {
        path: 'User',
        name: 'User',
        component: () => import('@admin/views/Systemconfiguration/User/index'),
        meta: {
          title: '人员组织架构'
        }
      }
    ]
  },
  {
    path: '/Systemconfiguration/SysConfig',
    name: 'SysConfig',
    component: Layout,
    system: 'Systemconfiguration',
    children: [
      {
        path: 'SysConfig',
        name: 'SysConfig',
        component: () =>
          import('@admin/views/Systemconfiguration/SysConfig/index'),
        meta: {
          title: '系统配置项管理'
        }
      }
    ]
  },
  {
    path: '/Systemconfiguration/DictTable',
    name: 'DictTable',
    component: Layout,
    system: 'Systemconfiguration',
    children: [
      {
        path: 'DictTable',
        name: 'DictTable',
        component: () =>
          import('@admin/views/Systemconfiguration/DictTable/index'),
        meta: {
          title: '字典管理'
        }
      }
    ]
  },
  {
    path: '/Systemconfiguration/Management',
    component: Layout,
    name: 'Management',
    system: 'Systemconfiguration',
    meta: {
      title: '权限管理'
    }, // 页面需要的权限
    children: [
      {
        path: 'application',
        name: 'application',
        component: () =>
          import(
            '@admin/views/Systemconfiguration/Management/application/index'
          ),
        meta: {
          title: '应用管理'
        }
      },
      {
        path: 'menu',
        name: 'Menu',
        component: () =>
          import('@admin/views/Systemconfiguration/Management/menu/index'),
        meta: {
          title: '菜单管理',
          role: ['staadmin', 'super_editor'],
          options: []
        } // 页面需要的权限
      },
      {
        path: 'role',
        name: 'role',
        component: () =>
          import('@admin/views/Systemconfiguration/Management/role/index'),
        meta: {
          title: '角色管理',
          role: ['staadmin', 'super_editor'],
          options: []
        } // 页面需要的权限
      },
      {
        path: 'administrator',
        name: 'administrator',
        component: () =>
          import(
            '@admin/views/Systemconfiguration/Management/administrator/index'
          ),
        meta: {
          title: '管理员管理',
          role: ['staadmin', 'super_editor'],
          options: []
        } // 页面需要的权限
      }
    ]
  },
  // 系统配置应用 结束
  // 系统应用 开始
  // 实验室与设备管理
  {
    path: '/Dailymanagement',
    name: 'Dailymanagement',
    hidden: true,
    component: Layout,
    meta: { title: '日常管理' },
    children: [
      {
        path: 'menu',
        name: 'menu',
        component: () => import('@admin/views/Dailymanagement/menu/index'),
        meta: { title: '导航栏菜单管理' }
      },
      // {
      //   path: 'banner',
      //   name: 'banner',
      //   component: () => import('@admin/views/Dailymanagement/banner/index'),
      //   meta: { title: 'banner管理' }
      // },
      // {
      //   path: 'news',
      //   name: 'news',
      //   component: () => import('@admin/views/Dailymanagement/News/index'),
      //   meta: { title: '工作动态/通知公告' }
      // },
      {
        path: 'banner',
        name: 'banner',
        component: () => import('@admin/views/Dailymanagement/banner/index'),
        meta: {
          title: 'Banner图管理'
        }
      },
      {
        path: 'news',
        name: 'news',
        component: () => import('@admin/views/Dailymanagement/news/index'),
        meta: {
          title: '新闻管理'
        }
      },
      {
        path: 'dailyService',
        name: 'dailyService',
        component: () => import('@admin/views/Dailymanagement/dailyService/index'),
        meta: { title: '日常服务' }
      },
      {
        path: 'friendLink',
        name: 'friendLink',
        component: () => import('@admin/views/Dailymanagement/friendLink/index'),
        meta: { title: '友情链接' }
      },
      {
        path: 'homeBottom',
        name: 'homeBottom',
        component: () => import('@admin/views/Dailymanagement/homeBottom/index'),
        meta: { title: '首页底部' }
      },
      // {
      //   path: 'SiteSettings',
      //   name: 'SiteSettings',
      //   component: () => import('@admin/views/Dailymanagement/SiteSettings/index'),
      //   meta: { title: '网站设置' }
      // },
    ]
  },
  // 系统应用 结束
]

export const errorRoutes = [
  // 404 页面必须放置在最后一个页面
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

// 创建路由
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    // base: '/Lab/',
    // mode: 'history',

    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes.concat(asyncRoutes).concat(errorRoutes)
    // routes: constantRoutes.concat(errorRoutes)
  })

export const router = createRouter()

// 重置路由
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
