import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

// 获取用户权限
import createPersistedState from 'vuex-persistedstate'
 let userInfo = {}
 try{
   if(sessionStorage.userInfo){
     userInfo = JSON.parse(sessionStorage.userInfo)
   }
 } catch(e){}
 let isLogin = false
 try{
   if(sessionStorage.isLogin){
     isLogin = JSON.parse(sessionStorage.isLogin)
   }
 } catch(e){}

export default new Vuex.Store({
  state: {
    // 是否登录
    isLogin: isLogin,
    //roleId
    roleId:roleId,
    trueName:trueName,
    // 记录用户创建的预约信息
    newResearch:{},
    activeTab:"",//顶部应用Tab标签选择
    tabRoutes:[]
  },
  mutations: {
    setIsLogin(state,val){
      state.isLogin = val
      sessionStorage.setItem('isLogin', val)
    },
    setroleId(state,val){
      state.roleId = val
    },
    settrueName(state,val){
      state.trueName = val
      sessionStorage.setItem('trueName', val)
    },
    'SET_CONFIG'(state, val){
      return state.config = val
    },
    setNewResearch(state,val){
      state.newResearch = val
    },
    setactiveTab(state,val){
      state.activeTab = val
      sessionStorage.setItem('activeTab', val)
    },
  },
  actions: {
  },
  plugins:[createPersistedState({
    storage: window.sessionStorage,
    reducer(val){
      return {
        
      }
    }
  })]
})