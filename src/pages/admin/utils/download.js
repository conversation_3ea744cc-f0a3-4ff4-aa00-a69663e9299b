/**
 * 下载工具函数集合
 */

/**
 * 通过URL下载文件
 * @param {Object} options - 下载配置项
 * @param {string} options.url - 文件URL路径（可以是相对路径）
 * @param {string} options.fileName - 下载后的文件名
 * @param {string} [options.baseUrl] - 基础URL，默认为当前站点API路径
 * @returns {Promise<void>}
 */
export function downloadFile ({ url, fileName, baseUrl }) {
  return new Promise((resolve, reject) => {
    try {
      if (!url) {
        throw new Error('下载链接不存在');
      }

      // 如果没有提供baseUrl，则使用当前站点的API路径
      const defaultBaseUrl = window.location.protocol + "//" + window.location.host + (window.g?.ApiUrl || '');
      const baseUrlToUse = baseUrl || defaultBaseUrl;

      // 处理URL，确保它是完整的
      const downloadUrl = baseUrlToUse + url;

      // 创建一个隐形的a标签进行下载
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = downloadUrl;
      link.setAttribute("download", fileName);

      // 添加到body，触发点击，然后移除
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      resolve();
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 下载文件并处理错误
 * @param {Object} options - 下载配置项
 * @param {string} options.url - 文件URL路径
 * @param {string} options.fileName - 下载后的文件名
 * @param {string} [options.baseUrl] - 基础URL，默认为当前站点API路径
 * @param {Function} [options.onError] - 错误处理函数，默认使用Element UI的Message组件
 */
export async function safeDownloadFile (options) {
  try {
    await downloadFile(options);
  } catch (error) {
    // 默认错误处理，如果项目使用了Element UI
    if (window.ELEMENT && ELEMENT.Message) {
      ELEMENT.Message.error(error.message || '下载失败');
    } else if (options.onError) {
      options.onError(error);
    } else {
      console.error('下载失败:', error);
      alert(error.message || '下载失败');
    }
  }
}