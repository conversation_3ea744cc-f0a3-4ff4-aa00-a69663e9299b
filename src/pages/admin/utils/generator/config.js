import { getToken } from '@admin/utils/auth'
const bodyWidth = document.body.clientWidth
// 表单属性【右面板】
export const formConf = {
  formRef: 'elForm',
  formModel: 'formData',
  size: 'medium',
  labelPosition: 'right',
  labelWidth: 100,
  formRules: 'rules',
  gutter: 15,
  disabled: false,
  span: 24,
  formBtns: true,
  confirmText: '提交'
}

// 输入型组件 【左面板】
export const inputComponents = [
  {
    // 组件的自定义配置
    __config__: {
      label: '单行文本',
      labelWidth: null,
      showLabel: true,
      changeTag: true,
      tag: 'el-input',
      tagIcon: 'input',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 组件的插槽属性
    __slot__: {
      prepend: '',
      append: ''
    },
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    types: 'input',
    showWeb: false,
    audit: false
  },
  {
    __config__: {
      label: '多行文本',
      labelWidth: null,
      showLabel: true,
      tag: 'el-input',
      tagIcon: 'textarea',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      regList: [],
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/input'
    },
    type: 'textarea',
    placeholder: '请输入',
    autosize: {
      minRows: 4,
      maxRows: 4
    },
    style: { width: '100%' },
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    types: 'input',
    showWeb: false,
    audit: false
  },
  {
    __config__: {
      label: '密码',
      showLabel: true,
      labelWidth: null,
      changeTag: true,
      tag: 'el-input',
      tagIcon: 'password',
      defaultValue: undefined,
      layout: 'colFormItem',
      span: 24,
      required: true,
      regList: [],
      document: 'https://element.eleme.cn/#/zh-CN/component/input'
    },
    __slot__: {
      prepend: '',
      append: ''
    },
    placeholder: '请输入',
    'show-password': true,
    style: { width: '100%' },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    audit: false
  },
  {
    __config__: {
      label: '计数器',
      showLabel: true,
      changeTag: true,
      labelWidth: null,
      tag: 'el-input-number',
      tagIcon: 'number',
      defaultValue: undefined,
      span: 24,
      layout: 'colFormItem',
      required: true,
      regList: [],
      document: 'https://element.eleme.cn/#/zh-CN/component/input-number'
    },
    placeholder: '',
    min: undefined,
    max: undefined,
    step: 1,
    'step-strictly': false,
    precision: undefined,
    'controls-position': '',
    disabled: false,
    show: true,
    showType: 'number',
    columnType: 'NUMBER',
    types: 'input',
    showWeb: false,
    audit: false
  },
  {
    // 组件的自定义配置
    __config__: {
      label: '超链接',
      labelWidth: null,
      showLabel: true,
      changeTag: true,
      tag: 'el-link',
      tagIcon: 'link',
      defaultValue: '超链接',
      layout: 'colLink',
      href: 'http://www.baidu.com',
      target: '_blank',
      span: 24,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    audit: false
  }
]

// 选择型组件 【左面板】
export const selectComponents = [
  {
    __config__: {
      label: '下拉选择',
      showLabel: true,
      labelWidth: null,
      tag: 'el-select',
      tagIcon: 'select',
      layout: 'colFormItem',
      dataConsumer: '__slot__.options',
      dataType: 'dynamic',
      url: '',
      method: 'get',
      dataKey: 'list',
      span: 24,
      required: true,
      showPop: false,
      regList: [],
      changeTag: true,
      export: false,
      document: 'https://element.eleme.cn/#/zh-CN/component/select'
    },
    __slot__: {
      options: [
        {
          label: '选项一',
          value: 1
        },
        {
          label: '选项二',
          value: 2
        }
      ]
    },
    props: {
      props: {
        label: 'label',
        value: 'value'
      }
    },
    'popper-class': 'cascader-popper',
    placeholder: '请选择',
    style: { width: '100%' },
    clearable: true,
    disabled: false,
    filterable: false,
    multiple: false,
    show: true,
    showType: 'select',
    columnType: 'STRING',
    types: 'select',
    showWeb: false,
    audit: false,
    on: {
      focus: 'focus'
    }
  },
  {
    __config__: {
      label: '级联选择',
      url: 'https://www.fastmock.site/mock/f8d7a54fb1e60561e2f720d5a810009d/fg/cascaderList',
      method: 'get',
      dataKey: 'list',
      dataConsumer: 'options',
      showLabel: true,
      labelWidth: null,
      tag: 'el-cascader',
      tagIcon: 'cascader',
      layout: 'colFormItem',
      defaultValue: [],
      dataType: 'dynamic',
      span: 24,
      required: true,
      showPop: false,
      regList: [],
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/cascader'
    },
    options: [
      {
        id: 1,
        value: 1,
        label: '选项1',
        children: [
          {
            id: 2,
            value: 2,
            label: '选项1-1'
          }
        ]
      }
    ],
    placeholder: '请选择',
    style: { width: '100%' },
    props: {
      props: {
        multiple: false,
        label: 'label',
        value: 'value',
        children: 'children'
      }
    },
    'popper-class': 'cascader-popper',
    'show-all-levels': false,
    disabled: false,
    clearable: true,
    filterable: false,
    show: true,
    separator: '/',
    showType: 'select',
    columnType: 'ARRAY',
    types: 'select',
    showWeb: false,
    audit: false,
    on: {
      'visible-change': 'visible-change'
    }
  },
  {
    __config__: {
      label: '单选框组',
      labelWidth: null,
      showLabel: true,
      tag: 'el-radio-group',
      tagIcon: 'radio',
      changeTag: true,
      dataConsumer: '__slot__.options',
      defaultValue: undefined,
      layout: 'colFormItem',
      span: 24,
      optionType: 'default',
      dataType: 'dynamic',
      url: 'https://www.fastmock.site/mock/f8d7a54fb1e60561e2f720d5a810009d/fg/cascaderList',
      method: 'get',
      dataKey: 'list',
      regList: [],
      required: true,
      border: false,
      document: 'https://element.eleme.cn/#/zh-CN/component/radio'
    },
    __slot__: {
      options: [
        {
          label: '选项一',
          value: 1
        },
        {
          label: '选项二',
          value: 2
        }
      ]
    },
    props: {
      props: {
        label: 'label',
        value: 'value'
      }
    },
    style: {},
    size: 'medium',
    show: true,
    disabled: false,
    showType: 'select',
    columnType: 'STRING',
    types: 'select',
    showWeb: false,
    audit: false
  },
  {
    __config__: {
      label: '多选框组',
      tag: 'el-checkbox-group',
      tagIcon: 'checkbox',
      defaultValue: [],
      span: 24,
      showLabel: true,
      labelWidth: null,
      layout: 'colFormItem',
      optionType: 'default',
      dataConsumer: '__slot__.options',
      dataType: 'dynamic',
      url: 'https://www.fastmock.site/mock/f8d7a54fb1e60561e2f720d5a810009d/fg/cascaderList',
      method: 'get',
      dataKey: 'list',
      required: true,
      regList: [],
      changeTag: true,
      border: false,
      document: 'https://element.eleme.cn/#/zh-CN/component/checkbox'
    },
    __slot__: {
      options: [
        {
          label: '选项一',
          value: 1
        },
        {
          label: '选项二',
          value: 2
        }
      ]
    },
    props: {
      props: {
        label: 'label',
        value: 'value'
      }
    },
    style: {},
    size: 'medium',
    min: null,
    max: null,
    disabled: false,
    show: true,
    showType: 'checkBox',
    columnType: 'ARRAY',
    types: 'select',
    showWeb: false,
    audit: false
  },
  {
    __config__: {
      label: '开关',
      tag: 'el-switch',
      tagIcon: 'switch',
      defaultValue: false,
      span: 24,
      showLabel: true,
      labelWidth: null,
      layout: 'colFormItem',
      required: true,
      relevance: false,
      relevanceTrue: [],
      relevanceFalse: [],
      relevanceTrueOptions: [],
      relevanceFalseOptions: [],
      regList: [],
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/switch'
    },
    style: {},
    disabled: false,
    show: true,
    'active-text': '',
    'inactive-text': '',
    'active-color': null,
    'inactive-color': null,
    'active-value': true,
    'inactive-value': false,
    showType: 'input',
    columnType: 'BOOLEAN',
    types: 'switch',
    showWeb: false,
    auditMsg: false,
    auditMsgValue: '',
    audit: false,
    on: {
      change: 'change'
    }
  },
  {
    __config__: {
      label: '滑块',
      tag: 'el-slider',
      tagIcon: 'slider',
      defaultValue: null,
      span: 24,
      showLabel: true,
      layout: 'colFormItem',
      labelWidth: null,
      required: true,
      regList: [],
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/slider'
    },
    disabled: false,
    min: 0,
    max: 100,
    step: 1,
    'show-stops': false,
    range: false,
    show: true,
    audit: false,
    showType: 'input',
    columnType: 'NUMBER'
  },
  {
    __config__: {
      label: '时间选择',
      tag: 'el-time-select',
      tagIcon: 'time',
      defaultValue: null,
      span: 24,
      showLabel: true,
      layout: 'colFormItem',
      labelWidth: null,
      required: true,
      regList: [],
      changeTag: true,
      showPop: false,
      $el: '',
      document: 'https://element.eleme.cn/#/zh-CN/component/time-picker'
    },
    placeholder: '请选择',
    style: { width: '100%' },
    disabled: false,
    show: true,
    clearable: true,
    'picker-options': {
      start: '00:00',
      end: '23:59',
      step: '00:01'
    },
    'popper-class': 'cascader-popper',
    showType: 'select',
    columnType: 'STRING',
    types: 'input',
    showWeb: false,
    audit: false,
    on: {
      focus: 'focus',
      change: 'change'
    }
  },
  //   {
  //     __config__: {
  //       label: '时间选择',
  //       tag: 'el-time-picker',
  //       tagIcon: 'time',
  //       defaultValue: null,
  //       span: 24,
  //       showLabel: true,
  //       layout: 'colFormItem',
  //       labelWidth: null,
  //       required: true,
  //       regList: [],
  //       changeTag: true,
  //       showPop: false,
  //       document: 'https://element.eleme.cn/#/zh-CN/component/time-picker'
  //     },
  //     placeholder: '请选择',
  //     style: {width: '100%'},
  //     disabled: false,
  //     clearable: true,
  //     'picker-options': {
  //       selectableRange: '00:00:00-23:59:59',
  //     },
  //     "popper-class": "cascader-popper",
  //     format: 'HH:mm',
  //     'value-format': 'HH:mm',
  //     step: '1',
  //     showType: "select",
  //     columnType: "STRING",
  //     on: {
  //         "focus": "focus",
  //     }
  //   },
  //   {
  //     __config__: {
  //       label: '时间范围',
  //       tag: 'el-time-picker',
  //       tagIcon: 'time-range',
  //       span: 24,
  //       showLabel: true,
  //       labelWidth: null,
  //       layout: 'colFormItem',
  //       defaultValue: null,
  //       required: true,
  //       regList: [],
  //       changeTag: true,
  //       document: 'https://element.eleme.cn/#/zh-CN/component/time-picker'
  //     },
  //     style: {width: '100%'},
  //     disabled: false,
  //     clearable: true,
  //     'is-range': true,
  //     'range-separator': '至',
  //     'start-placeholder': '开始时间',
  //     'end-placeholder': '结束时间',
  //     format: 'HH:mm:ss',
  //     'value-format': 'HH:mm:ss',
  //     showType: "select",
  //     columnType: "ARRAY"
  //   },
  {
    __config__: {
      label: '日期选择',
      tag: 'el-date-picker',
      tagIcon: 'date',
      defaultValue: null,
      showLabel: true,
      labelWidth: null,
      span: 24,
      layout: 'colFormItem',
      required: true,
      regList: [],
      showPop: false,
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/date-picker'
    },
    placeholder: '请选择',
    type: 'date',
    style: { width: '100%' },
    disabled: false,
    clearable: true,
    format: 'yyyy-MM-dd',
    'value-format': 'yyyy-MM-dd',
    readonly: false,
    show: true,
    showType: 'select',
    'popper-class': 'cascader-popper',
    columnType: 'STRING',
    types: 'input',
    showWeb: false,
    audit: false,
    on: {
      focus: 'focus'
    }
  },
  //   {
  //     __config__: {
  //       label: '日期范围',
  //       tag: 'el-date-picker',
  //       tagIcon: 'date-range',
  //       defaultValue: null,
  //       span: 24,
  //       showLabel: true,
  //       labelWidth: null,
  //       required: true,
  //       layout: 'colFormItem',
  //       regList: [],
  //       changeTag: true,
  //       document: 'https://element.eleme.cn/#/zh-CN/component/date-picker'
  //     },
  //     style: {width: '100%'},
  //     type: 'daterange',
  //     'range-separator': '至',
  //     'start-placeholder': '开始日期',
  //     'end-placeholder': '结束日期',
  //     disabled: false,
  //     clearable: true,
  //     format: 'yyyy-MM-dd',
  //     'value-format': 'yyyy-MM-dd',
  //     readonly: false,
  //     showType: "select",
  //     columnType: "ARRAY"
  //   },
  {
    __config__: {
      label: '评分',
      tag: 'el-rate',
      tagIcon: 'rate',
      defaultValue: 0,
      span: 24,
      showLabel: true,
      labelWidth: null,
      layout: 'colFormItem',
      required: true,
      regList: [],
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/rate'
    },
    style: {},
    max: 5,
    'allow-half': false,
    'show-text': false,
    'show-score': false,
    disabled: false,
    show: true,
    audit: false,
    showType: 'input',
    columnType: 'NUMBER'
  },
  {
    __config__: {
      label: '上传',
      tag: 'el-upload',
      tagIcon: 'upload',
      layout: 'colFormItem',
      defaultValue: null,
      showLabel: true,
      labelWidth: null,
      required: true,
      span: 24,
      showTip: false,
      buttonText: '点击上传',
      regList: [],
      changeTag: true,
      fileSize: 2,
      limit: '',
      sizeUnit: 'MB',
      document: 'https://element.eleme.cn/#/zh-CN/component/upload'
    },
    __slot__: {
      'list-type': true
    },
    action: window.g.ApiUrl + '/system/dev/file/info/add',
    disabled: false,
    accept: '',
    name: 'file',
    'auto-upload': true,
    'list-type': 'text',
    multiple: false,
    // limit: '',
    show: true,
    audit: false,
    showType: 'input',
    columnType: 'STRING',
    // headers: { token: getToken() },
    headers: { 'Uni-Access-Token': localStorage.getItem('token') }
  },
  {
    __config__: {
      label: '下载附件',
      tag: 'el-upload',
      tagIcon: 'file-upload',
      layout: 'colFormItem',
      defaultValue: null,
      showLabel: true,
      labelWidth: null,
      required: false,
      span: 24,
      showTip: false,
      buttonText: '上传附件',
      changeTag: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/upload'
    },
    __slot__: {
      'list-type': false
    },
    name: 'file',
    'auto-upload': true,
    multiple: false,
    limit: 1,
    show: true,
    audit: false,
    type: 'el-upload',
    showType: 'input',
    columnType: 'STRING'
  }
]

// 布局型组件 【左面板】
export const layoutComponents = [
  {
    __config__: {
      layout: 'rowFormItem',
      tagIcon: 'row',
      label: '行容器',
      layoutTree: true,
      document:
        'https://element.eleme.cn/#/zh-CN/component/layout#row-attributes'
    },
    type: 'default',
    justify: 'start',
    align: 'top'
  }
]

// 套件 【左面板】
export const nestComponents = [
  {
    __config__: {
      layout: 'rowFormItem',
      tagIcon: 'row',
      label: '请假套件',
      layoutTree: true,
      document:
        'https://element.eleme.cn/#/zh-CN/component/layout#row-attributes',
      nest: true,
      children: [
        {
          __config__: {
            label: '请假日期',
            tag: 'el-date-picker',
            tagIcon: 'date-range',
            defaultValue: null,
            span: 24,
            showLabel: true,
            labelWidth: null,
            required: true,
            layout: 'colFormItem',
            regList: [],
            changeTag: true,
            document: 'https://element.eleme.cn/#/zh-CN/component/date-picker',
            nest: true
          },
          __vModel__: 'time',
          style: { width: '100%' },
          type: 'daterange',
          'range-separator': '至',
          'start-placeholder': '开始日期',
          'end-placeholder': '结束日期',
          disabled: false,
          clearable: true,
          format: 'yyyy-MM-dd',
          'value-format': 'yyyy-MM-dd',
          readonly: false,
          showType: 'select',
          columnType: 'ARRAY'
        },
        {
          __config__: {
            label: '请假理由',
            labelWidth: null,
            showLabel: true,
            tag: 'el-input',
            tagIcon: 'textarea',
            defaultValue: undefined,
            required: true,
            layout: 'colFormItem',
            span: 24,
            regList: [],
            changeTag: true,
            document: 'https://element.eleme.cn/#/zh-CN/component/input',
            nest: true
          },
          __vModel__: 'memo',
          type: 'textarea',
          placeholder: '请输入',
          autosize: {
            minRows: 4,
            maxRows: 4
          },
          style: { width: '100%' },
          maxlength: null,
          'show-word-limit': false,
          readonly: false,
          disabled: false,
          showType: 'input',
          columnType: 'STRING'
        }
      ]
    },
    type: 'nest',
    justify: 'start',
    align: 'top'
  }
]
// 标题 【左面板】
export const titleComponents = [
  {
    // 组件的自定义配置
    __config__: {
      label: '标题',
      labelWidth: null,
      showLabel: true,
      tag: 'el-input',
      tagIcon: 'input',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      title: true,
      // 正则校验规则
      regList: []
    },
    __vModel__: 'unifound_title',
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    type: 'title',
    columnType: 'STRING',
    showType: 'input',
    types: 'input',
    showWeb: false,
    audit: false
  }
]
export const fixedComponents = [
  {
    // 组件的自定义配置
    __config__: {
      label: '退款金额',
      labelWidth: null,
      showLabel: true,
      changeTag: false,
      tag: 'el-input',
      tagIcon: 'input',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      title: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 组件的插槽属性
    __slot__: {
      prepend: '',
      append: ''
    },
    __vModel__: 'unifound_refundAmount',
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    types: 'input',
    type: 'fixed',
    showWeb: false,
    audit: false
  },
  {
    // 组件的自定义配置
    __config__: {
      label: '系统价格',
      labelWidth: null,
      showLabel: true,
      changeTag: false,
      tag: 'el-input',
      tagIcon: 'input',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      title: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 组件的插槽属性
    __slot__: {
      prepend: '',
      append: ''
    },
    __vModel__: 'unifound_payableAmount',
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    types: 'input',
    type: 'fixed3',
    showWeb: false,
    audit: false
  },
  {
    // 组件的自定义配置
    __config__: {
      label: '实付金额',
      labelWidth: null,
      showLabel: true,
      changeTag: false,
      tag: 'el-input',
      tagIcon: 'input',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      title: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 组件的插槽属性
    __slot__: {
      prepend: '',
      append: ''
    },
    __vModel__: 'unifound_paidInAmount',
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    types: 'input',
    type: 'fixed1',
    showWeb: false,
    audit: false
  },
  {
    // 组件的自定义配置
    __config__: {
      label: '退款阶梯',
      labelWidth: null,
      showLabel: true,
      changeTag: false,
      tag: 'el-input',
      tagIcon: 'input',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      title: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 组件的插槽属性
    __slot__: {
      prepend: '',
      append: ''
    },
    __vModel__: 'unifound_refundLadder',
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'input',
    columnType: 'STRING',
    types: 'input',
    type: 'fixed2',
    showWeb: false,
    audit: false
  },
  {
    // 组件的自定义配置
    __config__: {
      label: '备注',
      labelWidth: null,
      showLabel: true,
      changeTag: false,
      tag: 'el-input',
      tagIcon: 'textarea',
      defaultValue: undefined,
      required: true,
      layout: 'colFormItem',
      span: 24,
      title: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/input',
      // 正则校验规则
      regList: []
    },
    // 组件的插槽属性
    __slot__: {
      prepend: '',
      append: ''
    },
    __vModel__: 'unifound_memo',
    // 其余的为可直接写在组件标签上的属性
    placeholder: '请输入',
    style: { width: '100%' },
    autosize: {
      minRows: 4,
      maxRows: 4
    },
    clearable: true,
    'prefix-icon': '',
    'suffix-icon': '',
    maxlength: null,
    'show-word-limit': false,
    readonly: false,
    disabled: false,
    show: true,
    showType: 'textarea',
    columnType: 'STRING',
    types: 'fixed4',
    type: 'textarea',
    showWeb: false,
    audit: false
  },
  {
    __config__: {
      label: '上传',
      tag: 'el-upload',
      tagIcon: 'upload',
      layout: 'colFormItem',
      defaultValue: null,
      showLabel: true,
      labelWidth: null,
      required: true,
      span: 24,
      showTip: false,
      buttonText: '点击上传',
      regList: [],
      changeTag: false,
      fileSize: 2,
      limit: '',
      sizeUnit: 'MB',
      title: true,
      document: 'https://element.eleme.cn/#/zh-CN/component/upload',
      tips: ''
    },
    __slot__: {
      'list-type': true
    },
    __vModel__: 'unifound_upload',
    action: window.g.ApiUrl + '/system/dev/file/info/add',
    disabled: false,
    accept: '',
    name: 'file',
    'auto-upload': true,
    'list-type': 'text',
    multiple: false,
    show: true,
    auditSuccess: false,
    showType: 'input',
    type: 'fixed5',
    columnType: 'STRING',
    headers: { 'Uni-Access-Token': localStorage.getItem('token') }
  }
]
