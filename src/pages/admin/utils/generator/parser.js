import { deepClone } from '@admin/utils/index'
import { getToken } from '@admin/utils/auth'
import render from '@admin/utils/generator/render'
import axios from 'axios'
import Vue from 'vue'
const bodyWidth = document.body.clientWidth

Vue.prototype.$axios = axios

const ruleTrigger = {
  'el-input': 'blur',
  'el-input-number': 'blur',
  'el-select': 'change',
  'el-radio-group': 'change',
  'el-checkbox-group': 'change',
  'el-cascader': 'change',
  'el-time-picker': 'change',
  'el-date-picker': 'change',
  'el-rate': 'change',
  'el-upload': 'change'
}
function setRootFontSize(width) {
  return width / 192
}
const layouts = {
  colFormItem(h, scheme) {
    const config = scheme.__config__
    const listeners = buildListeners.call(this, scheme)

    let labelWidth = config.labelWidth ? `${config.labelWidth}px` : null
    const labelFontSize = config.labelFontSize ? `${config.labelFontSize}px` : null
    const labelFontWeight = config.labelFontWeight ? `${config.labelFontWeight}` : null
    if (config.showLabel === false) labelWidth = '0'
    if (this.formRequire.indexOf(scheme.__vModel__) != -1) { // 是必填项，关连了流程条件
      return (
        <el-col span={config.span}>
          <el-form-item label-width={labelWidth} prop={scheme.__vModel__}
            label={config.showLabel ? config.label : ''} style={scheme.show === undefined || scheme.show ? '' : 'display: none;'}>
            <div slot='label' style={`display: inline;${labelFontSize ? ('font-size:' + labelFontSize + ';') : ''}${labelFontWeight ? ('font-weight:' + labelFontWeight + ';') : ''}`}>{config.showLabel ? config.label : ''}</div>
            <render conf={scheme} on={listeners} oninput={() => { scheme.__config__.defaultValue !== '' && scheme.__config__.defaultValue !== undefined ? this.$emit('change', scheme) : '' }}
              onvisible-change={event => {
                // 级联选择器
                if (event) {
                  this.$set(config, 'showPop', true)
                } else {
                  // this.$set(config, 'showPop', false)
                }
              }}
              onfocus={event => {
                // 时间选择器
                if (event && config.tag == 'el-time-select') {
                  this.$set(config, 'showPop', true)
                  event.$el.children[0].setAttribute('readonly', 'readonly')
                  // event.$el.children[0].setAttribute("readonly", "readonly")
                  // this.$set(config, '$el', event.$el.children[0])
                }
                // 日期选择器
                if (event && config.tag == 'el-date-picker') {
                  this.$set(config, 'showPop', true)
                  event.$el.children[0].setAttribute('readonly', 'readonly')
                }
                // 下拉选择器
                if (event && config.tag == 'el-select') {
                  this.$set(config, 'showPop', true)
                  event.target.setAttribute('readonly', 'readonly')
                }
              }}
              onchange={event => {
                // 开关
                if (config.tag == 'el-switch') {
                  var arr = [...config.relevanceTrue, ...config.relevanceFalse]
                  arr.map((item, index) => {
                    this.formConfCopy.fields.map((item1, index1) => {
                      if (item1.__vModel__ == item) {
                        this.formConfCopy.fields[index1].show = false
                        this.rules[item1.__vModel__].map((item2, index2) => {
                          if (item2.required != undefined) {
                            this.rules[item1.__vModel__][index2].required = false
                          }
                        })
                      }
                    })
                  })
                  if (event) {
                    config.relevanceTrue.map((item, index) => {
                      this.formConfCopy.fields.map((item1, index1) => {
                        if (item1.__vModel__ == item) {
                          this.formConfCopy.fields[index1].show = true
                          this.rules[item1.__vModel__].map((item2, index2) => {
                            if (item2.required != undefined) {
                              this.rules[item1.__vModel__][index2].required = true
                            }
                          })
                        }
                      })
                    })
                  } else {
                    config.relevanceFalse.map((item, index) => {
                      this.formConfCopy.fields.map((item1, index1) => {
                        if (item1.__vModel__ == item) {
                          this.formConfCopy.fields[index1].show = true
                          this.rules[item1.__vModel__].map((item2, index2) => {
                            if (item2.required != undefined) {
                              this.rules[item1.__vModel__][index2].required = true
                            }
                          })
                        }
                      })
                    })
                  }
                }
                // 时间选择器
                // if(config.tag == "el-time-select"){
                //     config.$el.removeAttribute("readonly", "readonly")
                // }
              }} />
          </el-form-item>
          {
            bodyWidth < 600 && config.tag == 'el-cascader'
              ? h('van-popup', {
                props: {
                  value: config.showPop,
                  round: true,
                  position: 'bottom'
                },
                on: {
                  input: (val) => {
                    scheme.__config__.showPop = false
                  }
                }
              }, [
                h('van-cascader', {
                  props: {
                    title: scheme.placeholder,
                    'field-names': {
                      text: scheme.props.props.label,
                      value: scheme.props.props.value,
                      children: scheme.props.props.children
                    },
                    options: scheme.options,
                    value: config.defaultValue
                  },
                  on: {
                    close: (val) => {
                      scheme.__config__.showPop = false
                    },
                    finish: (val) => {
                      this[this.formConfCopy.formModel][scheme.__vModel__] = val.value
                      scheme.__config__.defaultValue = val.value
                      scheme.__config__.showPop = false
                    }
                  }
                })
              ]) : ''
          }
          {/* 移动端日期选择器 */}
          {
            bodyWidth < 600 && config.tag == 'el-date-picker'
              ? h('van-popup', {
                props: {
                  value: config.showPop,
                  round: true,
                  position: 'bottom'
                },
                on: {
                  input: (val) => {
                    scheme.__config__.showPop = false
                  }
                }
              }, [
                h('van-datetime-picker', {
                  props: {
                    title: scheme.placeholder,
                    type: scheme.type == 'month' ? 'year-month' : scheme.type,
                    value: config.defaultValue
                  },
                  on: {
                    close: (val) => {
                      console.log(val)
                      scheme.__config__.showPop = false
                    },
                    confirm: (val) => {
                      console.log(val)
                      this[this.formConfCopy.formModel][scheme.__vModel__] = new Date(val.toLocaleString('zh', { hour12: false })).Format(scheme['value-format'])
                      scheme.__config__.defaultValue = new Date(val.toLocaleString('zh', { hour12: false })).Format(scheme['value-format'])
                      scheme.__config__.showPop = false
                    }
                  }
                })
              ]) : ''
          }
          {/* 移动端下拉选择器单选 */}
          {
            bodyWidth < 600 && config.tag == 'el-select' && scheme.multiple == false
              ? h('van-popup', {
                props: {
                  value: config.showPop,
                  round: true,
                  position: 'bottom'
                },
                on: {
                  input: (val) => {
                    scheme.__config__.showPop = false
                  }
                }
              }, [
                h('van-picker', {
                  props: {
                    title: scheme.placeholder,
                    valueKey: scheme.props.props.label,
                    columns: scheme.__slot__.options,
                    'show-toolbar': true
                  },
                  on: {
                    close: (val) => {
                      scheme.__config__.showPop = false
                    },
                    cancel: (val) => {
                      scheme.__config__.showPop = false
                    },
                    confirm: (val) => {
                      this[this.formConfCopy.formModel][scheme.__vModel__] = val[scheme.props.props.value]
                      scheme.__config__.defaultValue = val[scheme.props.props.value]
                      scheme.__config__.showPop = false
                    }
                  }
                })
              ]) : ''
          }
          {/* 移动端下拉选择器多选 */}
          {
            bodyWidth < 600 && config.tag == 'el-select' && scheme.multiple == true
              ? h('van-popup', {
                props: {
                  value: config.showPop,
                  round: true,
                  position: 'bottom'
                },
                on: {
                  input: (val) => {
                    scheme.__config__.showPop = false
                  }
                }
              }, [
                h('selectMultiple', {
                  props: {
                    selectDataOpts: scheme.__slot__.options,
                    checkedList: config.defaultValue
                  },
                  on: {
                    confirm: (val) => {
                      this[this.formConfCopy.formModel][scheme.__vModel__] = val.checked
                      scheme.__config__.defaultValue = val.checked
                      scheme.__config__.showPop = false
                    },
                    close: () => {
                      scheme.__config__.showPop = false
                    }
                  }
                })
              ]) : ''
          }
          {/* 移动端时间选择器 */}
          {
            bodyWidth < 600 && config.tag == 'el-time-select'
              ? h('van-popup', {
                props: {
                  value: config.showPop,
                  round: true,
                  position: 'bottom'
                },
                on: {
                  input: (val) => {
                    scheme.__config__.showPop = false
                  }
                }
              }, [
                h('van-datetime-picker', {
                  props: {
                    title: scheme.placeholder,
                    type: 'time',
                    value: config.defaultValue,
                    filter: (type, options) => {
                      if (type === 'minute') {
                        return options.filter((option) => option % scheme['picker-options'].step.slice(3, 5) === 0)
                      }
                      return options
                    }
                  },
                  on: {
                    close: (val) => {
                      scheme.__config__.showPop = false
                    },
                    confirm: (val) => {
                      this[this.formConfCopy.formModel][scheme.__vModel__] = val
                      scheme.__config__.defaultValue = val
                      scheme.__config__.showPop = false
                    }
                  }
                })
              ]) : ''
          }
        </el-col>
      )
    } else {
      if (scheme.type == 'el-upload' && scheme.__config__.defaultValue?.length) {
        return (
          <el-col span={config.span}>
            <el-form-item label-width={labelWidth} prop={scheme.__vModel__}
              label={config.showLabel ? config.label : ''} style={scheme.show === undefined || scheme.show ? '' : 'display: none;'}>
              <div slot='label' style={`display: inline;${labelFontSize ? ('font-size:' + labelFontSize + ';') : ''}${labelFontWeight ? ('font-weight:' + labelFontWeight + ';') : ''}`}>{config.showLabel ? config.label : ''}</div>
              {scheme.__config__.defaultValue.map((item, index) => (
                <div style='cursor: pointer;color: #007aff;text-decoration: underline;border-radius: 5px;padding: 0 10px;margin-bottom:5px;' onclick={() => { this.downFiles(item) }} key={index}>{item.name}</div>
              ))}
            </el-form-item>
          </el-col>
        )
      } else {
        return (
          <el-col span={config.span}>
            <el-form-item label-width={labelWidth} prop={scheme.__vModel__}
              label={config.showLabel ? config.label : ''} style={scheme.show === undefined || scheme.show ? '' : 'display: none;'}>
              <div slot='label' style={`display: inline;${labelFontSize ? ('font-size:' + labelFontSize + ';') : ''}${labelFontWeight ? ('font-weight:' + labelFontWeight + ';') : ''}`}>{config.showLabel ? config.label : ''}</div>
              <render conf={scheme} on={listeners}
                onvisible-change={event => {
                  // 级联选择器
                  if (event) {
                    this.$set(config, 'showPop', true)
                  } else {
                    // this.$set(config, 'showPop', false)
                  }
                }}
                onfocus={event => {
                  // 时间选择器
                  if (event && config.tag == 'el-time-select') {
                    this.$set(config, 'showPop', true)
                    event.$el.children[0].setAttribute('readonly', 'readonly')
                    // event.$el.children[0].setAttribute("readonly", "readonly")
                    // this.$set(config, '$el', event.$el.children[0])
                  }
                  // 日期选择器
                  if (event && config.tag == 'el-date-picker') {
                    this.$set(config, 'showPop', true)
                    event.$el.children[0].setAttribute('readonly', 'readonly')
                  }
                  // 下拉选择器
                  if (event && config.tag == 'el-select') {
                    this.$set(config, 'showPop', true)
                    event.target.setAttribute('readonly', 'readonly')
                  }
                }}
                onchange={event => {
                  // 开关
                  if (config.tag == 'el-switch') {
                    var arr = [...config.relevanceTrue, ...config.relevanceFalse]
                    arr.map((item, index) => {
                      this.formConfCopy.fields.map((item1, index1) => {
                        if (item1.__vModel__ == item) {
                          this.formConfCopy.fields[index1].show = false
                          this.rules[item1.__vModel__].map((item2, index2) => {
                            if (item2.required != undefined) {
                              this.rules[item1.__vModel__][index2].required = false
                            }
                          })
                        }
                      })
                    })
                    if (event) {
                      config.relevanceTrue.map((item, index) => {
                        this.formConfCopy.fields.map((item1, index1) => {
                          if (item1.__vModel__ == item) {
                            this.formConfCopy.fields[index1].show = true
                            this.rules[item1.__vModel__].map((item2, index2) => {
                              if (item2.required != undefined) {
                                this.rules[item1.__vModel__][index2].required = true
                              }
                            })
                          }
                        })
                      })
                    } else {
                      config.relevanceFalse.map((item, index) => {
                        this.formConfCopy.fields.map((item1, index1) => {
                          if (item1.__vModel__ == item) {
                            this.formConfCopy.fields[index1].show = true
                            this.rules[item1.__vModel__].map((item2, index2) => {
                              if (item2.required != undefined) {
                                this.rules[item1.__vModel__][index2].required = true
                              }
                            })
                          }
                        })
                      })
                    }
                  }
                  // 时间选择器
                  // if(config.tag == "el-time-select"){
                  //     config.$el.removeAttribute("readonly", "readonly")
                  // }
                }} />
            </el-form-item>
            {/* 移动端级联选择器 */}
            {
              bodyWidth < 600 && config.tag == 'el-cascader'
                ? h('van-popup', {
                  props: {
                    value: config.showPop,
                    round: true,
                    position: 'bottom'
                  },
                  on: {
                    input: (val) => {
                      scheme.__config__.showPop = false
                    }
                  }
                }, [
                  h('van-cascader', {
                    props: {
                      title: scheme.placeholder,
                      'field-names': {
                        text: scheme.props.props.label,
                        value: scheme.props.props.value,
                        children: scheme.props.props.children
                      },
                      options: scheme.options,
                      value: config.defaultValue
                    },
                    on: {
                      close: (val) => {
                        scheme.__config__.showPop = false
                      },
                      finish: (val) => {
                        this[this.formConfCopy.formModel][scheme.__vModel__] = val.value
                        scheme.__config__.defaultValue = val.value
                        scheme.__config__.showPop = false
                      }
                    }
                  })
                ]) : ''
            }
            {/* 移动端日期选择器 */}
            {
              bodyWidth < 600 && config.tag == 'el-date-picker'
                ? h('van-popup', {
                  props: {
                    value: config.showPop,
                    round: true,
                    position: 'bottom'
                  },
                  on: {
                    input: (val) => {
                      scheme.__config__.showPop = false
                    }
                  }
                }, [
                  h('van-datetime-picker', {
                    props: {
                      title: scheme.placeholder,
                      type: scheme.type == 'month' ? 'year-month' : scheme.type,
                      value: config.defaultValue
                    },
                    on: {
                      close: (val) => {
                        scheme.__config__.showPop = false
                      },
                      confirm: (val) => {
                        console.log(val, (val.toLocaleString('zh', { hour12: false })))
                        this[this.formConfCopy.formModel][scheme.__vModel__] = new Date(val.toLocaleString('zh', { hour12: false })).Format(scheme['value-format'])
                        scheme.__config__.defaultValue = new Date(val.toLocaleString('zh', { hour12: false })).Format(scheme['value-format'])
                        scheme.__config__.showPop = false
                      }
                    }
                  })
                ]) : ''
            }
            {/* 移动端下拉选择器 */}
            {
              bodyWidth < 600 && config.tag == 'el-select' && scheme.multiple == false
                ? h('van-popup', {
                  props: {
                    value: config.showPop,
                    round: true,
                    position: 'bottom'
                  },
                  on: {
                    input: (val) => {
                      scheme.__config__.showPop = false
                    }
                  }
                }, [
                  h('van-picker', {
                    props: {
                      title: scheme.placeholder,
                      valueKey: scheme.props.props.label,
                      columns: scheme.__slot__.options,
                      'show-toolbar': true
                    },
                    on: {
                      close: (val) => {
                        scheme.__config__.showPop = false
                      },
                      cancel: (val) => {
                        scheme.__config__.showPop = false
                      },
                      confirm: (val) => {
                        this[this.formConfCopy.formModel][scheme.__vModel__] = val[scheme.props.props.value]
                        scheme.__config__.defaultValue = val[scheme.props.props.value]
                        scheme.__config__.showPop = false
                      }
                    }
                  })
                ]) : ''
            }
            {/* 移动端下拉选择器多选 */}
            {
              bodyWidth < 600 && config.tag == 'el-select' && scheme.multiple == true
                ? h('van-popup', {
                  props: {
                    value: config.showPop,
                    round: true,
                    position: 'bottom'
                  },
                  on: {
                    input: (val) => {
                      scheme.__config__.showPop = false
                    }
                  }
                }, [
                  h('selectMultiple', {
                    props: {
                      selectDataOpts: scheme.__slot__.options,
                      checkedList: config.defaultValue
                    },
                    on: {
                      confirm: (val) => {
                        this[this.formConfCopy.formModel][scheme.__vModel__] = val.checked
                        scheme.__config__.defaultValue = val.checked
                        scheme.__config__.showPop = false
                        // scheme.__config__.showPop = false
                      },
                      close: () => {
                        scheme.__config__.showPop = false
                      }
                    }
                  })
                  // <select-multiple :checked-list="checked" :select-name="selectName" @selectMutiple="selectMutiple" />
                ]) : ''
            }
            {/* 移动端时间选择器 */}
            {
              bodyWidth < 600 && config.tag == 'el-time-select'
                ? h('van-popup', {
                  props: {
                    value: config.showPop,
                    round: true,
                    position: 'bottom'
                  },
                  on: {
                    input: (val) => {
                      scheme.__config__.showPop = false
                    }
                  }
                }, [
                  h('van-datetime-picker', {
                    props: {
                      title: scheme.placeholder,
                      type: 'time',
                      value: config.defaultValue,
                      filter: (type, options) => {
                        if (type === 'minute') {
                          return options.filter((option) => option % scheme['picker-options'].step.slice(3, 5) === 0)
                        }
                        return options
                      }
                    },
                    on: {
                      close: (val) => {
                        scheme.__config__.showPop = false
                      },
                      confirm: (val) => {
                        this[this.formConfCopy.formModel][scheme.__vModel__] = val
                        scheme.__config__.defaultValue = val
                        scheme.__config__.showPop = false
                      }
                    }
                  })
                ]) : ''
            }
          </el-col>
        )
      }
    }
  },
  rowFormItem(h, scheme) {
    let child = renderChildren.apply(this, arguments)
    if (scheme.type === 'flex') {
      child = <el-row type={scheme.type} justify={scheme.justify} align={scheme.align}>
        {child}
      </el-row>
    }
    return (
      <el-col span={scheme.span}>
        <el-row gutter={scheme.gutter}>
          {child}
        </el-row>
      </el-col>
    )
  },
  colLink(h, scheme) {
    const config = scheme.__config__
    const listeners = buildListeners.call(this, scheme)

    let labelWidth = config.labelWidth ? `${config.labelWidth}px` : null
    const labelFontSize = config.labelFontSize ? `${config.labelFontSize}px` : null
    const labelFontWeight = config.labelFontWeight ? `${config.labelFontWeight}` : null
    if (config.showLabel === false) labelWidth = '0'
    return (
      <el-col span={config.span}>
        <el-form-item label-width={labelWidth} prop={scheme.__vModel__}
          label={config.showLabel ? config.label : ''} class={'colLink'}>
          <div slot='label' style={`display: inline;${labelFontSize ? ('font-size:' + labelFontSize + ';') : ''}${labelFontWeight ? ('font-weight:' + labelFontWeight + ';') : ''}`}>{config.showLabel ? config.label : ''}</div>
          <render conf={scheme} on={listeners} href={config.href} target={config.target} >{config.defaultValue}</render>
        </el-form-item>
      </el-col>
    )
  }
}

function renderFrom(h) {
  const { formConfCopy } = this
  return (
    <el-row gutter={formConfCopy.gutter}>
      <el-form
        size={formConfCopy.size}
        label-position={formConfCopy.labelPosition}
        disabled={formConfCopy.disabled}
        label-width={`${setRootFontSize(formConfCopy.labelWidth)}rem`}
        ref={formConfCopy.formRef}
        // model不能直接赋值 https://github.com/vuejs/jsx/issues/49#issuecomment-472013664
        props={{ model: this[formConfCopy.formModel] }}
        rules={this[formConfCopy.formRules]}
      >
        {renderFormItem.call(this, h, formConfCopy.fields)}
        {formConfCopy.formBtns && formBtns.call(this, h, formConfCopy)}
      </el-form>
    </el-row>
  )
}

function formBtns(h, formConfCopy) {
  return <el-col>
    {
      bodyWidth > 600
        ? <el-form-item size='large'>
          <el-button type='primary' onClick={this.submitForm}>{formConfCopy.confirmText || '提交'}</el-button>
          {/* <el-button onClick={this.resetForm}>重置</el-button> */}
        </el-form-item>
        : <el-form-item size='large' class='mobile-btn'>
          <el-button type='primary' round onClick={this.submitForm}>{formConfCopy.confirmText || '立即预约'}</el-button>
        </el-form-item>
    }
  </el-col>
}

function renderFormItem(h, elementList) {
  return elementList.map(scheme => {
    const config = scheme.__config__
    const layout = layouts[config.layout]

    if (layout) {
      return layout.call(this, h, scheme)
    }
    throw new Error(`没有与${config.layout}匹配的layout`)
  })
}

function renderChildren(h, scheme) {
  const config = scheme.__config__
  if (!Array.isArray(config.children)) return null
  return renderFormItem.call(this, h, config.children)
}

function setValue(event, config, scheme) {
  this.$set(config, 'defaultValue', event)
  this.$set(this[this.formConf.formModel], scheme.__vModel__, event)
}

function buildListeners(scheme) {
  const config = scheme.__config__
  const methods = this.formConf.__methods__ || {}
  const listeners = {}

  // 给__methods__中的方法绑定this和event
  Object.keys(methods).forEach(key => {
    listeners[key] = event => methods[key].call(this, event)
  })
  // 响应 render.js 中的 vModel $emit('input', val)
  listeners.input = event => setValue.call(this, event, config, scheme)
  return listeners
}

export default {
  components: {
    render
  },
  props: {
    formConf: {
      type: Object,
      required: true
    },
    formRequire: {
      type: Array,
      required: false,
      default: []
    },
    isAudit: {
      type: Boolean,
      required: false,
      default: false
    },
    isAuditSuccess: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    const data = {
      formConfCopy: deepClone(this.formConf),
      [this.formConf.formModel]: {},
      [this.formConf.formRules]: {}
    }
    if (!this.isAudit) {
      data.formConfCopy.fields = data.formConfCopy.fields.map((item, index) => {
        if (item.audit) { item.show = false }
        return item
      })
    }
    if (!this.isAuditSuccess) {
      data.formConfCopy.fields = data.formConfCopy.fields.map((item, index) => {
        if (item.auditSuccess) { item.show = false }
        return item
      })
    }
    this.initFormData(data.formConfCopy.fields, data[this.formConf.formModel])
    this.buildRules(data.formConfCopy.fields, data[this.formConf.formRules])
    return data
  },
  methods: {
    downFiles(file) {
      this.$download.oss(file.response.data.fileUrl, file.response.data.originFileName)
    },
    initFormData(componentList, formData) {
      componentList.forEach(cur => {
        this.buildOptionMethod(cur)
        const config = cur.__config__
        if (cur.__vModel__) {
          formData[cur.__vModel__] = config.defaultValue
          // 初始化文件列表
          if (cur.action && config.defaultValue) {
            cur['file-list'] = config.defaultValue
          }
        }
        if (cur.action) {
          cur['headers'] = {
            token: getToken()
          }
          cur['on-success'] = (res, file, fileList) => {
            if (res.code == 0) {
              fileList.forEach(val => {
                val.extname = val.name.split('.').pop()
                val.url = file.response.data.fileUrl
              })
              formData[cur.__vModel__] = fileList
              // 初始化文件列表
              if (cur.action && fileList) {
                cur['file-list'] = fileList
              }
              if (res.code === 0 && fileList) {
                // this.$set(config, 'defaultValue', fileList)
                config.defaultValue = fileList
                // console.log(config.defaultValue)
                // config.defaultValue.forEach(val => {
                //   val.url = file.response.data.url;
                //   val.ossId = file.response.data.ossId;
                //   val.response = null
                // })
              }
            } else {
              config.defaultValue = []
              formData[cur.__vModel__] = []
              // 初始化文件列表
              if (cur.action) {
                cur['file-list'] = []
              }
              this.$message({
                message: res.message,
                type: 'warning'
              })
            }
          }
          // 点击文件列表中已上传的文件时的钩子
          cur['on-preview'] = (file) => {
            this.$download.oss(file.response.data.fileUrl, file.response.data.originFileName)
          }
          cur['on-exceed'] = (files, fileList) => {
            this.$message({
              message: `最多只能上传${fileList.length}个文件`,
              type: 'warning'
            })
          }
          cur['before-upload'] = (files) => {
            let types = []
            if (cur.accept.indexOf('audio') != -1) {
              types = ['mp3', 'flac']
              if (types.indexOf(files.name.split('.').pop().toLowerCase()) == -1) {
                this.$message({
                  message: `请选择音乐！`,
                  type: 'warning'
                })
                return false
              }
            } else if (cur.accept.indexOf('xls') != -1) {
              types = ['xls', 'xlsx']
              if (types.indexOf(files.name.split('.').pop().toLowerCase()) == -1) {
                this.$message({
                  message: `请选择excel！`,
                  type: 'warning'
                })
                return false
              }
            } else if (cur.accept.indexOf('doc') != -1) {
              types = ['doc', 'docx']
              if (types.indexOf(files.name.split('.').pop().toLowerCase()) == -1) {
                this.$message({
                  message: `请选择word！`,
                  type: 'warning'
                })
                return false
              }
            } else if (cur.accept.indexOf('pdf') != -1) {
              types = ['pdf']
              if (types.indexOf(files.name.split('.').pop().toLowerCase()) == -1) {
                this.$message({
                  message: `请选择pdf！`,
                  type: 'warning'
                })
                return false
              }
            } else if (cur.accept.indexOf('txt') != -1) {
              types = ['txt']
              if (types.indexOf(files.name.split('.').pop().toLowerCase()) == -1) {
                this.$message({
                  message: `请选择txt！`,
                  type: 'warning'
                })
                return false
              }
            }
          }
          cur['on-remove'] = (files, fileList) => {
            formData[cur.__vModel__] = fileList
          }
        }
        if (config.children) {
          this.initFormData(config.children, formData)
        }
      })
    },
    // 特殊处理的 Option
    buildOptionMethod(scheme) {
      const config = scheme.__config__
      if (config && (config.tag === 'el-cascader' || config.tag === 'el-select' || config.tag === 'el-radio-group' || config.tag === 'el-checkbox-group')) {
        if (config.dataType === 'dynamic') {
          this.$axios({
            method: config.method,
            url: config.url
          }).then(resp => {
            var { data } = resp
            const arr = config.dataConsumer.split('.')
            if (arr.length == 1) {
              scheme[config.dataConsumer] = config.dataKey ? data[config.dataKey] : data
            } else {
              scheme.__slot__[arr[1]] = config.dataKey ? data[config.dataKey] : data
            }
          })
        }
      }
    },
    buildRules(componentList, rules) {
      componentList.forEach(cur => {
        const config = cur.__config__
        if (Array.isArray(config.regList)) {
          if (config.required) {
            const required = { required: config.required, message: cur.placeholder }
            if (Array.isArray(config.defaultValue)) {
              //   required.type = 'array'
              required.message = `请至少选择一个${config.label}`
            }
            required.message === undefined && (required.message = `${config.label}不能为空`)
            config.regList.push(required)
          }
          rules[cur.__vModel__] = config.regList.map(item => {
            item.pattern && (item.pattern = eval(item.pattern))
            item.trigger = ruleTrigger && ruleTrigger[config.tag]
            return item
          })
        }
        if (config.children) this.buildRules(config.children, rules)
      })
    },
    resetForm() {
      this.formConfCopy = deepClone(this.formConf)
      this.$refs[this.formConf.formRef].resetFields()
    },
    submitForm() {
      this.$refs[this.formConf.formRef].validate(valid => {
        if (!valid) return false
        const params = {
          formData: this.formConfCopy,
          valData: this[this.formConf.formModel]
        }
        this.$emit('submit', params)
        return true
      })
    },
    // 传值给父组件
    getData() {
      // debugger
      this.$emit('getData', this[this.formConf.formModel])
      // this.$emit('getData',this.formConfCopy)
    },
    initRules() {
      this.formConfCopy.fields.map((configs, i) => {
        var config = configs.__config__
        // 开关
        if (config.tag == 'el-switch') {
          var arr = [...config.relevanceTrue, ...config.relevanceFalse]
          arr.map((item, index) => {
            this.formConfCopy.fields.map((item1, index1) => {
              if (item1.__vModel__ == item) {
                this.formConfCopy.fields[index1].show = false
                this.rules[item1.__vModel__].map((item2, index2) => {
                  if (item2.required != undefined) {
                    this.rules[item1.__vModel__][index2].required = false
                  }
                })
              }
            })
          })
          if (config.defaultValue) {
            config.relevanceTrue.map((item, index) => {
              this.formConfCopy.fields.map((item1, index1) => {
                if (item1.__vModel__ == item) {
                  this.formConfCopy.fields[index1].show = true
                  this.rules[item1.__vModel__].map((item2, index2) => {
                    if (item2.required != undefined) {
                      this.rules[item1.__vModel__][index2].required = true
                    }
                  })
                }
              })
            })
          } else {
            config.relevanceFalse.map((item, index) => {
              this.formConfCopy.fields.map((item1, index1) => {
                if (item1.__vModel__ == item) {
                  this.formConfCopy.fields[index1].show = true
                  this.rules[item1.__vModel__].map((item2, index2) => {
                    if (item2.required != undefined) {
                      this.rules[item1.__vModel__][index2].required = true
                    }
                  })
                }
              })
            })
          }
        }
      })
    }
  },
  render(h) {
    setTimeout(() => {
      this.initRules()
    }, 0)
    // console.log(renderFrom.call(this, h));
    return renderFrom.call(this, h)
  }
}
