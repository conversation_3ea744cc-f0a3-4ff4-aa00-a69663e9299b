<template>
  <div class="boxmore">
    <div class="borderbox">
      <el-button
        type="primary"
        icon="el-icon-setting"
        class="addbtn"
        @click="handleSetting"
        >设置</el-button
      >
      <el-row style="margin-top: 20px">
        <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
          <div class="imgbox">
            <img src="@admin/assets/gray.png" v-if="flag == 1" />
            <img src="@admin/assets/no-gray.png" v-else />
          </div>
        </el-col>
      </el-row>
      <!-- 模态框 -->
      <el-dialog
        title="网站设置"
        :visible.sync="diaShow"
        width="500px"
        top="8vh"
        append-to-body
      >
        <el-form
          :model="diaForm"
          ref="diaForm"
          label-width="auto"
          style="width: 100%"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="是否置灰" prop="title">
                <el-select
                  v-model="diaForm.showflag"

                  clearable
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in grayOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row slot="footer" class="dialog-footer">
          <el-button @click="diaShow = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn"
            >确认</el-button
          >
        </el-row>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { addconfig, getconfig } from "@admin/api/Dailymanagement/SiteSettings";
export default {
  data() {
    return {
      flag: "",
      diaShow: false,
      diaForm: {
        showflag: "2",
      },
      grayOptions: [
        {
          label: "是",
          value: "1",
        },
        {
          label: "否",
          value: "2",
        },
      ],
    };
  },
  methods: {
    handleSetting() {
      this.diaShow = true;
      this.diaForm.showflag = "";
    },
    confirmBtn() {
      let json = {
        sysKey: "showflag",
        sysValue: this.diaForm.showflag,
      };
      addconfig(json).then((res) => {
        if (res.code == 0) {
          getconfig("showflag").then((res) => {
            if (res.code == 0) {
              this.flag = res.data[0].sysValue;
              this.diaShow = false;
            }
          });
        }
      });
    },
  },
  created() {
    getconfig("showflag").then((res) => {
      if (res.code == 0) {
        if (res.data[0].sysValue == "1") {
          this.flag = "1";
        } else {
          this.flag = "";
        }
      }
    });
  },
};
</script>
<style lang="scss" scoped>
.imgbox {
  width: 100%;
  height: 350px;
  border: 1px solid #ccc;
  img {
    width: 100%;
  }
}
</style>