<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="center" class="itemBox" label-width="80px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="图片名称">
                <el-input v-model="searchForm.imageName" placeholder="请输入图片名称"  clearable style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="分类">
                <el-select
                  v-model="searchForm.category"
                  placeholder="请选择"
                  class="publicselect"

                  style="width: 100%"
                  clearable
                >
                  <el-option v-for="(item, index) in categoryOptions" :key="index" :label="item.desc" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" style="margin-left: 20px" @click="searchBtn">
                搜索
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-row class="important marginTop">
        <el-col :span="24"><el-button type="primary" icon="el-icon-plus" class="addbtn" @click="addBtn">新增</el-button></el-col>
      </el-row>
      <!-- 数据表格开始 -->
      <el-row>
        <el-table :data="tabledata" stripe border style="width: 100%" class="marginTop" v-loading="loading">
          <el-table-column prop="orderNum" label="排序" align="center"></el-table-column>
          <el-table-column prop="imageSn" label="编号" align="center"></el-table-column>
          <el-table-column prop="imageName" label="名称" align="center"></el-table-column>
          <el-table-column prop="imgUrl" label="图片" align="center" width="600px">
            <template slot-scope="scope">
              <img :src="location + scope.row.imageUrl" style="height: 200px" />
            </template>
          </el-table-column>
          <el-table-column prop="isActive" label="是否激活展示" align="center" width="150">
            <template slot-scope="scope">{{ scope.row.isActive == 1 ? "是" : "否" }}</template>
          </el-table-column>
          <el-table-column label="操作" width="200px" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)">编辑</el-button>
              <el-button type="text" icon="el-icon-delete" style="color: red" @click="delBtn(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!-- 分页开始 -->
      <el-row>
        <el-col>
          <div class="paging">
            <Paging :pageCount="pageCount" @pagebar="changePage"></Paging>
          </div>
        </el-col>
      </el-row>
      <!-- 分页结束 -->

      <!-- 模态框 -->
      <el-dialog title="banner图管理" :visible.sync="diaShow" width="900px" top="8vh" append-to-body>
        <el-form :model="diaForm" ref="diaForm" label-width="auto" style="width: 100%" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="编号" prop="imageSn">
                <el-input v-model="diaForm.imageSn"  clearable placeholder="请输入banner编号"></el-input>
              </el-form-item>
              <el-form-item label="分类" prop="category">
                <el-select v-model="diaForm.category" placeholder="请选择" class="publicselect"  style="width: 100%" clearable>
                  <el-option v-for="(item, index) in categoryOptions" :key="index" :label="item.desc" :value="item.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="排序" prop="orderNum">
                <el-input-number
                  v-model="diaForm.orderNum"

                  controls-position="right"
                  :min="1"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="名称" prop="imageName">
                <el-input v-model="diaForm.imageName"  clearable placeholder="请输入banner名称"></el-input>
              </el-form-item>
              <el-form-item label="是否激活展示" prop="isActive">
                <el-select v-model="diaForm.isActive" placeholder="请选择" class="publicselect"  style="width: 100%" clearable>
                  <el-option v-for="(item, index) in isActiveOptions" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="banner图" prop="imageUrl">
              <file-uploader
                v-model="diaForm.imageUrl"
                type="image"
                :uploadApi="uploadbanner"
                :maxSize="10"
                tip="推荐分辨率1920 * 400"
              ></file-uploader>
            </el-form-item>
          </el-row>
          <el-row class="dialog-footer">
            <el-button @click="diaShow = false" >取消</el-button>
            <el-button type="primary"  @click="confirmBtn">确认</el-button>
          </el-row>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getbanner, addbanner, editbanner, delbanner, uploadbanner, categorybanner } from "@admin/api/Dailymanagement/banner";
import Paging from "@admin/components/pagebar/Paging.vue";
import FileUploader from "@admin/components/FileUploader/index.vue";

export default {
  data() {
    return {
      uploadbanner,
      pageCount: 0,
      isActiveOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      categoryOptions: [],
      searchForm: {
        imageName: "",
        category: "",
        pageNum: 1,
        pageSize: 10,
      },
      diaShow: false,
      tabledata: [],
      loading: false,
      baseUrl: window.g.ApiUrl + "/banner/image/upload",
      location: document.location.protocol + "//" + document.location.host + window.g.ApiUrl,
      diaForm: {
        category: "",
        imageSn: "",
        imageName: "",
        orderNum: "",
        imageUrl: "",
        isActive: "",
        uuid: "",
      },
      // 分页数据
      pageData: {
        page: 5,
        pageNum: 1,
      },
      pageCount: 0,
      imageUrl: "",
    };
  },
  methods: {
    getCategorys() {
      let json = {
        imageName: "",
        category: "",
        isActive: "",
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
      };
      categorybanner(json).then((res) => {
        if (res.code == 0) {
          this.categoryOptions = res.data;
        }
      });
    },
    //搜索列表
    searchBtn() {
      this.searchForm.pageNum = 1;
      this.initTable();
    },
    // 初始数据
    initTable() {
      this.loading = true;
      getbanner(this.searchForm).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.tabledata = res.data;
          this.pageCount = res.count;
        }
      });
    },
    addBtn() {
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.$nextTick(() => {
        this.diaForm = {
          category: "",
          imageSn: "",
          imageName: "",
          orderNum: "",
          imageUrl: "",
          isActive: "",
          uuid: "",
        };
        this.imageUrl = "";
      });
      this.diaShow = true;
    },
    confirmBtn() {
      if (this.diaForm.uuid == "") {
        addbanner(this.diaForm).then((res) => {
          if (res.code == 0) {
            this.$message.success(res.message);
            this.diaShow = false;
            this.initTable();
          } else {
            this.$message.error(res.message);
            this.diaShow = false;
          }
        });
      } else {
        editbanner(this.diaForm).then((res) => {
          if (res.code == 0) {
            this.$message.success(res.message);
            this.diaShow = false;
            this.initTable();
          } else {
            this.$message.error(res.message);
            this.diaShow = false;
          }
        });
      }
    },
    editBtn(row) {
      this.diaShow = true;
      this.diaForm.category = row.category;
      this.diaForm.imageSn = row.imageSn;
      this.diaForm.imageName = row.imageName;
      this.diaForm.imageUrl = row.imageUrl;
      this.diaForm.isActive = row.isActive;
      this.diaForm.uuid = row.uuid;
      this.imageUrl = this.location + row.imageUrl;
    },
    delBtn(row) {
      this.$confirm("此操作将删除该banner图, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delbanner(json).then((res) => {
            if (res.code === 0) {
              this.$message.success("删除成功！");
              this.initTable();
            } else {
              this.$message.error("删除失败！");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 分页函数
    changePage(pageData) {
      this.searchForm.pageNum = pageData.pageNum;
      this.searchForm.pageSize = pageData.page;
      // 页面刷新
      this.initTable();
    },
  },
  created() {
    this.initTable();
    this.getCategorys();
  },
  components: {
    Paging,
    FileUploader,
  },
  computed: {
    // 模态框表单验证
    rules() {
      return {
        imageSn: [
          {
            required: true,
            message: "请输入编号",
            trigger: "blur",
          },
        ],
        imageName: [
          {
            required: true,
            message: "请输入名称",
            trigger: "blur",
          },
        ],
        imageUrl: [
          {
            required: true,
            message: "请上传图片",
            trigger: "blur",
          },
        ],
        category: [
          {
            required: true,
            message: "请选择分类",
            trigger: "change",
          },
        ],
        isActive: [
          {
            required: true,
            message: "请选择是否激活展示",
            trigger: "change",
          },
        ],
      };
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
.tips {
  font-size: 14px;
  color: red;
}
</style>
