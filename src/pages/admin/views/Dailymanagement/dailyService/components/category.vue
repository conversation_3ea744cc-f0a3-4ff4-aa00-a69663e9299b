<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 搜索开始 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="center" class="itemBox" label-width="80px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="分类标题" prop="categoryName">
                <el-input
                  v-model="searchForm.categoryName"
                  placeholder="请输入应用分类标题"

                  clearable
                  style="width: 100%"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="状态" prop="categoryVisible">
                <el-select
                  class="publicselect"
                  v-model="searchForm.categoryVisible"
                  placeholder="请选择状态"

                  clearable
                  style="width: 100%"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="启用" :value="1"></el-option>
                  <el-option label="禁用" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-bottom: 15px">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" @click="searchBtn">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 新增按钮 -->
      <el-row class="important marginTop">
        <el-col :span="24"><el-button type="primary" icon="el-icon-plus" class="addbtn" @click="addBtn">新增</el-button></el-col>
      </el-row>
      <!-- 数据表格开始 -->
      <el-row>
        <el-table :data="tableData" stripe border style="width: 100%" class="marginTop" v-loading="loading">
          <el-table-column prop="orderNum" label="排序" width="80" align="center"></el-table-column>
          <el-table-column prop="categoryName" label="标题" align="center"></el-table-column>
          <el-table-column prop="categorySvg" label="图标" align="center" width="120">
            <template slot-scope="scope">
              <img :src="location + scope.row.categorySvg" style="height: 40px; width: 40px;" />
            </template>
          </el-table-column>
          <el-table-column prop="memo" label="描述" align="center"></el-table-column>
          <el-table-column prop="categoryVisible" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.categoryVisible === 1 ? 'success' : 'info'">
                {{ scope.row.categoryVisible === 1 ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200px" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)">编辑</el-button>
              <el-button type="text" icon="el-icon-delete" style="color: red" @click="delBtn(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!-- 分页开始 -->
      <el-row>
        <el-col>
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pageData.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageData.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </el-col>
      </el-row>
      <!-- 分页结束 -->

      <!-- 模态框 -->
      <el-dialog title="应用分类管理" :visible.sync="dialogVisible" width="900px" top="8vh" append-to-body>
        <el-form :model="formData" ref="formData" label-width="100px" style="width: 100%" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="标题" prop="categoryName">
                <el-input v-model="formData.categoryName"  clearable placeholder="请输入应用分类标题"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="orderNum">
                <el-input-number
                  v-model="formData.orderNum"

                  controls-position="right"
                  :min="1"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="categoryVisible">
                <el-switch
                  v-model="formData.categoryVisible"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="禁用"
                ></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="图标" prop="categorySvg">
              <file-uploader v-model="formData.categorySvg" type="image" :maxSize="2" tip="建议上传尺寸：40px * 40x"></file-uploader>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="描述" prop="memo">
              <el-input type="textarea" :rows="4" v-model="formData.memo"  placeholder="请输入应用分类描述"></el-input>
            </el-form-item>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { categoryList, categorySave, categoryUpdate, categoryDelete, uploadimg } from "@admin/api/Dailymanagement/dailyService";
import FileUploader from "@admin/components/FileUploader/index.vue";

export default {
  components: {
    FileUploader,
  },
  data() {
    return {
      searchForm: {
        categoryName: "",
        categoryVisible: "",
      },
      tableData: [],
      loading: false,
      dialogVisible: false,
      // 分页数据
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      baseUrl: this.$myStore.baseUrl + "/fileUpload/file",
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl,
      formData: {
        categoryName: "",
        orderNum: 1,
        categorySvg: "",
        categoryVisible: 1,
        memo: "",
        uuid: "",
      },
      imageUrl: "",
      rules: {
        categoryName: [
          {
            required: true,
            message: "请输入标题",
            trigger: "blur",
          },
        ],
        orderNum: [
          {
            required: true,
            message: "请输入排序",
            trigger: "blur",
          },
        ],
        categorySvg: [
          {
            required: true,
            message: "请上传图标",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    // 初始化数据
    initTable() {
      this.loading = true;
      categoryList({
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        categoryName: this.searchForm.categoryName,
        categoryVisible: this.searchForm.categoryVisible,
        orderItems: "orderNum",
        orderRule: "asc",
      })
        .then((res) => {
          this.loading = false;
          if (res.code === 0) {
            this.tableData = res.data;
            this.total = res.count;
          } else {
            this.$message.error(res.message || "获取数据失败");
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error("获取数据失败");
        });
    },
    // 搜索按钮
    searchBtn() {
      this.pageData.pageNum = 1;
      this.initTable();
    },
    // 重置按钮
    resetBtn() {
      this.searchForm.categoryName = "";
      this.searchForm.categoryVisible = "";
      this.pageData.pageNum = 1;
      this.initTable();
    },
    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageData.pageSize = val;
      this.initTable();
    },
    // 处理分页页码变化
    handleCurrentChange(val) {
      this.pageData.pageNum = val;
      this.initTable();
    },
    // 新增按钮
    addBtn() {
      if (this.$refs.formData) {
        this.$refs.formData.resetFields();
      }
      this.$nextTick(() => {
        this.formData.categoryName = "";
        this.formData.orderNum = 1;
        this.formData.categorySvg = "";
        this.formData.categoryVisible = 1;
        this.formData.memo = "";
        this.formData.uuid = "";
        this.imageUrl = "";
      });
      this.dialogVisible = true;
    },
    // 编辑按钮
    editBtn(row) {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.formData.categoryName = row.categoryName;
        this.formData.orderNum = row.orderNum;
        this.formData.categorySvg = row.categorySvg;
        this.formData.categoryVisible = row.categoryVisible !== undefined ? row.categoryVisible : 1;
        this.formData.memo = row.memo || "";
        this.formData.uuid = row.uuid;
        this.imageUrl = this.location + row.categorySvg;
      });
    },
    // 删除按钮
    delBtn(row) {
      this.$confirm("此操作将删除该应用分类, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const data = {
            uuid: row.uuid,
          };
          categoryDelete(data)
            .then((res) => {
              if (res.code === 0) {
                this.$message.success(res.message || "删除成功！");
                this.initTable();
              } else {
                this.$message.error(res.message || "删除失败！");
              }
            })
            .catch(() => {
              this.$message.error("删除失败！");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 确认按钮
    confirmBtn() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.formData.uuid) {
            // 编辑
            categoryUpdate(this.formData)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success(res.message || "修改成功！");
                  this.dialogVisible = false;
                  this.initTable();
                } else {
                  this.$message.error(res.message || "修改失败！");
                }
              })
              .catch(() => {
                this.$message.error("修改失败！");
              });
          } else {
            // 新增
            categorySave(this.formData)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success(res.message || "添加成功！");
                  this.dialogVisible = false;
                  this.initTable();
                } else {
                  this.$message.error(res.message || "添加失败！");
                }
              })
              .catch(() => {
                this.$message.error("添加失败！");
              });
          }
        }
      });
    },
  },
  created() {
    this.initTable();
  },
};
</script>

<style scoped lang="less">
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.marginTop {
  margin-top: 10px;
}
.addbtn {
  margin-bottom: 10px;
}
</style>
