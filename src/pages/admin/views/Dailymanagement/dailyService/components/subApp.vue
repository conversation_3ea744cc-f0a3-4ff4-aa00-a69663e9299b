<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 搜索开始 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="center" class="itemBox" label-width="80px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
              <el-form-item label="应用名称" prop="appName">
                <el-input v-model="searchForm.appName" placeholder="请输入应用名称"  clearable style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
              <el-form-item label="所属分类" prop="categoryUuid">
                <el-select v-model="searchForm.categoryUuid" placeholder="请选择所属分类" clearable  style="width: 100%">
                  <el-option v-for="item in categoryOptions" :key="item.uuid" :label="item.categoryName" :value="item.uuid"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
              <el-form-item label="状态" prop="appEnabled">
                <el-select v-model="searchForm.appEnabled" placeholder="请选择状态" clearable  style="width: 100%">
                  <el-option :label="'启用'" :value="1"></el-option>
                  <el-option :label="'禁用'" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-bottom: 15px">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" @click="searchBtn">搜索</el-button>
              <el-button type="info" icon="el-icon-refresh"  class="smallbtn" @click="resetBtn">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 新增按钮 -->
      <el-row class="important marginTop">
        <el-col :span="24"><el-button type="primary" icon="el-icon-plus" class="addbtn" @click="addBtn">新增</el-button></el-col>
      </el-row>
      <!-- 数据表格开始 -->
      <el-row>
        <el-table :data="tableData" stripe border style="width: 100%" class="marginTop" v-loading="loading">
          <el-table-column prop="orderNum" label="排序" width="80" align="center"></el-table-column>
          <el-table-column prop="appName" label="应用名称" align="center"></el-table-column>
          <el-table-column prop="appCode" label="应用编码" align="center"></el-table-column>
          <el-table-column prop="categoryName" label="所属分类" align="center"></el-table-column>
          <el-table-column prop="appIcon" label="图标" align="center" width="120">
            <template slot-scope="scope">
              <img v-if="scope.row.appIcon" :src="location + scope.row.appIcon" style="height: 40px; width: 40px;" />
              <span v-else>无图标</span>
            </template>
          </el-table-column>
          <el-table-column prop="pcUrl" label="PC端链接" align="center">
            <template slot-scope="scope">
              <el-link :href="formatUrl(scope.row.pcUrl)" target="_blank" type="primary" :underline="false">{{ scope.row.pcUrl }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="memo" label="备注" align="center"></el-table-column>
          <el-table-column prop="appEnabled" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.appEnabled === 1 ? 'success' : 'info'">
                {{ scope.row.appEnabled === 1 ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200px" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)">编辑</el-button>
              <el-button type="text" icon="el-icon-delete" style="color: red" @click="delBtn(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!-- 分页开始 -->
      <el-row>
        <el-col>
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pageData.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageData.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </el-col>
      </el-row>
      <!-- 分页结束 -->

      <!-- 模态框 -->
      <el-dialog title="子应用管理" :visible.sync="dialogVisible" width="900px" top="8vh" append-to-body>
        <el-form :model="formData" ref="formData" label-width="100px" style="width: 100%" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="应用名称" prop="appName">
                <el-input v-model="formData.appName"  clearable placeholder="请输入应用名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="应用编码" prop="appCode">
                <el-input v-model="formData.appCode"  clearable placeholder="请输入应用编码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属分类" prop="categoryId">
                <el-select v-model="formData.categoryId" placeholder="请选择所属分类"  style="width: 100%">
                  <el-option
                    v-for="item in categoryOptions"
                    :key="item.categoryId"
                    :label="item.categoryName"
                    :value="item.categoryId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序" prop="orderNum">
                <el-input-number
                  v-model="formData.orderNum"

                  controls-position="right"
                  :min="1"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="PC端链接" prop="pcUrl">
                <el-input v-model="formData.pcUrl"  clearable placeholder="请输入PC端链接地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="移动端链接" prop="mobileUrl">
                <el-input v-model="formData.mobileUrl"  clearable placeholder="请输入移动端链接地址（可选）"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="appEnabled">
                <el-switch
                  v-model="formData.appEnabled"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="禁用"
                ></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="图标" prop="appIcon">
              <file-uploader v-model="formData.appIcon" type="image" :maxSize="2" tip="建议上传尺寸：40px * 40px"></file-uploader>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="备注" prop="memo">
              <el-input type="textarea" :rows="4" v-model="formData.memo"  placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn">确认</el-button>
        </div>
        <el-row class="dialog-footer"></el-row>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { uploadimg } from "@admin/api/Dailymanagement/dailyService";
import { categorySelect, subAppList, subAppSave, subAppUpdate, subAppDelete } from "@admin/api/Dailymanagement/dailyServiceChild";
import FileUploader from "@admin/components/FileUploader/index.vue";

export default {
  components: {
    FileUploader,
  },
  data() {
    return {
      searchForm: {
        appName: "",
        categoryUuid: "",
        appEnabled: "",
      },
      tableData: [],
      loading: false,
      dialogVisible: false,
      // 分页数据
      pageData: {
        pageSize: 10,
        pageNum: 1,
      },
      total: 0,
      baseUrl: this.$myStore.baseUrl + "/fileUpload/img",
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl,
      formData: {
        appName: "",
        appCode: "",
        categoryId: "",
        appIcon: "",
        pcUrl: "",
        mobileUrl: "",
        orderNum: 1,
        appEnabled: 1,
        memo: "",
        uuid: "",
      },
      imageUrl: "",
      categoryOptions: [], // 应用分类下拉选项
      rules: {
        appName: [
          {
            required: true,
            message: "请输入应用名称",
            trigger: "blur",
          },
        ],
        appCode: [
          {
            required: true,
            message: "请输入应用编码",
            trigger: "blur",
          },
        ],
        categoryId: [
          {
            required: true,
            message: "请选择所属分类",
            trigger: "change",
          },
        ],
        orderNum: [
          {
            required: true,
            message: "请输入排序",
            trigger: "blur",
          },
        ],
        pcUrl: [
          {
            required: true,
            message: "请输入PC端链接地址",
            trigger: "blur",
          }
        ]
      },
    };
  },
  methods: {
    // 格式化URL，确保有http/https前缀
    formatUrl(url) {
      if (!url) return '';
      // 如果URL不是以http://或https://开头，则添加https://
      if (!/^https?:\/\//i.test(url)) {
        return 'https://' + url;
      }
      return url;
    },
    // 获取应用分类下拉列表
    getCategoryOptions() {
      categorySelect({
        num: 0, // 查询所有分类
      })
        .then((res) => {
          if (res.code === 0) {
            this.categoryOptions = res.data;
          } else {
            this.$message.error(res.message || "获取应用分类失败");
          }
        })
        .catch(() => {
          this.$message.error("获取应用分类失败");
        });
    },
    // 初始化数据
    initTable() {
      this.loading = true;
      subAppList({
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        appName: this.searchForm.appName,
        categoryUuid: this.searchForm.categoryUuid,
        appEnabled: this.searchForm.appEnabled,
        orderItems: "orderNum",
        orderRule: "asc",
      })
        .then((res) => {
          this.loading = false;
          if (res.code === 0) {
            this.tableData = res.data;
            this.total = res.count;
          } else {
            this.$message.error(res.message || "获取数据失败");
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error("获取数据失败");
        });
    },
    // 搜索按钮
    searchBtn() {
      this.pageData.pageNum = 1;
      this.initTable();
    },
    // 重置按钮
    resetBtn() {
      this.searchForm.appName = "";
      this.searchForm.categoryUuid = "";
      this.searchForm.appEnabled = "";
      this.pageData.pageNum = 1;
      this.initTable();
    },
    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageData.pageSize = val;
      this.initTable();
    },
    // 处理分页页码变化
    handleCurrentChange(val) {
      this.pageData.pageNum = val;
      this.initTable();
    },
    // 新增按钮
    addBtn() {
      if (this.$refs.formData) {
        this.$refs.formData.resetFields();
      }
      this.$nextTick(() => {
        this.formData.appName = "";
        this.formData.appCode = "";
        this.formData.categoryId = "";
        this.formData.appIcon = "";
        this.formData.pcUrl = "";
        this.formData.mobileUrl = "";
        this.formData.orderNum = 1;
        this.formData.appEnabled = 1;
        this.formData.memo = "";
        this.formData.uuid = "";
        this.imageUrl = "";
      });
      this.dialogVisible = true;
    },
    // 编辑按钮
    editBtn(row) {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.formData.appName = row.appName;
        this.formData.appCode = row.appCode;
        this.formData.categoryId = row.categoryId;
        this.formData.appIcon = row.appIcon;
        this.formData.pcUrl = row.pcUrl;
        this.formData.mobileUrl = row.mobileUrl || "";
        this.formData.orderNum = row.orderNum;
        this.formData.appEnabled = row.appEnabled !== undefined ? row.appEnabled : 1;
        this.formData.memo = row.memo || "";
        this.formData.uuid = row.uuid;
        if (row.appIcon) {
          this.imageUrl = this.location + row.appIcon;
        } else {
          this.imageUrl = "";
        }
      });
    },
    // 删除按钮
    delBtn(row) {
      this.$confirm("此操作将删除该子应用, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const data = {
            uuid: row.uuid,
          };
          subAppDelete(data)
            .then((res) => {
              if (res.code === 0) {
                this.$message.success(res.message || "删除成功！");
                this.initTable();
              } else {
                this.$message.error(res.message || "删除失败！");
              }
            })
            .catch(() => {
              this.$message.error("删除失败！");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 确认按钮
    confirmBtn() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.formData.uuid) {
            // 编辑
            subAppUpdate(this.formData)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success(res.message || "修改成功！");
                  this.dialogVisible = false;
                  this.initTable();
                } else {
                  this.$message.error(res.message || "修改失败！");
                }
              })
              .catch(() => {
                this.$message.error("修改失败！");
              });
          } else {
            // 新增
            subAppSave(this.formData)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success(res.message || "添加成功！");
                  this.dialogVisible = false;
                  this.initTable();
                } else {
                  this.$message.error(res.message || "添加失败！");
                }
              })
              .catch(() => {
                this.$message.error("添加失败！");
              });
          }
        }
      });
    },
  },
  created() {
    this.getCategoryOptions();
    this.initTable();
  },
};
</script>

<style scoped lang="less">
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.marginTop {
  margin-top: 10px;
}
.addbtn {
  margin-bottom: 10px;
}
</style>
