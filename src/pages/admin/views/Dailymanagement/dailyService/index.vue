<template>
  <div class="box">
    <el-tabs type="card" v-model="activeName">
      <el-tab-pane label="应用分类管理" lazy><Category></Category></el-tab-pane>
      <el-tab-pane label="子应用管理"><SubApp v-if="activeName === '1'"></SubApp></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Category from "./components/category";
import SubApp from "./components/subApp";

export default {
  components: {
    Category,
    SubApp,
  },
  data() {
    return {
      activeName: "0", // 默认选中第一个标签页
    };
  },
};
</script>

<style scoped lang="less"></style>
