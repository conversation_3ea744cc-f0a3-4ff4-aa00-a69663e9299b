<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 搜索开始 -->
      <div class="search">
        <el-form :model="listQuery" width="100%" label-position="left" class="itemBox" label-width="100px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
              <el-form-item label="链接名称" prop="linkName">
                <el-input v-model="listQuery.linkName" placeholder="请输入链接名称"  clearable style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
              <el-form-item label="链接地址" prop="linkUrl">
                <el-input v-model="listQuery.linkUrl" placeholder="请输入链接地址"  clearable style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
              <el-form-item label="是否显示" prop="linkVisible">
                <el-select v-model="listQuery.linkVisible" placeholder="请选择" clearable  style="width: 100%">
                  <el-option label="显示" value="1" />
                  <el-option label="隐藏" value="0" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-bottom: 15px">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" @click="searchBtn">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 新增按钮 -->
      <el-row class="important marginTop">
        <el-col :span="24"><el-button type="primary" icon="el-icon-plus" class="addbtn" @click="handleCreate">新增</el-button></el-col>
      </el-row>
      <!-- 数据表格开始 -->
      <el-row>
        <el-table
          v-loading="listLoading"
          :data="list"
          stripe
          border
          style="width: 100%"
          class="marginTop"
          element-loading-text="Loading"
          highlight-current-row
        >
          <el-table-column label="排序" width="100" align="center">
            <template slot-scope="scope">{{ scope.row.orderNum }}</template>
          </el-table-column>
          <el-table-column label="链接名称">
            <template slot-scope="scope">{{ scope.row.linkName }}</template>
          </el-table-column>
          <el-table-column label="链接地址" width="300">
            <template slot-scope="scope">
              <el-link type="primary" :href="scope.row.linkUrl" target="_blank">{{ scope.row.linkUrl }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="图标" width="100" align="center">
            <template slot-scope="scope">
              <img v-if="scope.row.linkSvg" :src="location + scope.row.linkSvg" class="table-icon" />
              <i v-else-if="scope.row.linkIcon" :class="'iconfont ' + scope.row.linkIcon" class="table-icon-class"></i>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="显示状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.linkVisible === 1 ? 'success' : 'info'">
                {{ scope.row.linkVisible === 1 ? "显示" : "隐藏" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template slot-scope="scope">{{ scope.row.memo }}</template>
          </el-table-column>
          <el-table-column label="创建时间" width="170" align="center">
            <template slot-scope="scope">{{ scope.row.gmtCreate }}</template>
          </el-table-column>
          <el-table-column label="操作" width="200px" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
              <el-button type="text" icon="el-icon-delete" style="color: red" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!-- 分页开始 -->
      <el-row>
        <el-col>
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="listQuery.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="listQuery.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </el-col>
      </el-row>
      <!-- 分页结束 -->

      <!-- Dialog for adding and editing -->
      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="900px" top="8vh" append-to-body>
        <el-form ref="dataForm" :rules="rules" :model="temp" label-width="100px" style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="链接名称" prop="linkName">
                <el-input v-model="temp.linkName"  clearable placeholder="请输入链接名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="链接地址" prop="linkUrl">
                <el-input v-model="temp.linkUrl"  clearable placeholder="请输入链接地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="图标类名" prop="linkIcon">
                <icon-selector v-model="temp.linkIcon"></icon-selector>
              </el-form-item>
              <div class="icon-container">
                <div v-if="temp.linkIcon" class="icon-preview">
                  <span>图标预览：</span>
                  <i :class="'iconfont ' + temp.linkIcon" style="font-size: 24px; margin-left: 10px;"></i>
                </div>
                <div class="tip-text">* 可以选择使用图标类名或上传图标图片，如果两者都设置，将优先显示图标图片</div>
              </div>
            </el-col>
            <el-col :span="12">
              <el-form-item label="链接图标" prop="linkSvg">
                <file-uploader
                  v-model="temp.linkSvg"
                  type="image"
                  :maxSize="2"
                  tip="建议上传尺寸：32px * 32px"
                  @remove="handleIconRemove"
                ></file-uploader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否显示" prop="linkVisible">
                <el-switch v-model="temp.linkVisible" :active-value="1" :inactive-value="0" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序权重" prop="orderNum">
                <el-input-number v-model="temp.orderNum"  controls-position="right" :min="1" :max="9999" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="memo">
                <el-input
                  v-model="temp.memo"
                  type="textarea"

                  placeholder="请输入备注"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">确认</el-button>
        </div>
      </el-dialog>

      <el-dialog title="提示" :visible.sync="dialogDeleteVisible" width="30%">
        <span>确定要删除该友情链接吗？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDeleteVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmDelete">确定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { friendLinkList, friendLinkSave, friendLinkUpdate, friendLinkDelete } from "@/pages/admin/api/Dailymanagement/friendLink";
import FileUploader from "@/pages/admin/components/FileUploader/index.vue";
import IconSelector from "@/pages/admin/components/IconSelector/index.vue";

export default {
  name: "FriendLink",
  components: {
    FileUploader,
    IconSelector,
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        linkName: "",
        linkUrl: "",
        linkVisible: "",
        orderItems: "orderNum",
        orderRule: "asc",
      },
      temp: {
        uuid: "",
        linkName: "",
        linkUrl: "",
        linkIcon: "",
        linkSvg: "",
        linkVisible: 1,
        orderNum: 0,
        memo: "",
      },
      dialogFormVisible: false,
      dialogDeleteVisible: false,
      dialogStatus: "",
      textMap: {
        update: "编辑友情链接",
        create: "添加友情链接",
      },
      rules: {
        linkName: [{ required: true, message: "链接名称不能为空", trigger: "blur" }],
        linkUrl: [{ required: true, message: "链接地址不能为空", trigger: "blur" }],
        linkVisible: [{ required: true, message: "请选择是否显示", trigger: "change" }],
        orderNum: [{ required: true, message: "排序权重不能为空", trigger: "blur" }],
      },
      deleteUuid: "", // 存储要删除的项目的UUID
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      friendLinkList(this.listQuery)
        .then((response) => {
          const { data, count } = response;
          this.list = data;
          this.total = count;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
          this.$message.error("获取列表失败");
        });
    },
    searchBtn() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    resetBtn() {
      this.listQuery.linkName = "";
      this.listQuery.linkUrl = "";
      this.listQuery.linkVisible = "";
      this.listQuery.pageNum = 1;
      this.getList();
    },
    // 处理分页大小变化
    handleSizeChange(val) {
      this.listQuery.pageSize = val;
      this.getList();
    },
    // 处理分页页码变化
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },
    resetTemp() {
      this.temp = {
        uuid: "",
        linkName: "",
        linkUrl: "",
        linkIcon: "",
        linkSvg: "",
        linkVisible: 1,
        orderNum: 0,
        memo: "",
      };
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          friendLinkSave(this.temp)
            .then(() => {
              this.dialogFormVisible = false;
              this.$message({
                message: "创建成功",
                type: "success",
              });
              this.getList();
            })
            .catch(() => {
              this.$message.error("创建失败");
            });
        }
      });
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row); // 复制对象，避免影响原数据
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          friendLinkUpdate(tempData)
            .then((res) => {
              if (res.code === 0) {
                this.dialogFormVisible = false;
                this.$message({
                  message: "更新成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch(() => {
              this.$message.error("更新失败");
            });
        }
      });
    },
    handleDelete(row) {
      this.deleteUuid = row.uuid;
      this.dialogDeleteVisible = true;
    },
    confirmDelete() {
      this.listLoading = true;
      friendLinkDelete({ uuid: this.deleteUuid })
        .then((response) => {
          this.$notify({
            title: "成功",
            message: "删除成功",
            type: "success",
            duration: 2000,
          });
          this.getList();
        })
        .catch((err) => {
          console.log(err);
        });
      this.dialogDeleteVisible = false;
      this.deleteUuid = "";
    },
    handleIconRemove() {
      // 清空链接图标路径
      this.temp.linkSvg = "";
      this.$notify({
        title: "提示",
        message: "图标已删除",
        type: "info",
        duration: 2000,
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.searchBtn {
  display: flex;
  justify-content: flex-start;
  margin-left: 20px;
}
.marginTop {
  margin-top: 10px;
}
.addbtn {
  margin-bottom: 10px;
}
.table-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}
.table-icon-class {
  font-size: 24px;
}
.icon-container {
  padding-left: 100px;
}
.icon-preview {
  margin-top: -15px;
  display: flex;
  align-items: center;
}
.tip-text {
  font-size: 12px;
  color: #909399;
}
</style>
