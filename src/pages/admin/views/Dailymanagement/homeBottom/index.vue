<template>
  <div class="HomeTemplate boxmore">
    <div class="search">
      <el-form
        :model="searchForm"
        width="100%"
        label-position="center"
        class="itemBox"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <el-form-item label="模版">
              <el-input
                v-model="searchForm.fileName"
                placeholder="请输入模版"

                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <el-button
              type="primary"
              icon="el-icon-search"

              class="smallbtn"
              style="margin-left: 20px"
              @click="searchBtn"
            >搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row class="important marginTop">
      <el-col
        :span="24"
      ><el-button
        v-allow="'home:template:save'"
        type="primary"
        icon="el-icon-plus"
        class="addbtn"
        @click="addBtn"
      >新增模版</el-button></el-col>
    </el-row>
    <!-- 数据表格开始 -->
    <el-row>
      <el-table
        v-loading="loading"
        :data="tabledata"
        stripe
        border
        style="width: 100%"
        class="marginTop"
      >
        <el-table-column prop="title" label="模版名称" align="center">
          <template slot-scope="scope">
            {{ scope.row.template.templateName }}
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="是否默认" align="center">
          <template slot-scope="scope">
            <p v-if="scope.row.isDefault == 0">否</p>
            <p v-else>是</p>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="模版文件" align="center">
          <template slot-scope="scope">
            <el-button
              v-allow="'home:template:detail'"
              type="text"
              icon="el-icon-document"
              @click="handle_template(scope.row)"
            >查看文件</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="模版绑定" align="center">
          <template slot-scope="scope">
            <el-button v-allow="'home:template-bind:page'" type="text" @click="template_binding(scope.row)">
              <div class="binding-box flex-box">
                <img src="@admin/icons/svg/binding.svg" class="binding">
                <span>查看</span>
              </div>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="gmtCreate" label="创建时间" align="center" />
        <el-table-column prop="gmtModified" label="更新时间" align="center" />
        <el-table-column prop="memo" label="备注" align="center" />
        <el-table-column label="操作" width="220px" align="center">
          <template slot-scope="scope">
            <el-button
              v-allow="'home:template:update'"
              type="text"
              icon="el-icon-edit"
              @click="editBtn(scope.row)"
            >编辑</el-button>
            <el-button
              v-allow="'home:template:delete'"
              type="text"
              icon="el-icon-delete"
              style="color: red"
              @click="delBtn(scope.row)"
            >删除</el-button>
            <el-button
              v-allow="'home:template:default'"
              type="text"
              icon="el-icon-setting"
              @click="defaultBtn(scope.row)"
            >设置默认模版</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 分页开始 -->
    <div class="paging">
      <Paging
        :page-count="pageCount"
        :currentpage="currentpage_cendev"
        @pagebar="changePage"
      />
    </div>
    <!-- 新增弹窗开始 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="diaShow"
      width="40%"
      top="10%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-row>
        <el-form
          ref="diaForm"
          :model="diaForm"
          label-width="100px"
          :rules="rules"
        >
          <el-form-item label="模版名称" prop="template.templateName">
            <el-input
              v-model="diaForm.template.templateName"

              :rules="[
                { required: true, message: '请输入模版名称', trigger: 'blur' },
              ]"
              prop="template.templateName"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="备注" prop="memo">
            <el-input
              v-model="diaForm.memo"

              type="textarea"
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button  @click="diaShow = false">取消</el-button>
        <el-button
          type="primary"

          @click="confirmBtn"
        >确认</el-button>
      </span>
    </el-dialog>
    <!-- 模板文件查看开始 -->
    <el-dialog
      title="模板文件管理"
      :visible.sync="diaShow_prievewtemp"
      append-to-body
      :close-on-click-modal="false"
      top="0px"
      width="80%"
      @close="CancleBtn"
    >
      <codemirror
        ref="Editortemplate"
        v-model="content"
        :options="cmOptions"
        class="code"
        @changes="changes"
      />
      <div class="footerbox">
        <span slot="footer" class="dialog-footer">
          <el-button

            class="fl marginTop30"
            @click="CancleBtn"
          >取消</el-button>
          <el-button
            v-allow="'home:template:update'"
            type="primary"

            class="fl marginTop30"
            @click="confirmBtn_prievwtemo"
          >保存</el-button>
          <el-button
            v-allow="'home:template:preview' && 'home:template:validate'"
            type="primary"

            style="margin-left: 100px"
            class="fl marginTop30"
            @click="handleprievew"
          >预览</el-button>
          <!-- <el-button
              type="primary"

              style="margin-left: 100px"
              class="fl marginTop30"
              @click="handleimg"
              >上传文档图片获取链接地址</el-button
            > -->
          <el-upload
            ref="upload_templateimg"
            action
            class="marginTop30 fl"
            style="margin-left: 4%"
            :http-request="sumbitUpload_templateimg"
            :show-file-list="false"
            :before-upload="beforeUpload_templateimg"
          >
            <el-button
              slot="trigger"
              v-allow="'home:template:upload'"
              type="primary"
              class="addbtn"

            >上传文件图片获取链接地址</el-button>
          </el-upload>
          <div v-if="showurl" class="fl urlbox">
            <p v-for="(item, index) in urlOptions" :key="index">{{ item }}</p>
          </div>
        </span>
      </div>
    </el-dialog>
    <el-dialog
      title="预览模板"
      :visible.sync="diaShow_preview"
      width="100%"
      top="0%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-row>
        <!-- <el-button
          type="primary"

          class="fl marginTop30"
          @click="pdfBtn(1)"
        >生成PDF</el-button> -->
        <!-- <el-button
          type="primary"

          class="fl marginTop30"
          @click="pdfBtn(2)"
        >生成图片</el-button> -->
      </el-row>
      <iframe
        :srcdoc="iframeContent"
        frameborder="0"
        style="width: 100%; height: 800px"
      />
    </el-dialog>
    <!-- 模版绑定查看 -->
    <el-dialog
      title="绑定模板"
      :visible.sync="diaShow_templateBind"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-row>
        <el-button
          v-allow="'home:template-bind:add'"
          type="primary"
          icon="el-icon-plus"
          class="addbtn"
          @click="addTemplateType"
        >新增</el-button>
        <!-- 数据表格开始 -->
        <el-row>
          <el-table
            v-loading="loading"
            :data="templateTabledata"
            stripe
            border
            style="width: 100%"
            class="marginTop"
          >
            <el-table-column prop="title" label="模版名称" align="center">
              <template slot-scope="scope">
                {{ scope.row.templateName }}
              </template>
            </el-table-column>
            <el-table-column prop="title" label="模版类型" align="center">
              <template slot-scope="scope">
                {{ scope.row.templateKindName }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="220px" align="center">
              <template slot-scope="scope">
                <el-button
                  v-allow="'home:template-bind:delete'"
                  type="text"
                  icon="el-icon-delete"
                  style="color: red"
                  @click="delTemplateBind(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
      </el-row>
    </el-dialog>

    <!-- 模版类型绑定 -->
    <el-dialog
      title="绑定类型"
      :visible.sync="diaShow_templateBindType"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="templateBindForm"
        :model="templateBindForm"
        label-width="100px"
      >
        <el-form-item
          label="类型绑定"
          prop="templateKind"
          :rules="[
            { required: true, message: '请选择类型', trigger: 'change' },
          ]"
        >
          <el-select
            v-model="templateBindForm.templateKind"
            placeholder="请选择模版类型"
          >
            <el-option
              v-for="(item, index) in bindList"
              :key="index + 'bindList'"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button

          @click="diaShow_templateBindType = false"
        >取消</el-button>
        <el-button
          type="primary"

          @click="confirmTemplateBind"
        >确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Paging from '@admin/components/pagebar/Paging.vue'
import {
  getFile,
  templateDetail,
  templateAdd,
  templateEdit,
  templateDefault,
  templateDelete,
  templatePreview,
  templateValidate,
  templateUpload,
  templateKind,
  templateKindSave,
  templateKindPage,
  templateKindDelete
} from '@admin/api/Dailymanagement/HomeTemplate'
import { codemirror } from 'vue-codemirror'
import { decode, encode } from '@admin/utils/base64'
// import Roomlab from '@admin/components/Select/Roomlab.vue'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/ambiance.css'
import 'codemirror/mode/xml/xml.js'
import 'codemirror/addon/edit/closebrackets.js'
require('codemirror/mode/javascript/javascript')
import { handleExportPdf, handleExportImg } from '@admin/utils/htmlPdf'
export default {
  components: {
    Paging,
    codemirror
  },
  data() {
    return {
      searchForm: {
        fileName: ''
      },
      pageData: {
        page: 10,
        pageNum: 1
      },
      loading: false, // 表格loading
      tabledata: [], // 表格数据
      pageCount: 0,
      currentpage_cendev: 0,
      diaShow: false,
      dialogTitle: '新增首页模版',
      diaForm: {
        template: {
          templateName: ''
        },
        memo: ''
      },
      rules: {
        'template.templateName': [
          { required: true, message: '请输入模版名称', trigger: 'blur' }
        ]
      },
      diaShow_prievewtemp: false,
      templateTitle: '',
      cmOptions: {
        tabSize: 4,
        mode: 'text/javascript',
        theme: 'ambiance',
        indentWithTabs: true,
        smartIndent: true,
        lineNumbers: true,
        matchBrackets: true,
        autoCloseBrackets: true
      },
      content: '<div><p>123</p></div>',
      diaShow_variable: false,
      showurl: false,
      urlOptions: [],
      tabledataVariable: [],
      diaShow_preview: false,
      divcontent: '',
      diaShow_bind_add: false,
      tableData_bind: [],
      diaForm_bind_add: {
        labTemplateUuid: '',
        labUuids: []
      },
      pageCount_bind: 0,
      currentpage_bind: 1,
      prievwuuid: '',
      iframeContent: '',
      diaShow_templateBind: false,
      templateBindForm: {
        templateKind: '',
        templateBindUUid: ''
      },
      bindList: [],
      templateTabledata: [],
      diaShow_templateBindType: false
    }
  },
  computed: {
    codemirror() {
      return this.$refs.Editortemplate.codemirror
    }
  },
  created() {
    this.initTable()
    this.getTemplateKind()
  },
  methods: {
    async initTable() {
      this.loading = true
      const postData = {
        pageNum: this.pageData.pageNum,
        pageSize: this.pageData.page,
        ...this.searchForm
      }
      const res = await getFile(postData)
      this.loading = false
      if (res.code === 0) {
        this.tabledata = res.data
        this.pageCount = res.count
      } else {
        this.$message({
          message: res.message,
          type: 'error',
          duration: '5000'
        })
      }
    },
    // 点击新增按钮
    addBtn() {
      this.diaShow = true
      this.dialogTitle = '新增模版'
      if (this.$refs.diaForm !== undefined) {
        this.$refs.diaForm.resetFields()
      }
      this.diaForm.uuid = ''
      this.diaShow = true
    },
    // 点击提交按钮
    async confirmBtn() {
      this.$refs.diaForm.validate(async(valid) => {
        if (valid) {
          if (this.diaForm.uuid) {
            // 编辑
            const postData = {
              ...this.diaForm
            }
            const res = await templateEdit(postData)
            if (res && res.code === 0) {
              this.$message.success(res.message)
              this.initTable()
              this.diaShow = false
            } else {
              this.$message({
                message: res.message,
                type: 'error',
                duration: '5000'
              })
            }
          } else {
            const postData = {
              ...this.diaForm
            }
            const res = await templateAdd(postData)
            if (res && res.code === 0) {
              this.$message.success(res.message)
              this.initTable()
              this.diaShow = false
            } else {
              this.$message({
                message: res.message,
                type: 'error',
                duration: '5000'
              })
            }
          }
        }
      })
    },
    // 点击表格中的预览操作
    handle_template(row) {
      this.prievwuuid = row.uuid
      this.templateTitle = row.template.templateName
      const json = {
        uuid: row.uuid
      }
      templateDetail(json).then((res) => {
        if (res.code === 0) {
          this.diaShow_prievewtemp = true
          this.content = decode(res.data.template.templateContent)
          this.$nextTick(() => {
            setTimeout(() => {
              this.codemirror.setValue(
                this.content ? JSON.stringify(JSON.parse(this.content), null, 2) : ''
              )
            }, 800)
          })
        }
      })
    },
    CancleBtn() {
      this.diaShow_prievewtemp = false
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData
      // 页面刷新
      this.initTable()
    },
    // 搜索列表
    searchBtn() {
      this.pageData.pageNum = 1
      this.initTable()
    },
    // 查看变量操作
    handle_variable() {
      this.diaShow_variable = true
      this.getallftlFields()
    },
    // 点击模板文件管理的确认按钮
    confirmBtn_prievwtemo() {
      const json = {
        uuid: this.prievwuuid,
        template: {
          templateName: this.templateTitle,
          templateContent: encode(this.content)
        }
      }
      templateEdit(json).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.message)
          this.diaShow_prievewtemp = false
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击预览
    handleprievew() {
      const json = {
        templateContent: encode(this.content)
      }
      templateValidate(json).then((res) => {
        if (res.code === 0) {
          const json = {
            uuid: this.prievwuuid
          }
          templatePreview(json).then((res) => {
            if (res.code === 0) {
              const content = decode(res.data)
              this.iframeContent = content
              this.diaShow_preview = true
            }
          })
        } else {
          this.$message.error(res.message)
        }
      })
      // window.open(
      //   '/transcriptapi/report-format/previewFtl?uuid=' + this.prievwuuid + ''
      // )
    },
    // 模板中图片上传
    sumbitUpload_templateimg(item) {
      this.$confirm(
        '图片上传成功后，完整链接地址会展示在右侧，可复制粘贴在编辑器中的img标签引入图片',
        '提示',
        {
          confirmButtonText: '确定上传',
          cancelButtonText: '取消上传',
          type: 'warning'
        }
      )
        .then(() => {
          this.$refs.upload_templateimg.clearFiles()
          const fd = new FormData()
          fd.append('file', item.file)
          templateUpload(fd).then((res) => {
            if (res.code === 0) {
              const host =
                window.location.protocol + '//' + window.location.host
              const data = res.data
              const url = host + window.g.ApiUrl + window.g.thirdurl + data
              this.urlOptions.push(url)
              this.showurl = true
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消上传'
          })
        })
    },
    // 模板上传文件里的图片的类型限制
    beforeUpload_templateimg(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = testmsg === 'jpg'
      const extension1 = testmsg === 'png'
      if (!extension && !extension1) {
        this.$message.error('上传文件只能是jpg、png类型')
        return false
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.parent == '0') {
        return 'warning-row'
      }
    },
    // 生成pdf或图片
    pdfBtn(flag) {
      if (flag == 1) {
        handleExportPdf(this.$refs.content, this.templateTitle)
      } else {
        handleExportImg(this.$refs.content, this.templateTitle)
      }
    },
    // 新增绑定
    addBtn_bind() {
      this.diaShow_bind_add = true
      this.diaForm_bind_add.labUuids = []
    },
    // 表格排序
    sortChange_bind(obj) {
      this.diaForm_bind.orderRule = obj.order == 'ascending' ? 'asc' : 'desc'
      this.diaForm_bind.orderItems = obj.prop + ',sid'
      this.initTable_bind()
    },
    // 删除模版
    delBtn(row) {
      this.$confirm('此操作将永久删除该模版, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const json = {
            uuid: row.uuid
          }
          const res = await templateDelete(json)
          if (res.code === 0) {
            this.$message.success(res.message)
            this.initTable()
          } else {
            this.$message({
              message: res.message,
              type: 'error',
              duration: '5000'
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    editBtn(row) {
      this.diaForm = { ...row }
      this.dialogTitle = '编辑模版'
      this.diaShow = true
    },
    changes() {},
    // 设置默认模版
    defaultBtn(row) {
      this.$confirm('此操作将设置该模版为默认模版, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const json = {
            uuid: row.uuid
          }
          const res = await templateDefault(json)
          if (res.code === 0) {
            this.$message.success(res.message)
            this.initTable()
          } else {
            this.$message({
              message: res.message,
              type: 'error',
              duration: '5000'
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //
    async getTemplateKind() {
      const res = await templateKind()
      if (res.code === 0) {
        this.bindList = res.data
      }
    },
    // 模版绑定 查看
    async template_binding(row) {
      this.templateBindForm.templateBindUUid = row.uuid
      this.getTemplateKindPage()
    },
    async getTemplateKindPage() {
      const postData = {
        homeTemplateUuid: this.templateBindForm.templateBindUUid
      }
      const res = await templateKindPage(postData)
      if (res.code === 0) {
        this.templateTabledata = res.data
        this.diaShow_templateBind = true
      }
    },
    //
    addTemplateType() {
      this.diaShow_templateBindType = true
    },
    // 模版绑定确定
    async confirmTemplateBind() {
      const postData = {
        homeTemplateUuid: this.templateBindForm.templateBindUUid,
        templateKind: this.templateBindForm.templateKind
      }
      const res = await templateKindSave(postData)
      if (res.code === 0) {
        this.$message.success(res.message)
        this.diaShow_templateBindType = false
        this.getTemplateKindPage()
      }
    },
    // 模版删除
    delTemplateBind(row) {
      this.$confirm('此操作将永久删除该模版绑定, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const postData = {
          uuidSet: [row.bindUuid]
        }
        const res = await templateKindDelete(postData)
        if (res.code === 0) {
          this.$message.success(res.message)
          this.getTemplateKindPage()
        } else {
          this.$message({
            message: res.message,
            type: 'error',
            duration: '5000'
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .CodeMirror {
  height: 500px;
  margin-top: 10px;
}
</style>
<style lang="scss" scoped>
.footerbox {
  text-align: center;
  margin-top: 20px;
  height: 80px;
  padding-left: 20%;
  box-sizing: border-box;
  .urlbox {
    margin-left: 2%;
    height: 100px;
    width: 500px;
    background: rgba($color: #000000, $alpha: 0.8);
    overflow: auto;
    p {
      color: white;
      line-height: 30px;
    }
  }
}
.binding-box {
  align-items: center;
  .binding {
    width: 13px;
    height: 13px;
    margin-right: 5px;
    vertical-align: bottom;
  }
}
</style>
<style>
.fl {
  float: left;
}
.fr {
  float: right;
}
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
.certificatebox {
  width: 1920px;
  height: 1080px;
  /* background: url("http://***********:86/lab/upload/image/bg-1867100267800039424.jpg")
              no-repeat;
            background-size: 100% 100%; */
  position: relative;
}
.bgimg {
  width: 1920px;
  height: 1080px;
  position: absolute;
  z-index: 1;
  top: 0px;
  left: 0px;
}
.certificatebox .cntbox {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  z-index: 2;
  padding-top: 8%;
  box-sizing: border-box;
}
.certificatebox .titlebox {
  width: 40%;
  text-align: center;
  margin: 0 auto;
}
.certificatebox .titlebox .toptitle {
  width: 100%;
  height: 110px;
}
.certificatebox .titlebox .toptitle .titleimg {
  width: 80px;
}
.certificatebox .titlebox .toptitle .chtext {
  font-size: 60px;
  font-weight: bold;
  line-height: 0px;
  margin-top: 40px;
  margin-left: 5px;
}
.certificatebox .titlebox .bottomimg {
  width: 100%;
}
.certificatebox .titlebox .entext {
  font-size: 32px;
  color: #c09a15;
  margin-top: 10px;
  letter-spacing: 2px;
}
.certificatebox .cnt {
  width: 50%;
  height: 400px;
  margin: 6% auto;
}
.certificatebox .cnt .name {
  text-align: center;
  font-size: 50px;
}
.certificatebox .cnt .name span:nth-of-type(2) {
  margin-left: 80px;
  color: #1e93d6;
}
.certificatebox .cnt .text {
  text-indent: 100px;
  line-height: 100px;
  font-size: 50px;
}
.certificatebox .date {
  position: absolute;
  bottom: 120px;
  right: 24%;
  font-size: 40px;
  z-index: 10;
}
.certificatebox .zhang {
  width: 240px;
  position: absolute;
  right: 25%;
  bottom: 90px;
  z-index: 8;
}
</style>