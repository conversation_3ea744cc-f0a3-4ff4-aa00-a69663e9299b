<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <!-- 搜索表单 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="left" class="itemBox">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item label="菜单名称" prop="menuName">
                <el-input v-model="searchForm.menuName" placeholder="请输入菜单名称"  clearable style="width: 80%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item label="菜单类型" prop="kind">
                <el-select v-model="searchForm.kind" placeholder="请选择菜单类型"  clearable style="width: 80%">
                  <el-option v-for="(item, index) in addOption" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-bottom: 15px">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" @click="handleSearch">搜索</el-button>
              <!-- <el-button  @click="resetSearch">重置</el-button> -->
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-row>
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="2">
          <el-button type="primary" icon="el-icon-plus" @click="addFn" class="addbtn">添加菜单</el-button>
        </el-col>
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="2">
          <el-button icon="el-icon-caret-bottom" type="primary" @click="openall" class="addbtn">全部展开</el-button>
        </el-col>
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="2">
          <el-button icon="el-icon-caret-top" type="primary" @click="closeall" class="addbtn">全部折叠</el-button>
        </el-col>
      </el-row>

      <el-row style="margin-top: 20px">
        <el-col :span="24">
          <!-- 树状表格开始-->
          <zk-table
            ref="table"
            sum-text="sum"
            index-text="#"
            :data="filteredTableData"
            :columns="columns"
            :stripe="props.stripe"
            :border="props.border"
            :show-header="props.showHeader"
            :show-summary="props.showSummary"
            :show-row-hover="props.showRowHover"
            :show-index="props.showIndex"
            :tree-type="props.treeType"
            :is-fold="props.isFold"
            :expand-type="props.expandType"
            :selection-type="props.selectionType"
            v-loading="loading"
          >
            <template slot="kind" slot-scope="scope">
              <el-tag  :type="['danger', 'primary', 'success', 'info', 'warning'][scope.row.kind % 5]" plain>
                {{
                  addOption.find((item) => item.value === scope.row.kind) && addOption.find((item) => item.value === scope.row.kind).label
                }}
              </el-tag>
            </template>
            <template slot="icon" slot-scope="scope">
              <img v-if="scope.row.menuSvg" :src="location + scope.row.menuSvg" class="table-icon" />
              <i v-else-if="scope.row.menuIcon" :class="'iconfont ' + scope.row.menuIcon" class="table-icon-class"></i>
              <span v-else>-</span>
            </template>
            <template slot="type" slot-scope="scope">
              <div>{{ scope.row.level | hierarchy(levelList) }}</div>
            </template>
            <template slot="likes" slot-scope="scope">
              <el-button
                v-if="scope.row.kind === 1"
                type="text"
                icon="el-icon-plus"
                @click="addChildMenu(scope.row)"
                title="仅导航菜单可添加子菜单"
              >
                添加子菜单
              </el-button>
              <el-button type="text" icon="el-icon-edit" @click="edit(scope.row)">编辑</el-button>
              <el-button type="text" icon="el-icon-delete" style="color: red" @click="remove(scope.row)">删除</el-button>
            </template>
          </zk-table>
          <!-- 树状表格结束-->

          <!-- 新增模态框开始-->
          <el-dialog
            title="菜单管理"
            :visible.sync="dialogFormVisible"
            :modal-append-to-body="false"
            :width="addForm.kind === 3 ? '900px' : '700px'"
            :close-on-click-modal="false"
            :destroy-on-close="true"
          >
            <div class="content">
              <el-form :model="addForm" label-width="100px" :rules="fnRules" ref="changeFn">
                <el-row :gutter="20">
                  <!-- 表单内容 -->
                  <el-col :span="12">
                    <el-form-item label="排序" prop="orderNum">
                      <el-input-number
                        v-model="addForm.orderNum"

                        controls-position="right"
                        :min="1"
                        style="width: 100%"
                      ></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="菜单类型" prop="kind">
                      <el-select
                        v-model="addForm.kind"
                        placeholder="请选择菜单类型"
                        class="size-full"

                        style="width: 100%"
                        @change="handleKindChange"
                        :disabled="!!addForm.uuid || addForm.kindDisabled"
                      >
                        <el-option v-for="(item, index) in addOption" :key="index" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                      <div v-if="!!addForm.uuid" class="tip-text" style="margin-top: 5px;">* 菜单类型创建后不可修改</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="菜单名称" prop="menuName">
                      <el-input v-model="addForm.menuName" placeholder="请输入菜单名称"  style="width: 100%"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="菜单简称" prop="menuAbbreviation">
                      <el-input v-model="addForm.menuAbbreviation" placeholder="请输入菜单简称"  style="width: 100%"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="父级菜单" prop="menuParentId">
                      <menu-tree-select
                        v-model="addForm.menuParentId"
                        :selectedNodeName="selectedMenuName"
                        :selectableKind="1"
                        placeholder="请选择父级菜单"
                        notSelectableTip="只能选择导航菜单类型的节点"
                        @select="handleMenuSelect"
                        clearable
                        :showTopLevelOption="showTopLevelMenuOption > 0"
                        :disabled="!!addForm.uuid"
                      ></menu-tree-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="addForm.kind == 1">
                    <el-form-item label="下级菜单展示方式" prop="submenuStyle">
                      <el-select
                        v-model="addForm.submenuStyle"
                        placeholder="请选择下级菜单展示方式"
                        class="size-full"

                        style="width: 100%"
                        :disabled="!!addForm.uuid"
                      >
                        <el-option
                          v-for="(item, index) in submenuStyleOptions"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="addForm.kind == 2">
                    <el-form-item label="超链接地址" prop="hyperlinks">
                      <el-input v-model="addForm.hyperlinks" placeholder="请输入超链接地址"  style="width: 100%"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="图标类名" prop="menuIcon">
                      <icon-selector v-model="addForm.menuIcon"></icon-selector>
                    </el-form-item>
                    <div class="icon-container">
                      <div v-if="addForm.menuIcon" class="icon-preview">
                        <span>图标预览：</span>
                        <!-- <svg class="icon" aria-hidden="true">
                          <use :xlink:href="`#${addForm.menuIcon}`"></use>
                        </svg> -->
                        <i :class="'iconfont ' + addForm.menuIcon" style="font-size: 24px; margin-left: 10px;"></i>
                      </div>
                      <div class="tip-text">* 可以选择使用图标类名或上传图标图片，如果两者都设置，将优先显示图标图片</div>
                    </div>
                  </el-col>
                  <el-col :span="12" v-if="addForm.kind == 1 && addForm.menuParentId == 0">
                    <el-form-item label="首页展示" prop="showHome">
                      <el-switch v-model="addForm.showHome"></el-switch>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="addForm.showHome && addForm.kind == 1 && addForm.menuParentId == 0">
                    <el-form-item label="首页展示排序" prop="homeOrderNum">
                      <el-input-number
                        v-model="addForm.homeOrderNum"

                        controls-position="right"
                        :min="1"
                        style="width: 100%"
                      ></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="菜单图标" prop="menuSvg">
                      <file-uploader
                        v-model="addForm.menuSvg"
                        type="image"
                        tip="建议上传尺寸：32px * 32px"
                        @remove="handleIconRemove"
                      ></file-uploader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="菜单封面" prop="coverImage">
                      <file-uploader v-model="addForm.coverImage" type="image" tip=""></file-uploader>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>

              <!-- 富文本编辑器开始 -->
              <!-- 富文本表单部分 (在选择富文本类型时显示) -->
              <div v-if="addForm.kind === 3">
                <hr style="margin: 20px 0;" />
                <el-form
                  :model="richTextForm"
                  width="100%"
                  label-position="left"
                  class="itemBox"
                  label-width="80px"
                  ref="richTextForm"
                  :rules="Contentrules"
                >
                  <el-row :gutter="20">
                    <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
                      <el-form-item label="文章标题" prop="title">
                        <el-input
                          v-model="richTextForm.title"
                          placeholder="请输入文章标题"

                          clearable
                          style="width: 80%"
                        ></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
                      <el-form-item label="状态" prop="publishStatus">
                        <el-select disabled v-model="richTextForm.publishStatus" placeholder="请选择状态"  style="width: 100%">
                          <el-option v-for="item in sysconfigOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
                      <el-form-item label="发布时间" prop="publishDate">
                        <el-date-picker
                          v-model="richTextForm.publishDate"
                          type="datetime"

                          value-format="yyyy-MM-dd HH:mm:ss"
                          style="width:100%"
                          placeholder="选择发布时间"
                        ></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
                <!-- 使用封装的富文本编辑器组件 -->
                <rich-text-editor v-model="editorContent" :height="300" />
              </div>
              <!-- 富文本编辑器结束 -->
            </div>
            <div slot="footer" class="dialog-footer">
              <el-button @click="dialogFormVisible = false" size="mini">取消</el-button>
              <el-button type="primary" @click="editFn('changeFn')" size="mini" plain>确定</el-button>
            </div>
          </el-dialog>
          <!-- 新增模态框结束-->

          <!-- 上传附件开始 -->
          <el-dialog
            title="上传附件"
            class="file-dialog"
            :visible.sync="visibleUpload"
            append-to-body
            :close-on-click-modal="false"
            width="600px"
          >
            <div id="uploader" class="wu-example">
              <div class="btns">
                <!-- 选择文件的按钮 -->
                <div id="fileUpload" class="selectbtn">选择文件</div>
                <!-- <div class="selectbtn" style="position:absolute;left:0">选择文件</div> -->
                <!-- 开始上传按钮 -->
                <div id="toServerBtn" class="btn-default">开始上传</div>
              </div>
              <!--用来存放文件信息-->
              <div id="uploadList" class="uploader-list"></div>
            </div>
          </el-dialog>
          <!-- 上传附件结束 -->
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import ZkTable from "vue-table-with-tree-grid";
Vue.use(ZkTable);
import MenuTreeSelect from "@admin/components/MenuTreeSelect/index.vue";
import FileUploader from "@admin/components/FileUploader/index.vue";
import IconSelector from "@admin/components/IconSelector/index.vue";
import RichTextEditor from "@admin/components/RichTextEditor/index.vue";

//引入接口
import {
  menuTree,
  menuSave,
  menuUpdate,
  menuDelete,
  menuKindDict,
  menuDetail,
  menuRichTextDetail,
  menuSaveRichText,
  menuUpdateRichText,
  menuDeleteRichText,
  uploadmenu,
  uploadvideo,
  uploadFile,
} from "@admin/api/Dailymanagement/menu";

export default {
  data() {
    return {
      tableData: [], //表格数据
      loading: true, //loading加载
      dialogFormVisible: false, //新增模态框初始状态
      props: {
        // 插件表格配置
        stripe: false, //是否显示间隔斑马纹
        border: false, //是否显示纵向边框
        showHeader: true, //是否显示表头
        showSummary: false, //是否显示表尾合计行
        showRowHover: true, //鼠标悬停时，是否高亮当前行
        showIndex: false, //是否显示数据索引
        treeType: true, //是否为树形表格
        isFold: true, //树形表格中父级是否默认折叠
        expandType: false, //是否为展开行类型表格（为 True 时，需要添加作用域插槽, 它可以获取到 row, rowIndex)
        selectionType: false, //是否显示间隔斑马纹
      },
      addForm: {
        //新增 编辑方法时的表单数据
        uuid: "",
        menuName: "",
        menuAbbreviation: "", // 菜单简称
        menuParentId: 0,
        kind: 1,
        kindDisabled: false, // 添加菜单类型是否禁用的标志
        submenuStyle: null, // 下级菜单展示方式
        hyperlinks: "",
        orderNum: 1,
        menuIcon: "",
        menuSvg: "",
        coverImage: "",
        imageUrl: "",
        property: null,
        showHome: false,
        homeOrderNum: 1,
        menuId: null, // 添加menuId字段
      },
      // 富文本表单数据
      richTextForm: {
        uuid: "",
        title: "",
        content: "",
        publishStatus: 2,
        publishDate: this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        menuId: null,
      },
      menuParentOptions: [],
      defaultProps: {
        //设置树节点对应的属性
        children: "children",
        label: "menuName",
      },
      parentName: "", //父级菜单文本名字
      sysconfigOptions: [
        {
          label: "草稿",
          value: 1,
        },
        {
          label: "发布",
          value: 2,
        },
      ],
      // 菜单层级计数
      menuLevelCount: 0,
      // 子菜单展示方式选项
      submenuStyleOptions: [
        {
          label: "下级菜单平铺",
          value: 1,
        },
        {
          label: "下级菜单为超链接列表",
          value: 2,
        },
      ],
      // 图标URL前缀
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      visibleUpload: false,
      // 菜单类型选项，将从接口获取
      addOption: [],
      searchForm: {
        menuName: "",
        kind: "",
      },
      filteredTableData: [],
      // 选中的菜单名称
      selectedMenuName: "",
      // 控制是否显示顶级菜单选项：0-不显示，1-显示（添加菜单时），2-显示（编辑一级菜单时）
      showTopLevelMenuOption: 0,
      editorContent: "",
    };
  },
  watch: {
    "addForm.kind": function(newVal) {
      // 当菜单类型不是菜单(1)或父级菜单不是顶级菜单(0)时，重置showHome和homeOrderNum
      if (newVal !== 1 || this.addForm.menuParentId !== 0) {
        this.addForm.showHome = false;
        this.addForm.homeOrderNum = 1;
      }

      if (newVal === 1) {
        // 当选择导航菜单类型时，保持submenuStyle为null，让用户选择
        this.addForm.submenuStyle = null;
      } else {
        // 其他类型菜单，默认设置submenuStyle为0
        this.addForm.submenuStyle = 0;
      }
    },
    "addForm.menuParentId": function(newVal) {
      // 当父级菜单不是顶级菜单(0)或菜单类型不是菜单(1)时，重置showHome和homeOrderNum
      if (newVal !== 0 || this.addForm.kind !== 1) {
        this.addForm.showHome = false;
        this.addForm.homeOrderNum = 1;
      }
    },
    "addForm.kindDisabled": function(newVal) {
      // 如果父级菜单的submenuStyle为2，提示用户
      if (newVal) {
        this.$notify.info({
          title: "提示",
          message: "当前父级菜单下级菜单为超链接列表，子菜单类型只能为超链接",
        });
      }
    },
  },
  computed: {
    // 表头
    columns() {
      return [
        // 控制插件中的表头和列内容
        {
          label: "排序",
          prop: "orderNum",
          align: "center",
          headerAlign: "center",
          type: "template",
          template: "orderType",
        },
        {
          label: "菜单ID",
          prop: "menuId",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "菜单名称",
          prop: "menuName",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "图标",
          prop: "menuSvg",
          align: "center",
          headerAlign: "center",
          width: "80px",
          type: "template",
          template: "icon",
        },
        {
          label: "类型",
          prop: "kind",
          type: "template",
          template: "kind",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "菜单等级",
          prop: "level",
          type: "template",
          template: "type",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "创建时间",
          prop: "gmtCreate",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "修改时间",
          prop: "gmtModified",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "操作",
          prop: "uuid",
          width: "250px",
          type: "template",
          template: "likes",
          align: "center",
          headerAlign: "center",
        },
      ];
    },
    // 表格层级过滤器
    levelList() {
      return {
        one: "一级菜单",
        two: "二级菜单",
        three: "三级菜单",
        four: "四级菜单",
        five: "五级菜单",
      };
    },
    // 模态框表单验证
    fnRules() {
      return {
        menuName: [
          {
            required: true,
            message: "请输入菜单名称",
            trigger: "blur",
          },
        ],
        menuAbbreviation: [
          {
            required: true,
            message: "请输入菜单简称",
            trigger: "blur",
          },
        ],
        orderNum: [
          {
            required: true,
            message: "请输入排序",
            trigger: "blur",
          },
        ],
        menuParentId: [
          {
            required: true,
            message: "请选择父菜单",
            trigger: "blur",
          },
        ],
        kind: [
          {
            required: true,
            message: "请选择菜单类型",
            trigger: "change",
          },
        ],
        submenuStyle: [
          {
            required: true,
            message: "请选择下级菜单展示方式",
            trigger: "change",
          },
        ],
        hyperlinks: [
          {
            required: true,
            message: `该项不能为空`,
            trigger: "blur",
          },
        ],
      };
    },
    Contentrules() {
      return {
        title: [
          {
            required: true,
            message: `该项不能为空`,
            trigger: "blur",
          },
        ],
        publishStatus: [
          {
            required: true,
            message: `该项不能为空`,
            trigger: "change",
          },
        ],
        publishDate: [
          {
            required: true,
            message: `请选择发布时间`,
            trigger: "change",
          },
        ],
      };
    },
  },
  components: {
    MenuTreeSelect,
    FileUploader,
    IconSelector,
    RichTextEditor,
  },
  filters: {
    // 层级
    hierarchy(val, obj) {
      var str = "";
      if (val === 1) {
        str = obj.one;
      } else if (val === 2) {
        str = obj.two;
      } else if (val === 3) {
        str = obj.three;
      } else if (val === 4) {
        str = obj.four;
      } else if (val === 5) {
        str = obj.five;
      } else {
        str = "未知层级";
      }
      return str;
    },
  },
  methods: {
    initTable() {
      this.loading = true;
      this.menuParentOptions = [];
      // 初始化表格
      menuTree().then((res) => {
        // 处理数据，添加level属性
        const processedData = this.addLevelToMenus(res.data);
        this.tableData = processedData;
        this.filteredTableData = processedData; // 初始化时，过滤后的数据与原始数据相同
        this.loading = false;
        res.data.map((item, index) => {
          this.menuParentOptions.push({
            label: item.menuName,
            value: item.menuId,
          });
        });
      });

      // 获取菜单类型字典
      this.getMenuKindDict();
    },

    // 递归添加level属性到菜单
    addLevelToMenus(menus, parentLevel = 0) {
      if (!menus || menus.length === 0) return [];

      return menus.map((menu) => {
        // 当前菜单的level是父菜单level+1
        const currentLevel = parentLevel + 1;
        // 复制一个新对象，避免修改原始对象
        const newMenu = { ...menu, level: currentLevel };

        // 如果有子菜单，递归处理
        if (newMenu.children && newMenu.children.length > 0) {
          newMenu.children = this.addLevelToMenus(newMenu.children, currentLevel);
        }

        return newMenu;
      });
    },

    // 获取菜单类型字典
    getMenuKindDict() {
      menuKindDict()
        .then((res) => {
          if (res.code === 0 && res.data) {
            this.addOption = res.data.map((item) => ({
              label: item.value,
              value: Number(item.label),
            }));
          } else {
            // 如果接口失败，使用默认值
            this.addOption = [
              { label: "菜单", value: 1 },
              { label: "超链接", value: 2 },
              { label: "富文本", value: 3 },
              { label: "文章列表", value: 4 },
              { label: "下载专区", value: 5 },
            ];
          }
        })
        .catch(() => {
          // 如果接口请求失败，使用默认值
          this.addOption = [
            { label: "菜单", value: 1 },
            { label: "超链接", value: 2 },
            { label: "富文本", value: 3 },
            { label: "文章列表", value: 4 },
            { label: "下载专区", value: 5 },
          ];
        });
    },
    // 处理菜单类型变更
    handleKindChange(val) {
      // 如果是编辑模式，不进行菜单类型变更检查
      if (this.addForm.uuid) {
        return;
      }

      // 检查菜单层级限制
      if (val === 1 && this.menuLevelCount >= 3) {
        this.$notify.warning({
          title: "警告",
          message: "第四级及以上菜单不能选择导航菜单类型",
        });
        // 重置为超链接类型
        this.addForm.kind = 2;
        return;
      }
    },
    // 初始化富文本表单
    initRichTextForm() {
      this.richTextForm = {
        uuid: "",
        title: "",
        content: "",
        publishStatus: 2,
        publishDate: this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        menuId: null,
      };
      // 重置编辑器内容
      this.editorContent = "";
    },
    addFn() {
      // 点击新增按钮
      this.dialogFormVisible = true;
      // 清空数据
      if (this.$refs.changeFn != undefined) {
        this.$refs["changeFn"].resetFields();
      }
      if (this.$refs.richTextForm != undefined) {
        this.$refs["richTextForm"].resetFields();
      }
      // 设置默认为顶级菜单
      this.selectedMenuName = "顶级菜单";
      // 设置为添加菜单模式，显示顶级菜单选项
      this.showTopLevelMenuOption = 1;
      this.$nextTick(function() {
        this.addForm = {
          uuid: "",
          menuName: "",
          menuAbbreviation: "",
          menuParentId: 0,
          kind: 1,
          kindDisabled: false, // 重置禁用状态
          submenuStyle: null, // 默认为null，让用户选择
          hyperlinks: "",
          orderNum: 1,
          menuIcon: "",
          menuSvg: "",
          coverImage: "",
          imageUrl: "",
          property: null,
          showHome: false, // 默认不显示到首页
          homeOrderNum: 1, // 默认首页展示顺序为空
          menuId: null, // 添加menuId字段
        };
        this.initRichTextForm();
      });
    },
    clear(formName) {
      // 点击取消清除数据
      this.dialogFormVisible = false;
      this.$refs[formName].resetFields();
      if (this.$refs.richTextForm) {
        this.$refs.richTextForm.resetFields();
      }
      // 重置顶级菜单选项显示状态
      this.showTopLevelMenuOption = 0;
    },
    handleNodeClick(data) {
      console.log(data);
      // 点击模态框中的右侧树状图
      // this.parentName = data.menuName;
      // this.addForm.menuParentId = data.menuId;
    },
    // 点击表格中的编辑按钮
    edit(rowData) {
      // 弹出弹框
      this.dialogFormVisible = true;

      // 判断是否是编辑一级菜单，如果是则显示顶级菜单选项
      this.showTopLevelMenuOption = rowData.level === 1 ? 2 : 0;

      if (rowData.kind === 3) {
        // 富文本类型菜单，需要获取富文本内容
        menuRichTextDetail({ uuid: rowData.uuid }).then((res) => {
          if (res.code === 0) {
            if (res.data && res.data.article) {
              if (this.$refs.changeFn != undefined) {
                this.$refs["changeFn"].resetFields();
              }
              if (this.$refs.richTextForm != undefined) {
                this.$refs["richTextForm"].resetFields();
              }

              const article = res.data.article;
              const menu = res.data.menu;

              this.$nextTick(() => {
                // 设置菜单表单数据
                this.addForm = {
                  uuid: menu.uuid,
                  menuName: menu.menuName,
                  menuAbbreviation: menu.menuAbbreviation,
                  menuParentId: menu.menuParentId,
                  kind: menu.kind,
                  orderNum: menu.orderNum,
                  menuIcon: menu.menuIcon,
                  menuSvg: menu.menuSvg,
                  coverImage: menu.coverImage,
                  imageUrl: menu.imageUrl,
                  property: menu.property,
                  showHome: menu.kind === 1 && menu.menuParentId === 0 ? menu.showHome : false,
                  homeOrderNum: menu.kind === 1 && menu.menuParentId === 0 ? menu.homeOrderNum || 1 : 1,
                  menuId: menu.menuId, // 保存menuId用于更新时使用
                };
                // 设置父级菜单名称
                this.selectedMenuName = this.findParentName(this.tableData, this.addForm.menuParentId) || "";
                // 设置富文本表单数据
                this.richTextForm = {
                  uuid: article.uuid,
                  title: article.title,
                  content: article.content,
                  publishStatus: article.publishStatus,
                  publishDate: article.publishDate,
                  menuId: article.menuId || menu.menuId, // 保存正确的menuId
                };

                // 设置编辑器内容
                this.editorContent = article.content;
              });
              this.$forceUpdate();
            }
          }
        });
      } else {
        // 获取普通菜单详情
        menuDetail({ uuid: rowData.uuid }).then((res) => {
          if (res.code === 0) {
            const menuData = res.data;
            if (this.$refs.changeFn != undefined) {
              this.$refs["changeFn"].resetFields();
            }
            this.$nextTick(() => {
              this.addForm = {
                uuid: menuData.uuid,
                menuName: menuData.menuName,
                menuAbbreviation: menuData.menuAbbreviation,
                menuParentId: menuData.menuParentId,
                kind: menuData.kind,
                submenuStyle: menuData.submenuStyle,
                hyperlinks: menuData.hyperlinks,
                orderNum: menuData.orderNum,
                menuIcon: menuData.menuIcon,
                menuSvg: menuData.menuSvg,
                coverImage: menuData.coverImage,
                imageUrl: menuData.imageUrl,
                property: menuData.property,
                showHome: menuData.kind === 1 && menuData.menuParentId === 0 ? menuData.showHome : false,
                homeOrderNum: menuData.kind === 1 && menuData.menuParentId === 0 ? menuData.homeOrderNum || 1 : 1,
                menuId: menuData.menuId, // 保存menuId
              };

              // 设置父级菜单名称
              this.selectedMenuName = this.findParentName(this.tableData, this.addForm.menuParentId) || "";
              // 检查菜单层级，判断是否允许创建导航菜单
              this.checkMenuLevel(menuData);
              this.$forceUpdate();
            });
          }
        });
      }
    },
    // 查找父菜单名称
    findParentName(menuList, parentId) {
      // 设置父级菜单名称
      if (parentId === 0) {
        return "顶级菜单";
      } else {
        for (const item of menuList) {
          if (item.menuId === parentId) {
            return item.menuName;
          }
          if (item.children && item.children.length > 0) {
            const found = this.findParentName(item.children, parentId);
            if (found) return found;
          }
        }
        return "";
      }
    },
    // 检查菜单层级
    checkMenuLevel(menu) {
      this.menuLevelCount = 0;
      this.countMenuLevel(menu);
    },
    // 递归计算菜单层级
    countMenuLevel(menu) {
      if (menu.menuParentId === 0) {
        return;
      }

      this.menuLevelCount++;

      // 查找父菜单
      const findParent = (menuList, parentId) => {
        for (const item of menuList) {
          if (item.menuId === parentId) {
            return item;
          }
          if (item.children && item.children.length > 0) {
            const found = findParent(item.children, parentId);
            if (found) return found;
          }
        }
        return null;
      };

      const parent = findParent(this.tableData, menu.menuParentId);
      if (parent) {
        this.countMenuLevel(parent);
      }
    },
    // 点击表格中的删除按钮
    remove(rowData) {
      this.$confirm("此操作将删除该菜单，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 用户点击确认删除
          let json = {
            uuid: rowData.uuid,
          };

          // 根据菜单类型选择不同的删除接口
          if (rowData.kind === 3) {
            // 富文本菜单删除
            menuDeleteRichText(json).then((res) => {
              this.handleDeleteResult(res);
            });
          } else {
            // 普通菜单删除
            menuDelete(json).then((res) => {
              this.handleDeleteResult(res);
            });
          }
        })
        .catch(() => {
          // 用户点击取消删除
          this.$notify.info({
            title: "消息",
            message: "已取消删除",
          });
        });
    },
    // 处理删除结果
    handleDeleteResult(res) {
      if (res.code == 0) {
        // 当删除接口正确提示删除成功
        this.initTable();
        // 重新应用搜索条件
        this.$nextTick(() => {
          this.handleSearch();
        });
        this.$notify({
          title: "成功",
          message: res.message,
          type: "success",
        });
      } else {
        this.$notify({
          title: "警告",
          message: res.message,
          type: "warning",
        });
      }
    },
    // 新增 修改 点击确认按钮触发事件
    editFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 检查必填字段
          if (!this.addForm.menuName || !this.addForm.menuAbbreviation) {
            this.$notify.warning({
              title: "警告",
              message: "菜单名称和菜单简称不能为空",
            });
            return;
          }

          // 检查菜单层级限制
          if (this.menuLevelCount >= 2 && this.addForm.kind === 1 && !this.addForm.uuid) {
            this.$notify.warning({
              title: "警告",
              message: "最多支持二级导航菜单，第三级只能是超链接、富文本、文章列表或下载专区",
            });
            return;
          }

          // 检查kind为1时必须选择submenuStyle
          if (this.addForm.kind === 1 && !this.addForm.submenuStyle) {
            this.$notify.warning({
              title: "警告",
              message: "导航菜单必须选择下级菜单展示方式",
            });
            return;
          }

          // 检查kind为2时必须填写hyperlinks
          if (this.addForm.kind === 2 && !this.addForm.hyperlinks) {
            this.$notify.warning({
              title: "警告",
              message: "超链接菜单必须填写链接地址",
            });
            return;
          }

          if (this.addForm.kind !== 1) {
            // 对于非导航菜单(kind!=1)和非文章列表(kind!=4)的菜单，设置submenuStyle为0
            this.addForm.submenuStyle = 0;
          }

          // 确保如果不满足条件，则重置showHome和homeOrderNum
          if (this.addForm.kind !== 1 || this.addForm.menuParentId !== 0) {
            this.addForm.showHome = false;
            this.addForm.homeOrderNum = 1;
          }

          // 富文本类型的特殊处理
          if (this.addForm.kind === 3) {
            // 检查富文本表单
            if (!this.richTextForm.title) {
              this.$notify.warning({
                title: "警告",
                message: "文章标题不能为空",
              });
              return;
            }

            // 获取编辑器内容
            this.richTextForm.content = this.editorContent;

            if (this.addForm.uuid === "") {
              // 构建新增富文本菜单的数据结构
              const saveData = {
                menu: {
                  menuParentId: this.addForm.menuParentId,
                  menuName: this.addForm.menuName,
                  menuAbbreviation: this.addForm.menuAbbreviation,
                  kind: 3, // 富文本类型
                  orderNum: this.addForm.orderNum,
                },
                article: {
                  menuId: 0, // 会在后端自动关联
                  title: this.richTextForm.title,
                  content: this.richTextForm.content,
                  publishStatus: this.richTextForm.publishStatus,
                  publishDate: this.richTextForm.publishDate,
                },
              };

              menuSaveRichText(saveData).then((res) => {
                if (res.code == 0) {
                  this.$notify({
                    title: "成功",
                    message: res.message,
                    type: "success",
                  });
                  this.dialogFormVisible = false;

                  // 清除数据
                  this.$refs[formName].resetFields();
                  if (this.$refs.richTextForm) {
                    this.$refs.richTextForm.resetFields();
                  }
                  this.parentName = "";

                  // 重新获取最新的数据
                  this.initTable();
                  // 重新应用搜索条件
                  this.$nextTick(() => {
                    this.handleSearch();
                  });
                } else {
                  this.$notify({
                    title: "警告",
                    message: res.message,
                    type: "warning",
                  });
                }
              });
              return;
            } else {
              // 获取富文本内容
              this.richTextForm.content = this.editorContent;

              // 构建更新富文本菜单的数据结构
              const updateData = {
                menu: {
                  uuid: this.addForm.uuid,
                  menuParentId: this.addForm.menuParentId,
                  menuName: this.addForm.menuName,
                  menuAbbreviation: this.addForm.menuAbbreviation,
                  kind: 3, // 富文本类型
                  orderNum: this.addForm.orderNum,
                  menuId: this.addForm.menuId, // 使用保存的menuId
                },
                article: {
                  uuid: this.richTextForm.uuid || "",
                  menuId: this.richTextForm.menuId || this.addForm.menuId, // 使用正确的menuId
                  title: this.richTextForm.title,
                  content: this.richTextForm.content,
                  publishStatus: this.richTextForm.publishStatus,
                  publishDate: this.richTextForm.publishDate || this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
                },
              };

              menuUpdateRichText(updateData).then((res) => {
                if (res.code == 0) {
                  this.$notify({
                    title: "成功",
                    message: res.message,
                    type: "success",
                  });
                  this.dialogFormVisible = false;

                  // 清除数据
                  this.$refs[formName].resetFields();
                  if (this.$refs.richTextForm) {
                    this.$refs.richTextForm.resetFields();
                  }
                  this.parentName = "";

                  // 重新获取最新的数据
                  this.initTable();
                  // 重新应用搜索条件
                  this.$nextTick(() => {
                    this.handleSearch();
                  });
                } else {
                  this.$notify({
                    title: "警告",
                    message: res.message,
                    type: "warning",
                  });
                }
              });
              return;
            }
          }

          if (this.addForm.uuid === "") {
            // 执行新增功能
            menuSave(this.addForm).then((res) => {
              if (res.code == 0) {
                this.$notify({
                  title: "成功",
                  message: res.message,
                  type: "success",
                });
                // 清除数据
                this.$refs[formName].resetFields();
                this.parentName = "";
                // 隐藏模态框
                this.dialogFormVisible = false;
                // 重新获取最新的数据
                this.initTable();
                // 重新应用搜索条件
                this.$nextTick(() => {
                  this.handleSearch();
                });
              } else {
                this.$notify({
                  title: "警告",
                  message: res.message,
                  type: "warning",
                });
              }
            });
          } else {
            // 执行编辑功能
            menuUpdate(this.addForm).then((res) => {
              if (res.code == 0) {
                this.$notify({
                  title: "成功",
                  message: res.message,
                  type: "success",
                });
                // 清除数据
                this.$refs[formName].resetFields();
                this.parentName = "";
                // 隐藏模态框
                this.dialogFormVisible = false;
                // 重新获取最新的数据
                this.initTable();
                // 重新应用搜索条件
                this.$nextTick(() => {
                  this.handleSearch();
                });
              } else {
                this.$notify({
                  title: "警告",
                  message: res.message,
                  type: "warning",
                });
              }
            });
          }
        } else {
          this.$notify.warning({
            title: "警告",
            message: "表单验证失败，请检查必填项",
          });
          return false;
        }
      });
    },
    //全部展开
    openall() {
      this.props.isFold = false;
    },
    //全部折叠
    closeall() {
      this.props.isFold = true;
    },
    handleSearch() {
      // 递归搜索菜单及其子菜单
      const searchMenus = (menus) => {
        if (!menus) return [];

        return menus.filter((menu) => {
          // 检查当前菜单是否符合搜索条件
          const nameMatch = !this.searchForm.menuName || menu.menuName.includes(this.searchForm.menuName);
          const kindMatch = !this.searchForm.kind || menu.kind === this.searchForm.kind;

          // 如果当前菜单有子菜单，递归搜索子菜单
          if (menu.children && menu.children.length > 0) {
            const matchedChildren = searchMenus(menu.children);

            // 如果子菜单中有匹配项，保留这些子菜单
            if (matchedChildren.length > 0) {
              menu.children = matchedChildren;
              return true; // 保留包含匹配子菜单的父菜单
            }
          }

          // 如果当前菜单符合搜索条件，保留它
          return nameMatch && kindMatch;
        });
      };

      // 深拷贝原始数据，避免修改原始数据结构
      const clonedData = JSON.parse(JSON.stringify(this.tableData));
      // 保留level属性（搜索时不重新计算level）
      this.filteredTableData = searchMenus(clonedData);
    },
    resetSearch() {
      this.searchForm.menuName = "";
      this.searchForm.kind = "";
      this.filteredTableData = JSON.parse(JSON.stringify(this.tableData)); // 重置为原始数据的深拷贝
    },

    // 处理编辑表单中菜单选择事件
    handleMenuSelect(node) {
      // 可以在这里处理额外的逻辑，如果需要
      console.log("选中的菜单节点:", node);
      this.selectedMenuName = node.menuName || node.label;

      // 检查选中的父级菜单是否为下级菜单为超链接列表（submenuStyle为2）
      if (node.submenuStyle === 2) {
        // 如果父级菜单的submenuStyle为2，则子菜单类型默认为超链接(2)且禁用
        this.addForm.kind = 2;
        // 设置一个标志，表示菜单类型因父级菜单限制而禁用
        this.addForm.kindDisabled = true;
      } else {
        // 恢复可选状态
        this.addForm.kindDisabled = false;
      }
    },

    // 处理图标移除
    handleIconRemove() {
      // 当图标图片被移除时，可以在这里添加额外的逻辑
      console.log("图标图片已移除");
    },

    // 添加子菜单
    addChildMenu(parentMenu) {
      // 弹出弹框
      this.dialogFormVisible = true;

      // 清空数据
      if (this.$refs.changeFn != undefined) {
        this.$refs["changeFn"].resetFields();
      }
      if (this.$refs.richTextForm != undefined) {
        this.$refs["richTextForm"].resetFields();
      }

      // 添加子菜单不显示顶级菜单选项
      this.showTopLevelMenuOption = 0;

      // 检查父菜单层级
      const parentLevel = parentMenu.level || 1;

      // 检查父级菜单的submenuStyle是否为2（下级菜单为超链接列表）
      let defaultKind = 1;
      let kindDisabled = false;

      if (parentMenu.submenuStyle === 2) {
        // 如果父级菜单的submenuStyle为2，则子菜单类型默认为超链接且禁用
        defaultKind = 2;
        kindDisabled = true;
      } else if (parentLevel >= 3) {
        // 如果父菜单已经是第三级菜单，则默认只能添加非导航菜单类型
        defaultKind = 2; // 如果父级是第三级，则默认创建超链接类型
      }

      this.$nextTick(() => {
        // 设置默认表单数据
        this.addForm = {
          uuid: "",
          menuName: "",
          menuAbbreviation: "",
          menuParentId: parentMenu.menuId, // 设置父级菜单为当前行的菜单ID
          kind: defaultKind, // 根据父级菜单层级或submenuStyle设置默认类型
          kindDisabled: kindDisabled, // 设置是否禁用菜单类型选择
          submenuStyle: null, // 默认为null，让用户选择
          hyperlinks: "",
          orderNum: 1,
          menuIcon: "",
          menuSvg: "",
          coverImage: "",
          imageUrl: "",
          property: null,
          showHome: false, // 默认不显示到首页
          homeOrderNum: 1, // 默认首页展示顺序为空
          menuId: null, // 添加menuId字段
        };

        // 设置父级菜单名称
        this.selectedMenuName = parentMenu.menuName;

        // 初始化富文本表单
        this.initRichTextForm();

        // 检查菜单层级，更新menuLevelCount
        this.menuLevelCount = parentLevel;

        // 如果父菜单层级已经达到3级，提示用户
        if (parentLevel >= 3) {
          this.$nextTick(() => {
            this.$notify.info({
              title: "提示",
              message: "当前菜单已是第三级菜单，子菜单只能选择非导航菜单类型",
            });
          });
        }
      });
    },
  },
  created() {
    this.initTable();
    // 初始化搜索表单
    this.searchForm = {
      menuName: "",
      kind: "",
    };
  },
  beforeDestroy() {
    // 组件销毁时，不需要手动销毁编辑器，已经在RichTextEditor组件中处理
  },
};
</script>
<style>
label {
  font-weight: 500 !important;
}
.zk-table {
  border: 1px solid white;
}
.zk-table__header-row {
  height: 32px;
  font-weight: 500;
  font-size: 14px;
}
.zk-table__header-row th {
  padding: 4px 0 !important;
  background: #dadce1 !important;
  color: #1a1a1a !important;
  height: 32px;
}
.zk-table__header-row th .zk-table__cell-inner {
  padding: 0 10px;
}
</style>
<style lang="less" scoped>
.bigbox {
  width: 96%;
  height: 96%;
  margin: 15px;
  background: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  padding: 2%;
}
.bigbox .table {
  margin-top: 2%;
}
.content {
  width: 100%;
  // height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}
//设置浏览器滚动条的样式
::-webkit-scrollbar {
  width: 6px;
  height: 500px;
}
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.table-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}
.table-icon-class {
  font-size: 24px;
}
</style>
<style lang="less" scoped>
.icon-container {
  padding-left: 100px;
  .icon-preview {
    // margin-top: -15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  .icon {
    width: 20px;
    height: 20px;
  }
  .tip-text {
    font-size: 12px;
    color: #909399;
  }
}

/deep/ .el-pagination {
  margin-top: 1% !important;
}
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
/deep/ .el-dialog {
  margin-top: 5vh !important;
}
/deep/ .close {
  position: absolute;
  right: 30px;
}
/deep/ .note-editor .note-toolbar {
  background: #e8e8e8;
}
/deep/ .note-editor .note-toolbar > .note-btn-group {
  background: white;
  border-radius: 4px;
}
.file-dialog {
  /deep/ .el-dialog__body {
    height: 400px;
  }
}
/deep/ .stateBox {
  width: 100%;
  height: 30px;
  font-size: 15px;
}
/deep/ .uploadState {
  float: left;
}
/deep/ .cancelFile {
  float: left;
  margin-left: 40px;
  cursor: pointer;
}
/deep/ .uploadInfo {
  font-size: 20px;
  color: #6fa2ce;
  font-weight: bold;
  margin-bottom: 2px;
}
/deep/ .progress {
  /* display: block; */
  margin-top: 10px;
}
/deep/ .progress-bar {
  height: 15px;
}
/deep/ .fileItem {
  margin-top: 10px;
  border-bottom: 1px dotted #ccc;
  padding-bottom: 0px !important;
  box-sizing: border-box;
}
/deep/ .el-form--label-left .el-form-item__label {
  text-align: center;
}
</style>
<style scoped lang="scss">
.wu-example {
  width: 100%;
  height: 60px;
  .btns {
    width: 100%;
    height: 100%;
    .selectbtn {
      width: 130px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 5px;
      font-size: 18px;
      float: left;
      color: white;
      background: #81bfff;
      margin-left: 15%;
    }
    .btn-default {
      width: 130px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      border-radius: 5px;
      font-size: 18px;
      float: left;
      color: white;
      background: #b0d67d;
      margin-left: 15%;
      cursor: pointer;
    }
  }
  .uploader-list {
    height: 500px;
  }
}
.search {
  margin-bottom: 20px;
}
</style>
