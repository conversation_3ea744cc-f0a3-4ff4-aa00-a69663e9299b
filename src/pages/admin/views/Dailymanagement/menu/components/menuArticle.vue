<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="left" class="itemBox">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="searchForm.title" placeholder="请输入文章标题"  clearable style="width: 80%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item style="display: flex;width: 100%" label="所属菜单" prop="menuId">
                <menu-tree-select
                  v-model="searchForm.menuId"
                  :selectableKind="4"
                  placeholder="请选择文章菜单"
                  notSelectableTip="只能选择文章列表类型的节点"
                  clearable
                ></menu-tree-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item label="状态" prop="publishStatus">
                <el-select v-model="searchForm.publishStatus" placeholder="请选择状态" clearable  style="width: 80%">
                  <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-bottom: 15px">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" @click="searchBtn">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-row class="important marginTop">
        <el-col :span="24">
          <el-button type="primary" icon="el-icon-plus" class="addbtn" style="margin-top: 10px" @click="addBtn()">新增</el-button>
        </el-col>
      </el-row>
      <!-- 表格 -->
      <el-table :data="tableArr" stripe border class="marginTop" v-loading="loading">
        <el-table-column prop="title" label="标题" align="center"></el-table-column>
        <el-table-column prop="menuName" label="所属菜单" align="center"></el-table-column>
        <el-table-column prop="publishStatus" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.publishStatus === 1 ? 'info' : 'success'">
              {{ scope.row.publishStatus === 1 ? "草稿" : "已发布" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishDate" label="发布时间" align="center">
          <template slot-scope="{}" slot="header">
            <span>发布时间</span>
            <el-tooltip class="item" effect="dark" placement="top">
              <i class="el-icon-question" style="font-size: 14px; margin-left: 5px"></i>
              <div slot="content">
                <p>
                  用户端列表和文章内容展示的时间为发布时间，发布时间默认为当前时间; 发布时间时分秒为00时，用户端只展示日期
                </p>
              </div>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <p v-if="scope.row.publishDate">
              {{ scope.row.publishDate }}
            </p>
            <p v-else>暂无</p>
          </template>
        </el-table-column>
        <el-table-column prop="gmtCreate" label="创建时间" align="center"></el-table-column>
        <el-table-column prop="gmtModified" label="更新时间" align="center"></el-table-column>
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)">编辑</el-button>
            <el-button type="text" icon="el-icon-delete" style="color: red" @click="delBtn(scope.row.uuid)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页开始 -->
      <div class="paging">
        <Paging :pageCount="pageCount" @pagebar="changePage"></Paging>
      </div>
      <!-- 分页结束 -->

      <!-- 编辑弹窗开始 -->
      <el-dialog title="文章内容管理" :visible.sync="menuShow" width="1000px" class="dialogbox" :close-on-click-modal="false">
        <el-form :model="addForm" width="100%" label-position="left" class="itemBox" label-width="80px" ref="changeFn" :rules="rules">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="addForm.title" placeholder="请输入文章标题"  clearable style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <el-form-item label="所属菜单" prop="menuId">
                <menu-tree-select
                  v-model="addForm.menuId"
                  :selectedNodeName.sync="selectedMenuName"
                  :selectableKind="4"
                  placeholder="请选择文章菜单"
                  notSelectableTip="只能选择文章列表类型的节点"
                  clearable
                  @select="handleMenuSelect"
                ></menu-tree-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <el-form-item label="状态" prop="publishStatus">
                <el-select v-model="addForm.publishStatus" placeholder="请选择分类"  style="width: 100%">
                  <el-option v-for="item in sysconfigOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
              <el-form-item label="发布时间" prop="publishDate">
                <el-date-picker
                  v-model="addForm.publishDate"
                  type="datetime"

                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width:100%"
                  placeholder="选择发布时间"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 使用封装的富文本编辑器组件 -->
        <div style="margin-top: 20px;">
          <rich-text-editor v-model="editorContent" :height="300" />
        </div>

        <div slot="footer">
          <el-button @click="menuShow = false" >取消</el-button>
          <el-button type="primary" @click="handleConfirm()" >确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  menuArticleList,
  menuTree,
  menuArticleSave,
  menuArticleUpdate,
  menuArticleDelete,
  uploadmenu,
  uploadFile,
  uploadvideo,
  menuArticleDetail,
} from "@admin/api/Dailymanagement/menu";
import Paging from "@admin/components/pagebar/Paging.vue";
import MenuTreeSelect from "@admin/components/MenuTreeSelect/index.vue";
import RichTextEditor from "@admin/components/RichTextEditor/index.vue";

export default {
  components: {
    Paging,
    MenuTreeSelect,
    RichTextEditor,
  },
  data() {
    return {
      searchForm: {
        title: "",
        menuId: "",
        publishStatus: "",
        pageNum: 1,
        pageSize: 10,
        orderItems: "publishDate",
        orderRule: "desc",
      },
      tableArr: [],
      loading: false,
      pageCount: 0,
      menuShow: false,
      addForm: {
        uuid: "",
        menuId: "",
        title: "",
        content: "",
        coverImage: "",
        tags: "",
        publishStatus: 2,
        publishDate: "",
      },
      sysconfigOptions: [
        {
          label: "草稿",
          value: 1,
        },
        {
          label: "发布",
          value: 2,
        },
      ],
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      currentMenuId: "",
      currentMenuName: "",
      // 选中的菜单名称
      selectedMenuName: "",
      statusOptions: [
        {
          label: "草稿",
          value: 1,
        },
        {
          label: "已发布",
          value: 2,
        },
      ],
      editorContent: "",
    };
  },
  methods: {
    // 表格初始化
    initTable() {
      this.loading = true;

      menuArticleList(this.searchForm).then((res) => {
        if (res.code == 0) {
          this.loading = false;
          this.tableArr = res.data;
          this.pageCount = res.count;
        }
      });
    },

    // 处理编辑表单中菜单选择事件
    handleMenuSelect(node) {
      console.log("选中的菜单节点:", node);
      this.currentMenuId = node.menuId;
      this.currentMenuName = node.menuName || node.label;
    },

    searchBtn() {
      this.searchForm.pageNum = 1;
      this.initTable();
    },

    //点击弹窗的确定按钮
    handleConfirm() {
      // 获取编辑器内容
      this.addForm.content = this.editorContent;

      this.$refs.changeFn.validate((valid) => {
        if (valid) {
          if (this.addForm.uuid == "") {
            // 新增文章
            menuArticleSave(this.addForm).then((res) => {
              if (res.code == 0) {
                this.$notify({
                  title: "成功",
                  message: res.message,
                  type: "success",
                });
                this.menuShow = false;
                this.initTable();
              }
            });
          } else {
            // 更新文章
            menuArticleUpdate(this.addForm).then((res) => {
              if (res.code == 0) {
                this.$notify({
                  title: "成功",
                  message: res.message,
                  type: "success",
                });
                this.menuShow = false;
                this.initTable();
              }
            });
          }
        }
      });
    },

    //点击新增按钮
    addBtn() {
      this.menuShow = true;
      if (this.$refs.changeFn != undefined) {
        this.$refs["changeFn"].resetFields();
      }

      this.$nextTick(() => {
        // 如果有选定的菜单，则默认使用该菜单
        const menuId = this.currentMenuId || "";

        this.addForm = {
          uuid: "",
          menuId: menuId,
          title: "",
          content: "",
          coverImage: "",
          tags: "",
          publishStatus: 2,
          publishDate: this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        };

        // 清空选中的菜单名称，除非有预选的菜单
        this.selectedMenuName = this.currentMenuName || "";

        // 清空编辑器内容
        this.editorContent = "";
      });
    },

    //点击编辑按钮
    editBtn(row) {
      this.menuShow = true;

      // 获取文章详情
      menuArticleDetail({ uuid: row.uuid }).then((res) => {
        if (res.code === 0 && res.data) {
          const articleData = res.data;

          if (this.$refs.changeFn != undefined) {
            this.$refs["changeFn"].resetFields();
          }

          this.$nextTick(() => {
            this.addForm = {
              uuid: articleData.uuid,
              menuId: articleData.menuId,
              title: articleData.title,
              content: articleData.content || "",
              coverImage: articleData.coverImage || "",
              tags: articleData.tags || "",
              publishStatus: articleData.publishStatus,
              publishDate: articleData.publishDate || this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
            };

            // 设置选中的菜单名称
            this.selectedMenuName = row.menuName || articleData.menuName || "";
            this.currentMenuId = articleData.menuId;
            this.currentMenuName = row.menuName || articleData.menuName || "";

            // 设置富文本内容
            this.editorContent = articleData.content || "";
          });
        }
      });
    },

    delBtn(uuid) {
      this.$confirm("此操作将永久删除该菜单, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: uuid,
          };
          menuArticleDelete(json).then((res) => {
            if (res.code == 0) {
              this.$notify({
                title: "成功",
                message: "删除成功",
                type: "success",
              });
              this.initTable();
            }
          });
        })
        .catch(() => {
          this.$notify({
            title: "提示",
            message: "已取消删除",
            type: "info",
          });
        });
    },

    //初始数据表格分页
    changePage(pageData) {
      this.searchForm.pageNum = pageData.pageNum;
      this.searchForm.pageSize = pageData.pageSize;
      // 页面刷新
      this.initTable();
    },
  },
  created() {
    this.initTable();
  },
  beforeDestroy() {
    // 组件销毁时，销毁编辑器
    // 不需要手动销毁编辑器，已经在RichTextEditor组件中处理
  },
  computed: {
    // 模态框表单验证
    rules() {
      return {
        menuId: [
          {
            required: true,
            message: "请选择所属菜单",
            trigger: "change",
          },
        ],
        publishStatus: [
          {
            required: true,
            message: "请选择分类",
            trigger: "change",
          },
        ],
        title: [
          {
            required: true,
            message: "请输入文章标题",
            trigger: "blur",
          },
        ],
        publishDate: [
          {
            required: true,
            message: "请选择发布时间",
            trigger: "change",
          },
        ],
      };
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .el-pagination {
  margin-top: 1% !important;
}
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
/deep/ .el-form--label-left .el-form-item__label {
  text-align: center;
}
</style>
