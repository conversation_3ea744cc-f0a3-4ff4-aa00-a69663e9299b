<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="left" class="itemBox">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item label="标题" prop="title">
                <el-input v-model="searchForm.title" placeholder="请输入标题"  clearable style="width: 80%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item style="display: flex;width: 100%" label="所属菜单" prop="menuId">
                <menu-tree-select
                  v-model="searchForm.menuId"
                  :selectableKind="5"
                  placeholder="请选择下载菜单"
                  notSelectableTip="只能选择下载菜单类型的节点"
                  clearable
                ></menu-tree-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="10" :lg="10" :xl="8">
              <el-form-item label="状态" prop="publishStatus">
                <el-select v-model="searchForm.publishStatus" placeholder="请选择状态" clearable  style="width: 80%">
                  <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-bottom: 15px">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" @click="searchBtn">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-row class="important marginTop">
        <el-col :span="24">
          <el-button type="primary" icon="el-icon-plus" class="addbtn" style="margin-top: 10px" @click="addBtn()">新增下载</el-button>
        </el-col>
      </el-row>
      <!-- 表格 -->
      <el-table :data="tableData" stripe border class="marginTop" v-loading="loading">
        <el-table-column prop="title" label="标题" min-width="200" align="center"></el-table-column>
        <el-table-column prop="menuName" label="所属菜单" min-width="150" align="center"></el-table-column>
        <el-table-column prop="subtitle" label="副标题" min-width="150" align="center"></el-table-column>
        <el-table-column prop="fileName" label="文件名" min-width="150" align="center">
          <template slot-scope="scope">
            <span class="file-name-link" @click="downloadFile(scope.row)" v-if="scope.row.attachmentUrl && scope.row.fileName">
              {{ scope.row.fileName }}
            </span>
            <span v-else>{{ scope.row.fileName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" min-width="100" align="center">
          <template slot-scope="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="publishStatus" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.publishStatus === 1 ? 'info' : 'success'">
              {{ scope.row.publishStatus === 1 ? "草稿" : "已发布" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishDate" label="发布时间" width="160" align="center"></el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" icon="el-icon-edit" @click="editItem(scope.row)">编辑</el-button>
            <el-button type="text" icon="el-icon-delete" style="color: red" @click="deleteItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页开始 -->
      <div class="paging">
        <Paging :pageCount="pageCount" @pagebar="changePage"></Paging>
      </div>
      <!-- 分页结束 -->

      <!-- 编辑弹窗开始 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="700px" class="dialogbox" :close-on-click-modal="false">
        <el-form :model="form" :rules="rules" ref="form" label-width="100px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入标题" ></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="所属菜单" prop="menuId">
                <menu-tree-select
                  v-model="form.menuId"
                  :selectedNodeName.sync="selectedMenuName"
                  :selectableKind="5"
                  placeholder="请选择下载菜单"
                  notSelectableTip="只能选择下载菜单类型的节点"
                  clearable
                  @select="handleMenuSelect"
                ></menu-tree-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="副标题" prop="subtitle">
                <el-input v-model="form.subtitle" placeholder="请输入副标题" ></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="标签" prop="tags">
                <el-input v-model="form.tags" placeholder="请输入标签，多个标签用逗号分隔" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="文件" prop="attachmentUrl">
                <file-uploader
                  v-model="form.attachmentUrl"
                  type="file"
                  :fileName="fileList.length > 0 ? fileList[0].name : ''"
                  tip="请上传文件，大小不超过50MB"
                  @upload-success="handleUploadSuccess"
                  @remove="handleRemove"
                ></file-uploader>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="发布状态" prop="publishStatus">
                <el-select v-model="form.publishStatus" placeholder="请选择状态"  style="width: 100%">
                  <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="发布时间" prop="publishDate">
                <el-date-picker
                  v-model="form.publishDate"
                  type="datetime"

                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width:100%"
                  placeholder="选择发布时间"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="备注" prop="memo">
                <el-input type="textarea" v-model="form.memo" placeholder="请输入备注信息" :rows="3" ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button @click="dialogVisible = false" >取 消</el-button>
          <el-button type="primary" @click="submitForm" >确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { menuDownLoadList, menuDownLoadSave, menuDownLoadUpdate, menuDownLoadDelete, uploadFile } from "@admin/api/Dailymanagement/menu";
import Paging from "@admin/components/pagebar/Paging.vue";
import MenuTreeSelect from "@admin/components/MenuTreeSelect/index.vue";
import FileUploader from "@admin/components/FileUploader/index.vue";
import { safeDownloadFile } from "@index/utils/download";

export default {
  data() {
    return {
      // 搜索表单
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        title: "",
        menuId: "",
        publishStatus: "",
        menuUuid: "",
        menuName: "",
        orderItems: "publishDate",
        orderRule: "desc",
      },

      // 表格数据
      tableData: [],
      loading: false,
      pageCount: 0,

      // 状态选项
      statusOptions: [
        { label: "草稿", value: 1 },
        { label: "已发布", value: 2 },
      ],

      // 弹窗相关
      dialogVisible: false,
      dialogTitle: "新增下载",
      form: {
        uuid: "",
        menuId: "",
        title: "",
        subtitle: "",
        tags: "",
        attachmentUrl: "",
        publishStatus: 2,
        publishDate: "",
        memo: "",
      },
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        menuId: [{ required: true, message: "请选择所属菜单", trigger: "change" }],
        attachmentUrl: [{ required: true, message: "请上传文件", trigger: "change" }],
        publishStatus: [{ required: true, message: "请选择发布状态", trigger: "change" }],
        publishDate: [{ required: true, message: "请选择发布时间", trigger: "change" }],
      },

      // 上传相关
      fileList: [],

      // 选中的菜单名称
      selectedMenuName: "",
    };
  },
  created() {
    this.initTable();
  },
  methods: {
    // 表格初始化
    initTable() {
      this.loading = true;
      menuDownLoadList(this.searchForm)
        .then((res) => {
          if (res.code == 0) {
            this.loading = false;
            this.tableData = res.data;
            this.pageCount = res.count;
          }
          this.loading = false;
        });
    },

    // 搜索
    searchBtn() {
      this.searchForm.pageNum = 1;
      this.initTable();
    },

    // 新增
    addBtn() {
      this.dialogTitle = "新增下载";
      this.form = {
        uuid: "",
        menuId: "",
        title: "",
        subtitle: "",
        tags: "",
        attachmentUrl: "",
        publishStatus: 2,
        publishDate: this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        memo: "",
      };
      // 清空选中的菜单名称
      this.selectedMenuName = "";
      this.fileList = [];
      this.dialogVisible = true;

      if (this.$refs.form) {
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      }
    },

    // 编辑
    editItem(row) {
      this.dialogTitle = "编辑下载";
      this.form = {
        uuid: row.uuid,
        menuId: row.menuId,
        title: row.title,
        subtitle: row.subtitle || "",
        tags: row.tags || "",
        attachmentUrl: row.attachmentUrl || "",
        publishStatus: row.publishStatus,
        publishDate: row.publishDate || this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        memo: row.memo || "",
      };

      // 设置选中的菜单名称
      this.selectedMenuName = row.menuName || "";

      // 设置文件列表
      if (row.attachmentUrl && row.fileName) {
        this.fileList = [
          {
            name: row.fileName,
            url: row.attachmentUrl,
          },
        ];
      } else {
        this.fileList = [];
      }

      this.dialogVisible = true;

      if (this.$refs.form) {
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      }
    },

    // 删除
    deleteItem(row) {
      this.$confirm("确定要删除该下载项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          menuDownLoadDelete({ uuid: row.uuid }).then((res) => {
            if (res.code === 0) {
              this.$notify({
                title: "成功",
                message: "删除成功",
                type: "success",
              });
              this.initTable();
            }
          });
        })
        .catch(() => {});
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const action = this.form.uuid ? menuDownLoadUpdate : menuDownLoadSave;

          action(this.form).then((res) => {
            if (res.code === 0) {
              this.$notify({
                title: "成功",
                message: this.form.uuid ? "更新成功" : "新增成功",
                type: "success",
              });
              this.dialogVisible = false;
              this.initTable();
            }
          });
        }
      });
    },

    // 处理上传成功
    handleUploadSuccess(file) {
      // 文件上传成功后，更新文件列表显示
      this.fileList = [
        {
          name: file.name,
          url: file.url,
        },
      ];
    },

    // 移除文件
    handleRemove() {
      this.fileList = [];
      this.form.attachmentUrl = "";
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return "0 B";

      const units = ["B", "KB", "MB", "GB", "TB"];
      let index = 0;
      let fileSize = size;

      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024;
        index++;
      }

      return fileSize.toFixed(2) + " " + units[index];
    },

    //初始数据表格分页
    changePage(pageData) {
      this.searchForm.pageNum = pageData.pageNum;
      this.searchForm.pageSize = pageData.pageSize;
      // 页面刷新
      this.initTable();
    },

    // 处理菜单选择事件
    handleMenuSelect(node) {
      // 可以在这里处理额外的逻辑，如果需要
      console.log("选中的菜单节点:", node);
    },

    // 添加下载文件方法
    downloadFile(row) {
      safeDownloadFile({
        url: row.attachmentUrl,
        fileName: row.fileName,
      });
    },
  },
  components: {
    Paging,
    MenuTreeSelect,
    FileUploader,
  },
};
</script>

<style scoped lang="less">
/deep/ .el-pagination {
  margin-top: 1% !important;
}
/deep/ .el-table td {
  padding: 4px 0;
}
/deep/ .el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
/deep/ .close {
  position: absolute;
  right: 30px;
}
/deep/ .el-form--label-left .el-form-item__label {
  text-align: center;
}
.file-name-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}
.file-name-link:hover {
  color: #66b1ff;
}
</style>
