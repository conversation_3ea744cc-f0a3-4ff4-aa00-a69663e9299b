<template>
  <div class="box">
    <el-tabs type="card" v-model="activeName">
      <el-tab-pane label="菜单管理" name="0" lazy><Menu v-if="activeName === '0'"></Menu></el-tab-pane>
      <el-tab-pane label="菜单文章列表管理" name="1" lazy><MenuArticle v-if="activeName === '1'"></MenuArticle></el-tab-pane>
      <el-tab-pane label="菜单下载管理" name="2" lazy><MenuDownload v-if="activeName === '2'"></MenuDownload></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import Menu from "./components/menu";
import MenuArticle from "./components/menuArticle";
import MenuDownload from "./components/menuDownload";

export default {
  data() {
    return {
      activeName: "0", // 默认选中第一个标签页
    };
  },
  components: {
    Menu,
    MenuArticle,
    MenuDownload,
  },
};
</script>
<style lang="scss" scoped></style>
