<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 搜索功能开始 -->
      <div class="search">
        <el-form :model="searchForm" width="100%" label-position="center" class="itemBox" label-width="80px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="标题">
                <el-input v-model="searchForm.title" placeholder="请输入标题"  clearable style="width: 100%"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="状态">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  class="publicselect"

                  style="width: 100%"
                  clearable
                >
                  <el-option v-for="item in statusArr" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-form-item label="分类">
                <el-select
                  v-model="searchForm.kind"
                  placeholder="请选择分类"
                  class="publicselect"

                  style="width: 100%"
                  clearable
                >
                  <el-option v-for="item in kindsArr" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <el-button type="primary" icon="el-icon-search"  class="smallbtn" style="margin-left: 20px" @click="searchBtn">
                搜索
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 搜索功能结束 -->
      <el-row class="important marginTop">
        <el-col :span="24">
          <el-button type="primary" icon="el-icon-plus" class="addbtn" @click="addBtn" v-allow="'news:save'">新增</el-button>
        </el-col>
      </el-row>
      <!-- 数据表格开始 -->
      <el-row>
        <el-table :data="tabledata" stripe border style="width: 100%" @sort-change="sortChange" class="marginTop" v-loading="loading">
          <!-- <el-table-column
            type="index"
            width="65px"
            label="序号"
            align="center"
          ></el-table-column> -->
          <el-table-column prop="title" label="标题" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.topFlag == 1" style="font-size: 18px; margin-right: 10px">🔝</span>
              {{ scope.row.title }}
            </template>
          </el-table-column>
          <el-table-column prop="kind" label="分类" align="center" :formatter="kindFormatter"></el-table-column>
          <!-- <el-table-column prop="status" label="状态" align="center" :formatter="statusFormatter"></el-table-column> -->
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'info' : 'success'">
                {{ scope.row.status === 1 ? "草稿" : "已发布" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="gmtModified" label="更新时间" align="center"></el-table-column>
          <el-table-column label="操作" width="180px" align="center">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)" v-allow="'news:update'">编辑</el-button>
              <el-button type="text" icon="el-icon-delete" style="color: red" @click="delBtn(scope.row)" v-allow="'news:delete'">
                删除
              </el-button>
              <!-- <el-button v-if="scope.row.kind == 1" type="text" icon="el-icon-setting" @click="setTop(scope.row)">设置置顶</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!-- 数据表格结束 -->
      <!-- 分页开始 -->
      <div class="paging">
        <Paging :pageCount="pageCount" @pagebar="changePage"></Paging>
      </div>
      <!-- 分页结束 -->
      <!-- 新增编辑弹窗开始 -->
      <el-dialog :title="dialogTitle" :visible.sync="diaShow" width="1200px" top="0%" append-to-body>
        <div>
          <el-form :model="diaForm" ref="diaForm" label-width="auto" style="width: 100%" :rules="rules">
            <el-row :gutter="40" style="padding-left: 40px">
              <el-col :span="12">
                <el-form-item label="标题" prop="title">
                  <el-input v-model="diaForm.title"  clearable placeholder="请输入标题"></el-input>
                </el-form-item>
                <el-form-item label="分类" prop="kind">
                  <el-select v-model="diaForm.kind" placeholder="请选择分类"  style="width: 100%">
                    <el-option v-for="item in kindsArr" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="发布时间" prop="publishDate">
                  <el-date-picker
                    v-model="diaForm.publishDate"
                    type="datetime"

                    style="width: 100%"
                    placeholder="选择日期时间"
                    value-format="YYYY-MM-dd HH:mm:ss"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select v-model="diaForm.status" placeholder="请选择状态"  style="width: 100%">
                    <el-option v-for="item in statusArr" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="封面上传" prop="newsImage">
                  <file-uploader v-model="diaForm.newsImage" type="image" :maxSize="10" tip="重新上传点击图片即可"></file-uploader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item prop="content">
              <!-- 替换原来的quill-editor为RichTextEditor组件 -->
              <rich-text-editor v-model="diaForm.content" :height="450" placeholder="请输入内容"></rich-text-editor>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diaShow = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn">确认</el-button>
        </span>
      </el-dialog>
      <!-- 新增编辑弹窗结束 -->
      <!-- 查看封面弹窗开始 -->
      <el-dialog title="查看封面" :visible.sync="diaShowImg" width="500px" append-to-body>
        <div class="imgbox">
          <img :src="Imgurl" />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getNews, delNews, addNews, editNews, setTopNew, uploadimg } from "@admin/api/Dailymanagement/news";
import Paging from "@admin/components/pagebar/Paging.vue";
import FileUploader from "@admin/components/FileUploader/index.vue";
import RichTextEditor from "@admin/components/RichTextEditor/index.vue";

export default {
  data() {
    return {
      ApiUrl: window.g.ApiUrl,
      TopArr: [
        {
          label: "是",
          value: 0,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      searchForm: {
        title: "",
        status: "",
        kind: "",
      },
      Imgurl: "",
      diaShowImg: false,
      dialogTitle: "新增新闻公告", //新闻公告弹窗
      loading: false, //表格loading
      tabledata: [], //表格数据
      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      pageCount: 0,
      orderModel: "",
      orderKey: "",
      diaShow: false, //新增/编辑模态框
      diaForm: {
        title: "",
        content: "",
        status: "",
        kind: "",
        newsImage: "",
        publishDate: this.$moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      },
      statusArr: [
        { label: "草稿", value: 1 },
        { label: "发布", value: 2 },
      ],
      kindsArr: [
        // { label: "中心新闻", value: 1 },
        // { label: "课程公告", value: 2 },
        { label: "通知公告", value: 3 },
        // { label: "文件资料", value: 4 },
        { label: "工作动态", value: 5 },
      ],
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
        kind: [{ required: true, message: "请选择分类", trigger: "change" }],
      },
    };
  },
  methods: {
    // 初始数据
    initTable() {
      this.loading = true;
      getNews(
        this.pageData.pageNum,
        this.pageData.page,
        this.searchForm.kind,
        this.searchForm.status,
        this.searchForm.title,
        this.orderKey,
        this.orderModel,
      ).then((res) => {
        this.loading = false;
        if (res.code === 0) {
          this.tabledata = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },

    //查看封面
    CatchImg(data) {
      this.diaShowImg = true;
      this.Imgurl = data.imgurl;
    },
    // 点击编辑按钮
    editBtn(row) {
      this.diaShow = true;
      this.dialogTitle = "编辑新闻公告";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.uuid = row.uuid;
      this.diaForm.newsImage = row.newsImage;
      this.diaForm.title = row.title;
      this.diaForm.status = row.status;
      this.diaForm.content = row.content;
      this.diaForm.kind = row.kind;
    },
    //点击置顶按钮
    setTop(row) {
      this.$confirm("是否确认将该新闻设为置顶🔝?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          setTopNew(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消置顶",
          });
        });
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将删除该项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delNews(json).then((res) => {
            if (res.code === 0) {
              this.$message.success("删除成功！");
              this.initTable();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击新增按钮
    addBtn() {
      this.diaShow = true;
      this.dialogTitle = "新增新闻公告";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.uuid = "";
      this.diaForm.kind = "";
      this.diaForm.newsImage = "";
    },
    // 点击提交按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.uuid) {
            // 编辑
            editNews(this.diaForm).then((res) => {
              if (res.code === 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            addNews(this.diaForm).then((res) => {
              if (res.code === 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        }
      });
    },
    // 表格状态格式化
    statusFormatter(row, column) {
      if (row.status === 1) {
        return "草稿";
      } else if (row.status === 2) {
        return "发布";
      }
    },
    //表格分类格式化
    kindFormatter(row, column) {
      return this.kindsArr.find((item) => item.value === row.kind)?.label || "其他";
    },
    // 表格的排序
    sortChange(obj) {
      this.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.orderKey = obj.prop;
      this.initTable(); //
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      // 页面刷新
      this.initTable();
    },
    searchBtn() {
      this.pageData.pageNum = 1;
      this.initTable();
    },
  },
  created() {
    this.initTable(); //表格数据
  },
  components: {
    Paging,
    FileUploader,
    RichTextEditor,
  },
};
</script>

<style lang="less" scoped>
.imgbox {
  width: 100%;
  img {
    width: 100%;
  }
}
.avatar {
  width: 120px;
  height: 120px;
}
</style>
