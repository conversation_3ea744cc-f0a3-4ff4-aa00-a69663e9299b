<template>
  <div>
    <template v-if="isClick">
      <el-table
        :data="tableData"
        stripe
        lazy
        :load="load"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'childrenCopy' }"
        class="table"
      >
        <el-table-column prop="name" label="字典名称" align="center"></el-table-column>
        <el-table-column prop="codeValue" label="字典值" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="enitBtn(scope.row)"
              v-allow="'codingTable:update'"
              >编辑</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              style="color: red"
              size="mini"
              @click="delBtn(scope.row)"
              v-allow="'codingTable:delete'"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </template>
    <div v-else>请先从左侧选择字典类型</div>
    <DiaForm ref="dialog" :codeType="codeType"></DiaForm>
  </div>
</template>

<script>
import Paging from "@admin/components/pagebar/Paging.vue";
import {
  editDict,
  delDict,
  getDictAll,
  getTypeAll,
} from "@admin/api/Settings/DictTable.js";
import DiaForm from "./DiaForm";
export default {
  name: "",
  props: ["codeType"],
  data() {
    return {
      tableDataCopy: [],
      tableData: [],
      pageData: {
        page: 1,
        pageNum: 10,
      },
      pageCount: 0,
    };
  },
  methods: {
    initData() {
      getDictAll(this.codeType).then((res) => {
        console.log(res);
        if (res.code === 0) {
          this.tableDataCopy = res.data || []; // 备份的全量数据
          this.tableData = res.data;
          this.tableData.map((item) => {
            // 展示数据
            // hasChildren 表示需要展示一个箭头图标
            item.hasChildren = item.children && item.children.length > 0;
            // 只展示一层
            // 如果有children数据，会自动加载，就不是懒加载了，也可以配置tree-props里面的children为其他字段
            item.childrenCopy = null;
            // 记住层级关系
            item.idList = [item.id];
            return item;
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    load(tree, treeNode, resolve) {
      console.log(tree, treeNode, resolve);
      // 层级关系备份
      // const idCopy = tree.idList;
      // // 查找下一层数据
      // let resolveArr = this.tableDataCopy;
      // let id;
      // // eslint-disable-next-line
      // while ((id = tree.idList.shift())) {
      //   console.log(id, resolveArr);
      //   const tarItem = resolveArr.find((item) => item.id === id);
      //   console.log(tarItem);
      //   tarItem.loadedChildren = true;
      //   resolveArr = tarItem.children;
      // }

      // 处理下一层数据的属性
      let resolveArr = tree.children;
      const idCopy = tree.idList;
      resolveArr.forEach((item) => {
        item.hasChildren = item.children && item.children.length > 0;
        item.childrenCopy = null;
        // 此处需要深拷贝，以防各个item的idList混乱
        item.idList = idCopy;
        item.idList.push(item.id);
      });

      // 标识已经加载子节点
      tree.loadedChildren = true;

      console.log(resolveArr);

      // 渲染子节点
      resolve(resolveArr);
    },
    unload() {
      this.showTable = false;
      // eslint-disable-next-line
      this.$nextTick(() => (this.showTable = true));
      this.tableData = this.tableDataCopy.map((item) => {
        // hasChildren 表示需要展示一个箭头图标
        item.hasChildren = item.children && item.children.length > 0;
        // 只展示一层
        item.children = null;
        // 记住层级关系
        item.idList = [item.id];
        return item;
      });
    },
    addBtn(row) {},
    enitBtn(row) {
      this.$refs.dialog.isAdd = false;
      this.$refs.dialog.dialog = true;
      this.$refs.dialog.initEditData(row);
    },
    delBtn(row) {
      this.$confirm("是否删除该项?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delDict(json).then((res) => {
            if (res.code === 0) {
              this.$message.success(res.message);
              this.initData();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
  computed: {
    isClick() {
      return this.codeType ? true : false;
    },
  },
  watch: {
    codeType: {
      handler(val) {
        this.initData();
      },
      immediate: true,
    },
  },
  components: {
    Paging,
    DiaForm,
  },
};
</script>

<style scoped></style>

<style scoped lang="less">
.table /deep/.el-table__body-wrapper td {
  text-align: left;
}

.table /deep/.el-table__header-wrapper th {
  text-align: left;
}
</style>
