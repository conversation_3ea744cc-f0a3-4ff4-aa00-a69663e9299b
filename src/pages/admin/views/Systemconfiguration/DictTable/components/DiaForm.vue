<template>
  <div>
    <!-- 模态框 -->
    <el-dialog
      title="字典信息"
      :visible.sync="dialog"
      :show-close="true"
      :close-on-click-modal="false"
      @close="cancel"
      @open="open"
      append-to-body
      width="900px"
    >
      <el-row>
        <el-col :span="12">
          <el-form :model="form" label-width="auto" >
            <el-form-item label="目录类型">
              <el-radio-group v-model="menu" size="mini" @change="changeRadio">
                <el-radio-button :label="1">顶级</el-radio-button>
                <el-radio-button :label="2">子级</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="menu === 2" label="父级菜单">
              <el-input
                v-model="form.parentName"
                placeholder="请在右边选择父级菜单"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="字典名称">
              <el-input v-model="form.codeName"></el-input>
            </el-form-item>
            <el-form-item label="字典值">
              <el-input v-model="form.codeValue"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <el-tree
            v-if="menu === 2"
            :data="treeData"
            :props="defaultProps"
            @node-click="handleNodeClick"
          ></el-tree>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialog = false">取消</el-button>
        <el-button type="primary" size="mini" @click="confirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDict,
  addDict,
  editDict,
  delDict,
  getDictAll,
  getTypeAll,
} from "@admin/api/Settings/DictTable.js";
export default {
  name: "",
  props: ["codeType"],
  data() {
    return {
      isAdd: true,
      dialog: false,
      menu: 1,
      form: {
        uuid: "",
        codeName: "",
        codeValue: "",
        parentUuid: null,
        parentName: "",
        codeType: "",
      },
      treeData: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
    };
  },
  methods: {
    changeRadio(val) {
      if (val === 1) {
        this.form.parentUuid = null;
        this.form.parentName = "";
      }
    },
    handleNodeClick(data) {
      this.form.parentName = data.name;
      this.form.parentUuid = data.uuid;
    },
    confirm() {
      if (this.isAdd) {
        addDict(this.form).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.message);
            this.dialog = false;
            this.$parent.initData();
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
          }
        });
      } else {
        // 点击编辑
        editDict(this.form).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.message);
            this.dialog = false;
            this.$parent.initData();
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
          }
        });
      }
    },
    cancel() {
      this.resetForm();
    },
    resetForm() {
      this.menu = 1;
      this.form.uuid = "";
      this.form.codeName = "";
      this.form.codeValue = "";
      this.form.parentUuid = null;
      this.form.parentName = "";
    },
    open() {
      this.form.codeType = this.codeType;
      this.getTreeData();
    },
    getTreeData() {
      // 根据类型获取树状数据
      getDictAll(this.codeType).then((res) => {
        if (res.code === 0) {
          this.treeData = res.data;
        }
      });
    },
    initEditData(row) {
      // 设置编辑数据
      this.form.uuid = row.uuid ? row.uuid : "";
      this.form.codeName = row.name ? row.name : "";
      this.form.codeValue = row.codeValue ? row.codeValue : "";
      this.form.parentUuid = row.parentUuid ? row.parentUuid : "";
      this.form.parentName = row.parentName ? row.parentName : "";
      if (this.form.parentUuid && this.form.parentUuid != 0) {
        this.menu = 2;
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
