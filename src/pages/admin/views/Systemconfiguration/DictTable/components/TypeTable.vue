<template>
	<div>
		<el-table :data="tableData" :row-class-name="tableRowClassName"  style="width:100%" @row-click="rowClick">
			<el-table-column type="index" align="center" width="65px" label="序号"></el-table-column>
			<el-table-column prop="name" label="字典类型" align="center"></el-table-column>
			<el-table-column prop="id" label="类型值" align="center"></el-table-column>
		</el-table>
	</div>
</template>

<script>
import { getTypeAll } from '@admin/api/Settings/DictTable.js';
import { promises } from 'fs';
export default {
	name: '',
	data() {
		return {
			tableData: [],
			rowIndex: '-1'
		};
	},
	methods: {
		tableRowClassName({ row, rowIndex }) {
			if (this.rowIndex == rowIndex) {
				console.log(rowIndex)
				return 'warning-row';
			}
		},
		initTable() {
			getTypeAll().then(res => {
				if (res.code === 0) {
					this.tableData = res.data;
				}
			});
		},
		rowClick(row) {
			let id = row.id;
			this.rowIndex = "-1"
			this.tableData.map((item, index) => {
				if (item.id == id) {
					this.rowIndex = index;
				}
			});
			this.$emit('rowClick', row);
		}
	},
	created() {
		this.initTable();
	}
};
</script>

<style>
.el-table .warning-row {
	background: #3dacd4 !important;
	color: white;
}
</style>
