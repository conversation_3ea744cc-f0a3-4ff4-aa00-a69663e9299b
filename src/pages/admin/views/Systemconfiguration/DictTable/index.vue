<template>
  <div class="boxmore">
    <div class="borderbox">
      <el-row :gutter="20">
        <el-col class="box-wrapper" :span="10">
          <el-card class="box-card">
            <div slot="header" class="card-header">
              <span>字典类型</span>
            </div>
            <TypeTable @rowClick="handleClick"></TypeTable>
          </el-card>
        </el-col>
        <el-col class="box-wrapper" :span="14">
          <el-card class="box-card">
            <div slot="header" class="card-header">
              <span>类型详情</span>
              <el-button
                type="success"
                :disabled="bool"
                icon="el-icon-plus"

                class="addbtn"
                @click="add"
                v-allow="'codingTable:save'"
                >新增</el-button
              >
            </div>
            <DetailTable ref="detail" :codeType="selectType"></DetailTable>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import TypeTable from "./components/TypeTable";
import DetailTable from "./components/DetailTable";
export default {
  data() {
    return {
      selectType: "",
      detailshow: false,
    };
  },
  methods: {
    handleClick(row) {
      // 点击了表格某一项
      this.selectType = row.id;
    },
    add() {
      this.$refs.detail.$refs.dialog.dialog = true;
      this.$refs.detail.$refs.dialog.isAdd = true;
    },
  },
  computed: {
    bool() {
      return this.selectType === "" ? true : false;
    },
  },
  components: {
    TypeTable,
    DetailTable,
  },
};
</script>

<style lang="scss" scoped>
.box-wrapper {
  .card-header {
    display: flex;
    justify-content: space-between;
  }
}
</style>
