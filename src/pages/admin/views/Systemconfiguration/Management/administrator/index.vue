<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <el-row class="search">
        <el-col>
          <el-form :model="searchData" width="100%" class="itemBox">
            <el-row :gutter="30">
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
                <el-form-item
                  label="学工号/姓名"
                  prop="logonName"
                  label-width="120px"
                >
                  <!-- <el-select
										v-model="searchForm.logonName"
										placeholder="请输入工号或者姓名关键词查询选择"
										style="width: 100%"
										clearable

										filterable
										remote
										:remote-method="remoteMethod2"
										allow-create
									>
										<el-option  v-for="(item, index) in teacherOptions" :key="index" :label="item.label" :value="item.value"></el-option>
									</el-select> -->
                  <!-- <el-autocomplete
                    class="inline-input"
                    v-model="searchForm.logonName"
                    :fetch-suggestions="querySearch"
                    clearable
                    placeholder="请输入工号或者姓名关键词查询选择"
                    @select="handleSelect"
                  ></el-autocomplete> -->
                  <Account @getlogonName="getLogonName"></Account>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
                <el-form-item
                  label="角色名称"
                  prop="className"
                  label-width="80px"
                >
                  <el-select
                    v-model="searchData.manRoleId"
                    clearable
                    placeholder="请选择角色"
                    style="width: 100%"

                  >
                    <el-option
                      v-for="(item, i) in serachRoleOption"
                      :key="i"
                      :label="item.roleName"
                      :value="item.roleId"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="8"
                :md="4"
                :lg="4"
                :xl="3"
                style="text-align: center"
              >
                <el-button
                  type="primary"
                  icon="el-icon-search"

                  class="smallbtn"
                  @click="searchBtn"
                  >搜索</el-button
                >
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="marginTop">
        <el-col :span="24"
          ><el-button
            type="success"
            icon="el-icon-plus"
            @click="addBtn('addForm')"
            class="addbtn"
            v-allow="'manager:save'"
            >添加管理员</el-button
          ></el-col
        >
      </el-row>
      <el-row class="table">
        <el-col :span="24">
          <!-- 数据表格 开始 -->
          <el-table
            :data="tableData"
            style="width: 100%"
            class="table_init"
            @sort-change="sortChange"
            stripe
            v-loading="loading"
          >
            <!-- <el-table-column type="index" align="center" label="序号"></el-table-column> -->
            <el-table-column
              prop="logonName"
              label="学工号"
              align="center"
              label-width="100px"
            ></el-table-column>
            <el-table-column
              prop="trueName"
              label="姓名"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="roleName"
              label="角色名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="memo"
              label="备注"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="edit(scope.row)"
                  v-allow="'manager:update'"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  style="color: red"
                  @click="remove(scope.row)"
                  v-allow="'manager:delete'"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 数据表格结束 -->

          <!-- 分页开始 -->
          <div class="paging">
            <paging
              :pageCount="pageCount"
              @pagebar="changePage"
              :currentpage="currentpage"
            ></paging>
          </div>
          <!-- 分页结束 -->

          <!-- 弹窗开始-->
          <el-dialog
            title="管理员管理"
            :visible.sync="dialogFormVisible"
            :modal-append-to-body="false"
            class="admin_dialog"
            width="500px"
            :close-on-click-modal="false"
          >
            <el-form
              :model="editForm"
              :rules="addRules"
              ref="addForm"
              label-width="80px"
            >
              <el-row>
                <el-col>
                  <el-form-item label="学工号" prop="editFormlogonName">
                    <el-select
                      v-model="editFormlogonName"
                      placeholder="请输入工号或者姓名关键词查询选择"
                      style="width: 100%"
                      clearable

                      filterable
                      remote
                      :remote-method="remoteMethod"
                      @change="handleSelect"
                    >
                      <el-option
                        v-for="(item, index) in teacherOptions"
                        :key="index"
                        :label="item.accNoTrueName"
                        :value="item.accNo"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="角色名称" prop="roleIds">
                    <el-select
                      v-model="editForm.roleIds"
                      placeholder="请选择"

                      class="size-full"
                      clearable
                      multiple
                    >
                      <el-option
                        v-for="(item, index) in roleOption"
                        :key="index"
                        :label="item.roleName"
                        :value="item.roleId"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="备注">
                    <el-input
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="editForm.memo"

                      clearable
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div slot="footer" class>
              <el-button @click="dialogFormVisible = false" size="mini"
                >取消</el-button
              >
              <el-button type="primary" @click="addUser('addForm')" size="mini"
                >确定</el-button
              >
            </div>
          </el-dialog>
          <!-- 弹窗结束-->
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import ZkTable from "vue-table-with-tree-grid";
Vue.use(ZkTable);

//引入接口
import {
  getManager,
  getAccNo,
  addManager,
  updateManager,
  delManager,
} from "@admin/api/Management/administrator.js";
import { getAllrole } from "@admin/api/Management/role.js";
import Paging from "@admin/components/pagebar/Paging.vue";
import Account from "@admin/components/Select/Account.vue";
export default {
  data() {
    return {
      currentpage: 1,
      tableData: [], //表格数据
      loading: true, //loading加载
      dialogFormVisible: false, //新增模态框初始状态
      // 查询角色的关键词
      searchData: {
        manRoleId: "",
      },
      // 查询角色下拉框
      serachRoleOption: [],
      //表格接口参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderKey: "",
        orderModel: "",
        logonName: "",
      },

      //表格总数
      pageCount: 0,

      //新增修改管理员
      editForm: {
        accNo: "",
        roleIds: [],
        memo: "",
        manKind: "",
        status: "",
      },
      editFormlogonName: "",

      trueName: "",

      // 选择的logonName值
      logonName: "",

      // 角色下拉框
      roleOption: [],

      treeData: [], //权限数数据
      parentName: "", //父级菜单文本名字

      teacherOptions: [], //学工号\姓名下拉框
      teacherOptions2: [],
    };
  },
  methods: {
    // 初始化表格
    initTable() {
      this.loading = true;
      const pageNum = this.searchForm.pageNum;
      const pageSize = this.searchForm.pageSize;
      const logonName = this.searchForm.logonName;
      const manRoleId = this.searchData.manRoleId;
      const orderKey = this.searchForm.orderKey;
      const orderModel = this.searchForm.orderModel;
      getManager(
        pageNum,
        pageSize,
        logonName,
        manRoleId,
        orderKey,
        orderModel
      ).then((res) => {
        this.tableData = res.data;
        this.loading = false;
        this.pageCount = res.count;
      });
    },
    querySearch(queryString, cb) {
      let arr = [];
      getAccNo(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.accNoTrueName,
              value: item.accNoTrueName,
            });
            this.teacherOptions.push({
              label: item.accNoTrueName,
              value: item.accNo,
            });
          });
          cb(arr);
        }
      });
    },
    getLogonName(data) {
      this.searchForm.logonName = data;
    },
    // 点击查询按钮
    searchBtn() {
      this.loading = true;
      this.searchForm.pageNum = 1;
      this.initTable();
    },
    // 获取查询角色数据
    getSearchRole() {
      getAllrole().then((res) => {
        if (res.code == 0) {
          this.serachRoleOption = res.data;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 表格排序
    sortChange(obj) {
      this.searchForm.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.searchForm.orderKey = obj.prop == "accNo" ? "acc_no" : obj.prop;
      this.initTable();
    },
    // 分页组件改变了值后触发的事件
    changePage(pageData) {
      this.searchForm.pageNum = pageData.pageNum;
      this.searchForm.pageSize = pageData.page;
      this.currentpage = pageData.pageNum;
      this.initTable();
    },
    //点击学工号，姓名下拉框
    remoteMethod(val) {
      this.SearchName(val, 20); //学工号、姓名下拉框查询
    },
    //点击学工号，姓名下拉框
    remoteMethod2(val) {
      this.SearchName2(val, 20); //学工号、姓名下拉框查询
    },
    //学工号、姓名下拉框查询
    SearchName(key, num) {
      this.teacherOptions = [];
      getAccNo(key, num).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.teacherOptions.push({
              accNoTrueName: item.accNoTrueName,
              logonName: item.logonName,
              accNo: item.accNo,
            });
          });
        }
      });
    },
    SearchName2(key, num) {
      getAccNo(key, num).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.teacherOptions2.push({
              label: item.accNoTrueName,
              value: item.logonName,
            });
          });
        }
      });
    },
    // 点击新增按钮
    addBtn(formName) {
      this.dialogFormVisible = true;
      this.editForm.uuid = "";
      this.editForm.memo = "";
      this.trueName = "";
      this.accNo = "";
      //--------
      this.editForm.accNo = "";
      this.editFormlogonName = "";
      this.queryStr = "";
      if (this.$refs[formName] != undefined) {
        this.$refs[formName].resetFields();
      }
      // 获取最新的数据
      // this.getAcc();
      getAllrole().then((res) => {
        if (res.code == 0) {
          this.roleOption = res.data;
          console.log(this.roleOption);
        }
      });
      // 清空数据
    },
    clear(formName) {
      // 点击取消清除数据
      this.dialogFormVisible = false;
      this.$refs[formName].resetFields();
    },
    handleNodeClick(data) {
      // 点击模态框中的右侧树状图
      this.parentName = data.funName;
      this.addForm.funParenId = data.funId;
    },
    // 点击编辑按钮
    edit(row) {
      if (this.$refs.addForm != undefined) {
        this.$refs.addForm.resetFields();
      }
      this.dialogFormVisible = true;
      // 获取最新的数据
      this.SearchName("", 100);
      getAllrole().then((res) => {
        if (res.code == 0) {
          this.roleOption = res.data;
          if (row.roleList) {
            this.editForm.roleIds = [];
            row.roleList.map((item, index) => {
              this.editForm.roleIds.push(item.roleId);
            });
          }
        }
      });

      this.editForm.uuid = row.uuid;
      // this.editForm.accNo = row.accNoTrueName;
      this.queryStr = `${row.logonName}`;
      //--------
      this.logonName = "";
      this.$nextTick(function () {
        this.editForm.accNo = row.accNo;
        this.editForm.memo = row.memo;
        this.trueName = row.trueName;
        this.editForm.status = row.status;
        this.editForm.manKind = row.manKind;
        this.logonName = row.logonName; //--------
        this.editFormlogonName = row.logonName + "(" + row.trueName + ")";
      });
    },
    // 点击表格中的删除按钮
    remove(rowData) {
      this.$confirm("此操作将删除该管理员，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 用户点击确认删除
          let json = {
            uuid: rowData.uuid,
          };
          delManager(json).then((res) => {
            if (res.code == 0) {
              // 当删除接口正确提示删除成功
              this.initTable();
              this.$message({
                type: "success",
                message: res.message,
              });
              // this.$notify.success({
              // 	title: '消息',
              // 	message: res.message
              // });
            } else {
              this.$message({
                type: "info",
                message: res.message,
              });
            }
          });
        })
        .catch(() => {
          // 用户点击取消删除
          this.$notify.info({
            title: "消息",
            message: "已取消删除",
          });
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    // 新增用户
    addUser(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.editForm.uuid == "") {
            this.editForm.accNo = this.editFormlogonName;
            this.$delete(this.editForm, "uuid"); //删除uuid键值，新增管理员接口不需要该参数
            // 新增发送请求
            addManager(this.editForm).then((res) => {
              if (res.code == 0) {
                this.dialogFormVisible = false;
                // 刷新数据
                this.initTable();
                this.$message({
                  message: res.message,
                  type: "success",
                });
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                });
                this.dialogFormVisible = false;
                console.log(res);
              }
            });
          } else {
            console.log(this.editForm);
            // 发送编辑请求
            updateManager(this.editForm).then((res) => {
              if (res.code == 0) {
                this.initTable();
                this.$message({
                  message: res.message,
                  type: "success",
                });
                this.dialogFormVisible = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                });
                this.dialogFormVisible = false;
              }
            });
          }
        } else {
          this.$message({
            message: res.message,
            type: "warning",
          });
          return false;
        }
      });
    },
    //全部展开
    openall() {
      this.props.isFold = false;
    },
    //全部折叠
    closeall() {
      this.props.isFold = true;
    },
    // 选择某一项
    handleSelect(data) {
      this.editForm.accNo = data;
    },
  },
  created() {
    this.initTable();
    this.getSearchRole();
  },
  computed: {
    // 新增表单验证
    addRules() {
      return {
        roleIds: [
          {
            required: true,
            message: "请选择角色",
            trigger: "change",
          },
        ],
      };
    },
    // 菜单下拉框
    addOption() {
      return [
        { label: "菜单", value: 1 },
        { label: "按钮", value: 2 },
        { label: "目录", value: 4 },
      ];
    },
    // 表格层级过滤器
    levelList() {
      return {
        one: "一级",
        two: "二级",
        three: "三级",
      };
    },
    // 模态框表单验证
    fnRules() {
      return {
        funName: [
          {
            required: true,
            message: "请输入菜单名称",
            trigger: "blur",
          },
        ],
        funParenId: [
          {
            required: true,
            message: "请选择父菜单",
            trigger: "blur",
          },
        ],
        funType: [
          {
            required: true,
            message: "请选择菜单类型",
            trigger: "change",
          },
        ],
        orderNum: [
          {
            required: true,
            message: `该项不能为空`,
            trigger: "blur",
          },
        ],
        uiUrl: [
          {
            required: true,
            message: "请输入菜单地址",
            trigger: "blur",
          },
        ],
      };
    },
  },
  filters: {
    // 层级
    hierarchy(val, obj) {
      var str = "";
      switch (val) {
        case 4:
          str = obj.one;
          break;
        case 1:
          str = obj.two;
          break;
        case 2:
          str = obj.three;
          break;
      }
      return str;
    },
  },
  components: {
    Paging,
    Account,
  },
};
</script>
<style>
label {
  font-weight: 500 !important;
}
/* .el-col {
  margin-top: 20px;
} */
.el-autocomplete {
  width: 100%;
}
.el-select {
  width: 100%;
}
</style>
<style lang="less" scoped>
.table {
  margin-top: 20px;
}
//设置浏览器滚动条的样式
::-webkit-scrollbar {
  width: 6px;
  height: 500px;
}
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
</style>
