<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <el-row class="search">
        <el-col :span="24">
          <el-form :model="searchForm" width="100%" label-width="110px" class="itemBox">
            <el-row :gutter="30">
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
                <el-form-item label="应用编号">
                  <el-input v-model="searchForm.applicationSn"  placeholder="请选择" style="width: 100%"
                    clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
                <el-form-item label="应用名称">
                  <el-input v-model="searchForm.applicationName"  placeholder="请选择" style="width: 100%"
                    clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
                <el-form-item label="应用类型">
                  <el-select v-model="searchForm.kind" placeholder="请选择"  style="width: 100%">
                    <el-option v-for="(item, index) in TypeOptions" :key="index" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="text-align: center">
              <el-button type="primary" icon="el-icon-search" @click="searchBtn"
                class="smallbtn">搜索</el-button>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="marginTop">
        <el-col :span="24"><el-button type="success" icon="el-icon-plus" class="addbtn" @click="addBtn"
            v-allow="'sysApplication:save'">新增</el-button></el-col>
      </el-row>
      <!-- 数据表格开始 -->
      <el-row>
        <el-col>
          <el-table :data="tb_data" stripe border style="width: 100%" class="marginTop" v-loading="loading">
            <el-table-column prop="applicationSn" label="应用编号" align="center"></el-table-column>
            <el-table-column prop="applicationName" label="应用名称" align="center"></el-table-column>
            <el-table-column prop="applicationType" label="应用类型" align="center"
              :formatter="TypeFormatter"></el-table-column>
            <el-table-column prop="applicationIcon" label="应用图标" align="center">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-view" @click="Handlercatchimg(scope.row)">查看</el-button>
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="appSecret"
              label="应用密钥"
              align="center"
            ></el-table-column> -->
            <el-table-column prop="active" label="默认激活" align="center">
              <template slot-scope="scope">
                <el-switch style="display: block" v-model="scope.row.activeFlag" active-color="#13ce66"
                  inactive-color="#ff4949" active-text="是" inactive-text="否"
                  @change="handleractive(scope.row.activeFlag, scope.row)">
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center" :formatter="StatusFormatter"></el-table-column>
            <el-table-column prop="applicationDesc" label="应用说明" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
              <template v-slot="scope">
                <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)"
                  v-allow="'sysApplication:update'">编辑</el-button>
                <el-button type="text" icon="el-icon-delete" style="color: red" @click="delBtn(scope.row)"
                  v-if="scope.row.systemFlag == 0" v-allow="'sysApplication:delete'">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <!-- 数据表格结束 -->
      <!-- 分页开始 -->
      <el-row>
        <div class="paging">
          <Paging :pageCount="pageCount" @pagebar="changePage" :currentpage="currentpage"></Paging>
        </div>
      </el-row>
      <!-- 分页结束 -->
    </div>
    <el-dialog title="查看图标" :visible.sync="diaShow_img" width="500px" append-to-body :close-on-click-modal="false">
      <img :src="imgurl" style="width: 100%" />
    </el-dialog>
    <el-dialog :title="dialogtitle" :visible.sync="diaShow" width="900px" append-to-body :close-on-click-modal="false">
      <el-form :model="addForm" label-width="120px" :rules="formRule" ref="diaForm">
        <el-row>
          <el-col :span="12">
            <el-form-item label="应用编号" prop="applicationSn">
              <el-input v-model="addForm.applicationSn"  placeholder="请输入..."
                :disabled="disabledflag"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="addForm.status" placeholder="请选择"  style="width: 100%">
                <el-option v-for="(item, index) in StatusOptions" :key="index" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="应用类型" prop="applicationType">
              <el-select v-model="addForm.applicationType" placeholder="请选择"  style="width: 100%">
                <el-option v-for="(item, index) in TypeOptions" :key="index" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="用户访问路径" prop="userPath" v-if="addForm.applicationType == 2">
              <el-input v-model="addForm.userPath"  placeholder="请输入..."></el-input>
            </el-form-item>
            <el-form-item label="管理员访问路径" prop="adminPath" v-if="addForm.applicationType == 1 && addForm.kind == 2">
              <el-input v-model="addForm.adminPath"  placeholder="请输入..."></el-input>
            </el-form-item>
            <el-form-item label="授权应用密钥" prop="appSecret" v-if="addForm.kind == 2">
              <el-input v-model="addForm.appSecret"  placeholder="请输入..."></el-input>
            </el-form-item>
            <el-form-item label="图标上传" prop="imageUrl">
              <el-upload action="#" multiple ref="upload" :show-file-list="false" :auto-upload="true"
                :on-preview="showImg" :on-change="uploadChange" :http-request="submitFile" list-type="picture-card"
                accept="image/png, image/gif, image/jpeg">
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <i v-else slot="default" class="el-icon-plus"></i>
                <div slot="tip" class="el-upload-tip" style="font-size: 14px; color: #909399">
                  <p style="margin-left: 20px; margin-bottom: 0px">
                    重新上传点击图片即可
                  </p>
                  <p style="margin-bottom: 0px">只能上传png/gif/jpeg/jpg格式的图片</p>
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用名称" prop="applicationName">
              <el-input v-model="addForm.applicationName"  placeholder="请输入..."></el-input>
            </el-form-item>
            <el-form-item label="排序" prop="orderNum">
              <el-input v-model="addForm.orderNum"  type="number" placeholder="请输入..."></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="kind">
              <el-select v-model="addForm.kind" filterable placeholder="请选择"  style="width: 100%">
                <el-option v-for="(item, index) in kindOptions" :key="index" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="授权应用id" prop="appId" v-if="addForm.kind == 2">
              <el-input v-model="addForm.appId"  placeholder="请输入..."></el-input>
            </el-form-item>
            <el-form-item label="应用说明" prop="applicationDesc">
              <el-input v-model="addForm.applicationDesc"  placeholder="请输入..." type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diaShow = false" >取消</el-button>
        <el-button type="primary"  @click="confirmBtn">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getApplication,
  saveApplication,
  updateApplication,
  deleteApplication,
  updateActive,
} from "@admin/api/Management/management.js";
import { uploadimg } from "@admin/api/Settings/DictTable.js";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      ApiUrl:window.g.ApiUrl,
      disabledflag: false,
      addForm: {
        uuid: "",
        applicationSn: "",
        applicationName: "",
        applicationDesc: "",
        applicationIcon: "",
        applicationType: "",
        status: "",
        userPath: "",
        adminPath: "",
        appId: "",
        appSecret: "",
        orderNum: "",
        kind: "",
      },
      imageUrl: "",
      StatusOptions: [
        {
          label: "正常",
          value: 1,
        },
        {
          label: "禁用",
          value: 2,
        },
      ],
      kindOptions: [
        {
          label: "本地",
          value: 1,
        },
        {
          label: "第三方",
          value: 2,
        },
      ],
      TypeOptions: [
        {
          label: "管理端应用",
          value: 1,
        },
        {
          label: "用户端应用",
          value: 2,
        },
      ],
      dialogtitle: "",
      diaShow: false,
      imgurl: "",
      diaShow_img: false,
      location: window.location.protocol + "//" + window.location.host + "/",
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        applicationSn: "",
        applicationName: "",
      },
      pageCount: 0,
      currentpage: "",
      tb_data: [], // 表格数据
      loading: false,
      // 模态框表单验证规则
      formRule: {
        applicationSn: [{ required: true, message: "该项不能为空", trigger: "blur" }],
        applicationName: [{ required: true, message: "该项不能为空", trigger: "blur" }],
      },
    };
  },
  methods: {
    //首页
    initTable() {
      this.loading = true;
      getApplication(this.searchForm).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          res.data.map((item, index) => {
            if (item.active == 0) {
              res.data[index].activeFlag = false;
            } else {
              res.data[index].activeFlag = true;
            }
          });
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //确定
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.addForm.uuid == "") {
            saveApplication(this.addForm).then((res) => {
              if (res.code == 0) {
                this.diaShow = false;
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            updateApplication(this.addForm).then((res) => {
              if (res.code == 0) {
                this.diaShow = false;
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        }
      });
    },
    handleractive(flag, data) {
      if (flag) {
        let json = {
          uuid: data.uuid,
        };
        updateActive(json)
          .then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
              // data.activeFlag = true;
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
              this.initTable();
            }
          })
          .catch(() => {
            this.initTable();
          });
      }
    },
    //搜索
    searchBtn() {
      this.currentpage = 1;
      this.searchForm.pageNum = 1;
      this.initTable();
    },
    //新增
    addBtn() {
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.disabledflag = false;
      this.addForm.uuid = "";
      this.dialogtitle = "新增应用";
      this.imageUrl = "";
      this.addForm.applicationSn = "";
      this.addForm.applicationName = "";
      this.addForm.applicationDesc = "";
      this.addForm.applicationIcon = "";
      this.addForm.applicationType = "";
      this.addForm.status = "";
      this.addForm.userPath = "";
      this.addForm.adminPath = "";
      this.addForm.appId = "";
      this.addForm.appSecret = "";
      this.addForm.orderNum = "";
      this.addForm.kind = "";
    },
    //编辑
    editBtn(data) {
      this.diaShow = true;
      this.disabledflag = false;
      this.dialogtitle = "编辑应用";
      this.addForm.uuid = data.uuid;
      this.addForm.applicationSn = data.applicationSn;
      this.addForm.applicationName = data.applicationName;
      this.addForm.applicationDesc = data.applicationDesc;
      this.addForm.applicationIcon = data.applicationIcon;
      this.addForm.applicationType = data.applicationType;
      this.addForm.status = data.status;
      this.addForm.userPath = data.userPath;
      this.addForm.adminPath = data.adminPath;
      this.addForm.appId = data.appId;
      this.addForm.appSecret = data.appSecret;
      this.addForm.orderNum = data.orderNum;
      this.addForm.kind = data.kind;
      if (data.applicationIcon) {
        this.imageUrl = this.ApiUrl + "/" + data.applicationIcon;
      } else {
        this.imageUrl = "";
      }
      if (data.kind == 1) {
        this.disabledflag = true;
      }
    },
    //删除
    delBtn(row) {
      this.$confirm("此操作将永久删除该应用, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          deleteApplication(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //查看应用图标
    Handlercatchimg(data) {
      if (data.applicationIcon) {
        this.imgurl = this.location + "lab/" + data.applicationIcon;
        this.diaShow_img = true;
      } else {
        this.$message.error("暂无图标");
        this.diaShow_img = false;
      }

      console.log(this.imgurl);
    },
    //上传图片部分
    // 查看大图
    showImg(res) {
      this.imgDialogVisible = true;
      this.formData.img = res.url;
    },
    uploadChange(file, fileList) {
      if (fileList.length > 0) {
        this.canUpload = true;
        this.imageUrl = URL.createObjectURL(file.raw);
      }
      // if (fileList.length > 0) {
      //   this.imageUrl = URL.createObjectURL(file.raw);
      // }
    },
    // 上传图片
    submitFile(params) {
      // 获取要上传的图片
      const file = params.file,
        fileType = file.type,
        isImage = fileType.includes("image"); // 是否是图片
      // console.log('params',params);
      // 如果不是图片
      if (!isImage) {
        this.$message({
          message: "只能上传图片格式png,gif,jpeg!",
          type: "error",
          duration: "5000",
        });
        return;
      }
      this.fileData = new FormData();
      this.fileData.append("file", file);
      if (this.canUpload) {
        uploadimg(this.fileData).then((res) => {
          if (res.code === 0) {
            this.$message.success("上传成功");
            this.addForm.applicationIcon = res.data;
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
          }
        });
      }
      this.canUpload = false;
    },
    // 分页函数
    changePage(pageData) {
      this.searchForm.pageNum = pageData.pageNum;
      this.searchForm.pageSize = pageData.page;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
    //应用类型筛选
    TypeFormatter(row) {
      if (row.applicationType == 0) {
        return "未知";
      } else if (row.applicationType == 1) {
        return "管理端应用";
      } else if (row.applicationType == 2) {
        return "用户端应用";
      }
    },
    StatusFormatter(row) {
      if (row.status == 1) {
        return "正常";
      } else if (row.status == 2) {
        return "禁用";
      }
    },
  },
  created() {
    this.initTable();
  },
  components: {
    Paging,
  },
};
</script>
<style lang="less" scoped>
/deep/ .avatar {
  width: 120px;
}
</style>
