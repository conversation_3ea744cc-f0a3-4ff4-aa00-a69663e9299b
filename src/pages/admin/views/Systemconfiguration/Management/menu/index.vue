<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <el-row>
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="2"
          ><el-button
            type="primary"
            icon="el-icon-plus"
            @click="addFn"
            class="addbtn"
            v-allow="'function:save'"
            >添加菜单</el-button
          ></el-col
        >
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="2"
          ><el-button
            icon="el-icon-caret-bottom"
            type="primary"
            @click="openall"
            class="addbtn"
            >全部展开</el-button
          ></el-col
        >
        <el-col :xs="8" :sm="6" :md="4" :lg="3" :xl="2"
          ><el-button
            icon="el-icon-caret-top"
            type="primary"
            @click="closeall"
            class="addbtn"
            >全部折叠</el-button
          ></el-col
        >
      </el-row>
      <el-row style="margin-top: 20px">
        <el-col :span="24">
          <!-- 树状表格开始-->
          <zk-table
            ref="table"
            sum-text="sum"
            index-text="#"
            :data="tableData"
            :columns="columns"
            :stripe="props.stripe"
            :border="props.border"
            :show-header="props.showHeader"
            :show-summary="props.showSummary"
            :show-row-hover="props.showRowHover"
            :show-index="props.showIndex"
            :tree-type="props.treeType"
            :is-fold="props.isFold"
            :expand-type="props.expandType"
            :selection-type="props.selectionType"
            v-loading="loading"
          >
            <template slot="funType" slot-scope="scope">
              <el-tag

                v-if="scope.row.funType == 1"
                type="primary"
                plain
                >菜单</el-tag
              >
              <el-tag

                v-if="scope.row.funType == 2"
                type="primary"
                plain
                >按钮</el-tag
              >
              <el-tag
                type="primary"
                plain

                v-if="scope.row.funType == 3"
                >Tab页</el-tag
              >
              <el-tag
                type="primary"
                plain

                v-if="scope.row.funType == 4"
                >目录</el-tag
              >
            </template>
            <template slot="type" slot-scope="scope">
              <div>{{ scope.row.funType | hierarchy(levelList) }}</div>
            </template>
            <template slot="likes" slot-scope="scope">
              <!-- {{ scope.row.name }} -->
              <!-- <el-button
                type="text"
                icon="el-icon-edit"
                @click="edit(scope.row)"
                v-if="scope.row.funType != -1"
                v-allow="'function:update'"
                >编辑</el-button
              >
              <el-button
                type="text"
                icon="el-icon-delete"
                style="color: red"
                @click="remove(scope.row)"
                v-if="scope.row.funType != -1"
                v-allow="'function:delete'"
                >删除</el-button
              > -->
              <el-button
                type="text"
                icon="el-icon-edit"
                @click="edit(scope.row)"
                v-if="scope.row.funType != -1"
                >编辑</el-button
              >
              <el-button
                type="text"
                icon="el-icon-delete"
                style="color: red"
                @click="remove(scope.row)"
                v-if="scope.row.funType != -1"
                >删除</el-button
              >
            </template>
          </zk-table>
          <!-- 树状表格结束-->

          <!-- 新增模态框开始-->
          <el-dialog
            title="菜单管理"
            :visible.sync="dialogFormVisible"
            :modal-append-to-body="false"
            width="900px"
            top="4%"
            :close-on-click-modal="false"
          >
            <div class="content">
              <el-row :gutter="20">
                <!-- 左侧表单 -->
                <el-col :span="14">
                  <el-form
                    :model="addForm"
                    label-width="120px"
                    :rules="fnRules"
                    ref="changeFn"
                  >
                    <el-form-item label="菜单类型" prop="funType">
                      <el-select
                        v-model="addForm.funType"
                        placeholder="请选择..."
                        class="size-full"

                        style="width: 100%"
                        @change="handlerChangefun"
                      >
                        <el-option
                          v-for="(item, index) in addOption"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="菜单编号" prop="funCode">
                      <el-input
                        v-model="addForm.funCode"
                        placeholder="请输入菜单编号"

                      ></el-input>
                    </el-form-item>
                    <el-form-item label="菜单名称" prop="funName">
                      <el-input
                        v-model="addForm.funName"
                        placeholder="请输入菜单名称"

                      ></el-input>
                    </el-form-item>
                    <el-form-item label="所属应用" prop="applicationSn">
                      <el-select
                        v-model="addForm.applicationSn"
                        placeholder="请选择..."
                        class="size-full"

                        style="width: 100%"
                        @change="handlerapplication"
                      >
                        <el-option
                          v-for="(item, index) in applicationOption"
                          :key="index"
                          :label="item.applicationName"
                          :value="item.applicationSn"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="排序" prop="orderNum">
                      <el-input-number
                        v-model="addForm.orderNum"

                        controls-position="right"
                        :min="1"
                        style="width: 100%"
                      ></el-input-number>
                    </el-form-item>
                    <el-form-item label="父级" prop="funParenId">
                      <el-input
                        v-model="parentName"
                        :disabled="true"
                        placeholder="请在右侧进行选择"

                      ></el-input>
                    </el-form-item>
                    <!-- 目录 表单 -->
                    <el-row v-if="addForm.funType == 4">
                      <el-form-item label="菜单地址" prop="uiUrl">
                        <el-input
                          v-model="addForm.uiUrl"
                          placeholder="请输入菜单地址"

                        ></el-input>
                      </el-form-item>
                      <el-form-item label="菜单组件地址" prop="componentUrl">
                        <el-input
                          v-model="addForm.componentUrl"
                          placeholder="请输入菜单组件地址"

                        ></el-input>
                      </el-form-item>
                      <el-form-item label="备注" prop="memo"
                        ><el-input
                          v-model="addForm.memo"
                          placeholder="请输入备注"

                          type="textarea"
                        ></el-input
                      ></el-form-item>
                      <el-form-item label="图标" prop="icon"
                        ><el-input
                          v-model="addForm.icon"

                        ></el-input
                      ></el-form-item>
                    </el-row>
                    <!-- 菜单 表单 -->
                    <el-row v-if="addForm.funType == 1">
                      <el-form-item label="菜单地址" prop="uiUrl">
                        <el-input
                          v-model="addForm.uiUrl"
                          placeholder="请输入菜单地址"

                        ></el-input>
                      </el-form-item>
                      <el-form-item label="菜单组件地址" prop="componentUrl">
                        <el-input
                          v-model="addForm.componentUrl"
                          placeholder="请输入菜单组件地址"

                        ></el-input>
                      </el-form-item>
                      <el-form-item label="权限" prop="sysUrl">
                        <el-input
                          v-model="addForm.sysUrl"
                          placeholder="请输入权限"

                        ></el-input>
                      </el-form-item>
                      <el-form-item label="图标" prop="icon"
                        ><el-input
                          v-model="addForm.icon"

                        ></el-input
                      ></el-form-item>
                    </el-row>
                    <!-- 按钮 菜单 -->
                    <el-row v-if="addForm.funType == 2">
                      <el-form-item label="权限" prop="sysUrl">
                        <el-input
                          v-model="addForm.sysUrl"
                          placeholder="请输入权限"

                        ></el-input>
                      </el-form-item>
                      <el-form-item label="图标" prop="icon"
                        ><el-input
                          v-model="addForm.icon"

                        ></el-input
                      ></el-form-item>
                    </el-row>
                  </el-form>
                </el-col>
                <!-- 右侧树形 -->
                <!-- <el-col :span="10" v-show="addForm.funType == 1 || addForm.funType == 2"> -->
                <el-col :span="10">
                  <el-tree
                    :data="treeData"
                    :props="defaultProps"
                    @node-click="handleNodeClick"
                  ></el-tree>
                </el-col>
              </el-row>
            </div>

            <div slot="footer" class="dialog-footer">
              <el-button @click="clear('changeFn')" size="mini">取消</el-button>
              <el-button
                type="primary"
                @click="editFn('changeFn')"
                size="mini"
                plain
                >确定</el-button
              >
            </div>
          </el-dialog>
          <!-- 新增模态框结束-->
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import ZkTable from "vue-table-with-tree-grid";
Vue.use(ZkTable);

//引入接口
import {
  addFunction,
  getFunction,
  getAllmenu,
  delFunction,
  updateFunction,
  getApplicationlist,
} from "@admin/api/Management/management";
export default {
  data() {
    return {
      tableData: [], //表格数据
      loading: true, //loading加载
      dialogFormVisible: false, //新增模态框初始状态
      props: {
        // 插件表格配置
        stripe: false, //是否显示间隔斑马纹
        border: false, //是否显示纵向边框
        showHeader: true, //是否显示表头
        showSummary: false, //是否显示表尾合计行
        showRowHover: true, //鼠标悬停时，是否高亮当前行
        showIndex: false, //是否显示数据索引
        treeType: true, //是否为树形表格
        isFold: true, //树形表格中父级是否默认折叠
        expandType: false, //是否为展开行类型表格（为 True 时，需要添加作用域插槽, 它可以获取到 row, rowIndex)
        selectionType: false, //是否显示间隔斑马纹
      },
      addForm: {
        //新增 编辑方法时的表单数据
        applicationSn: "",
        funName: "",
        funCode: "",
        funParenId: 0,
        funType: 4,
        icon: "",
        uiUrl: "",
        componentUrl: "",
        sysUrl: "",
        memo: "",
        uuid: "",
        orderNum: "",
      },
      applicationOption: [], //所属应用下拉框
      treeData: [], //权限数数据
      defaultProps: {
        //设置树节点对应的属性
        children: "children",
        label: "funName",
      },
      parentName: "", //父级菜单文本名字
    };
  },
  methods: {
    initTable() {
      this.loading = true;
      // 初始化表格
      getFunction().then((res) => {
        this.tableData = res.data;
        this.loading = false;
      });
    },
    addFn() {
      // 点击新增按钮
      this.dialogFormVisible = true;
      // 清空数据
      this.addForm.uuid = "";
      this.parentName = "顶级";
      this.treeData = [];
      if (this.$refs.changeFn != undefined) {
        this.$refs["changeFn"].resetFields();
      }
    },
    clear(formName) {
      // 点击取消清除数据
      this.dialogFormVisible = false;
      this.$refs[formName].resetFields();
    },
    //选择菜单类型
    handlerChangefun(data) {},
    handleNodeClick(data) {
      // 点击模态框中的右侧树状图
      this.parentName = data.funName;
      this.addForm.funParenId = data.funId;
    },
    //获取应用下拉框数据
    getApplicationlist() {
      getApplicationlist().then((res) => {
        if (res.code == 0) {
          this.applicationOption = res.data;
        }
      });
    },
    // 点击表格中的编辑按钮
    edit(rowData) {
      // 弹出弹框
      this.dialogFormVisible = true;
      // 将菜单信息给上去
      this.addForm.uuid = rowData.uuid;
      if (this.$refs.changeFn != undefined) {
        this.$refs["changeFn"].resetFields();
      }
      this.treeData = [];
      this.getallmenu(rowData.applicationSn);
      this.$nextTick(function () {
        this.addForm.funCode = rowData.funCode;
        this.addForm.applicationSn = rowData.applicationSn;
        this.addForm.funName = rowData.funName;
        this.addForm.funParenId = rowData.funParenId;
        if (rowData.funParenId == 0) {
          this.parentName = "顶级";
        } else {
          this.treeData.map((item, index) => {
            if (rowData.applicationSn == item.applicationSn) {
              item.children.map((v, k) => {
                if (rowData.funParenId == item.funId) {
                  this.parentName = item.funName;
                }
              });
            }
          });
        }
        console.log(rowData);
        this.addForm.funType = rowData.funType;
        this.addForm.icon = rowData.icon;
        this.addForm.uiUrl = rowData.uiUrl;
        this.addForm.componentUrl = rowData.componentUrl;
        this.addForm.sysUrl = rowData.sysUrl;
        this.addForm.memo = rowData.memo;
        this.addForm.orderNum = rowData.orderNum;
      });
    },
    // 点击表格中的删除按钮
    remove(rowData) {
      this.$confirm("此操作将删除该菜单，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 用户点击确认删除
          let json = {
            uuid: rowData.uuid,
          };
          delFunction(json).then((res) => {
            if (res.code == 0) {
              // 当删除接口正确提示删除成功
              this.initTable();
              this.$message({
                type: "success",
                message: res.message,
              });
            } else {
              this.$message({
                type: "info",
                message: res.message,
              });
            }
          });
        })
        .catch(() => {
          // 用户点击取消删除
          this.$notify.info({
            title: "消息",
            message: "已取消删除",
          });
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    // 新增 修改 点击确认按钮触发事件
    editFn(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.parentName == "顶级") {
            this.addForm.funParenId = 0;
          }
          if (this.addForm.uuid == "") {
            // 执行新增功能
            addFunction(this.addForm).then((res) => {
              if (res.code == 0) {
                this.$message({
                  message: res.message,
                  type: "success",
                });
                // 清除数据
                this.$refs[formName].resetFields();
                this.parentName = "";
                // 隐藏模态框
                this.dialogFormVisible = false;
                // 重新获去最新的数据
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "4000",
                });
              }
            });
          } else {
            if (this.parentName == "顶级") {
              this.addForm.funParenId = 0;
            }
            // 执行编辑功能
            updateFunction(this.addForm).then((res) => {
              if (res.code == 0) {
                this.$message({
                  message: res.message,
                  type: "success",
                });
                // 清除数据
                this.$refs[formName].resetFields();
                this.parentName = "";
                // 隐藏模态框
                this.dialogFormVisible = false;
                // 重新获去最新的数据
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "4000",
                });
              }
            });
          }
        } else {
          this.$message({
            message: "请完善信息后再操作",
            type: "warning",
          });
          return false;
        }
      });
    },
    //全部展开
    openall() {
      this.props.isFold = false;
    },
    //全部折叠
    closeall() {
      this.props.isFold = true;
    },
    //获取弹窗右侧的权限菜单（没有按钮）
    getallmenu(applicationSn) {
      getAllmenu().then((res) => {
        res.data.map((item, index) => {
          if (item.applicationSn == applicationSn) {
            this.treeData = item.children;
            console.log(this.treeData);
          }
        });
      });
    },
    //选择应用
    handlerapplication(data) {
      this.getallmenu(data);
      this.parentName = "顶级";
    },
  },
  created() {
    this.initTable();
    this.getApplicationlist();
    //this.getallmenu();
  },
  computed: {
    // 表头
    columns() {
      return [
        // 控制插件中的表头和列内容
        {
          label: "菜单ID",
          prop: "funId",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "菜单名称",
          prop: "funName",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "类型",
          prop: "funType",
          type: "template",
          template: "funType",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "图标",
          prop: "icon",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "URL地址",
          prop: "uiUrl",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "权限标识",
          prop: "sysUrl",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "层级",
          prop: "funType",
          type: "template",
          template: "type",
          align: "center",
          headerAlign: "center",
        },
        {
          label: "操作",
          prop: "uuid",
          width: "250px",
          type: "template",
          template: "likes",
          align: "center",
          headerAlign: "center",
        },
      ];
    },
    // 菜单下拉框
    addOption() {
      return [
        { label: "菜单", value: 1 },
        { label: "按钮", value: 2 },
        { label: "Tab页", value: 3 },
        { label: "目录", value: 4 },
      ];
    },
    // 表格层级过滤器
    levelList() {
      return {
        one: "一级",
        two: "二级",
        three: "三级",
      };
    },
    // 模态框表单验证
    fnRules() {
      return {
        funCode: [
          {
            required: true,
            message: "请输入菜单编号",
            trigger: "blur",
          },
        ],
        funName: [
          {
            required: true,
            message: "请输入菜单名称",
            trigger: "blur",
          },
        ],
        funParenId: [
          {
            required: true,
            message: "请选择父菜单",
            trigger: "blur",
          },
        ],
        funType: [
          {
            required: true,
            message: "请选择菜单类型",
            trigger: "change",
          },
        ],
        orderNum: [
          {
            required: true,
            message: `该项不能为空`,
            trigger: "blur",
          },
        ],
        uiUrl: [
          {
            required: true,
            message: "请输入菜单地址",
            trigger: "blur",
          },
        ],
        componentUrl: [
          {
            required: true,
            message: "请输入菜单组件地址",
            trigger: "blur",
          },
        ],
        applicationSn: [
          {
            required: true,
            message: "请选择所属应用",
            trigger: "change",
          },
        ],
      };
    },
  },
  filters: {
    // 层级
    hierarchy(val, obj) {
      var str = "";
      switch (val) {
        case 4:
          str = obj.one;
          break;
        case 1:
          str = obj.two;
          break;
        case 2:
          str = obj.three;
          break;
      }
      return str;
    },
  },
};
</script>
<style>
label {
  font-weight: 500 !important;
}

.zk-table {
  border: 1px solid white;
}
</style>
<style lang="less" scoped>
.bigbox {
  width: 96%;
  height: 96%;
  margin: 15px;
  background: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  padding: 2%;
}

.bigbox .table {
  margin-top: 2%;
}

.content {
  width: 100%;
  // height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}

//设置浏览器滚动条的样式
::-webkit-scrollbar {
  width: 6px;
  height: 500px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
</style>
