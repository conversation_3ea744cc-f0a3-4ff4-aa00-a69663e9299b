<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <el-row class="search">
        <el-col>
          <el-form
            :model="searchForm"
            width="100%"
            label-width="70px"
            class="itemBox"
          >
            <el-row :gutter="30">
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
                <el-form-item label="角色名称" prop="manRoleId">
                  <el-input
                    v-model="searchForm.roleName"
                    placeholder="请选择"

                    class="size-full"
                    clearable
                    style="width: 100%"
                  ></el-input>
                  <!-- <el-select v-model="searchForm.roleName" placeholder="请选择"  class="size-full" clearable style="width: 100%;">
										<el-option v-for="(item, index) in roleOption" :key="index" :label="item.roleName" :value="item.roleId"></el-option>
									</el-select> -->
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="8"
                :md="4"
                :lg="4"
                :xl="3"
                style="text-align: center"
              >
                <el-button
                  type="primary"
                  icon="el-icon-search"

                  class="smallbtn"
                  @click="searchBtn"
                  >搜索</el-button
                >
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="marginTop">
        <el-col :span="24"
          ><el-button
            type="primary"
            icon="el-icon-plus"
            @click="addRole('roleForm')"
            class="addbtn"
            v-allow="'roleMan:save'"
            >添加角色</el-button
          ></el-col
        >
      </el-row>
      <el-row class="table">
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <!-- 数据表格开始 -->
          <el-table
            ref="table"
            :data="roleData"
            @sort-change="sortChange"
            class="table_init table_col"
            style="width: 100%"
            stripe
            v-loading="loading"
          >
            <!-- <el-table-column type="index" label="序号" align="center" width="65px"></el-table-column> -->
            <el-table-column
              prop="roleName"
              label="角色名称"
              sortable="custom"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="ruleLevel"
              label="角色等级"
              sortable="custom"
              align="center"
            ></el-table-column>
            <el-table-column type="expand" width="1px">
              <template>
                <el-tree
                  :data="roleFn"
                  :props="defaultProps"
                  ref="kk"
                  node-key="roleFn"
                ></el-tree>
              </template>
            </el-table-column>
            <el-table-column label="角色功能" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="toogleExpand(scope.row)"
                  >查看详情</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="gmtCreate"
              label="创建时间"
              sortable="custom"
              align="center"
            >
              <template slot-scope="scope">
                {{ $moment(scope.row.gmtCreate).format("YYYY-MM-DD HH:mm") }}
              </template>
            </el-table-column>
            <el-table-column
              prop="gmtModified"
              label="更新时间"
              sortable="custom"
              align="center"
            >
              <template slot-scope="scope">
                {{ $moment(scope.row.gmtModified).format("YYYY-MM-DD HH:mm") }}
              </template>
            </el-table-column>
            <el-table-column
              prop="memo"
              label="备注"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center" width="260px">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="edit(scope.row)"
                  class="marginright"
                  v-allow="'roleMan:update'"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  style="color: red"
                  @click="remove(scope.row)"
                  class="marginright"
                  v-allow="'roleMan:delete'"
                  >删除</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-setting"
                  @click="distribution(scope.row)"
                  style="color: #21bd21"
                  v-allow="'function'"
                  >权限分配</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-setting"
                  @click="AuthLabRooms(scope.row)"
                  style="color: #21bd21"
                  >区域授权</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- 数据表格结束 -->
          <!-- 分页开始 -->
          <div class="paging">
            <paging :pageCount="count" @pagebar="changePage"></paging>
          </div>
          <!-- 分页结束 -->
        </el-col>

        <!-- 编辑弹出框 -->
        <el-dialog
          title="角色管理"
          :visible.sync="roleDialog"
          :modal-append-to-body="false"
          width="450px"
          :close-on-click-modal="false"
        >
          <el-row :gutter="15">
            <el-col :span="24">
              <!-- 左侧表单 -->
              <el-form
                label-width="100px"
                :model="roleForm"
                :rules="roleFormRules"
                ref="roleForm"
              >
                <el-form-item label="角色名称" prop="roleName">
                  <el-input
                    v-model="roleForm.roleName"
                    placeholder="请输入角色名称"

                  ></el-input>
                </el-form-item>
                <el-form-item label="角色等级" prop="ruleLevel">
                  <el-select
                    v-model="roleForm.ruleLevel"
                    placeholder="请输入角色等级"
                    class="size-full"

                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, index) in ruleOption"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    placeholder="请输入备注"
                    v-model="roleForm.memo"

                  ></el-input>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" @click="roleDialog = false">取消</el-button>
            <el-button type="primary" @click="addSubmit('roleForm')" size="mini"
              >确定</el-button
            >
          </div>
        </el-dialog>
        <!-- 编辑弹出框 -->

        <!-- 权限分配弹出框 -->
        <el-dialog
          title="权限分配"
          :visible.sync="permissionsDialog"
          :modal-append-to-body="false"
          width="400px"
          :close-on-click-modal="false"
          class="permisbox"
        >
          <div class="scrollbox">
            <el-form
              label-width="30px"
              :model="roleForm"
              :rules="roleFormRules"
              ref="roleForm"
            >
              <el-form-item>
                <el-tree
                  :data="treeData"
                  show-checkbox
                  node-key="funId"
                  ref="tree"
                  highlight-current
                  default-expand-all
                  :props="defaultProps"
                ></el-tree>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button size="mini" @click="permissionsDialog = false"
              >取消</el-button
            >
            <el-button type="primary" @click="updateper('roleForm')" size="mini"
              >确定</el-button
            >
          </div>
        </el-dialog>
        <!-- 权限分配弹出框 -->

        <!-- 区域授权 -->
        <el-dialog
          title="区域授权"
          :visible.sync="AuthlabShow"
          :modal-append-to-body="false"
          width="900px"
          :close-on-click-modal="false"
          :append-to-body="false"
          top="0%"
        >
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="实验中心-实验室-房间" name="first">
              <div style="width: 100%; height: 100%" v-loading="loading">
                <el-tree
                  :data="AuthlabData"
                  show-checkbox
                  node-key="label"
                  ref="authTree"
                  :props="{ label: 'name', children: 'children' }"
                  @check="handleCheck"
                ></el-tree>
              </div>
              <!-- <div class="dialog-footer" v-allow="'roleData:update'">
                <el-button
                  size="mini"
                  @click="AuthlabShow = false"
                  v-allow="'roleData:update'"
                  >取消</el-button
                >
                <el-button
                  type="primary"
                  @click="SubmitAuthlab()"
                  size="mini"
                  v-allow="'roleData:update'"
                  >确定</el-button
                >
              </div> -->
            </el-tab-pane>
            <el-tab-pane label="校区-楼宇-实验室-房间" name="second">
              <div style="width: 100%; height: 100%" v-loading="loading_campus">
                <el-tree
                  :data="AuthlabData_campus"
                  show-checkbox
                  node-key="label"
                  ref="authTree_campus"
                  :props="{ label: 'name', children: 'children' }"
                  @check="handleCheck"
                ></el-tree>
              </div>
            </el-tab-pane>
          </el-tabs>
          <div class="dialog-footer" v-allow="'roleData:update'">
            <el-button
              size="mini"
              @click="AuthlabShow = false"
              v-allow="'roleData:update'"
              >取消</el-button
            >
            <el-button
              type="primary"
              @click="SubmitAuthlab()"
              size="mini"
              v-allow="'roleData:update'"
              >确定</el-button
            >
          </div>
        </el-dialog>
        <!-- 区域授权 -->
      </el-row>
    </div>
  </div>
</template>

<script>
//引入接口
import Paging from "@admin/components/pagebar/Paging.vue";
import {
  getroleMan,
  updateRole,
  delRole,
  addRole,
} from "@admin/api/Management/role";
import {
  getFnByroleId,
  getAppMenuList,
  getFunction,
} from "@admin/api/Management/management";
import {
  getAllrole,
  getRoleData,
  getRoleData_campus,
  editRoleData,
} from "@admin/api/Management/role.js";
export default {
  data() {
    return {
      loading_campus: false,
      AuthlabData_campus: [],
      activeName: "first",
      loading: true, //loading加载
      roleData: [], //表格数据
      roleFn: [],
      searchForm: {
        //表格接口参数
        pageNum: 1,
        pageSize: 10,
        roleName: "",
        orderKey: "",
        orderModel: "",
      },
      //表格数据总数
      count: 0,
      defaultProps: {
        //设置树节点对应的属性
        children: "children",
        label: "funName",
      },
      roleDialog: false, //角色管理弹窗初始状态
      permissionsDialog: false, //权限分配弹窗初始状态
      // 角色下拉框
      roleOption: [],
      // 模态框中的表单数据
      roleForm: {
        roleName: "",
        ruleLevel: "",
        memo: "",
        funIdList: [],
        uuid: "",
        roleId: "",
      },
      roleUuid: "", //角色id
      treeData: [], //权限数数据
      parentName: "", //父级菜单文本名字
      AuthlabShow: false, //是否显示实验室授权弹窗
      AuthlabData: [], //实验室授权数据
      authedAreaIds: [],
      rolelevel: "",
    };
  },
  methods: {
    //点击tab切换
    handleClick() {},
    // 初始化表格
    initTable() {
      this.loading = true;
      const pageNum = this.searchForm.pageNum;
      const pageSize = this.searchForm.pageSize;
      const orderKey = this.searchForm.orderKey;
      const orderModel = this.searchForm.orderModel;
      getroleMan(
        pageNum,
        pageSize,
        this.searchForm.roleName,
        orderKey,
        orderModel
      ).then((res) => {
        this.roleData = res.data;
        this.loading = false;
        this.count = res.count;
      });
    },
    // 点击区域菜单树的复选框
    handleCheck(data) {
      this.$refs.authTree.store.nodesMap[data.label].expanded = true;
    },
    //点击实验室授权
    AuthLabRooms(row) {
      this.roleUuid = row.uuid;
      // 获取当前roleUuid的所有树
      this.AuthlabShow = true;
      this.getRoleData();
      this.getRoleData_campus();
    },
    //获取实验中心-实验室授权菜单树
    getRoleData() {
      this.canAuthed = [];
      getRoleData(this.roleUuid).then((res) => {
        if (res.code === 0) {
          // this.canAuthed = res.data;
          this.AuthlabData = this.addLabelRoleData(res.data);
          // console.log('cann: ', this.canAuthed);
          // 获取选中的节点并设置到界面上
          this.authedAreaIds = []; // 清空已选中的，重新设置已选中的
          this.getChecked(this.AuthlabData);
          this.$nextTick(() => {
            this.$refs.authTree.setCheckedKeys(this.authedAreaIds);
          });
        }
        this.treeLoading = false;
      });
    },
    //获取校区-实验室授权菜单树
    getRoleData_campus() {
      this.canAuthed = [];
      getRoleData_campus(this.roleUuid).then((res) => {
        if (res.code === 0) {
          // this.canAuthed = res.data;
          this.AuthlabData_campus = this.addLabelRoleData(res.data);
          // console.log('cann: ', this.canAuthed);
          // 获取选中的节点并设置到界面上
          this.authedAreaIds = []; // 清空已选中的，重新设置已选中的
          this.getChecked(this.AuthlabData_campus);
          this.$nextTick(() => {
            this.$refs.authTree_campus.setCheckedKeys(this.authedAreaIds);
          });
        }
        this.treeLoading = false;
      });
    },
    //点击实验室授权弹窗的确定按钮
    SubmitAuthlab() {
      // 确定授权
      // console.log("selected: ", this.$refs.authTree.getCheckedKeys());
      // 所有选中的节点
      const ckdArr = this.$refs.authTree.getCheckedKeys();
      const ckdArr2 = this.$refs.authTree_campus.getCheckedKeys();
      const roleUuid = this.roleUuid;
      // 例子: permissionList: [{"dataId": 115, "kind": 4}]
      let params = { roleUuid, roleDataList: [] };
      params.roleDataList = [
        ...this.getCheckedNodes(this.AuthlabData, ckdArr),
        ...this.getCheckedNodes(this.AuthlabData_campus, ckdArr2),
      ];
      var json = {},
        json2 = {},
        json3 = {},
        json4 = {};
      params.roleDataList.map((item, index) => {
        this.AuthlabData.map((a, b) => {
          a.children.map((v, k) => {
            if (v.children) {
              v.children.map((d, c) => {
                if (item.id == d.id) {
                  json = {
                    id: a.id,
                    kind: a.kind,
                  };
                  json2 = {
                    id: v.id,
                    kind: v.kind,
                  };
                }
              });
            } else {
              if (item.id == v.id) {
                json = {
                  id: a.id,
                  kind: a.kind,
                };
              }
            }
          });
        });
        this.AuthlabData_campus.map((a, b) => {
          a.children.map((v, k) => {
            if (v.children) {
              v.children.map((d, c) => {
                if (item.id == d.id) {
                  json3 = {
                    id: a.id,
                    kind: a.kind,
                  };
                  json4 = {
                    id: v.id,
                    kind: v.kind,
                  };
                }
              });
            } else {
              if (item.id == v.id) {
                json3 = {
                  id: a.id,
                  kind: a.kind,
                };
              }
            }
          });
        });
      });
      let afterparams = { roleUuid, roleDataList: [] };
      if (json) {
        afterparams.roleDataList.push(json);
      }
      if (json2) {
        afterparams.roleDataList.push(json2);
      }
      if (json3 && json3.id != json.id) {
        afterparams.roleDataList.push(json3);
      }
      if (json4 && json4.id != json2.id) {
        afterparams.roleDataList.push(json4);
      }

      editRoleData(params).then((res) => {
        if (res.code === 0) {
          this.$message({ type: "success", message: res.message });
          this.AuthlabShow = false;
        } else {
          this.$message({
            type: "error",
            message: res.message,
            duration: "5000",
          });
        }
      });
    },
    //处理角色实验室授权的菜单树数据
    addLabelRoleData(data) {
      return data.map((d) => {
        if (d.children) {
          this.addLabelRoleData(d.children);
        }
        d.label = `${d.kind}_${d.id}`;
        return d;
      });
    },
    // 获取选中的节点
    getCheckedNodes(arr, ckdArr) {
      if (!Array.isArray(arr) || !Array.isArray(ckdArr)) {
        return [];
      }
      let res = [];
      // 如果当前节点下的所有子节点都被选中了, 则应使用当前节点的id和kind
      // 如果当前节点下的不是所有子节点都被选中了, 则应使用选中节点的id和kind
      arr.forEach((ar) => {
        // 1. 不存在子节点，就看当前节点是否被选中了，选中则添加，未选中则不添加
        // 2. 存在子节点，看子节点是否被全选中，子节点被全选中了则添加当前节点; 子节点未被全选中, 则添加所有选中的子节点
        if (ckdArr.includes(ar.label)) {
          res = res.concat([
            {
              id: ar.id,
              kind: ar.kind,
            },
          ]);
        } else if (ar.children && ar.children.length > 0) {
          res = res.concat(this.getCheckedNodes(ar.children, ckdArr));
        }
      });
      return res;
    },
    getChecked(arr) {
      arr.forEach((ar) => {
        // 如果当前的选中了,则要提取当前的id
        if (ar.checked) {
          this.authedAreaIds.push(ar.label);
        } else if (ar.children) {
          // 则去查找子类的
          this.getChecked(ar.children);
        }
      });
    },
    // 排序改变时
    sortChange(obj) {
      if (obj.prop == "roleName") {
        obj.prop = "role_name";
      } else if (obj.prop == "ruleLevel") {
        obj.prop = "rule_level";
      } else if (obj.prop == "gmtCreate") {
        obj.prop = "gmt_create";
      } else if (obj.prop == "gmtModified") {
        obj.prop = "gmt_modified";
      }
      this.searchForm.orderKey = obj.prop;
      this.searchForm.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.initTable();
    },
    //转换表格中的操作端
    tableconsolekind(row, col) {
      const arr = getCheckArr(row.consoleKind, this.consoleKindOption);
      const includeArr = this.consoleKindOption.filter((item) => {
        return arr.includes(item.value);
      });
      let res = "";
      includeArr.forEach((item) => {
        res = res + " " + item.label;
      });
      return res;
    },
    // 表格展开行单行显示
    toogleExpand(row, expandedRows) {
      this.roleFn = [];
      let myTable = this.$refs.table;
      this.roleData.map((item) => {
        if (row.roleId != item.roleId) {
          myTable.toggleRowExpansion(item, false);
        }
      });
      getFnByroleId(row.roleId).then((res) => {
        if (res.code == 0) {
          this.roleFn = res.data;
          myTable.toggleRowExpansion(row);
        }
      });
    },
    // 点击查询按钮
    searchBtn() {
      this.initTable();
    },
    // 分页组件改变了值后触发的事件
    changePage(pageData) {
      this.searchForm.pageNum = pageData.pageNum;
      this.searchForm.pageSize = pageData.page;
      this.initTable();
    },

    // 点击新增按钮
    addRole(formData) {
      this.roleDialog = true;
      getAppMenuList().then((res) => {
        this.treeData = res.data;
      });
      // 清空数据
      this.roleForm.uuid = "";
      this.roleForm.roleName = "";
      this.roleForm.ruleLevel = "";
      this.roleForm.memo = "";
      this.roleForm.funIdList = [];
      this.roleForm.uuid = "";
      this.roleForm.roleId = "";
      // 设置默认选中项
      // this.$refs.tree.setCheckedKeys(this.roleForm.funIdList);
      if (this.$refs.roleForm != undefined) {
        this.$refs.roleForm.resetFields();
      }
    },
    clear(formName) {
      // 点击取消清除数据
      this.dialogFormVisible = false;
      this.$refs[formName].resetFields();
    },
    handleNodeClick(data) {
      // 点击模态框中的右侧树状图
      this.parentName = data.funName;
      this.addForm.funParenId = data.funId;
    },
    // 点击表格编辑按钮
    edit(rowData) {
      this.roleDialog = true;
      // 获取最新的树数据
      getAppMenuList().then((res) => {
        this.treeData = res.data;
        // 将用户的信息上传上去
        this.roleForm.uuid = rowData.uuid;
        if (this.$refs.roleForm != undefined) {
          this.$refs.roleForm.resetFields();
        }
        this.$nextTick(function () {
          this.roleForm.roleName = rowData.roleName;
          this.roleForm.ruleLevel = rowData.ruleLevel;
          this.roleForm.memo = rowData.memo;
          this.roleForm.funIdList = this.getTreeData(rowData.funIdList);
          this.roleForm.roleId = rowData.roleId;
          // 设置默认选中项
          this.$refs.tree.setCheckedKeys(this.roleForm.funIdList);
        });
      });
    },
    // 点击表格删除按钮
    remove(rowData) {
      this.$confirm("此操作将永久删除该角色, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: rowData.uuid,
          };
          delRole(json).then((res) => {
            if (res.code == 0) {
              this.initTable();
              this.getSearchRole();
              this.$message({
                type: "success",
                message: res.message,
              });
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击模态框中确认按钮
    addSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.roleForm.uuid == "") {
            // 如果为空值 则为新增
            // 新增
            // 先将选择的树id传到roleForm 中
            //this.roleForm.funIdList = this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys());
            // 这个时候调用新增接口
            addRole(this.roleForm).then((res) => {
              if (res.code == 0) {
                // 提示新增成功
                this.$message({
                  message: res.message,
                  type: "success",
                });
                // 初始化数据
                this.roleDialog = false;
                this.permissionsDialog = false;
                // 刷新数据
                this.initTable();
                this.getSearchRole();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            // 这是编辑 点击确认

            // 提交修改
            updateRole(this.roleForm).then((res) => {
              if (res.code == 0) {
                this.$message({
                  message: res.message,
                  type: "success",
                });
                // 初始化数据
                this.roleDialog = false;
                // 刷新数据
                this.initTable();
                this.getSearchRole();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          // 当验证没有通过
          this.$message({
            message: "请填写完整信息,再执行本次操作",
            type: "warning",
          });
          return false;
        }
      });
    },
    //点击权限分配框里的分配
    updateper(formName) {
      // 将修改后的树给到里面
      this.roleForm.funIdList = this.$refs.tree
        .getCheckedKeys()
        .concat(this.$refs.tree.getHalfCheckedKeys());
      // 提交修改
      updateRole(this.roleForm).then((res) => {
        if (res.code == 0) {
          this.$message({
            message: res.message,
            type: "success",
          });
          // 初始化数据
          this.permissionsDialog = false;
          // 刷新数据
          this.initTable();
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //  筛选点击编辑按钮后的 父级id
    checked(id, data, newArr) {
      // id 从后台返回的每一项id data 树结构数据 newArr 返回的数据
      data.forEach((item) => {
        if (item.funId == id) {
          if (item.children.length == 0) {
            newArr.push(item.funId);
          }
        } else {
          if (item.children.length != 0) {
            this.checked(id, item.children, newArr);
          }
        }
      });
    },
    getTreeData(arr) {
      // arr 从后台获取到的所有数据
      var newArr = [];
      arr.forEach((item) => {
        this.checked(item, this.treeData, newArr);
      });
      return newArr;
    },
    //点击权限分配按钮
    distribution(rowData) {
      this.roleForm.roleName = rowData.roleName;
      this.roleForm.ruleLevel = rowData.ruleLevel;
      this.roleForm.uuid = rowData.uuid;
      this.roleForm.roleId = rowData.roleId;
      this.permissionsDialog = true;
      getAppMenuList().then((res) => {
        this.treeData = res.data;
        this.$nextTick(function () {
          this.roleForm.funIdList = this.getTreeData(rowData.funIdList);
          // 设置默认选中项
          this.$refs.tree.setCheckedKeys(this.roleForm.funIdList);
        });
      });
    },
    //点击实验室授权按钮

    // 获取查询角色数据
    getSearchRole() {
      getAllrole().then((res) => {
        if (res.code == 0) {
          this.roleOption = res.data;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
  },
  created() {
    this.getSearchRole();
    this.initTable();
    this.rolelevel = sessionStorage.getItem("roleLevel");
  },
  computed: {
    // 模态框验证
    roleFormRules() {
      return {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        ruleLevel: [
          { required: true, message: "请选择角色等级", trigger: "change" },
        ],
      };
    },
    // 等级下拉框
    ruleOption() {
      if (this.rolelevel == 1) {
        return [
          { label: "一级", value: 1 },
          { label: "二级", value: 2 },
          { label: "三级", value: 3 },
        ];
      } else if (this.rolelevel == 2) {
        return [
          { label: "二级", value: 2 },
          { label: "三级", value: 3 },
        ];
      } else if (this.rolelevel == 3) {
        return [{ label: "三级", value: 3 }];
      } else {
        return [];
      }
    },
  },
  components: {
    Paging,
  },
};
</script>
<style>
label {
  font-weight: 500 !important;
}
.table_col .el-table__expand-column {
  overflow: hidden !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
}
</style>
<style lang="less" scoped>
.table {
  margin-top: 20px;
}
.scrollbox {
  overflow: auto;
  height: 500px;
  width: 100%;
}
.scrollbox::-webkit-scrollbar {
  width: 6px;
  height: 500px;
}
.scrollbox::-webkit-scrollbar-thumb {
  border-radius: 3px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.2);
}
.scrollbox::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.marginright {
  margin-right: 1%;
}
</style>
