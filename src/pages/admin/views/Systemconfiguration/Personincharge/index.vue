<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <el-row class="search">
        <el-col :span="24">
          <el-form :model="searchForm" width="100%" label-width="150px" class="itemBox">
            <el-row :gutter="30">
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
                <el-form-item label="单位名称" prop="deptName">
                  <el-input
                    v-model="searchForm.deptName"

                    placeholder="请选择"
                    style="width: 100%"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="8">
                <el-form-item label="负责人姓名或手机号" prop="search">
                  <el-input
                    v-model="searchForm.search"

                    placeholder="请选择"
                    style="width: 100%"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" style="text-align: center; margin-bottom: 20px">
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  class="smallbtn"
                  @click="searchBtn"

                  >搜索</el-button
                >
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="marginTop">
        <el-col :span="24"
          ><el-button
            type="success"
            icon="el-icon-plus"
            @click="addBtn"
            class="addbtn"
            v-allow="'principal:save'"
            >新增负责人</el-button
          ></el-col
        >
      </el-row>
      <!-- 新增和搜索结束 -->
      <!-- 数据表格开始 -->
      <el-row>
        <el-col>
          <el-table
            :data="tb_data"
            stripe
            border
            style="width: 100%"
            @sort-change="sortChange"
            class="marginTop"
            v-loading="loading"
            :default-sort="{ prop: 'gmtCreate', order: 'descending' }"
          >
            <!-- <el-table-column type="index" width="65px" label="序号" align="center"></el-table-column> -->
            <el-table-column
              prop="principalName"
              label="责任人姓名"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="principalPhone"
              label="责任人联系电话"
              type="number"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="deptName"
              label="单位名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="gmtCreate"
              label="创建时间"
              align="center"
              sortable="custom"
            >
              <template slot-scope="scope">
                {{ $moment(scope.row.gmtCreate).format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column prop="memo" label="备注" align="center"></el-table-column>
            <el-table-column label="操作" width="300px" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="editBtn(scope.row)"
                  v-allow="'principal:update'"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  style="color: red"
                  @click="delBtn(scope.row)"
                  v-allow="'principal:delete'"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <!-- 数据表格结束 -->
      <!-- 分页开始 -->
      <el-row>
        <div class="paging">
          <Paging
            :pageCount="pageCount"
            @pagebar="changePage"
            :currentpage="currentpage"
          ></Paging>
        </div>
      </el-row>
      <!-- 分页结束 -->

      <!-- 编辑、新增弹窗开始-->
      <el-dialog
        title="新增负责人"
        :visible.sync="diaShow"
        append-to-body
        :close-on-click-modal="false"
        width="500px"
      >
        <el-form :model="diaForm" ref="diaForm" label-width="130px" :rules="rules">
          <el-row>
            <el-col :span="24">
              <el-form-item label="责任人姓名" prop="principalName">
                <el-input
                  v-model="diaForm.principalName"

                  placeholder="请输入..."
                ></el-input>
              </el-form-item>
              <el-form-item label="责任人联系电话" prop="principalPhone">
                <el-input
                  v-model="diaForm.principalPhone"

                  placeholder="请输入..."
                ></el-input>
              </el-form-item>
              <el-form-item label="单位" prop="deptId">
                <Dept @getDeptName="getDeptName"></Dept>
              </el-form-item>
              <el-form-item label="备注" prop="memo">
                <el-input
                  v-model="diaForm.memo"

                  type="textarea"
                  placeholder="请输入..."
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diaShow = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn">确定</el-button>
        </span>
      </el-dialog>
      <!-- 编辑、新增弹窗结束-->
    </div>
  </div>
</template>
<script>
import {
  getprincipal,
  addprincipal,
  editprincipal,
  delprincipal,
} from "@admin/api/Settings/Personincharge.js";
import { getBuildMaxFloor } from "@admin/api/Settings/Laboratory/Laboratory.js";
import Dept from "@admin/components/Select/Dept.vue";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      // 表格数据
      tb_data: [],
      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      pageCount: 0,
      currentpage: 0,

      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        uuid: "",
        principalName: "",
        principalPhone: "",
        deptId: "",
        memo: "",
      },

      //校区下拉框
      campusArr: [],
      //楼宇下拉框
      buildingOptions: [],
      buildingfloor: [],

      floorshow: false,

      searchForm: {
        deptName: "",
        search: "",
      },

      orderKey: "",
      orderModel: "",

      buildShow: false,

      loading: false,
      rules: {
        principalName: [{ required: true, message: "该项不能为空", trigger: "blur" }],
        principalPhone: [{ required: true, message: "该项不能为空", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleChangebuild(id) {
      this.buildingOptions.map((item, index) => {
        if (item.value == id) {
          this.getmaxfloor(item.uuid);
        }
      });
    },
    getDeptName(id) {
      this.diaForm.deptId = id;
    },
    //获取楼层最大值
    getmaxfloor(uuid) {
      this.floorshow = true;
      this.buildingfloor = [];
      getBuildMaxFloor(uuid).then((res) => {
        if (res.code == 0) {
          for (let i = 1; i < res.data + 1; i++) {
            this.buildingfloor.push({
              label: i,
              value: i,
            });
          }
        }
      });
    },
    sortChange(obj) {
      this.orderKey = obj.order == "ascending" ? "asc" : "desc";
      this.orderModel = obj.prop + ",floorId";
      this.initTable();
    },
    searchBtn() {
      this.pageData.pageNum = 1;
      this.currentpage = 0;
      this.initTable();
    },
    // 表格初始化
    initTable() {
      const pageNum = this.pageData.pageNum;
      const page = this.pageData.page;
      this.loading = true;
      getprincipal(
        pageNum,
        page,
        this.searchForm.deptName,
        this.searchForm.search,
        this.orderKey,
        this.orderModel
      ).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 点击新增按钮
    addBtn() {
      // 初始化数据
      this.diaForm.uuid = "";
      this.diaForm.maxFloor = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.floorshow = false;
    },
    // 点击编辑按钮
    editBtn(row) {
      // 初始化数据
      this.diaForm.uuid = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.floorshow = true;

      // 重新赋值
      this.$nextTick(function () {
        this.diaForm.uuid = row.uuid;
        this.diaForm.principalName = row.principalName;
        this.diaForm.principalPhone = row.principalPhone;
        this.diaForm.deptId = row.deptId;
        this.diaForm.memo = row.memo;
      });
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该单位负责人, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delprincipal(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.deptId != "") {
            if (this.diaForm.uuid == "") {
              // 新增操作
              addprincipal(this.diaForm).then((res) => {
                if (res.code == 0) {
                  this.$message.success(res.message);
                  this.initTable();
                  this.diaShow = false;
                } else {
                  this.$message({
                    message: res.message,
                    type: "error",
                    duration: "5000",
                  });
                }
              });
            } else {
              // 编辑操作
              editprincipal(this.diaForm).then((res) => {
                if (res.code == 0) {
                  this.$message.success(res.message);
                  this.initTable();
                  this.diaShow = false;
                } else {
                  this.$message({
                    message: res.message,
                    type: "error",
                    duration: "5000",
                  });
                }
              });
            }
          } else {
            this.$message.warning("请选择单位");
            return false;
          }
        } else {
          this.$message.warning("请完善信息后再操作");
          return false;
        }
      });
    },
    BuildingquerySearch(queryString, cb) {
      let arr = [];
      getAllbuilding(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.buildingName,
              value: item.buildingName,
            });
          });
          cb(arr);
        }
      });
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
  },
  created() {
    this.initTable();
  },
  components: {
    Paging,
    Dept,
  },
};
</script>
<style>
.el-pagination {
  margin-top: 1% !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
</style>
