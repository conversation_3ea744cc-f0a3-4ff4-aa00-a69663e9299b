<template>
  <div class="boxmore">
    <div class="borderbox">
      <!-- 新增和搜索开始 -->
      <el-row class="search">
        <el-col>
          <el-form
            :model="searchForm"
            width="100%"
            label-width="70px"
            class="itemBox"
          >
            <el-row :gutter="20">
              <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="6">
                <el-form-item label="配置键" label-width="60px" prop="key">
                  <el-input
                    v-model="searchForm.key"

                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="18" :sm="8" :md="6" :lg="5" :xl="5">
                <el-button
                  type="primary"
                  icon="el-icon-search"

                  class="smallbtn"
                  @click="searchBtn"
                  >搜索</el-button
                >
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <!-- <el-row class="marginTop">
        <el-col :span="24"
          ><el-button
            type="success"
            icon="el-icon-plus"
            @click="addBtn"
            class="addbtn"
            >添加教师</el-button
          ></el-col
        >
      </el-row> -->
      <!-- 新增和搜索结束 -->
      <!-- 数据表格开始 -->
      <el-row>
        <el-table
          :data="tb_data"
          stripe
          border
          style="width: 100%"
          class="marginTop"
          v-loading="loading"
          :key="num"
        >
          <el-table-column
            type="index"
            width="65px"
            label="序号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="sysKey"
            label="配置键"
            align="center"
          ></el-table-column>
          <el-table-column prop="sysValue" label="配置值" align="center">
            <template slot-scope="scope">
              <div v-if="!scope.row.isSet">
                <div
                  v-if="
                    scope.row.sysKey == 'schoolLogo' &&
                    scope.row.sysKey == 'schoolLogoH5' &&
                    scope.row.sysKey == 'uploadImageUrl' &&
                    scope.row.sysKey == 'padLogo'
                  "
                >
                  <img
                    :src="ApiUrl + '/' + scope.row.sysValue"
                    style="width: 200px"
                  />
                </div>
                <div v-else>
                  {{ scope.row.sysValue }}
                </div>
              </div>
              <div v-else>
                <el-input
                  v-if="scope.row.sysKey != 'accountSyncTime'"

                  type="text"
                  v-model="nowTableDate.sysValue"
                ></el-input>
                <el-time-picker
                  v-else
                  v-model="nowTableDate.sysValue"
                  arrow-control
                  align="center"
                  value-format="HH:mm"
                  format="HH:mm"
                  placeholder="选择时间"
                ></el-time-picker>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="memo" label="备注" align="center">
            <template slot-scope="scope">
              {{ scope.row.memo }}
              <!-- <div v-if="!scope.row.isSet">
								展示区域
								{{ scope.row.memo }}
							</div>
							<div v-else>
								<el-input  type="text" v-model="nowTableDate.memo"></el-input>
							</div> -->
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300px" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                icon="el-icon-edit"
                @click="editBtn(scope.row, scope.$index)"
                v-if="
                  scope.row.sysKey != 'schoolLogo' &&
                  scope.row.sysKey != 'schoolLogoH5' &&
                  scope.row.sysKey != 'uploadImageUrl'
                "
                v-allow="'sysInfo:update'"
              >
                {{ scope.row.isSet ? "保存" : "修改" }}
              </el-button>
              <el-upload
                action
                ref="upload"
                class="upload-demo"
                :http-request="sumbitUpload"
                :show-file-list="false"
                :before-upload="beforeUpload"
                v-else
              >
                <el-button
                  type="primary"
                  slot="trigger"
                  class="addbtn"
                  @click="handleupload(scope.row)"
                  >上传</el-button
                >
              </el-upload>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <!-- 数据表格结束 -->
      <!-- 分页开始 -->
      <!-- <el-row>
        <el-col>
          <div class="paging">
            <Paging :pageCount="pageCount" @pagebar="changePage"></Paging>
          </div>
        </el-col>
      </el-row> -->
      <!-- 分页结束 -->
      <!-- 新增弹窗开始-->
      <el-dialog
        title="教师管理"
        :visible.sync="diaShow"
        append-to-body
        :close-on-click-modal="false"
        width="500px"
      >
        <el-form
          :model="diaForm"
          ref="diaForm"
          label-width="110px"
          :rules="rules"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="工号(姓名)" prop="teacherId">
                <!-- <el-select
									v-model="diaForm.teacherAccNo"
									placeholder="请输入工号或者关键词查询选择"
									clearable
									filterable
									remote
									:remote-method="remoteMethod"
									style="width: 100%"

								>
									<el-option v-for="item in accNos" :key="item.value" :label="item.label" :value="item.accNo"></el-option>
								</el-select> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diaShow = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn"
            >确定</el-button
          >
        </span>
      </el-dialog>
      <!-- 编辑、新增弹窗结束-->
    </div>
  </div>
</template>
<script>
import {
  getsysconfig,
  editsysconfig,
  uploadimg,
} from "@admin/api/Settings/DictTable.js";
import { getAllType } from "@admin/api/Settings/Asset/Type.js";
import { getAllLaboratory } from "@admin/api/Settings/Laboratory/Laboratory.js";
import { getAccNo } from "@admin/api/Management/administrator";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      ApiUrl:window.g.ApiUrl,
      // 表格数据
      tb_data: [],
      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      pageCount: 0,
      num: 0,

      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        teacherAccNo: "",
      },
      logonName: "",

      //校区下拉框
      campusArr: [],
      //教师下拉框
      teacherNames: [],

      searchForm: {
        key: "",
      },

      accNos: [],

      rules: {
        teacherAccNo: [
          { required: true, message: "请输入教师编号", trigger: "blur" },
        ],
      },

      loading: false,
      search: "",
      // 当前修改项id
      setId: "",
      // 当前修改的项
      nowTableDate: {
        uuid: "",
        sysKind: "",
        sysKey: "",
        sysValue: "",
        memo: "",
      },
      uploaddata: {},
    };
  },
  methods: {
    handleupload(data) {
      this.uploaddata = data;
    },
    sumbitUpload(item) {
      let fd = new FormData();
      fd.append("file", item.file);
      let path = "";
      uploadimg(fd).then((res) => {
        if (res.code == 0) {
          path = res.data;
          if (this.uploaddata) {
            let json = {
              memo: this.uploaddata.memo,
              sysKey: this.uploaddata.sysKey,
              sysValue: path,
              uuid: this.uploaddata.uuid,
            };
            editsysconfig(json).then((res) => {
              if (res.code == 0) {
                this.initTable();
                this.$message.success(res.message);
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        }
      });
    },
    //上传文件类型限制
    beforeUpload(file) {
      // const isLt40M = file.size / 1024 / 1024 < 40;
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "jpg";
      const extension2 = testmsg === "png";
      if (!extension && !extension2) {
        this.$message({
          message: "上传的只能是jpg或png格式图片",
          type: "error",
          duration: "5000",
        });
        return false;
      }
    },
    searchBtn() {
      this.pageData.pageNum = 1;
      this.initTable();
    },
    // 表格初始化
    initTable() {
      this.loading = true;
      getsysconfig(this.searchForm.key).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.search = "";
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 点击新增按钮
    addBtn() {
      // 初始化数据
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.teacherAccNo = "";
      this.diaShow = true;
    },
    // 点击编辑按钮
    editBtn(row, index) {
      console.log(this.setId);
      console.log(row);
      if (this.setId == "" || this.setId == row.uuid) {
        // this.setId = row.uuid;
        // 如果 这个id值是空的 或者这个值是当前正在修改的值 就可以进行下面的操作
        // true  点击了修改  false 点击保存
        let newItem = this.tb_data.find((td) => td.sysKey === row.sysKey);
        console.log(newItem);
        if (newItem) {
          // 点击了修改
          this.nowTableDate.uuid = row.uuid;
          this.nowTableDate.sysKind = row.sysKind;
          this.nowTableDate.sysKey = row.sysKey;
          this.nowTableDate.memo = row.memo;
          newItem.isSet = !newItem.isSet;
          this.num++;
          if (newItem.isSet) {
            // 编辑时设值
            this.nowTableDate.sysValue =
              row.sysKind === 3 ? parseInt(row.sysValue) : row.sysValue;
            // 记录下要编辑的行uuid todo
            this.setId = row.uuid;
            // this.$set(this.tb_data, 0, newItem);
            // this.$set(this.nowTableDate, "memo", row.memo);
          } else {
            this.setId = "";
            editsysconfig(this.nowTableDate).then((res) => {
              if (res.code == 0) {
                this.initTable();
                this.$message.success(res.message);
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          this.$message.error("未找到要设置的系统配置项, 请刷新页面重新操作");
        }
      } else {
        this.$message.error("请先保存其他的");
      }
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该教师, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delTeacher(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (!this.diaForm.uuid) {
            console.log(this.diaForm);
            // 新增操作
            addTeacher(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            // 编辑操作
            editTeacher(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          this.$message.warning("请完善信息后再操作");
          return false;
        }
      });
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.pageData.pageNum = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
    //SearchName(key,num)学工号、姓名下拉框查询
    SearchName(key, num) {
      getAllCount(key, num).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.accNos.push({
              label: item.accNoTrueName,
              value: item.trueName,
              accNo: item.accNo,
            });
          });
        }
      });
    },
  },
  created() {
    this.initTable();
    // this.GetAllAccNo();//学工号下拉框
  },
  components: {
    Paging,
  },
};
</script>
<style>
.el-pagination {
  margin-top: 1% !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
</style>
