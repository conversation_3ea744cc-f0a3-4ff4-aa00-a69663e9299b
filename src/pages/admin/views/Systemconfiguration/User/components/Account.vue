<template>
  <div class="boxcontent">
    <!-- 查询搜索 -->
    <div class="search" style="padding: 15px">
      <el-form
        :model="searchForm"
        width="100%"
        label-width="70px"
        class="itemBox"
      >
        <el-row :gutter="10">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item
              label="学工号(姓名)"
              label-width="110px"
              prop="ctrlName"
            >
              <el-autocomplete
                class="inline-input"
                v-model="searchForm.accNo"
                :fetch-suggestions="TeacherquerySearch"
                clearable

                placeholder="请输入教师工号或者姓名"
                style="width: 100%"
                @change="HandleTeacher2"
                @select="HandleTeacher"
              ></el-autocomplete>
              <!-- <el-select
								v-model="searchForm.accNo"
								placeholder="请输入工号或者姓名关键词查询选择"
								clearable
								filterable
								remote
								:remote-method="remoteMethod"
								style="width: 100%"

								allow-create
								v-if="accNos"
							>
								<el-option v-for="(item, index) in accNos" :key="index+'A'" :label="item.label" :value="item.value"></el-option>
							</el-select> -->
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="18" :sm="8" :md="6" :lg="6" :xl="6">
            <el-form-item label="卡号" prop="cardNo">
              <el-input v-model="searchForm.cardNo"  clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>-->
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="账号类型" prop="kind">
              <el-select
                v-model="searchForm.kind"

                clearable
                placeholder="请选择"
                style="width: 100%"
                v-if="kindArr"
              >
                <el-option
                  v-for="(item, index) in kindArr"
                  :key="index + 'B'"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="身份" prop="ident">
              <el-select
                v-model="searchForm.ident"

                placeholder="请选择"
                clearable
                style="width: 100%"
                v-if="identArr"
              >
                <el-option
                  v-for="(item, index) in identArr"
                  :key="index + 'C'"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="本地状态" prop="localstatus">
              <el-select
                v-model="searchForm.localstatus"

                placeholder="请选择"
                clearable
                style="width: 98%"
                v-if="statusArr"
              >
                <el-option
                  v-for="(item, index) in statusArr"
                  :key="index + 'D'"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="班级名称" prop="className" label-width="110px">
              <el-autocomplete
                class="inline-input"
                v-model="searchForm.classId"
                :fetch-suggestions="ClassquerySearch"
                clearable

                :placeholder="'请输入' + Classofstudy + '名称'"
                style="width: 100%"
              ></el-autocomplete>
              <!-- <el-select
                v-model="searchForm.classId"

                placeholder="请选择"
                style="width: 98%"
                clearable
                filterable
                allow-create
                v-if="className"
              >
                <el-option
                  v-for="(item, index) in className"
                  :key="index + 'E'"
                  :label="item.label"
                  :value="item.label"
                ></el-option>
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="学院名称" prop="deptName">
              <el-autocomplete
                class="inline-input"
                v-model="searchForm.deptId"
                :fetch-suggestions="DeptquerySearch"
                clearable

                placeholder="请输入学院名称"
                style="width: 100%"
              ></el-autocomplete>

              <!-- <el-select
                v-model="searchForm.deptId"

                placeholder="请选择"
                style="width: 98%"
                filterable
                clearable
                allow-create
                v-if="deptNames"
              >
                <el-option
                  v-for="(item, index) in deptNames"
                  :key="index + 'F'"
                  :label="item.label"
                  :value="item.label"
                ></el-option>
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="手机" prop="handPhone"
              ><el-input
                v-model="searchForm.handPhone"

                clearable
                type="number"
                placeholder="请输入"
              ></el-input
            ></el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
            <el-form-item label="邮箱" prop="email"
              ><el-input
                v-model="searchForm.email"

                clearable
                placeholder="请输入"
              ></el-input
            ></el-form-item>
          </el-col>
        </el-row>
        <el-row></el-row>
      </el-form>
      <div class="search-btn">
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="searchBtn"
          class="smallbtn"

          >搜索</el-button
        >
      </div>
    </div>
    <div>
      <el-button
        type="success"
        icon="el-icon-plus"
        @click="addBtn"
        class="addbtn"
        style="margin-top: 1%"
        v-allow="'account:save'"
        >新增账户</el-button
      >
      <el-button
        icon="el-icon-user"
        type="success"
        class="addbtn"
        @click="synchronousBtn"
        v-allow="'account:sysAccount'"
        >同步账户</el-button
      >
      <el-dropdown
        trigger="click"
        class="import"
        szie="small"
        @command="handleCommand"
      >
        <span class="el-dropdown-link">
          <i class="el-icon-download icon"></i>
          补助模板下载
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="balanceMubanExcel"
            ><a href="./template_Subsidies_amount.xlsx" style="color: #606266"
              >补助金额模板下载</a
            ></el-dropdown-item
          >
          <el-dropdown-item command="freeTimeMubanExcel"
            ><a href="./template_Subsidies_machine.xlsx" style="color: #606266"
              >补助机时模板下载</a
            ></el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown
        trigger="click"
        class="import"
        szie="small"
        @command="handleCommand"
      >
        <span class="el-dropdown-link">
          <i class="el-icon-upload2 icon"></i>
          导入补助
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="balanceExcel">
            <el-upload
              action
              ref="banalceupload"
              class="upload-demo"
              :http-request="banalceUpload"
              :show-file-list="false"
              :before-upload="beforeUpload"
              accept=".xls,.xlsx"
            >
              <el-button
                type="text"
                slot="trigger"

                style="color: #40abd2; font-size: 15px"
                >重置补助金额Excel导入</el-button
              >
            </el-upload>
          </el-dropdown-item>
          <el-dropdown-item command="balanceExcel_add">
            <el-upload
              action
              ref="banalceupload"
              class="upload-demo"
              :http-request="banalceUpload_add"
              :show-file-list="false"
              :before-upload="beforeUpload"
              accept=".xls,.xlsx"
            >
              <el-button
                type="text"
                slot="trigger"

                style="color: #40abd2; font-size: 15px"
                >追加补助金额Excel导入</el-button
              >
            </el-upload>
          </el-dropdown-item>
          <el-dropdown-item
            command="freeTimeExcel"
            v-allow="'account:importFreeTime'"
          >
            <el-upload
              action
              ref="freeTimeupload"
              class="upload-demo"
              :http-request="freeTimeUpload"
              :show-file-list="false"
              :before-upload="beforeUpload"
              accept=".xls,.xlsx"
            >
              <el-button
                type="text"
                slot="trigger"

                style="color: #40abd2; font-size: 15px"
                >重置补助机时Excel导入</el-button
              >
            </el-upload>
          </el-dropdown-item>
          <el-dropdown-item command="freeTimeExcel_add">
            <el-upload
              action
              ref="freeTimeupload"
              class="upload-demo"
              :http-request="freeTimeUpload_add"
              :show-file-list="false"
              :before-upload="beforeUpload"
              accept=".xls,.xlsx"
            >
              <el-button
                type="text"
                slot="trigger"

                style="color: #40abd2; font-size: 15px"
                >追加补助机时Excel导入</el-button
              >
            </el-upload>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-button
        type="success"
        icon="el-icon-edit"
        class="addbtn"
        style="margin-top: 1%; margin-left: 20px"
        @click="Batchedit"
        v-allow="'account:updateSubsidyAboutByWildcard'"
        >批量修改补助和机时</el-button
      >
    </div>
    <!-- 搜索 -->

    <!-- 新增 / 同步账户-->
    <!-- <div class="bar mb-10">
      <el-button
        icon="el-icon-plus"
        type="success"

        v-allow="'account:account:get'"
        @click="addBtn"
      >{{$t('com_btn_add')}}</el-button>
      <el-button
        icon="el-icon-user"
        type="success"

        v-allow="'account:account:get'"
        @click="synchronousBtn"
      >{{$t('com_btn_synchronous')}}</el-button>
    </div> -->
    <!-- 表格开始 -->
    <el-table
      :data="tb_data"
      stripe
      border
      style="width: 100%"
      @sort-change="sortChange"
      class="marginTop"
      v-loading="loading"
    >
      <!-- <el-table-column
        type="index"
        width="65px"
        label="序号"
        align="center"
      ></el-table-column> -->
      <el-table-column
        prop="logonName"
        label="登录名"
        align="center"
        sortable="custom"
      ></el-table-column>
      <el-table-column
        prop="trueName"
        label="姓名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="kind"
        label="类型"
        :formatter="kindFormat"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="className"
        label="班级名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="deptName"
        label="学院名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="ident"
        label="身份"
        :formatter="identFormat"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="sex"
        label="性别"
        :formatter="sexFormat"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="handPhone"
        label="手机"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="email"
        label="邮箱"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="subsidy"
        label="补助(元)"
        align="center"
        sortable="custom"
      >
        <template slot-scope="scope">
          <p v-if="scope.row.subsidy">{{ scope.row.subsidy / 100 }}</p>
          <p v-else>0</p>
        </template>
      </el-table-column>
      <el-table-column
        prop="freeTime"
        label="剩余机时"
        align="center"
        sortable="custom"
      ></el-table-column>
      <el-table-column
        prop="localstatus"
        label="本地状态"
        width="150px"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            style="display: block"

            v-model="scope.row.localstatus"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="正常"
            inactive-text="禁用"
            :active-value="1"
            :inactive-value="2"
            @change="changeStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="一卡通状态"
        align="center"
        :formatter="StatusFormat"
        width="120px"
      ></el-table-column>
      <el-table-column label="操作" min-width="180px" align="center">
        <template slot-scope="scope">
          <!-- <el-button
            type="text"
            icon="el-icon-edit"
            @click="editBtn(scope.row)"
            v-if="scope.row.kind == 2"
            v-allow="'account:update'"
            >编辑</el-button
          > -->
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="editBtn(scope.row)"
            v-allow="'account:update'"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-lock"
            style="color: #e6a23c"
            @click="editPass(scope.row)"
            v-if="scope.row.kind == 2 || showeditpassFlag"
            v-allow="'account:reset'"
            >修改密码</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="delBtn(scope.row)"
            v-if="scope.row.kind == 2"
            v-allow="'account:delete'"
            >删除</el-button
          >
          <el-button
            type="text"
            icon="el-icon-coin"
            @click="subsidiesBtn(scope.row)"
            v-allow="'account:updateSubsidyAbout'"
            >补助</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 表格结束-->

    <!-- 分页开始-->
    <div class="paging">
      <Paging
        :pageCount="pageCount"
        @pagebar="changePage"
        :currentpage="currentpage"
      ></Paging>
    </div>
    <!-- 分页结束-->

    <!-- 编辑、新增弹窗开始 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="diaShow"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        :model="diaForm"
        ref="diaForm"
        label-width="110px"
        :rules="rules"
        v-loading="loading"
      >
        <el-row :gutter="50">
          <el-col :span="12">
            <el-form-item label="登录名" prop="logonName">
              <el-input
                v-model="diaForm.logonName"

                placeholder="请输入..."
                v-if="!logonShow"
              ></el-input>
              <el-input
                v-model="diaForm.logonName"

                placeholder="请输入..."
                disabled
                v-if="logonShow"
              ></el-input>
            </el-form-item>
            <el-form-item label="卡号" prop="cardNo"
              ><el-input
                v-model="diaForm.cardNo"

                placeholder="请输入..."
                :disabled="carshowflag"
              ></el-input
            ></el-form-item>
            <el-form-item label="班级" prop="classId">
              <el-select
                v-model="diaForm.classId"

                placeholder="请选择"
                style="width: 100%"
                filterable
                v-if="className"
                :disabled="carshowflag"
              >
                <el-option
                  v-for="(item, index) in className"
                  :key="index + 'I'"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="免费机时" prop="freeTime"
              ><el-input
                v-model="diaForm.freeTime"

                type="number"
                min="0"
                placeholder="请输入..."
                :disabled="carshowflag"
              ></el-input
            ></el-form-item>
            <el-form-item label="性别" prop="sex">
              <el-select
                v-model="diaForm.sex"

                placeholder="请选择"
                style="width: 100%"
                v-if="sexArr"
                :disabled="carshowflag"
              >
                <el-option
                  v-for="(item, index) in sexArr"
                  :key="index + 'G'"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="邮箱" prop="email"
              ><el-input
                v-model="diaForm.email"

                placeholder="请输入..."
              ></el-input
            ></el-form-item>
            <el-form-item label="备注" prop="memo"
              ><el-input
                v-model="diaForm.memo"

                placeholder="请输入"
                type="textarea"
                :disabled="carshowflag"
              ></el-input
            ></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="trueName"
              ><el-input
                v-model="diaForm.trueName"

                placeholder="请输入..."
                :disabled="carshowflag"
              ></el-input
            ></el-form-item>
            <el-form-item label="卡序列号" prop="credIdFormat">
              <el-input
                v-model="diaForm.credIdFormat"

                placeholder="请输入..."
                type="number"
                :disabled="carshowflag"
              ></el-input>
            </el-form-item>
            <el-form-item label="身份" prop="ident">
              <el-select
                v-model="diaForm.ident"

                placeholder="请选择"
                style="width: 100%"
                v-if="identArr"
                :disabled="carshowflag"
              >
                <el-option
                  v-for="(item, index) in identArr"
                  :key="index + 'H'"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="补助余额(元)" prop="subsidy"
              ><el-input
                v-model="diaForm.subsidy"

                type="number"
                min="0"
                placeholder="请输入..."
                :disabled="carshowflag"
              ></el-input
            ></el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input
                v-model="diaForm.idCard"

                placeholder="请输入..."
                style="width: 100%"
                :disabled="carshowflag"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机" prop="handPhone"
              ><el-input
                v-model="diaForm.handPhone"

                placeholder="请输入..."
              ></el-input
            ></el-form-item>
            <el-form-item label="电话" prop="tel"
              ><el-input
                v-model="diaForm.tel"

                placeholder="请输入..."
              ></el-input
            ></el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diaShow = false" >取消</el-button>
        <el-button type="primary"  @click="confirmBtn"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 编辑、新增弹窗结束 -->

    <!-- 修改密码弹窗开始 -->
    <el-dialog
      title="修改密码"
      :visible.sync="diaPass"
      width="500px"
      append-to-body
    >
      <el-form
        :model="passForm"
        ref="passForm"
        label-width="80px"
        :rules="passRules"
      >
        <el-form-item label="新密码" prop="newOne"
          ><el-input
            type="password"
            v-model="passForm.newOne"

            show-password
          ></el-input
        ></el-form-item>
        <el-form-item label="确认密码" prop="newTwo"
          ><el-input
            type="password"
            v-model="passForm.newTwo"

            show-password
          ></el-input
        ></el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diaPass = false" >取消</el-button>
        <el-button type="primary"  @click="confirmPass"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 修改密码弹窗结束 -->

    <!-- 补助弹窗开始 -->
    <el-dialog
      title="补助管理"
      :visible.sync="dia_subsidies"
      width="500px"
      append-to-body
    >
      <el-form
        :model="subsidiesForm"
        ref="passForm"
        label-width="80px"
        :rules="passRules"
      >
        <el-form-item label="补助(元)" prop="subsidy"
          ><el-input
            type="text"
            v-model="subsidiesForm.subsidy"

          ></el-input
        ></el-form-item>
        <el-form-item label="剩余机时" prop="freeTime"
          ><el-input
            type="text"
            v-model="subsidiesForm.freeTime"

          ></el-input
        ></el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dia_subsidies = false" >取消</el-button>
        <el-button type="primary"  @click="confirmPass_subsidies"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 补助弹窗结束 -->

    <!-- 批量补助弹窗开始 -->
    <el-dialog
      title="批量修改补助和机时"
      :visible.sync="dia_Batchdit"
      width="500px"
      append-to-body
    >
      <el-form
        :model="BtachForm"
        ref="BtachForm"
        label-width="120px"
        :rules="BtachRules"
      >
        <el-form-item label="通配符表达式" prop="wildcard"
          ><el-input
            type="text"
            v-model="BtachForm.wildcard"

          ></el-input
        ></el-form-item>
        <el-form-item label="修改类型" prop="type">
          <el-select
            v-model="BtachForm.type"

            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="(item, index) in Typeedit"
              :label="item.label"
              :key="index"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="补助(元)"
          ><el-input
            type="text"
            v-model="BtachForm.subsidy"

          ></el-input
        ></el-form-item>
        <el-form-item label="剩余机时"
          ><el-input
            type="text"
            v-model="BtachForm.freeTime"

          ></el-input
        ></el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dia_Batchdit = false" >取消</el-button>
        <el-button type="primary"  @click="confirm_BtachForm"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 批量补助弹窗结束 -->
  </div>
</template>
<script>
import { getAllCount } from "@admin/api/Settings/Teacher.js";
import {
  getAccount,
  updateStatus,
  delAccount,
  addAccount,
  editAccount,
  sysAccount,
  editPassword,
  editSubsidyAbout,
  updateSubsidyAboutByWildcard,
  countByWildcard,
} from "@admin/api/Settings/User/Account.js";
import { getAllClass } from "@admin/api/Settings/User/Class.js";
import { getAllDept } from "@admin/api/Settings/User/Department.js";
import { getAccNo } from "@admin/api/Management/administrator";
import {
  importSubsidy,
  importFreeTime,
} from "@admin/api/Settings/User/Account";
import { getsysconfig } from "@admin/api/Settings/DictTable.js";
import { getPublicKey } from "@admin/api/Settings/Laboratory/videorecorder.js";
import { JSEncrypt } from "jsencrypt";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      carshowflag: false,
      dialogTitle: "新增账户",
      Classofstudy: window.g.Classofstudy,
      dia_Batchdit: false,
      batchformInfo: "",
      Typeedit: [
        {
          label: "重置",
          value: 1,
        },
        {
          label: "追加",
          value: 2,
        },
      ],
      BtachForm: {
        wildcard: "",
        type: "",
        subsidy: "",
        freeTime: "",
      },
      showeditpassFlag: false,
      subsidiesForm: {
        subsidy: "",
        freeTime: "",
        uuid: "",
      }, //补助弹窗表单
      dia_subsidies: false, //补助弹窗开始
      loading: false,
      orderKey: "",
      orderModel: "",
      // 表格数据
      tb_data: [],
      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      pageCount: 0,
      currentpage: 0,

      logonShow: false, //控制登录名是否禁止操作

      //同步账号提示弹窗
      accountdialogVisible: false,

      loading: false, //loading加载

      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        uuid: "",
        logonName: "",
        cardNo: "",
        credIdFormat: "",
        idCard: "",
        trueName: "",
        ident: "",
        status: 1,
        classId: "",
        sex: "",
        handPhone: "",
        email: "",
        memo: "",
        subsidy: "",
        freeTime: "",
        tel: "",
      },
      // // 班级下拉框数据存放处
      className: [],
      //部门下拉框数据存放处
      deptNames: [],
      // 模态框身份下拉框
      identArr: [
        { label: "学生", value: 256 },
        { label: "教师", value: 512 },
      ],
      // 模态框性别下拉框
      sexArr: [
        { label: "男", value: 1 },
        { label: "女", value: 2 },
        { label: "保密", value: 0 },
      ],
      // 搜索框类型下拉框
      kindArr: [
        { label: "一卡通账户", value: 1 },
        { label: "本地账户", value: 2 },
      ],
      // 搜索框状态下拉框
      statusArr: [
        { label: "正常", value: 1 },
        { label: "禁用", value: 2 },
      ],
      // 修改密码模态框显示隐藏
      diaPass: false,
      // 修改密码表单
      passForm: {
        uuid: "",
        newOne: "",
        newTwo: "",
      },
      key: "",
      // 搜索查询表单
      searchForm: {
        accNo: "",
        logonName: "",
        cardNo: "",
        trueName: "",
        kind: "",
        ident: "",
        classId: "",
        deptId: "",
        handPhone: "",
        email: "",
        localstatus: "",
        deptName: "",
      },
      accNos: [],
      search: "", //学工号（姓名）
      //批量修改机时补助的规则
      BtachRules: {
        wildcard: [
          { required: true, message: "请输入通配符表达式", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择类型", trigger: "change" }],
      },
      // 表单验证规则
      rules: {
        logonName: [
          { required: true, message: "请输入登录名", trigger: "blur" },
          { validator: this.validateName, trigger: "blur" },
        ],
        cardId: [
          { required: true, message: "请输入卡序列号", trigger: "blur" },
        ],
        andPhone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
        ],
        sex: [{ required: true, message: "请输入性别", trigger: "change" }],
        trueName: [
          { required: true, message: "请输入真实姓名", trigger: "blur" },
        ],
        ident: [{ required: true, message: "请选择身份", trigger: "change" }],
        classId: [{ required: true, message: "请选择班级", trigger: "change" }],
        cardNo: [
          { required: true, message: "请填写卡号", trigger: "blur" },
          {
            message: "卡号为16进制",
            trigger: "blur",
            transform(value) {
              if (value) {
                if (!/^(0[xX])?[0-9a-fA-F]+$/.test(value)) {
                  return true;
                }
              }
            },
          },
        ],
        email: [
          { required: false, message: "请输入邮箱", trigger: "blur" },
          {
            message: "邮箱格式不正确",
            trigger: "blur",
            transform(value) {
              if (value) {
                if (
                  !/^\w+((-\w+)|(\.\w+))*@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(
                    value
                  )
                ) {
                  return true;
                }
              }
            },
          },
        ],
        //handPhone: [{ required: true, message: '请输入手机', trigger: 'blur' }, { validator: checkMobile, message: '手机格式不正确，请重新输入', trigger: 'blur' }]
      },
      // 密码框表单验证
      passRules: {
        newOne: [
          { validator: this.validatePass, trigger: "blur" },
          { required: true, message: "请输入新密码", trigger: "blur" },
        ],
        newTwo: [
          { validator: this.validatePass2, trigger: "blur" },
          { required: true, message: "请输入确认密码", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //批量化修改补助和机时
    confirm_BtachForm() {
      this.$refs.BtachForm.validate((valid) => {
        if (valid) {
          this.countByWildcard();
        }
      });
    },
    countByWildcard() {
      let json = {
        wildcard: this.BtachForm.wildcard,
      };
      countByWildcard(json).then((res) => {
        if (res.code == 0) {
          if (res.data == 0) {
            this.batchformInfo = "暂无查询到匹配通配符的账户，请重新输入通配符";
            this.$confirm(
              "暂无查询到匹配通配符的账户，请重新输入通配符",
              "提示",
              {
                confirmButtonText: "确定",
                type: "warning",
              }
            )
              .then(() => {})
              .catch(() => {});
          } else {
            // this.batchformInfo = "根据通配符表达式查询到的账户数量为：" + res.data + "";
            this.$confirm(
              "根据通配符表达式查询到的账户数量为：" + res.data + "",
              "提示",
              {
                confirmButtonText: "确定",
                type: "warning",
              }
            )
              .then(() => {
                this.BtachForm.subsidy = this.BtachForm.subsidy * 100;
                updateSubsidyAboutByWildcard(this.BtachForm).then((res) => {
                  if (res.code == 0) {
                    this.dia_Batchdit = false;
                    this.initTable();
                  } else {
                    this.$message.error(res.message);
                  }
                });
              })
              .catch(() => {
                this.$message.error(res.message);
              });
          }
        }
      });
    },
    Batchedit() {
      this.dia_Batchdit = true;
      this.BtachForm.wildcard = "";
      this.BtachForm.type = "";
      this.BtachForm.subsidy = "";
      this.BtachForm.freeTime = "";
    },
    //补助余额表格导入
    banalceUpload(item) {
      let fd = new FormData();
      fd.append("file", item.file);
      fd.append("type", 1);
      this.importSubsidy(fd);
    },
    banalceUpload_add(item) {
      let fd = new FormData();
      fd.append("file", item.file);
      fd.append("type", 2);
      this.importSubsidy(fd);
    },
    importSubsidy(fd) {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.successData = [];
      this.failData = [];
      this.$refs.banalceupload.clearFiles();
      importSubsidy(fd)
        .then((res) => {
          loading.close();
          if (res.code == 0) {
            this.$message.success(res.message);
            this.initTable(); //初始化数据
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    //免费机时表格导入
    freeTimeUpload(item) {
      let fd = new FormData();
      fd.append("file", item.file);
      fd.append("type", 1);
      this.importFreeTime(fd);
    },
    freeTimeUpload_add(item) {
      let fd = new FormData();
      fd.append("file", item.file);
      fd.append("type", 2);
      this.importFreeTime(fd);
    },
    importFreeTime(fd) {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.successData = [];
      this.failData = [];
      this.$refs.freeTimeupload.clearFiles();
      importFreeTime(fd)
        .then((res) => {
          loading.close();
          if (res.code == 0) {
            this.$message.success(res.message);
            this.initTable(); //初始化数据
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    //点击同步账户的确定按钮后
    handleaccountcommit() {
      setaccount().then((res) => {
        if (res.code == 0) {
          this.accountdialogVisible = false;
          this.$message.success("恭喜您，同步账号已开始！");
        } else {
          this.accountdialogVisible = false;
          this.$message({
            message: "不好意思，操作失败！",
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //上传文件类型限制
    beforeUpload(file) {
      // const isLt40M = file.size / 1024 / 1024 < 40;
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "xls";
      const extension2 = testmsg === "xlsx";
      if (!extension && !extension2) {
        this.$message({
          message: "导入表格类型只能是xls或者xlsx!",
          type: "error",
          duration: "5000",
        });
        return false;
      }
    },
    //表格导入和下载的下拉框
    handleCommand(command) {
      // if (command == "import") {
      // }
    },
    // 表格排序
    sortChange(obj) {
      this.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.orderKey = obj.prop;
      this.initTable();
    },
    //学工号（姓名）
    TeacherquerySearch(queryString, cb) {
      let arr = [];
      getAccNo(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.accNoTrueName,
              value: item.accNoTrueName,
            });
          });
          cb(arr);
        }
      });
    },
    //将选择的学工号/账号给分割处理
    HandleTeacher(data) {
      let text = data.label;
      this.search = text.split("(")[0];
    },
    HandleTeacher2(data) {
      if (this.search == "") {
        this.search = data;
      }
    },
    //班级下拉查询
    ClassquerySearch(queryString, cb) {
      let arr = [];
      getAllClass(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.className,
              value: item.className,
            });
          });
          cb(arr);
        }
      });
    },
    //学院下拉查询
    DeptquerySearch(queryString, cb) {
      let arr = [];
      getAllDept(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.deptName,
              value: item.deptName,
            });
          });
          cb(arr);
        }
      });
    },
    // 查询班级
    searchClass() {
      getAllClass().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.className.push({
              label: item.className,
              value: item.classId,
            });
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //点击学工号，姓名下拉框
    remoteMethod(val) {
      this.accNos = [];
      this.SearchName(val, 20); //学工号、姓名下拉框查询
    },
    //SearchName(key,num)学工号、姓名下拉框查询
    SearchName(key, num) {
      getAllCount(key, num).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.accNos.push({
              label: item.accNoTrueName,
              value: item.trueName,
            });
          });
        }
      });
    },
    //查询部门
    selectdeptName() {
      getAllDept().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.deptNames.push({
              label: item.deptName,
              value: item.deptId,
            });
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 选择了某一项
    searchDept(item) {
      this.searchForm.deptId = item.deptId;
    },
    // 选择了搜索模块中的班级
    searchSelect(item) {
      this.searchForm.classId = item.classId;
    },
    // 切换表格状态
    changeStatus(row) {
      updateStatus({ uuid: row.uuid, localstatus: row.localstatus }).then(
        (res) => {
          if (res.code == 0) {
            this.$message.success(res.message);
            this.initTable();
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
            this.initTable();
          }
        }
      );
    },
    // 点击修改密码按钮
    editPass(row) {
      // 数据初始化
      this.passForm.uuid = "";
      if (this.$refs.passForm != undefined) {
        this.$refs.passForm.resetFields();
      }
      this.diaPass = true;
      // 赋值
      this.passForm.uuid = row.uuid;
    },
    subsidiesBtn(row) {
      if (row.subsidy) {
        this.subsidiesForm.subsidy = row.subsidy / 100;
      } else {
        this.subsidiesForm.subsidy = 0;
      }
      this.subsidiesForm.freeTime = row.freeTime;
      this.subsidiesForm.uuid = row.uuid;
      this.dia_subsidies = true;
    },
    //点击补助弹窗的确定按钮
    confirmPass_subsidies() {
      this.subsidiesForm.subsidy = this.subsidiesForm.subsidy * 100;
      editSubsidyAbout(this.subsidiesForm).then((res) => {
        if (res.code == 0) {
          this.$message.success(res.message);
          this.dia_subsidies = false;
          this.initTable();
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 点击修改密码确定按钮
    confirmPass() {
      this.$refs.passForm.validate((valid) => {
        if (valid) {
          let encryptor = new JSEncrypt(); // 实例化一个 jsEncrypt 对象
          encryptor.setPublicKey(this.key); //配置公钥
          let newOne = encryptor.encrypt(this.passForm.newOne);
          let newTwo = encryptor.encrypt(this.passForm.newTwo);
          let json = {
            newOne: newOne,
            newTwo: newTwo,
            uuid: this.passForm.uuid,
          };
          editPassword(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.diaPass = false;
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        } else {
          this.$message.warning("请完善信息再操作!");
          return false;
        }
      });
    },
    getkey() {
      getPublicKey().then((res) => {
        if (res.code == 0) {
          this.key = res.data;
        }
      });
    },
    // 模态框中的密码验证
    validatePass(rule, value, callback) {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.passForm.newTwo !== "") {
          this.$refs.passForm.validateField("newTwo");
        }
        callback();
      }
    },
    validatePass2(rule, value, callback) {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.passForm.newOne) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    },
    // 自定义表单验证 不允许有汉字
    validateName(rule, value, callback) {
      if (value) {
        if (/[\u4E00-\u9FA5]/g.test(value)) {
          callback(new Error("登录名不能有汉字!"));
        } else {
          callback();
        }
      }
    },
    handleSelect(item) {
      this.diaForm.classId = item.classId;
    },
    searchBtn() {
      this.pageData.pageNum = 1;
      this.currentpage = 0;
      this.initTable();
    },
    // 表格初始数据
    initTable() {
      this.loading = true;
      const pageNum = this.pageData.pageNum;
      const pageSize = this.pageData.page;
      if (!this.searchForm.accNo) {
        this.search = "";
      }
      getAccount(
        pageNum,
        pageSize,
        this.searchForm.logonName,
        this.searchForm.cardNo,
        this.searchForm.kind,
        this.searchForm.ident,
        this.searchForm.classId,
        this.searchForm.deptId,
        this.searchForm.handPhone,
        this.searchForm.email,
        this.searchForm.localstatus,
        this.search,
        this.orderKey,
        this.orderModel
      ).then((res) => {
        this.loading = false;
        this.search = "";
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 点击新增按钮
    addBtn() {
      this.logonShow = false;
      // 表单数据格式化
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.uuid = "";
      this.diaForm.accNo = "";
      this.diaForm.tel = "";
      // this.className = "";

      this.diaShow = true;
      this.dialogTitle = "新增账户";
    },
    //点击同步账号
    synchronousBtn() {
      this.$confirm(
        "该命令从一卡通大账户表里获取最新的用户信息，可能需要较长的时间，是否继续？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          sysAccount().then((res) => {
            if (res.code == 0) {
              this.$message({
                type: "success",
                message: "同步账户已开始",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消同步账户",
          });
        });
    },
    // 点击编辑按钮
    editBtn(row) {
      if (row.kind == 1) {
        this.carshowflag = true;
      } else {
        this.carshowflag = false;
      }
      // 表单数据格式化
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.uuid = "";
      // this.className = "";

      this.diaShow = true;
      this.logonShow = true;
      this.dialogTitle = "编辑账户";
      // 重新赋值
      this.$nextTick(function () {
        this.diaForm.uuid = row.uuid;
        this.diaForm.logonName = row.logonName;
        // this.diaForm.kind = row.kind;
        this.diaForm.cardNo = row.cardNo;
        this.diaForm.credIdFormat = row.credIdFormat;
        this.diaForm.idCard = row.idCard;
        this.diaForm.trueName = row.trueName;
        this.diaForm.ident = row.ident;
        this.diaForm.sex = row.sex;
        this.diaForm.handPhone = row.handPhone;
        this.diaForm.email = row.email;
        this.diaForm.memo = row.memo;
        this.diaForm.classId = row.classId;
        this.diaForm.accNo = row.accNo;
        this.diaForm.subsidy = row.subsidy / 100;
        this.diaForm.freeTime = row.freeTime;
        this.diaForm.tel = row.tel;
        // this.className = row.className;
      });
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该账户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const uuid = row.uuid;
          let json = {
            uuid: row.uuid,
          };
          delAccount(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.uuid == "") {
            // 新增操作
            this.diaForm.subsidy = this.diaForm.subsidy * 100;
            addAccount(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.diaShow = false;
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            // 编辑操作
            this.diaForm.subsidy = this.diaForm.subsidy * 100;
            editAccount(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.diaShow = false;
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          this.$message.warning("请完善信息后操作");
          return false;
        }
      });
    },
    getsysconfig() {
      getsysconfig().then((res) => {
        if (res.code == 0) {
          res.data.map((item, index) => {
            if (item.sysKey == "updateSyncPassword") {
              if (item.sysValue == "1") {
                this.showeditpassFlag = true;
              } else {
                this.showeditpassFlag = false;
              }
            }
          });
        }
      });
    },
    // 表格 kind 数据格式
    kindFormat(row, col) {
      if (row.kind == 1) {
        return "一卡通账户";
      } else if (row.kind == 2) {
        return "本地账户";
      } else {
        return "未知";
      }
    },
    // 表格 身份数据格式化
    identFormat(row, col) {
      var str = "";
      switch (row.ident) {
        case 256:
          str = "学生";
          break;
        case 512:
          str = "教师";
          break;
      }
      return str;
    },
    // 表格 一卡通状态数据格式化
    StatusFormat(row, col) {
      var str = "";
      switch (row.status) {
        case 1:
          str = "正常";
          break;
        case 2:
          str = "禁用";
          break;
      }
      return str;
    },
    // 表格 性别数据格式化
    sexFormat(row, col) {
      var str = "";
      switch (row.sex) {
        case 1:
          str = "男";
          break;
        case 2:
          str = "女";
          break;
        case 3:
          str = "保密";
          break;
      }
      return str;
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
  },
  created() {
    this.initTable(); //表格数据初始化
    this.searchClass(); //查询班级下拉框
    this.selectdeptName(); //查询部门下拉框
    this.getsysconfig(); //获取一卡通是否修改密码的配置项
    this.getkey(); //获取rsa加密的公共密钥
  },
  components: {
    Paging,
  },
};
</script>
<style scoped>
.el-pagination {
  margin-top: 1% !important;
}

.el-table td {
  padding: 4px 0;
}

.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}

.import {
  display: inline-block;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  margin-left: 20px;
  background: #40abd2;
  color: white;
}

.import .icon {
  font-size: 16px;
  margin-right: 5px;
}
</style>
<style lang="scss" scoped>
.search-btn {
  width: 100%;
  text-align: center;
}
</style>
