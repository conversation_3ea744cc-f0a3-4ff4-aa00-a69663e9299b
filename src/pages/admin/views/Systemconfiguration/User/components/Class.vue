<template>
  <div class="boxcontent">
    <!-- 新增和搜索开始 -->
    <div class="search">
      <el-form :model="searchForm" width="100%" label-width="70px" class="itemBox">
        <el-row :gutter="40">
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="5">
            <el-form-item label="班级名称" prop="roleName">
              <el-autocomplete
                class="inline-input"
                v-model="searchForm.className"
                :fetch-suggestions="ClassquerySearch"
                clearable

                placeholder="请输入课程班名称"
                style="width: 100%"
              ></el-autocomplete>
              <!-- <el-select v-model="searchForm.className" allow-create  clearable filter placeholder="请选择" style="width: 100%" >
								<el-option v-for="(item,index) in classOptions" :key="index" :label="item.label" :value="item.label"></el-option>
							</el-select> -->
              <!-- <el-input placeholder="请输入班级名称" class="fl" clearable v-model="searchForm.className" ></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
            <el-form-item label="班级类型" prop="roleName">
              <el-select
                v-model="searchForm.kind"

                placeholder="请选择"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in kinds"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="5" :sm="5" :md="6" :lg="4" :xl="2">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchBtn"
              class="smallbtn"

              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row class="marginTop">
      <el-col :span="24"
        ><el-button
          type="success"
          icon="el-icon-plus"
          @click="addBtn"
          class="addbtn"
          v-allow="'accClass:add'"
          >新增班级</el-button
        ></el-col
      >
    </el-row>
    <!-- 新增和搜索开始 -->

    <!-- 查询搜索开始-->
    <!-- <el-row :gutter="10">
      <el-col :xs="12" :sm="10" :md="8" :lg="6" :xl="4">
        <el-input placeholder="请输入班级名称" class="fl" clearable v-model="searchForm.className"></el-input>
      </el-col>
      <el-col :xs="5" :sm="5" :md="6" :lg="4" :xl="2">
        <el-button type="primary" icon="el-icon-search" plain @click="searchBtn">搜索</el-button>
      </el-col>
    </el-row>
    <el-row>
      <div class="addstyle">
        <el-button type="success" icon="el-icon-plus" @click="addBtn" plain>添加班级</el-button>
      </div>
    </el-row> -->
    <!-- 查询搜索结束-->
    <!-- 数据表格开始-->
    <el-row class="marginTop">
      <el-table
        :data="tb_data"
        stripe
        border
        style="width: 100%"
        @sort-change="sortChange"
        v-loading="loading"
      >
        <!-- <el-table-column
          type="index"
          width="65px"
          label="序号"
          align="center"
        ></el-table-column> -->
        <el-table-column
          sortable="custom"
          prop="classSn"
          label="班级编号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="className"
          label="班级名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="deptName"
          label="学院名称"
          align="center"
        ></el-table-column>
        <el-table-column prop="classKind" label="班级类型" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.classKind == '1'">一卡通</span>
            <span v-if="scope.row.classKind == '2'">本地</span>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="备注" align="center"></el-table-column>
        <el-table-column label="操作" width="300px" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="editBtn(scope.row)"
              v-allow="'accClass:update'"
              >编辑</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              style="color: red"
              @click="delBtn(scope.row)"
              v-allow="'accClass:delete'"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="paging">
        <Paging
          :pageCount="pageCount"
          @pagebar="changePage"
          :currentpage="currentpage"
        ></Paging>
      </div>
    </el-row>
    <!-- 数据表格结束-->

    <!-- 编辑、新增弹窗开始 -->
    <el-row>
      <el-dialog
        :title="dialogTitle"
        :visible.sync="diaShow"
        width="500px"
        append-to-body
        :close-on-click-modal="false"
      >
        <el-form :model="diaForm" ref="diaForm" label-width="80px" :rules="rules">
          <el-form-item label="班级编号" prop="classSn"
            ><el-input
              v-model="diaForm.classSn"

              placeholder="请输入..."
            ></el-input
          ></el-form-item>
          <el-form-item label="班级名称" prop="className"
            ><el-input
              v-model="diaForm.className"

              placeholder="请输入..."
            ></el-input
          ></el-form-item>
          <el-form-item label="学院名称" prop="deptId">
            <el-select
              v-model="diaForm.deptId"

              placeholder="请选择"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in deptNames"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="年份" prop="enrolYear">
          <el-date-picker
            v-model="diaForm.enrolYear"

            type="year"
            style="width:100%"
            format="yyyy"
            value-format="yyyy"
            placeholder="请选择年份"
          ></el-date-picker>
          </el-form-item>-->
          <el-form-item label="备注" prop="memo"
            ><el-input
              v-model="diaForm.memo"

              placeholder="请输入..."
              type="textarea"
            ></el-input
          ></el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diaShow = false" >取消</el-button>
          <el-button type="primary"  @click="confirmBtn">确定</el-button>
        </span>
      </el-dialog>
    </el-row>
    <!-- 编辑、新增弹窗结束 -->
  </div>
</template>
<script>
import {
  getAccClass,
  addAccClass,
  editAccClass,
  delAccClass,
  getAllClass,
  //   getAccDept
} from "@admin/api/Settings/User/Class.js";
import { getAllDept } from "@admin/api/Settings/User/Department.js";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      dialogTitle: "新增班级",
      // 表格数据
      tb_data: [],

      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      pageCount: 0,
      currentpage: 0,

      orderModel: "",
      orderKey: "",

      kinds: [
        {
          label: "本地",
          value: "2",
        },
        {
          label: "一卡通",
          value: "1",
        },
      ],

      classOptions: [], //班级下拉框
      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        // uuid: "",
        classSn: "",
        deptId: "",
        className: "",
        // classKind: 2,
        magorId: 0,
        enrolYear: "",
        memo: "",
      },
      // 搜索框
      searchForm: {
        className: "",
        kind: "",
      },
      loading: false,
      // 模态框中部门名称
      deptName: "",
      //下拉菜单部门名称
      deptNames: [],
      // 表单数据验证
      rules: {
        classSn: [{ required: true, message: "请输入班级编号", trigger: "blur" }],
        className: [{ required: true, message: "请输入班级名称", trigger: "blur" }],
        enrolYear: [{ required: true, message: "请选择年份", trigger: "blur" }],
        deptId: [{ required: true, message: "请选择学院", trigger: "change" }],
      },
    };
  },
  methods: {
    //班级下拉查询
    ClassquerySearch(queryString, cb) {
      let arr = [];
      getAllClass(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.className,
              value: item.className,
            });
          });
          cb(arr);
        }
      });
    },
    handleSelect(item) {
      this.diaForm.deptId = item.deptId;
    },
    sortChange(obj) {
      this.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.orderKey = "classSn";
      this.initTable();
    },
    // 表格初始化
    initTable() {
      this.loading = true;
      const pageNum = this.pageData.pageNum;
      const page = this.pageData.page;
      getAccClass(
        pageNum,
        page,
        this.searchForm.className,
        this.searchForm.kind,
        this.orderKey,
        this.orderModel
      ).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 点击新增按钮
    addBtn() {
      // 数据初始化
      this.diaForm.uuid = "";
      this.diaForm.deptId = "";
      this.deptName = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.dialogTitle = "新增班级";
    },
    // 点击编辑按钮
    editBtn(row) {
      // 数据初始化
      this.diaForm.uuid = "";
      this.diaForm.deptId = "";
      this.deptName = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.dialogTitle = "编辑班级";
      this.$nextTick(function () {
        this.deptName = row.deptName;
        this.diaForm.uuid = row.uuid;
        this.diaForm.classSn = row.classSn;
        this.diaForm.deptId = row.deptId;
        this.diaForm.className = row.className;
        // this.diaForm.enrolYear = `${row.enrolYear}`;
        this.diaForm.memo = row.memo;
      });
    },
    //点击查询按钮
    searchBtn() {
      this.pageData.pageNum = 1;
      this.currentpage = 0;
      this.initTable();
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该班级, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delAccClass(json).then((res) => {
            console.log(res);
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
              this.selectAllclass();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.uuid == "") {
            // 新增操作
            addAccClass(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.diaShow = false;
                this.initTable();
                this.selectAllclass();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            // 编辑操作
            editAccClass(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.diaShow = false;
                this.initTable();
                this.selectAllclass();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          this.$message.warning("请完善信息后再操作");
          return false;
        }
      });
    },
    // 表格 kind 数据格式
    kindFormat(row, col) {
      if (row.classKind == 1) {
        return "一卡通班级";
      } else if (row.classKind == 2) {
        return "本地班级";
      } else {
        return "未知";
      }
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
    //部门数据下拉框
    selectdeptName() {
      getAllDept().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.deptNames.push({
              label: item.deptName,
              value: item.deptId,
            });
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //班级数据下拉框
    selectAllclass() {
      this.classOptions = [];
      getAllClass().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.classOptions.push({
              label: item.className,
              value: item.classId,
            });
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
  },
  created() {
    this.initTable();
    this.selectdeptName(); //部门数据下拉框
    this.selectAllclass(); //班级数据下拉框
  },
  components: {
    Paging,
  },
};
</script>
<style>
.el-pagination {
  margin-top: 1% !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
</style>
<style lang="scss" scoped>
.addstyle {
  margin-top: 1%;
}
</style>
