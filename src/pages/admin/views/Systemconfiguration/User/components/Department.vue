<template>
  <div class="boxcontent">
    <!-- 新增和搜索开始 -->
    <div class="search">
      <el-form
        :model="searchForm"
        width="100%"
        label-width="70px"
        class="itemBox"
      >
        <el-row :gutter="40">
          <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
            <el-form-item label="学院名称" prop="roleName">
              <el-autocomplete
                class="inline-input"
                v-model="searchForm.deptName"
                :fetch-suggestions="DeptquerySearch"
                clearable

                placeholder="请输入学院名称"
                style="width: 100%"
              ></el-autocomplete>
              <!-- <el-select v-model="searchForm.deptName"  placeholder="请选择" style="width: 100%" filterable clearable allow-create>
								<el-option v-for="(item,index) in deptNames" :key="index" :label="item.label" :value="item.label"></el-option>
							</el-select> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :lg="8" :xl="6">
            <el-form-item label="学院类型" prop="roleName">
              <el-select
                v-model="searchForm.kind"

                placeholder="请选择"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in kinds"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="5" :sm="5" :md="6" :lg="4" :xl="2">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchBtn"
              class="smallbtn"

              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row class="marginTop">
      <el-col :span="24"
        ><el-button
          type="success"
          icon="el-icon-plus"
          @click="addBtn"
          class="addbtn"
          v-allow="'accDept:add'"
          >新增学院</el-button
        ></el-col
      >
    </el-row>
    <!-- 新增和搜索开始 -->

    <!-- 数据表格开始 -->
    <el-table
      :data="tb_data"
      stripe
      border
      style="width: 100%"
      @sort-change="sortChange"
      class="marginTop"
      v-loading="loading"
    >
      <!-- <el-table-column
        type="index"
        width="65px"
        label="序号"
        align="center"
      ></el-table-column> -->
      <el-table-column
        sortable="custom"
        prop="deptSn"
        label="学院编号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="deptName"
        label="学院名称"
        align="center"
      ></el-table-column>
      <el-table-column prop="kind" label="学院类型" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.kind == '1'">一卡通学院</span>
          <span v-if="scope.row.kind == '2'">本地学院</span>
        </template>
      </el-table-column>
      <el-table-column label="学院管理员" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-user"
            @click="catchBtn(scope.row)"
            v-allow="'accDept:manager:page'"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="memo"
        label="备注"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" width="300px" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="editBtn(scope.row)"
            v-if="scope.row.kind == 2"
            v-allow="'accDept:update'"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="delBtn(scope.row)"
            v-if="scope.row.kind == 2"
            v-allow="'accDept:delete'"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 数据表格结束 -->
    <!-- 分页开始 -->
    <div class="paging">
      <Paging
        :pageCount="pageCount"
        @pagebar="changePage"
        :currentpage="currentpage"
      ></Paging>
    </div>
    <!-- 分页结束 -->

    <!-- 编辑、新增弹窗开始-->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="diaShow"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="diaForm" ref="diaForm" label-width="80px" :rules="rules">
        <el-form-item label="学院编号" prop="deptSn"
          ><el-input
            v-model="diaForm.deptSn"

            placeholder="请输入..."
          ></el-input
        ></el-form-item>
        <el-form-item label="学院名称" prop="deptName"
          ><el-input
            v-model="diaForm.deptName"

            placeholder="请输入..."
          ></el-input
        ></el-form-item>
        <el-form-item label="备注" prop="memo"
          ><el-input
            v-model="diaForm.memo"

            placeholder="请输入..."
            type="textarea"
          ></el-input
        ></el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diaShow = false" >取消</el-button>
        <el-button type="primary"  @click="confirmBtn"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <!-- 编辑、新增弹窗结束-->

    <!-- 查看部门管理员开始-->
    <el-dialog
      title="查看学院管理员"
      :visible.sync="diaShow_manager"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-row>
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="addBtn_manager"
          size="medium"
          v-allow="'accDept:manager:save'"
          >新增</el-button
        >
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="delBatchBtn"
          size="medium"
          style="margin-left: 20px"
          v-preventReClick
          v-allow="'accDept:manager:deleteBatch'"
          >批量删除</el-button
        >
      </el-row>
      <el-table
        :data="tb_data_manager"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        class="marginTop"
        v-loading="loading_manager"
        :row-key="getRowKeys"
      >
        <el-table-column
          type="selection"
          min-width="50px"
          align="center"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column
          prop="logonName"
          label="学工号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="trueName"
          label="姓名"
          align="center"
        ></el-table-column>
      </el-table>
      <!-- 分页开始 -->
      <div class="paging">
        <Paging
          :pageCount="pageCount_manager"
          @pagebar="changePage_manager"
          :currentpage="currentpage_manager"
        ></Paging>
      </div>
      <!-- 分页结束 -->
    </el-dialog>
    <!-- 查看部门管理员结束-->
    <!-- 新增部门管理员开始-->
    <el-dialog
      title="新增部门管理员"
      :visible.sync="diaShow_addmanager"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm_addmanager"
        width="100%"
        label-width="80px"
        class="itemBox"
        :rules="addFormrules"
        ref="addFormaddmanager"
      >
        <el-form-item label="人员" prop="accNoSet">
          <el-select
            class="inline-input"
            v-model="addForm_addmanager.accNoSet"
            :remote-method="querySearch"
            clearable
            placeholder="请输入工号或者姓名关键词查询选择"

            filterable
            style="width: 100%"
            multiple
            remote
            :loading="loading_select"
          >
            <el-option
              v-for="item in AccNo_options"
              :key="item.value"
              :label="item.accNoTrueName"
              :value="item.accNo"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="diaShow_addmanager = false"
          >取消</el-button
        >
        <el-button type="primary"  @click="confirmBtn_addmanager"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAccDept,
  addAccDept,
  editAccDept,
  delAccDept,
  getmanager,
  savemanager,
  deleteBatchmanager,
} from "@admin/api/Settings/User/Department.js";
import { getAccNo } from "@admin/api/Management/administrator.js";
import { getAllDept } from "@admin/api/Settings/User/Department.js";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      AccNo_options: [],
      loading_select: false,
      addForm_addmanager: {
        accNoSet: [],
        deptUuid: "",
      },
      diaShow_addmanager: false,
      pageCount_manager: 0,
      currentpage_manager: 1,
      changedata: [],
      diaShow_manager: false,
      tb_data_manager: [],
      loading_manager: false,
      dialogTitle: "",
      // 表格数据
      tb_data: [],
      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      searchForm_manager: {
        pageNum: 1,
        pageSize: 10,
        deptUuid: "",
        search: "",
      },
      pageCount: 0,
      currentpage: 0,

      //下拉菜单部门名称
      deptNames: [],

      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        deptSn: "",
        deptName: "",
        kind: 2,
        parentId: 0,
        deptCase: 0,
        memo: "",
      },
      searchForm: {
        deptName: "",
        kind: "",
      },
      kinds: [
        {
          label: "本地学院",
          value: "2",
        },
        {
          label: "一卡通学院",
          value: "1",
        },
      ],
      // 表单验证
      rules: {
        deptSn: [
          { required: true, message: "请输入学院编号", trigger: "blur" },
        ],
        deptName: [
          { required: true, message: "请输入学院名称", trigger: "blur" },
        ],
      },
      addFormrules: {
        accNoSet: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },

      orderKey: "",
      orderModel: "",

      loading: false,
    };
  },
  methods: {
    //新增管理员的确定按钮
    confirmBtn_addmanager() {
      this.$refs.addFormaddmanager.validate((valid) => {
        if (valid) {
          savemanager(this.addForm_addmanager).then((res) => {
            if (res.code == 0) {
              this.diaShow_addmanager = false;
              this.initTable();
              this.getmanager();
              this.$message.success(res.message);
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        }
      });
    },
    //学院管理员的新增
    addBtn_manager() {
      this.diaShow_addmanager = true;
      this.addForm_addmanager.accNoSet = [];
    },
    //学工号查询
    querySearch(queryString) {
      let show = true;
      this.addForm_addmanager.accNoSet.map((item, index) => {
        if (item == queryString) {
          show = false;
        }
      });
      if (queryString == "") {
        show = false;
      }
      if (show) {
        this.loading_select = true;
        getAccNo(queryString, 500).then((res) => {
          this.loading_select = false;
          if (res.code == 0) {
            this.AccNo_options = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    catchBtn(row) {
      this.searchForm_manager.deptUuid = row.uuid;
      this.addForm_addmanager.deptUuid = row.uuid;
      this.searchForm_manager.pageNum = 1;
      this.diaShow_manager = true;
      this.getmanager();
    },
    getmanager() {
      this.loading_manager = true;
      getmanager(this.searchForm_manager).then((res) => {
        this.loading_manager = false;
        if (res.code == 0) {
          this.tb_data_manager = res.data;
          this.pageCount_manager = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //点击批量删除按钮
    delBatchBtn() {
      if (this.changedata.length == 0) {
        this.$message({
          message: "至少勾选一个",
          type: "error",
          duration: "5000",
        });
      } else {
        this.$confirm("此操作将永久删除所勾选的管理员, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let json = {
              uuidSet: this.changedata,
            };
            deleteBatchmanager(json).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.getmanager();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },
    //勾选表格中多选的事件
    handleSelectionChange(val) {
      this.changedata = [];
      val.map((item, index) => {
        this.changedata.push(item.uuid);
      });
    },
    //获取表格选中的数据
    getRowKeys(row) {
      return row.uuid;
    },
    sortChange(obj) {
      this.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.orderKey = "deptSn";
      this.initTable();
    },
    //学院下拉查询
    DeptquerySearch(queryString, cb) {
      let arr = [];
      getAllDept(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.deptName,
              value: item.deptName,
            });
          });
          cb(arr);
        }
      });
    },
    // 表格初始化
    initTable() {
      //部门数据下拉框
      this.selectdeptName();
      const pageNum = this.pageData.pageNum;
      const page = this.pageData.page;
      this.loading = true;
      getAccDept(
        pageNum,
        page,
        this.searchForm.deptName,
        this.searchForm.kind,
        this.orderKey,
        this.orderModel
      ).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 点击新增按钮
    addBtn() {
      // 初始化数据
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.uuid = "";
      this.diaShow = true;
      this.dialogTitle = "新增学院";
    },
    // 点击编辑按钮
    editBtn(row) {
      // 初始化数据
      this.diaForm.uuid = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.dialogTitle = "编辑学院";

      // 重新赋值
      this.$nextTick(function () {
        this.diaForm.uuid = row.uuid;
        this.diaForm.deptSn = row.deptSn;
        this.diaForm.deptName = row.deptName;
        this.diaForm.memo = row.memo;
      });
    },
    //点击查询按钮
    searchBtn() {
      this.pageData.pageNum = 1;
      this.currentpage = 0;
      this.initTable();
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该学院, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delAccDept(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.uuid == "") {
            // 新增操作
            addAccDept(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            // 编辑操作
            editAccDept(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          this.$message.warning("请完善信息后再操作");
          return false;
        }
      });
    },
    //部门数据下拉框
    selectdeptName() {
      this.deptNames = [];
      getAllDept().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.deptNames.push({
              label: item.deptName,
              value: item.deptName,
            });
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 表格 kind 数据格式
    kindFormat(row, col) {
      if (row.kind == 1) {
        return "一卡通学院";
      } else if (row.kind == 2) {
        return "本地学院";
      } else {
        return "未知";
      }
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
    changePage_manager(pageData) {
      this.searchForm_manager.pageSize = pageData.page;
      this.searchForm_manager.pageNum = pageData.pageNum;
      this.currentpage_manager = pageData.pageNum;
      // 页面刷新
      this.getmanager();
    },
  },
  created() {
    this.initTable();
  },
  components: {
    Paging,
  },
};
</script>
<style>
.el-pagination {
  margin-top: 1% !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
</style>
<style lang="scss" scoped>
.addstyle {
  margin-top: 1%;
}
</style>
