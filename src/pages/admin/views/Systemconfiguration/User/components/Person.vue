<template>
  <div class="boxcontent">
    <!-- 新增和搜索开始 -->
    <div class="search">
      <el-form
        :model="searchForm"
        width="100%"
        label-width="90px"
        class="itemBox"
      >
        <el-row :gutter="40">
          <el-col :xs="12" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="学工号" prop="ctrlName">
              <el-input
                v-model="searchForm.logonName"

                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="8" :md="8" :lg="8" :xl="8">
            <el-form-item label="姓名" prop="ctrlName">
              <el-input
                v-model="searchForm.trueName"

                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="search-btn" style="text-align: center">
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="searchBtn"
          class="smallbtn"

          >搜索</el-button
        >
      </div>
    </div>
    <el-row class="marginTop">
      <el-col :span="24"
        ><el-button
          type="success"
          icon="el-icon-plus"
          @click="addBtn"
          class="addbtn"
          v-allow="'responsible:person:save'"
          >新增</el-button
        ></el-col
      >
    </el-row>
    <!-- 表格开始 -->
    <el-table
      :data="tb_data"
      stripe
      border
      style="width: 100%"
      @sort-change="sortChange"
      class="marginTop"
      v-loading="loading"
    >
      <el-table-column
        prop="logonName"
        label="学工号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="trueName"
        label="姓名"
        align="center"
      ></el-table-column>
      <el-table-column prop="tel" label="电话" align="center"></el-table-column>
      <el-table-column
        prop="handPhone"
        label="手机"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="email"
        label="邮箱"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="responsiblePersonPhoto"
        label="照片"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-view"
            @click="catchBtn(scope.row)"
            v-if="scope.row.responsiblePersonPhoto != ''"
            >查看</el-button
          >
          <p v-else>暂无</p>
        </template>
      </el-table-column>
      <el-table-column
        prop="personnelAcademic"
        label="所属学科"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="technicalPosition"
        label="专业技术职务"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="educationalLevel"
        label="文化程度"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="expertCategory"
        label="专家类别"
        align="center"
      ></el-table-column>
      <el-table-column prop="expertCategory" label="学历教育" align="center">
        <el-table-column
          prop="domesticEducationTrainingDuration"
          label="国内培训时长"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="foreignEducationTrainingDuration"
          label="国外培训时长"
          align="center"
        ></el-table-column>
      </el-table-column>
      <el-table-column prop="expertCategory" label="非学历教育" align="center">
        <el-table-column
          prop="domesticTrainingDuration"
          label="国内培训时长"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="foreignTrainingDuration"
          label="国外培训时长"
          align="center"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="editBtn(scope.row)"
            v-allow="'responsible:person:update'"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="delBtn(scope.row)"
            v-allow="'responsible:person:delete'"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页开始-->
    <div class="paging">
      <Paging
        :pageCount="pageCount"
        @pagebar="changePage"
        :currentpage="currentpage"
      ></Paging>
    </div>
    <!-- 新增/编辑-->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="diaShow"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
      top="0%"
    >
      <el-form
        :model="diaForm"
        ref="diaForm"
        label-width="150px"
        :rules="rules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="accNo">
              <Account @getaccNo="getaccNo" :accNo="trueName"></Account>
            </el-form-item>
            <el-form-item label="专业技术职务">
              <PositionSelect
                @getId="getpositionid"
                :position="diaForm.technicalPosition"
              ></PositionSelect>
            </el-form-item>
            <el-form-item label="专家类别">
              <ExpertscategorySelect
                @getId="getexpertscategoryid"
                :expertscategory="diaForm.expertCategory"
              ></ExpertscategorySelect>
            </el-form-item>
            <el-form-item label="照片">
              <el-upload
                action="#"
                multiple
                ref="upload"
                :file-list="fileList"
                :show-file-list="false"
                :auto-upload="true"
                :on-change="uploadChange"
                :http-request="submitFile"
                list-type="picture-card"
                accept="image/png, image/gif, image/jpeg"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <i v-else slot="default" class="el-icon-plus"></i>
                <div
                  slot="tip"
                  class="el-upload-tip"
                  style="font-size: 14px; color: #909399"
                >
                  <p style="margin-left: 20px; margin-bottom: 0px">
                    重新上传点击图片即可；<span style="color: red"
                      >推荐图片分辨率：360*200</span
                    >
                  </p>
                  <p style="margin-bottom: 0px">
                    只能上传png/gif/jpeg/jpg格式的图片
                  </p>
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属学科">
              <DisciplineSelect
                @getId="getdisciplineid"
                :disciline="diaForm.personnelAcademic"
              ></DisciplineSelect>
            </el-form-item>
            <el-form-item label="文化程度">
              <EduSelect
                @getId="geteduid"
                :edu="diaForm.educationalLevel"
              ></EduSelect>
            </el-form-item>
            <el-form-item label="备注"
              ><el-input
                v-model="diaForm.memo"

                placeholder="请输入"
                type="textarea"
              ></el-input
            ></el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <p>学历教育：</p>
          <el-col :span="12">
            <el-form-item label="国内培训时长(天)"
              ><el-input
                v-model="diaForm.domesticEducationTrainingDuration"

                placeholder="请输入"
                type="number"
              ></el-input
            ></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国外培训时长(天)"
              ><el-input
                v-model="diaForm.foreignEducationTrainingDuration"

                placeholder="请输入"
                type="number"
              ></el-input
            ></el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <p>非学历教育：</p>
          <el-col :span="12">
            <el-form-item label="国内培训时长(天)"
              ><el-input
                v-model="diaForm.domesticTrainingDuration"

                placeholder="请输入"
                type="number"
              ></el-input
            ></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国外培训时长(天)"
              ><el-input
                v-model="diaForm.foreignTrainingDuration"

                placeholder="请输入"
                type="number"
              ></el-input
            ></el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diaShow = false" >取消</el-button>
        <el-button type="primary"  @click="confirmBtn"
          >确定</el-button
        >
      </span>
    </el-dialog>

    <!-- 查看图片-->
    <el-dialog
      title="查看照片"
      :visible.sync="diaShow_img"
      width="500px"
      append-to-body
      top="0%"
      :close-on-click-modal="false"
    >
      <img :src="imgUrl" style="width: 100%" />
    </el-dialog>
  </div>
</template>
<script>
import {
  getperson,
  saveperson,
  updateperson,
  delperson,
  uploadpersonimg,
} from "@admin/api/Systemconfiguration/User/Person.js";
import { getAccNo } from "@admin/api/Management/administrator";
import Paging from "@admin/components/pagebar/Paging.vue";
import Account from "@admin/components/Select/Account.vue";
import DisciplineSelect from "@admin/components/Select/DisciplineSelect.vue";
import PositionSelect from "@admin/components/Select/PositionSelect.vue";
import EduSelect from "@admin/components/Select/EduSelect.vue";
import ExpertscategorySelect from "@admin/components/Select/ExpertscategorySelect.vue";
export default {
  data() {
    return {
      ApiUrl:window.g.ApiUrl,
      imgUrl: "",
      diaShow_img: false,
      fileList: [],
      imageUrl: "",
      location:
        window.location.protocol + "//" + window.location.host + window.g.ApiUrl +  "/",
      diaForm: {
        accNo: "",
        responsiblePersonPhoto: "",
        personnelAcademic: "",
        technicalPosition: "",
        educationalLevel: "",
        expertCategory: "",
        domesticEducationTrainingDuration: "",
        domesticTrainingDuration: "",
        foreignEducationTrainingDuration: "",
        foreignTrainingDuration: "",
        kind: "",
        property: "",
        memo: "",
        uuid: "",
      },
      accNo: "",
      diaShow: false,
      dialogTitle: "新增负责人",
      loading: false,
      tb_data: [],
      currentpage: 1,
      pageCount: 0,
      searchForm: {
        logonName: "",
        trueName: "",
        pageNum: 1,
        pageSize: 10,
        orderItems: "",
        orderRule: "",
      },
      trueName: "",
      searchName: "",
      rules: {
        accNo: [{ required: true, message: "请选择用户", trigger: "change" }],
      },
    };
  },
  methods: {
    // 表格初始数据
    initTable() {
      this.loading = true;
      getperson(this.searchForm).then((res) => {
        this.loading = false;
        this.search = "";
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //新增
    addBtn() {
      this.diaShow = true;
      this.$refs.diaForm.resetFields();
      this.diaForm.accNo = "";
      this.trueName = "";
      this.diaForm.responsiblePersonPhoto = "";
      this.diaForm.personnelAcademic = "";
      this.diaForm.technicalPosition = "";
      this.diaForm.educationalLevel = "";
      this.diaForm.expertCategory = "";
      this.diaForm.domesticEducationTrainingDuration = "";
      this.diaForm.domesticTrainingDuration = "";
      this.diaForm.foreignEducationTrainingDuration = "";
      this.diaForm.foreignTrainingDuration = "";
      this.diaForm.kind = "";
      this.diaForm.property = "";
      this.diaForm.memo = "";
      this.diaForm.uuid = "";
      this.imageUrl = "";
    },
    //编辑
    editBtn(row) {
      this.diaShow = true;
      this.diaForm.accNo = row.accNo;
      this.trueName = row.logonName + "(" + row.trueName + ")";
      this.diaForm.responsiblePersonPhoto = row.responsiblePersonPhoto;
      if (row.responsiblePersonPhoto != "") {
        this.imageUrl = this.ApiUrl +  "/" + row.responsiblePersonPhoto;
      } else {
        this.imageUrl = "";
      }
      this.diaForm.personnelAcademic = row.personnelAcademic;
      this.diaForm.technicalPosition = row.technicalPosition;
      this.diaForm.expertCategory = row.expertCategory;
      this.diaForm.domesticEducationTrainingDuration =
        row.domesticEducationTrainingDuration;
      this.diaForm.domesticTrainingDuration = row.domesticTrainingDuration;
      this.diaForm.foreignEducationTrainingDuration =
        row.foreignEducationTrainingDuration;
      this.diaForm.foreignTrainingDuration = row.foreignTrainingDuration;
      this.diaForm.kind = row.kind;
      this.diaForm.property = row.property;
      this.diaForm.memo = row.memo;
      this.diaForm.uuid = row.uuid;
      this.diaForm.educationalLevel = row.educationalLevel;
    },
    //弹窗的新增按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.personnelAcademic.length > 0) {
            this.diaForm.personnelAcademic =
              this.diaForm.personnelAcademic[
                this.diaForm.personnelAcademic.length - 1
              ];
          }
          if (this.diaForm.uuid == "") {
            //新增
            saveperson(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.diaShow = false;
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            //编辑
            updateperson(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.diaShow = false;
                this.initTable();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        }
      });
    },
    //查看照片
    catchBtn(row) {
      this.diaShow_img = true;
      this.imgUrl = this.location + row.responsiblePersonPhoto;
    },
    //上传照片
    uploadChange(file, fileList) {
      if (fileList.length > 0) {
        this.canUpload = true;
        this.imageUrl = URL.createObjectURL(file.raw);
      }
    },
    // 上传图片
    submitFile(params) {
      // 获取要上传的图片
      const file = params.file,
        fileType = file.type,
        isImage = fileType.includes("image"); // 是否是图片
      // console.log('params',params);
      // 如果不是图片
      if (!isImage) {
        this.$message({
          message: "只能上传图片格式png,gif,jpeg!",
          type: "error",
          duration: "5000",
        });
        return;
      }
      this.fileData = new FormData();
      this.fileData.append("file", file);
      if (this.canUpload) {
        uploadpersonimg(this.fileData).then((res) => {
          if (res.code === 0) {
            this.$message.success("上传成功");
            this.diaForm.responsiblePersonPhoto = res.data;
          } else {
            this.$message({
              message: res.message,
              type: "error",
              duration: "5000",
            });
          }
        });
      }
      this.canUpload = false;
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该负责人, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delperson(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //搜索
    searchBtn() {
      this.searchForm.pageNum = 1;
      this.currentpage = 0;
      this.initTable();
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
    //将选择的学工号/账号给分割处理
    HandleTeacher(data) {
      let text = data.label;
      this.searchName = text.split("(")[0];
    },
    HandleTeacher2(data) {
      if (this.search == "") {
        this.searchName = data;
      }
    },
    //学工号（姓名）
    TeacherquerySearch(queryString, cb) {
      let arr = [];
      getAccNo(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.accNoTrueName,
              value: item.accNoTrueName,
            });
          });
          cb(arr);
        }
      });
    },
    // 表格排序
    sortChange(obj) {
      this.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.orderKey = obj.prop;
      this.initTable();
    },
    //负责人获取
    getaccNo(data) {
      this.diaForm.accNo = data;
    },
    //所属学科获取
    getdisciplineid(data) {
      this.diaForm.personnelAcademic = data;
    },
    //专业技术职务
    getpositionid(data) {
      this.diaForm.technicalPosition = data;
    },
    //文化程度
    geteduid(data) {
      this.diaForm.educationalLevel = data;
    },
    //专家类别
    getexpertscategoryid(data) {
      this.diaForm.expertCategory = data;
    },
  },
  created() {
    this.initTable();
  },
  components: {
    Paging,
    Account,
    DisciplineSelect,
    PositionSelect,
    EduSelect,
    ExpertscategorySelect,
  },
};
</script>
<style lang="less" scoped>
/deep/ .avatar {
  width: 120px;
  height: 120px;
}
</style>
