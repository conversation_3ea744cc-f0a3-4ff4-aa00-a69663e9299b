<template>
  <div class="boxcontent">
    <!-- 新增和搜索开始 -->
    <div class="search">
      <el-form :model="searchForm" width="100%" label-width="70px" class="itemBox">
        <el-row :gutter="40">
          <el-col :xs="18" :sm="15" :md="14" :lg="8" :xl="5">
            <el-form-item label="专业名称" prop="roleName">
              <el-autocomplete
                class="inline-input"
                v-model="searchForm.majorName"
                :fetch-suggestions="MajorquerySearch"
                clearable

                placeholder="请输入专业名称"
                style="width: 100%"
              ></el-autocomplete>
              <!-- <el-select v-model="searchForm.majorName"  placeholder="请选择" style="width: 100%" filterable clearable allow-create>
								<el-option v-for="item in majorNames" :key="item.value" :label="item.label" :value="item.label"></el-option>
							</el-select> -->
            </el-form-item>
          </el-col>
          <el-col :xs="5" :sm="5" :md="6" :lg="4" :xl="2">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchBtn"
              class="smallbtn"

              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row class="marginTop">
      <el-col :span="24">
        <el-button
          type="success"
          icon="el-icon-plus"
          @click="addBtn"
          class="addbtn"
          v-allow="'accMajor:save'"
          >新增专业</el-button
        >
        <el-dropdown trigger="click" class="import">
          <span class="el-dropdown-link">
            <i class="el-icon-download icon"></i>
            导入/下载
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-upload
                action
                ref="Courseupload"
                class="upload-demo"
                :http-request="CourseUpload"
                :show-file-list="false"
                :before-upload="beforeUpload"
                accept=".xls,.xlsx"
              >
                <el-button
                  type="text"
                  slot="trigger"

                  style="color: #40abd2; font-size: 15px"
                  v-allow="'accMajor:fileSave'"
                  >Excel导入</el-button
                >
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item
              ><a href="./Professional.xlsx" style="color: #40abd2; font-size: 15px"
                >模板下载</a
              ></el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
    </el-row>
    <!-- 新增和搜索开始 -->

    <!-- 数据表格开始 -->
    <el-table
      :data="tb_data"
      stripe
      border
      style="width: 100%"
      @sort-change="sortChange"
      class="marginTop"
      v-loading="loading"
    >
      <el-table-column prop="deptName" label="学院名称" align="center"></el-table-column>
      <el-table-column prop="majorSn" label="专业代码" align="center"></el-table-column>
      <el-table-column
        prop="majorName"
        label="专业名称"
        align="center"
        style="width: 150px"
      ></el-table-column>
      <el-table-column prop="memo" label="备注" align="center"></el-table-column>
      <el-table-column label="操作" width="300px" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="editBtn(scope.row)"
            v-allow="'accMajor:update'"
            >编辑</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="delBtn(scope.row)"
            v-allow="'accMajor:delete'"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 数据表格结束 -->
    <!-- 分页开始 -->
    <div class="paging">
      <Paging
        :pageCount="pageCount"
        @pagebar="changePage"
        :currentpage="currentpage"
      ></Paging>
    </div>
    <!-- 分页结束 -->

    <!-- 编辑、新增弹窗开始-->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="diaShow"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="diaForm" ref="diaForm" label-width="80px" :rules="rules">
        <el-form-item label="学院名称" prop="deptId">
          <el-select
            v-model="diaForm.deptId"

            placeholder="请选择"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in deptNames"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专业代码" prop="majorSn">
          <el-input
            v-model="diaForm.majorSn"

            placeholder="请输入..."
          ></el-input>
        </el-form-item>
        <el-form-item label="专业名称" prop="majorName">
          <el-input
            v-model="diaForm.majorName"

            placeholder="请输入..."
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input
            type="textarea"
            v-model="diaForm.memo"

            placeholder="请输入..."
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="diaShow = false" >取消</el-button>
        <el-button type="primary"  @click="confirmBtn">确定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑、新增弹窗结束-->

    <!-- 导入课程表结果弹窗开始 -->
    <el-dialog
      title="导入结果展示"
      :visible.sync="MajorresultShow"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-tabs type="card">
        <el-tab-pane label="成功数据" lazy>
          <el-table
            :data="successData"
            stripe
            border
            style="width: 100%"
            class="marginTop"
            v-loading="loading"
          >
            <el-table-column
              prop="deptName"
              label="学院名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="majorSn"
              label="专业代码"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="majorName"
              label="专业名称"
              align="center"
            ></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="失败数据" lazy>
          <el-table
            :data="failData"
            stripe
            border
            style="width: 100%"
            class="marginTop"
            v-loading="loading"
          >
            <el-table-column
              prop="deptName"
              label="学院名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="majorSn"
              label="专业代码"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="majorName"
              label="专业名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="resultMsg"
              label="专业名称"
              align="center"
            ></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <!-- 导入课程表结果弹窗结束 -->
  </div>
</template>
<script>
import { getAllDept } from "@admin/api/Settings/User/Department.js";
import Paging from "@admin/components/pagebar/Paging.vue";
import {
  getAllMajor,
  getMajor,
  addMajor,
  editMajor,
  delMajor,
  importMajor,
} from "@admin/api/Settings/User/Professional.js";
export default {
  data() {
    return {
      dialogTitle: "新增专业", //弹窗标题
      // 表格数据
      tb_data: [],
      // 分页数据
      pageData: {
        page: 10,
        pageNum: 1,
      },
      pageCount: 0,
      currentpage: 0,

      //下拉菜单部门名称
      deptNames: [],
      majorNames: [],

      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        deptId: "",
        majorSn: "",
        majorName: "",
        memo: "",
      },
      searchForm: {
        majorName: "",
      },
      // 表单验证
      rules: {
        deptId: [{ required: true, message: "请选择学院名称", trigger: "change" }],
        majorSn: [{ required: true, message: "请输入专业代码", trigger: "blur" }],
        majorName: [{ required: true, message: "请输入专业名称", trigger: "blur" }],
      },

      orderKey: "",
      orderModel: "",

      loading: false,

      //导入表格后展示的数据
      successData: [],
      failData: [],
      MajorresultShow: false,
    };
  },
  methods: {
    sortChange(obj) {
      this.orderModel = obj.order == "ascending" ? "asc" : "desc";
      this.orderKey = "deptSn";
      this.initTable();
    },
    // 表格初始化
    initTable() {
      this.loading = true;
      const pageNum = this.pageData.pageNum;
      const page = this.pageData.page;
      getMajor(
        pageNum,
        page,
        this.searchForm.majorName,
        this.orderKey,
        this.orderModel
      ).then((res) => {
        this.loading = false;
        if (res.code == 0) {
          this.deptNames.map((v, k) => {
            res.data.map((item, index) => {
              if (item.deptId == v.value) {
                res.data[index].deptName = v.label;
              }
            });
          });
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //导入课程表格
    CourseUpload(item) {
      this.successData = [];
      this.failData = [];
      this.$refs.Courseupload.clearFiles();
      let fd = new FormData();
      fd.append("file", item.file);
      importMajor(fd)
        .then((res) => {
          this.selectmajorName(); //更新专业下拉框数据
          this.initTable(); //初始化数据
          if (res.code == 0) {
            this.$message.success(res.message);
          } else {
            console.log(res.data);
            this.successData = res.data.successList;
            this.$message.success(res.message);
            this.MajorresultShow = true;
            this.failData = res.data.failList;
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    //上传文件类型限制
    beforeUpload(file) {
      // const isLt40M = file.size / 1024 / 1024 < 40;
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "xls";
      const extension2 = testmsg === "xlsx";
      if (!extension && !extension2) {
        this.$message({
          message: "导入表格类型只能是xls或者xlsx!",
          type: "error",
          duration: "5000",
        });
        return false;
      }
    },
    // 点击新增按钮
    addBtn() {
      // 初始化数据
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaForm.uuid = "";
      this.diaShow = true;
      this.dialogTitle = "新增专业";
    },
    // 点击编辑按钮
    editBtn(row) {
      // 初始化数据
      this.diaForm.uuid = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
      this.dialogTitle = "编辑专业";

      // 重新赋值
      this.$nextTick(function () {
        this.diaForm.deptId = row.deptId;
        this.diaForm.majorSn = row.majorSn;
        this.diaForm.majorName = row.majorName;
        this.diaForm.memo = row.memo;
        this.diaForm.uuid = row.uuid;
      });
    },
    //点击查询按钮
    searchBtn() {
      this.pageData.pageNum = 1;
      this.currentpage = 0;
      this.initTable();
    },
    // 点击删除按钮
    delBtn(row) {
      console.log(row.uuid);
      this.$confirm("此操作将永久删除该专业, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let json = {
            uuid: row.uuid,
          };
          delMajor(json).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
              this.selectmajorName();
            } else {
              this.$message({
                message: res.message,
                type: "error",
                duration: "5000",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.uuid == "") {
            // 新增操作
            addMajor(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
                this.selectmajorName();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          } else {
            // 编辑操作
            editMajor(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
                this.selectmajorName();
              } else {
                this.$message({
                  message: res.message,
                  type: "error",
                  duration: "5000",
                });
              }
            });
          }
        } else {
          this.$message.warning("请完善信息后再操作");
          return false;
        }
      });
    },
    MajorquerySearch(queryString, cb) {
      let arr = [];
      getAllMajor(queryString, 20).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            arr.push({
              label: item.majorName,
              value: item.majorName,
            });
          });
          cb(arr);
        }
      });
    },
    //专业数据下拉框
    selectmajorName() {
      this.majorNames = [];
      getAllMajor().then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.majorNames.push({
              label: item.majorName,
              value: item.majorId,
            });
          });
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    //部门数据下拉框
    selectdeptName() {
      getAllDept("", 1000).then((res) => {
        if (res.code == 0) {
          res.data.forEach((item) => {
            this.deptNames.push({
              label: item.deptName,
              value: item.deptId,
            });
          });
          this.initTable();
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });
        }
      });
    },
    // 表格 kind 数据格式
    kindFormat(row, col) {
      if (row.kind == 1) {
        return "一卡通学院";
      } else if (row.kind == 2) {
        return "本地学院";
      } else {
        return "未知";
      }
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      this.currentpage = pageData.pageNum;
      // 页面刷新
      this.initTable();
    },
  },
  created() {
    //部门数据下拉框
    this.selectdeptName();
    this.selectmajorName();
  },
  components: {
    Paging,
  },
};
</script>
<style>
.el-pagination {
  margin-top: 1% !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
</style>
<style lang="scss" scoped>
.addstyle {
  margin-top: 1%;
}
.import {
  display: inline-block;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  margin-left: 20px;
  background: #40abd2;
  color: white;
  .icon {
    font-size: 16px;
    margin-right: 5px;
  }
}
</style>
