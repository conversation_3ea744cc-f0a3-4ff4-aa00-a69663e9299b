<template>
  <div>
    <!-- 数据表格开始 -->
    <el-table :data="tb_data" stripe border style="widht:100%" @sort-change="sortChange">
      <el-table-column type="index" width="65px" label="序号" align="center"></el-table-column>
      <el-table-column sortable="custom" prop="majorSn" label="专业编号" align="center"></el-table-column>
      <el-table-column sortable="custom" prop="majorName" label="专业名称" align="center"></el-table-column>
      <el-table-column sortable="custom" prop="deptName" label="部门名称" align="center"></el-table-column>
      <el-table-column sortable="custom" prop="schoolYear" label="年份" align="center"></el-table-column>
      <el-table-column label="操作" width="300px" align="center">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="editBtn(scope.row)">编辑</el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            style="color:red"
            @click="delBtn(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 数据表格结束 -->

    <!-- 分页开始-->
    <div class="paging">
      <Paging :pageCount="pageCount" @pagebar="changePage"></Paging>
    </div>
    <!-- 分页结束-->

    <!-- 编辑、新增弹窗开始 -->
    
     <!-- 编辑、新增弹窗结束 -->
  </div>
</template>
<script>
import {
  getMajor,
  //   addAccDept,
  //   editAccDept,
  //   delAccDept
} from "@admin/api/Settings/User/Professional.js";
import Paging from "@admin/components/pagebar/Paging.vue";
export default {
  data() {
    return {
      // 表格数据
      tb_data: [],
      // 分页数据
      pageData: {
        page: 1,
        pageNum: 5,
      },
      pageCount: 0,

      // 控制模态框显示隐藏
      diaShow: false,
      // 模态框表单数据
      diaForm: {
        uuid: "",
        deptSn: "",
        deptName: "",
        kind: 2,
        parentId: 0,
        deptCase: 0,
        memo: "",
      },
      searchForm: {
        deptName: "",
      },
      // 表单验证
      rules: {
        deptSn: [
          { required: true, message: "请输入部门编号", trigger: "blur" },
        ],
        deptName: [
          { required: true, message: "请输入部门名称", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    sortChange(obj) {
      const orderModel = obj.order == "ascending" ? "asc" : "desc";
      getAccDept({
        params: {
          ...this.pageData,
          orderModel,
          orderKey: obj.prop,
          ...this.searchForm,
        },
      }).then((res) => {
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });;
        }
      });
    },
    // 表格初始化
    initTable() {
      const pageNum = this.pageData.pageNum;
      const page = this.pageData.page;
      getMajor(pageNum, page).then((res) => {
        if (res.code == 0) {
          this.tb_data = res.data;
          this.pageCount = res.count;
        } else {
          this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });;
        }
      });
    },
    // 点击新增按钮
    addBtn() {
      // 初始化数据
      this.diaForm.uuid = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;
    },
    // 点击编辑按钮
    editBtn(row) {
      // 初始化数据
      this.diaForm.uuid = "";
      if (this.$refs.diaForm != undefined) {
        this.$refs.diaForm.resetFields();
      }
      this.diaShow = true;

      // 重新赋值
      this.$nextTick(function () {
        this.diaForm.uuid = row.uuid;
        this.diaForm.deptSn = row.deptSn;
        this.diaForm.deptName = row.deptName;
        this.diaForm.memo = row.memo;
      });
    },
    // 点击删除按钮
    delBtn(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delAccDept({ params: { uuid: row.uuid } }).then((res) => {
            if (res.code == 0) {
              this.$message.success(res.message);
              this.initTable();
            } else {
              this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });;
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 点击确定按钮
    confirmBtn() {
      this.$refs.diaForm.validate((valid) => {
        if (valid) {
          if (this.diaForm.uuid == "") {
            // 新增操作
            addAccDept(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });;
              }
            });
          } else {
            // 编辑操作
            editAccDept(this.diaForm).then((res) => {
              if (res.code == 0) {
                this.$message.success(res.message);
                this.initTable();
                this.diaShow = false;
              } else {
                this.$message({
            message: res.message,
            type: "error",
            duration: "5000",
          });;
              }
            });
          }
        } else {
          this.$message.warning("请完善信息后再操作");
          return false;
        }
      });
    },
    // 表格 kind 数据格式
    kindFormat(row, col) {
      if (row.kind == 1) {
        return "一卡通部门";
      } else if (row.kind == 2) {
        return "本地部门";
      } else {
        return "未知";
      }
    },
    // 分页函数
    changePage(pageData) {
      this.pageData = pageData;
      // 页面刷新
      this.initTable();
    },
  },
  created() {
    this.initTable();
  },
  components: {
    Paging,
  },
};
</script>
<style>
.el-pagination {
  margin-top: 1% !important;
}
.el-table td {
  padding: 4px 0;
}
.el-table th {
  padding: 4px 0;
  background: #dadce1;
  color: #1a1a1a;
}
</style>