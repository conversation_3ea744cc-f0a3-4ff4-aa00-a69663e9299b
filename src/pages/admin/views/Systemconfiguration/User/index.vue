<template>
  <div class="box">
    <el-tabs type="card" @tab-click="handleLoad">
      <el-tab-pane
        label="账户管理"
        lazy
        v-if="$route.meta.options.includes('account:get') && AuthorityTest"
        ><Account></Account
      ></el-tab-pane>
      <el-tab-pane
        label="班级管理"
        lazy
        v-if="$route.meta.options.includes('accClass:page') && AuthorityTest"
        ><Classes></Classes
      ></el-tab-pane>
      <el-tab-pane
        label="学院管理"
        lazy
        v-if="$route.meta.options.includes('accDept:page') && AuthorityTest"
        ><Department></Department
      ></el-tab-pane>
      <el-tab-pane
        label="专业管理"
        lazy
        v-if="$route.meta.options.includes('accMajor:page') && AuthorityTest"
        ><Professional></Professional
      ></el-tab-pane>
      <el-tab-pane
        label="负责人管理"
        lazy
        v-if="$route.meta.options.includes('responsible:person:page') && AuthorityTest"
        ><Person></Person 
      ></el-tab-pane>      
      <el-tab-pane label="账户管理" lazy v-if="!AuthorityTest"
        ><Account></Account
      ></el-tab-pane>
      <el-tab-pane label="班级管理" lazy v-if="!AuthorityTest"
        ><Classes></Classes
      ></el-tab-pane>
      <el-tab-pane label="学院管理" lazy v-if="!AuthorityTest"
        ><Department></Department
      ></el-tab-pane>
      <el-tab-pane label="专业管理" lazy v-if="!AuthorityTest"
        ><Professional></Professional
      ></el-tab-pane>
      <el-tab-pane
        label="负责人管理"
        lazy
        v-if="!AuthorityTest"
        ><Person></Person 
      ></el-tab-pane>  
      <!-- <el-tab-pane label="专业管理" lazy>
        <Professional></Professional>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>
<script>
import Account from "./components/Account";
import Classes from "./components/Class";
import Department from "./components/Department";
import Professional from "./components/Professional";
import Person from "./components/Person";
export default {
  data() {
    return {
      AuthorityTest: window.g.AuthorityTest,
      activeindex: "",
    };
  },
  methods: {
    handleLoad(tab, event) {
      this.activeindex = tab.index;
    },
  },
  created() {},
  components: {
    Account,
    Classes,
    Department,
    Professional,
    Person
  },
};
</script>
<style>
.el-tabs__header {
  margin: 0 0 0 0px;
}
</style>
