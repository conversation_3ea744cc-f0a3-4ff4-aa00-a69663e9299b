<template>
  <div></div>
</template>
<script>
import { authLogin } from "@admin/api/login";
import { getUrlKey } from "@admin/utils/index";
export default {
  name: "login",
  data() {
    return {
      redirect: undefined,
    };
  },
  methods: {},
  watch: {
    // $route: {
    //   handler: function (route) {
    //     this.redirect = route.query && route.query.redirect;
    //   },
    //   immediate: true,
    // },
  },
  created() {
    if (getUrlKey("uniToken")) {
      let json = {
        uniToken: getUrlKey("uniToken"),
      };
      authLogin(json).then((res) => {
        if (res.code == 0) {
          // this.$router.push({ path: this.redirect || "/" });
        } else {
          // this.$store.dispatch("user/thirdlogin");
        }
      });
    }
    // this.$store
    //   .dispatch("user/thirdlogin")
    //   .then(() => {
    //     this.$router.push({ path: this.redirect || "/" });
    //   })
    //   .catch(() => {
    //     this.loading = false;
    //   });
  },
};
</script>
<style lang="scss"></style>
