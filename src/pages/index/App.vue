<template>
  <div id="app" :style="colorstyle" :data-theme="currentTheme">
    <router-view />
  </div>
</template>

<script>
import { getconfig } from "@admin/api/Dailymanagement/SiteSettings";
export default {
  data() {
    return {
      colorstyle: "",
      currentTheme: "浅蓝",
    };
  },
  created() {
    this.currentTheme = window.g.themeColorStyle;
    getconfig("showflag").then((res) => {
      if (res.code == 0) {
        if (res.data.length != 0) {
          if (res.data[0].sysValue == "1") {
            this.colorstyle = "filter: grayscale(100%)";
          }
        } else {
          this.colorstyle = "";
        }
      }
    });
  },
};
</script>

<style lang="scss">
@import "@index/css/global.scss";
</style>
