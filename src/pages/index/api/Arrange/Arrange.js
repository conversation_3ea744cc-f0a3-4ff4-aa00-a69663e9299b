import request from "@index/utils/request";

//获取今日课程数据-门户网站
export function getMemberCountTime(date, roomFloor, buildingNo) {
  return request({
    url: "/reserve/getMemberCountTime",
    method: "get",
    params: { date, roomFloor, buildingNo },
  });
}

//查看所有实验项目名称和id
export function GettestPlan(yearTermId, teacherAccNo) {
  return request({
    url: "/testPlan",
    method: "get",
    params: { yearTermId, teacherAccNo },
  });
}

//查看每个实验项目的相关数据
export function TestPlan(testPlanId, teacherAccNo, orderItems, orderRule) {
  return request({
    url: "/testPlan/getTestPlanById",
    method: "get",
    params: { testPlanId, teacherAccNo, orderItems, orderRule },
  });
}

//删除实验项目
export function DeltPlan(data) {
  return request({
    url: "/testPlan/delete",
    method: "post",
    data,
  });
}

//编辑修改实验项目
export function editPlan(data) {
  return request({
    url: "/testPlan/update",
    method: "post",
    data,
  });
}

//删除节次预约
export function DelTeachingtime(data) {
  return request({
    url: "/reserve/deleteApi",
    method: "post",
    data,
  });
}

//新增实验室项目
export function AddtestPlan(data) {
  return request({
    url: "/testPlan",
    method: "post",
    data,
  });
}

//查看学生
export function GetAllStudentens(classId, page, pageNum, search) {
  return request({
    url: "/testPlan/getStudent",
    method: "get",
    params: { classId, page, pageNum, search },
  });
}

//查看学生-单个删除学生
export function delschoolClassMember(data) {
  return request({
    url: "/schoolClassMember/api/delete",
    method: "post",
    data,
  });
}

//查看学生-批量删除学生
export function delBatchschoolClassMember(data) {
  return request({
    url: "/schoolClassMember/api/deleteBatch",
    method: "post",
    data,
  });
}

//学期数据下拉框
export function getAllYearTerm(data) {
  return request({
    url: "/yearTerm/getAllYearTerm",
    method: "get",
    data,
  });
}

//课程数据下拉框
export function getAllCourse(key, num) {
  return request({
    url: "/course/getAll",
    method: "get",
    params: { key, num },
  });
}

//上课班级下拉框
export function getAllClass(yearTermId) {
  return request({
    url: "/testPlan/getAllClass",
    //url: '/accClass/getAll',
    method: "get",
    params: { yearTermId },
  });
}

//字典表获取所有类型
export function getType(data) {
  return request({
    url: "/codingTable/getType",
    method: "get",
    data,
  });
}

//获取大的类型下的所有子类型
export function getTypeAll(codeType) {
  return request({
    url: "/codingTable/getAll",
    method: "get",
    params: { codeType },
  });
}

//教师端-新增课程班成员
export function addschoolclassmate(data) {
  return request({
    url: "/schoolClassMember/api/save",
    method: "post",
    data,
  });
}

//教师端-删除课程班成员
export function delschoolclassmate(data) {
  return request({
    url: "/schoolClassMember/api/delete",
    method: "post",
    data,
  });
}

//课程备注-获取列表
export function getcoursememo(params) {
  return request({
    url: "/api/course/memo",
    method: "get",
    params,
  });
}

//课程备注-添加备注
export function addcoursememo(data) {
  return request({
    url: "/api/course/memo",
    method: "post",
    data,
  });
}

//课程备注-删除备注
export function delcoursememo(data) {
  return request({
    url: "/api/course/memo/delete",
    method: "post",
    data,
  });
}

//教师端-当天排课
export function teacherArrangementCourse(data) {
  return request({
    url: "/api/reserve/teacherArrangementCourse",
    method: "post",
    data,
  });
}

//楼宇下拉框-用户端
export function getAllbuilding(key, num, campusId) {
  return request({
    url: "/building/getApiAll",
    method: "get",
    params: { key, num, campusId },
  });
}

//获取该老师绑定的课程和班级
export function getTeachercourse() {
  return request({
    url: "/api/testPlan/teacher/course",
    method: "get",
  });
}

//用户端获取校区下拉框
export function getAllCampus() {
  return request({
    url: "/campus/getApiAll",
    method: "get",
  });
}

//获取教学项目列表
export function gettestPlanitem(params) {
  return request({
    url: "/api/testPlan/item",
    method: "get",
    params,
  });
}

//新增教学项目
export function addtestPlanitem(data) {
  return request({
    url: "/api/testPlan/item",
    method: "post",
    data,
  });
}

//修改教学项目
export function updatetestPlanitem(data) {
  return request({
    url: "/api/testPlan/item/update",
    method: "post",
    data,
  });
}

//删除教学项目
export function deltestPlanitem(data) {
  return request({
    url: "/api/testPlan/item/delete",
    method: "post",
    data,
  });
}

//删除教学项目-批量
export function delBatchtestPlanitem(data) {
  return request({
    url: "/api/testPlan/item/deleteBatch",
    method: "post",
    data,
  });
}

//教师端-导入教学项目
export function importtestPlan(data) {
  return request({
    url: "/api/testPlan/item/import",
    method: "post",
    data,
  });
}

//教师端-日历排课
export function teacherCalendarArrangementCourse(data) {
  return request({
    url: "/api/reserve/teacherCalendarArrangementCourse",
    method: "post",
    data,
  });
}

//教师端-根据日期换算出周次
export function getWeekByDate(params) {
  return request({
    url: "/yearTerm/getWeekByDate",
    method: "get",
    params,
  });
}

//教师端-获取空闲实验室
export function getAvailableRoom(params) {
  return request({
    url: "/api/reserve/getAvailableRoom",
    method: "get",
    params,
  });
}

//教师端-编辑预约
export function updateArrangementCourse(data) {
  return request({
    url: "/api/reserve/updateArrangementCourse",
    method: "post",
    data
  });
}
