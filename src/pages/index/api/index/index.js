import request from '@admin/utils/request';

export function getDict (pageNum, pageSize, dictTypeSn) {
  return request({
    url: '/codingTable',
    method: 'get',
    params: { pageNum, pageSize, dictTypeSn }
  })
}
/*
   联系我们
*/
export function templatePreview (params) {
  return request({
    url: '/api/home/<USER>/preview',
    method: 'get',
    params
  })
}
// 首页菜单树 ✅
export function getMenuTree (params) {
  return request({
    url: '/api/home/<USER>/tree',
    method: 'get',
    params
  })
}
// 首页日常服务列表 （首页应用分类及应用列表）✅
export function getSubAppList (params) {
  return request({
    url: '/api/home/<USER>/list',
    method: 'get',
    params
  })
}
// 首页区块列表（菜单栏需在首页展示的接口） 半✅
export function getBlockLink (params) {
  return request({
    url: '/api/home/<USER>/list',
    method: 'get',
    params
  })
}
// 首页友情链接列表 ✅
export function getFriendLink (params) {
  return request({
    url: '/api/home/<USER>/list',
    method: 'get',
    params
  })
}

// 菜单文章列表
export function getMenuArticlePage (params) {
  return request({
    url: '/api/home/<USER>/page',
    method: 'get',
    params
  })
}
// 菜单文章详情
export function getMenuArticleDetail (params) {
  return request({
    url: '/api/home/<USER>/detail',
    method: 'get',
    params
  })
}
// 菜单下载内容列表
export function getMenuDownloadPage (params) {
  return request({
    url: '/api/home/<USER>/page',
    method: 'get',
    params
  })
}
// 菜单富文本详情
export function getRichTextDetail (params) {
  return request({
    url: '/api/home/<USER>/rich-text-detail',
    method: 'get',
    params
  })
}
//获取全部banner
export function getbanners (params) {
  return request({
    url: '/api/banner/image/list',
    method: 'get',
    params
  })
}
//获取新闻列表
export function getNews (params) {
  return request({
    url: '/api/news/list',
    method: 'get',
    params,
  })
}

//获取新闻详情
export function getNewsDetail (params) {
  return request({
    url: '/api/news/detail',
    method: 'get',
    params
  })
}