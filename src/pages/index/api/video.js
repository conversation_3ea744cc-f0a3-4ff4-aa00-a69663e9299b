import request from '@index/utils/request';
//获取视频列表
export function getvideos(pageNum,pageSize,title,categoryCode,status,author) {
    return request({
        url: '/video',
        method: 'get',
        params:{pageNum,pageSize,title,categoryCode,status,author}
    })
}
//视频审核
export function reviewvideo(data){
    return request({
        url: '/video/review',
        method: 'post',
        data
    })
}
//删除视频
export function delvideo(data){
    return request({
        url: '/video/delete',
        method: 'post',
        data
    })
}

//获取全部视频
export function getallvideo(pageNum,pageSize,keyword,categoryCode){
    return request({
        url: '/video/api',
        method: 'get',
        params:{pageNum,pageSize,keyword,categoryCode}
    })
}

//获取个人视频
export function getpersonalvideo(pageNum,pageSize,keyword,status,title,categoryCode,){
    return request({
        url: '/video/personal',
        method: 'get',
        params:{pageNum,pageSize,keyword,status,title,categoryCode}
    })
}

//视频信息上传
export function uploadvideoinfo(data){
    return request({
        url: '/video',
        method: 'post',
        data
    })
}

//删除视频
export function delvideoupload(data){
    return request({
        url: '/video/api/delete',
        method: 'post',
        data
    })
}

//编辑视频
export function editvideo(data){
    return request({
        url: '/video/update',
        method: 'post',
        data
    })
}

//视频上传
export function uploadvideo(data){
    return request({
        url: '/video/upload',
        method: 'post',
        data
    })
}