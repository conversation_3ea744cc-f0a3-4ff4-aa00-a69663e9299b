<template>
  <div>
    <transition name="fade">
      <div class="top-btn" v-if="showBackTop" @click="scrollToTop">
        <i class="el-icon-arrow-up"></i>
        <span>TOP</span>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  name: "BackTop",
  data() {
    return {
      showBackTop: false,
    };
  },
  mounted() {
    window.addEventListener("scroll", this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    handleScroll() {
      this.showBackTop = window.pageYOffset > 1000;
    },
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";

.top-btn {
  width: 60px;
  height: 60px;
  position: fixed;
  right: 40px;
  bottom: 480px;
  z-index: 100;
  background: var(--themeMainColor);
  color: #fff;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.4s;
  .el-icon-arrow-up {
    font-size: 20px;
  }
}
.top-btn:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
