<template>
  <div class="card">
    <span class="ellipsis">{{ title }}</span>
    <img class="arrow" src="@index/assets/images/Home/right.png" />
  </div>
</template>
<script>
export default {
  name: "CardItem",
  props: {
    item: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    title() {
      return [1, 2, 3, 4, 5].includes(this.item.kind) ? this.item.menuName : this.item.title;
    },
  },
};
</script>
<style lang="scss" scoped>
.card {
  width: 100%;
  height: 100%;
  padding: 0 30px 0 42px;
  font-size: 22px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f2f3fb;
  cursor: pointer;
  position: relative;
  .arrow {
    width: 25px;
    height: 15px;
  }
}
.card::before {
  content: "";
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--themeMainColor);
  z-index: 9;
}
.card.active,
.card:hover {
  background: var(--themeMainColor);
  color: #fff;
  .arrow {
    opacity: 1;
  }
}
.card.active::before,
.card:hover::before {
  background: #fff;
}
.card .arrow {
  opacity: 0;
  font-size: 22px;
}
</style>
