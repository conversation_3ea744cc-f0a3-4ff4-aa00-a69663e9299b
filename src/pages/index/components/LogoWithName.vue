<template>
  <div class="navbar-left">
    <img :src="logo" alt="logo" class="navbar-logo" />
    <div class="line"></div>
    <div class="navbar-titles">
      <div class="navbar-title-sub nowarp">{{ name }}</div>
      <div class="navbar-title-en-sub ellipsis2">{{ enName }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "LogoWithName",
  props: {
    logo: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    enName: {
      type: String,
      default: "",
    },
  },
};
</script>
<style lang="scss" scoped>
.navbar-left {
  display: flex;
  align-items: center;
  .navbar-logo {
    width: 209px;
    height: 64px;
  }
  .line {
    width: 1px;
    height: 50px;
    background: rgba(255, 255, 255, 0.48);
    margin: 0 18px;
  }
  .navbar-titles {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .navbar-title-sub {
      font-size: 26px;
      color: #fff;
      font-weight: bold;
      margin-top: 2px;
    }
    .navbar-title-en-sub {
      font-size: 12px;
      color: #fff;
      letter-spacing: 0.5px;
    }
  }
}
</style>
