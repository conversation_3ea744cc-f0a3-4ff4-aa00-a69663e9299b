<template>
  <div class="news-item" @click="handleClick">
    <div v-if="showImage" class="news-image">
      <img class="swiper-item-img"
        :src="item.newsImage ? location + item.newsImage : require('@index/assets/images/Home/news.png')"
        fit="cover" alt="加载错误"></img>

    </div>
    <div class="news-content">
      <div class="news-title ellipsis">{{ item.title }}</div>
      <div class="news-desc ellipsis2">{{ item.newsSummary }}</div>
      <div class="news-date">{{ item.publishDate }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NewsItem",
  props: {
    item: {
      type: Object,
      required: true,
    },
    showImage: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl + "/",
    };
  },
  methods: {
    handleClick() {
      this.$emit("itemClick", this.item);
    },
  },
};
</script>

<style lang="scss" scoped>
.news-item {
  display: flex;
  margin-bottom: 10px;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 60px 0;
    transform: translateY(-3px);
  }

  .news-image {
    width: 300px;
    height: 180px;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  .news-content {
    flex: 1;
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #fff;

    .news-title {
      font-size: 20px;
      color: #333;
      font-weight: 500;
      margin-bottom: 15px;
    }

    .news-desc {
      font-size: 16px;
      color: #666;
      margin-bottom: 15px;
    }

    .news-date {
      font-size: 14px;
      color: var(--themeMainColor);
    }

    &:hover {
      background-color: var(--themeMainColor);
      border-radius: 0 0 60px 0;

      .news-title,
      .news-desc,
      .news-date {
        color: #ffffff;
      }
    }
  }
}
</style>
