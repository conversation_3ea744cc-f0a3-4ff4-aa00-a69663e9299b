<template>
  <div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.pageNum"
      :page-sizes="[10, 15, 20, 25, 30]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.pageCount"
    ></el-pagination>
    <!-- <i class="el-icon-refresh-right" @click="refersh"></i> -->
  </div>
</template>
<script>
export default {
  props: {
    pagination: {
      type: Object,
      default: () => ({
        pageNum: 1,
        pageSize: 10,
        pageCount: 0,
      }),
    },
  },
  data() {
    return {
      data: {
        orderItems: "publishDate,sid",
        orderRule: "desc",
      },
    };
  },
  methods: {
    handleSizeChange(val) {
      this.$emit("Pagebar", { ...this.pagination, pageSize: val });
    },
    handleCurrentChange(val) {
      this.$emit("Pagebar", { ...this.pagination, pageNum: val });
    },
    refersh() {
      this.$emit("Pagebar", { ...this.pagination });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-pagination button,
.el-pagination span:not([class*="suffix"]) {
  font-size: 12px;
}
.el-pagination {
  text-align: center;
  // float: right;
}
::v-deep .el-pager {
  .active,
  li:hover {
    color: var((--themeMainColor));
  }
}
</style>
