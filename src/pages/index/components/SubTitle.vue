<template>
  <div class="title">
    <img :src="iconLocal" class="title-icon" v-if="title && iconLocal" />
    <img :src="location + iconSvg" class="title-icon" v-else-if="title && iconSvg" />
    <i class="title-icon iconfont " :class="icon" v-else-if="title && icon"></i>
    <!-- <svg class="icon" aria-hidden="true" v-else-if="title && icon">
      <use :xlink:href="`#${icon}`"></use>
    </svg> -->
    <span class="title-text" :class="{ fs18 }" v-if="title">{{ title }}</span>
    <slot>
      <span v-if="showMore" class="title-more" @click="handleMore">Read More &gt;</span>
    </slot>
  </div>
</template>
<script>
export default {
  name: "SubTitle",
  props: {
    fs18: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    iconLocal: {
      type: String,
      default: "",
    },
    iconSvg: {
      type: String,
      default: "",
    },
    icon: {
      type: String,
      default: "",
    },
    showMore: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl + "/",
    };
  },
  methods: {
    handleMore() {
      this.$emit("handleMore");
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  display: flex;
  align-items: center;
  height: 35px;
}
.title-icon {
  width: 32px;
  margin-right: 7px;
  color: var(--themeMainColor);
}
.title-text {
  font-size: 22px;
  font-weight: bold;
  font-weight: 500;
  font-size: 26px;
  color: var(--themeMainColorDeep);
}
.iconfont {
  font-size: 30px;
}
.icon {
  width: 30px;
  height: 30px;
  color: var(--themeMainColorDeep);
}
.fs18 {
  font-size: 18px;
}
.title-more {
  margin-left: auto;
  cursor: pointer;
  width: 110px;
  height: 35px;
  background: #D1D3E8;
  border-radius: 17px;
  text-align: center;
  line-height: 35px;
  font-family: Arial;
  font-size: 14px;
  color: var(--themeMainColor);
}
.title-more:hover {
  background-color: var(--themeMainColor);
  color: #ffffff;
}
</style>
