$ratio: 750/10;
@function px2rem($px) {
    @return $px/$ratio + rem;
}
// $themeMainColor: #314099;
// $hemeSecondColor: #edb52e;
$bgc1 : rgb(246, 246, 246);
$bgred : #e25140;
$bggary: rgb(230, 230, 230);

:root {
  --themeMainColor: #314099;
  --themeMainColorDeep:#1E3968;
  --themeMainColorLucency: rgba(22, 36, 111, 0.76);
  --themeSecondColor: #edb52e;
}

[data-theme="浅红"] {
  --themeMainColor: #B11919;
  --themeMainColorDeep: #333333;
  --themeMainColorLucency: rgba(136, 9, 19, 0.76);
  --themeSecondColor: #edb52e;
}