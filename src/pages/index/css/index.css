* {
  padding: 0px;
  margin: 0px
}

html, body {
  width: 100%;
  height: 100%;
  background-color: #f8f9fc;
}

#app {
  width: 100%;
  height: 100%;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

p {
  margin-top: 0px;
  margin-bottom: 0px;
}

@media screen and (min-width: 1920px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 30%;
  }
}

@media screen and (max-width: 1920px) and (min-width: 1680px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 30%;
  }
}

@media screen and (max-width: 1680px) and (min-width: 1600px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 25%;
  }
}

@media screen and (max-width: 1600px) and (min-width: 1366px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 25%;
  }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 25%;
  }
}

@media screen and (max-width: 1280px) and (min-width: 795px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 20%;
  }
}

@media screen and (max-width: 795px) {
  .pubwidth{
    width: 100%;
  }
  .mask{
    right: 15%;
  }
}

.containerbox {
  width: 100%;
  /* height: 100%; */
}

ul, li {
  list-style-type: none;
}

.clearfix{
  display: block;
  content: "";
  clear: both;
}
.ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ellipsis2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nowarp {
  white-space: nowrap;
}
.default-img {
  background: url('~@index/assets/images/erweima.png') no-repeat 100% 100%;
}