import Vue from 'vue'
import App from '@index/App.vue'
import router from '@index/router'
import store from '@index/store/store'
// import '@index/utils/util.js';

// UI框架
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

// import MemoryManager from './memoryManager';
// Vue.use(MemoryManager, {
//   interval: 60000, // 每分钟检查一次
//   thresholdMB: 150, // 超过150MB触发清理
//   autoStart: true
// });

// 富文本编辑器样式
import "@wangeditor/editor/dist/css/style.css";
// 注册附件上传插件(配合wangeditor使用)
import { Boot } from "@wangeditor/editor";
import attachmentModule from "@wangeditor/plugin-upload-attachment";
Boot.registerModule(attachmentModule);

// web端自适应适配
import '@index/utils/flexible.js'
import { setRem } from '@index/plugins/setRem.js'
setRem('1920') // 代表设计稿的大小

// 插件引入
import Scroll from 'vue-seamless-scroll'
import waterfall from 'vue-waterfall2'
import Moment from 'moment'

// 插件注册
Vue.use(Scroll)
Vue.use(waterfall)
Moment.locale('zh-cn')
Vue.prototype.$moment = Moment

// 引入公共样式
import '@index/css/index.css'

// 全局状态管理
import myStore from '@index/views/Store'
Vue.prototype.$myStore = myStore

Vue.config.productionTip = false

new Vue({
  render: h => h(App),
  router,
  store
}).$mount('#app')
