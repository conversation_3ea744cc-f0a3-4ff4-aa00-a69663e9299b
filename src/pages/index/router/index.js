import Vue from 'vue'
import Router from 'vue-router'
import Index from '@index/views/index'
import store from '@index/store/store'

Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '*',
      redirect: '/home',
    },
    {
      path: '/index',
      name: 'index',
      component: Index,
      redirect: '/home',
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('@index/views/home/<USER>'),
        },
        {
          path: '/viewDetail',
          name: 'viewDetail',
          component: () => import('@index/views/viewDetail/index'),
        },
      ]
    },
  ],
  scrollBehavior (to, from, savedPosition) {
    return { x: 0, y: 0 }
  },
})

export default router