import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

// 获取用户权限
import createPersistedState from 'vuex-persistedstate'
let userInfo = {}
try {
  if (sessionStorage.userInfo) {
    userInfo = JSON.parse(sessionStorage.userInfo)
  }
} catch (e) { }
let isLogin = false
try {
  if (sessionStorage.isLogin) {
    isLogin = JSON.parse(sessionStorage.isLogin)
  }
} catch (e) { }

export default new Vuex.Store({
  state: {
    // 用户信息
    userInfo: userInfo,
    // 是否登录
    isLogin: isLogin,
    isCollapse: true,
    // 记录用户创建的预约信息
    newResearch: {},
    // 记录用户是否创建了一个预约
    createdStatus: false,
    // 记录是否显示登录
    isLoginShow: false,
    testPlain: '',//存下新建实验项目的id
    testHour: '',//存下新建实验项目的学时
    yearTerm: '',//存下新建项目的学期id
    reserve: '',//存放已有预约
    isfooter: false,//底部是否显示
    // 配置项
    config: {},
    uuid: sessionStorage.getItem("uuid") || null,
    activeUuid: sessionStorage.getItem("activeUuid") || '',
    activeUuidChild: sessionStorage.getItem("activeUuidChild") || '',
    module: sessionStorage.getItem("module") || {},//存放模块的相关内容
    indexdata: JSON.parse(localStorage.getItem("indexdata")) || {},//存放首页点击新闻的内容
    viewDetailFlag: sessionStorage.getItem("viewDetailFlag") || '',//存放首页点击非菜单栏项目 目前有新闻与公告
    saveViewListDetailUuid: sessionStorage.getItem("saveViewListDetailUuid") || '',
    activeData: JSON.parse(sessionStorage.getItem("activeData")) || {},//存放导航点击内容
    activeDataFather: JSON.parse(sessionStorage.getItem("activeDataFather")) || {},//存放导航点击内容父级
    activeDataChild: JSON.parse(sessionStorage.getItem("activeDataChild")) || {},//存放导航点击内容下级
    activeDataFatherChild: JSON.parse(sessionStorage.getItem("activeDataFatherChild")) || {},//存放导航点击内容下级父级
  },
  getters: {
    userInfo (state) {
      return state.userInfo
    },
    newResearch (state) {
      return state.newResearch
    },
    sysConfig (state) {
      return state.config
    },
    module (state) {
      return state.module
    },
    indexdata (state) {
      return state.indexdata
    },
    activeData (state) {
      return state.activeData
    },
    activeDataFather (state) {
      return state.activeDataFather
    },
    activeDataChild (state) {
      return state.activeDataChild
    },
    activeDataFatherChild (state) {
      return state.activeDataFatherChild
    }
  },
  mutations: {
    setUserInfo (state, val) {
      state.userInfo = val
      sessionStorage.setItem('userInfo', JSON.stringify(val))
    },
    setIsLogin (state, val) {
      state.isLogin = val
      sessionStorage.setItem('isLogin', val)
    },
    setCollapse (state, val) {
      state.isCollapse = val
    },
    setNewResearch (state, val) {
      state.newResearch = val
    },
    setCreatedStatus (state, val) {
      state.createdStatus = val
    },
    setIsLoginShow (state, val) {
      state.isLoginShow = val
    },
    setTestPlain (state, val) {
      state.testPlain = val
    },
    setTestHour (state, val) {
      state.testHour = val
    },
    setyearTerm (state, val) {
      state.yearTerm = val
    },
    setReserve (state, val) {
      state.reserve = val
    },
    setFooter (state, val) {
      state.isfooter = val
    },
    'SET_CONFIG' (state, val) {
      return state.config = val
    },
    saveUuid (state, uuid) {
      state.uuid = uuid;
      sessionStorage.setItem("uuid", JSON.stringify(state.uuid))
    },
    saveActiveUuid (state, activeUuid) {
      state.activeUuid = activeUuid;
      sessionStorage.setItem("activeUuid", state.activeUuid)
    },
    saveActiveUuidChild (state, activeUuidChild) {
      state.activeUuidChild = activeUuidChild;
      sessionStorage.setItem("activeUuidChild", state.activeUuidChild)
    },
    setModule (state, val) {
      state.module = val
      sessionStorage.setItem('module', JSON.stringify(val))
    },
    setIndexdata (state, val) {
      state.indexdata = val
      localStorage.setItem('indexdata', JSON.stringify(val))
    },
    viewDetailFlag (state, val) {
      state.viewDetailFlag = val
      sessionStorage.setItem('viewDetailFlag', val)
    },
    saveViewListDetailUuid (state, val) {
      state.saveViewListDetailUuid = val
      sessionStorage.setItem('saveViewListDetailUuid', val)
    },
    saveActiveData (state, val) {
      if (val.length > 1) {
        state.activeData = val[0]
        state.activeDataFather = val[1]
        sessionStorage.setItem('activeData', JSON.stringify(val[0]))
        sessionStorage.setItem('activeDataFather', JSON.stringify(val[1]))
      } else {
        state.activeData = val[0]
        sessionStorage.setItem('activeData', JSON.stringify(val[0]))
      }
      // if (val[0].kind === 1 && val[0].submenuStyle === 1) {
      //   saveActiveDataChild(state, [val[0], val[0].children[0]])
      // }
    },
    saveActiveDataChild (state, val) {
      console.log(val, 'store');
      if (val.length > 1) {
        state.activeDataChild = val[0]
        state.activeDataFatherChild = val[1]
        sessionStorage.setItem('activeDataChild', JSON.stringify(val[0]))
        sessionStorage.setItem('activeDataFatherChild', JSON.stringify(val[1]))
      } else {
        state.activeDataChild = val[0]
        sessionStorage.setItem('activeDataChild', JSON.stringify(val[0]))
      }
    },
    initClear (state) {
      state.activeUuid = ''
      state.activeUuidChild = ''
      state.activeData = {}
      state.activeDataFather = {}
      state.activeDataChild = {}
      state.activeDataFatherChild = {}
      sessionStorage.setItem('activeData', JSON.stringify({}))
      sessionStorage.setItem('activeDataFather', JSON.stringify({}))
      sessionStorage.setItem('activeDataChild', JSON.stringify({}))
      sessionStorage.setItem('activeDataFatherChild', JSON.stringify({}))
    }
  },
  actions: {
  },
  plugins: [createPersistedState({
    storage: window.sessionStorage,
    reducer (val) {
      return {

      }
    }
  })]
})