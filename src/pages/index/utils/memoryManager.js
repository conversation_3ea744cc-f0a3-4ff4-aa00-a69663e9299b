export default {
  install (Vue, options = {}) {
    const interval = options.interval || 30000; // 默认30秒
    const thresholdMB = options.thresholdMB || 100; // 默认100MB阈值

    let timer = null;

    // 添加到Vue原型，可在组件中使用
    Vue.prototype.$memoryManager = {
      start () {
        if (timer) return;

        timer = setInterval(() => {
          this.checkMemory();
        }, interval);
      },

      stop () {
        if (timer) {
          clearInterval(timer);
          timer = null;
        }
      },

      checkMemory () {
        if (!window.performance || !window.performance.memory) return;

        const memoryInfo = window.performance.memory;
        const usedHeapSizeMB = memoryInfo.usedJSHeapSize / (1024 * 1024);

        if (usedHeapSizeMB > thresholdMB) {
          console.warn(`内存使用超过阈值: ${usedHeapSizeMB.toFixed(2)}MB`);
          this.cleanup();
        }
      },

      cleanup () {
        // 强制垃圾回收的一些技巧
        // 注意：这些方法并不能直接触发GC，只是帮助释放一些引用

        // 1. 清理Vue组件缓存
        if (Vue.prototype.$root && Vue.prototype.$root.$store) {
          // 如果使用了Vuex，可以清理一些不必要的状态
          Vue.prototype.$root.$store.dispatch('clearUnusedCache');
        }

        // 2. 清理可能的事件监听器
        // 这需要在应用中正确实现

        // 3. 尝试释放一些内存
        try {
          const largeArrays = [];
          for (let i = 0; i < 10; i++) {
            largeArrays.push(new Array(1000000));
          }
          largeArrays.length = 0;
        } catch (e) {
          console.log('尝试释放内存');
        }
      }
    };

    // 自动启动
    if (options.autoStart !== false) {
      Vue.prototype.$memoryManager.start();
    }
  }
};