import axios from 'axios'
import store from '@index/store/store'
import router from '@index/router/index'
import { MessageBox, Message } from 'element-ui'
// import { getToken } from '@index/utils/auth'
import 'element-ui/lib/theme-chalk/message.css'
let baseURL = '';
let env = process.env.NODE_ENV == 'development' ? 'development' : 'production';
if (env == 'development') {
  baseURL = "/lab"
} else if (env == 'production') {
  // baseURL = process.env.VUE_APP_URL
  baseURL = window.g.ApiUrl
}
const service = axios.create({
  baseURL: baseURL,
  timeout: 50000 // request timeout
})
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['X-Token'] = getToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 0) {
      //判断未登录
      if (res.code == 300) {
        // Message({
        //   message: res.message || 'Error',
        //   type: 'error',
        //   duration: 5 * 1000
        // })
        // store.commit('setIsLoginShow', true);
        // router.replace({
        //   path: "/lab",
        //   query: {
        //     t: Date.now(),
        //   },
        // });
      } if (res.code == 302) {
        // Message({
        //   message: res.message || 'Error',
        //   type: 'error',
        //   duration: 5 * 1000
        // })
        // store.commit('setIsLoginShow', true);
        // router.replace({
        //   path: "/lab",
        //   query: {
        //     t: Date.now(),
        //   },
        // });
      } if (res.code == 2) {
        return res
      } else {
        Message({
          message: res.message || 'Error',
          type: 'error',
          duration: 1000
        })
      }
    } else {
      return res
    }
  },
  error => {
    // console.log('err' + error) // for debug
    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 1000
    // })
    // store.commit('setIsLoginShow', true);
    router.replace({
      path: "/lab",
      query: {
        t: Date.now(),
      },
    });
    return Promise.reject(error)
  }
)

export default service
