<template>
  <div class="home-content-box">
    <News></News>
    <!-- 主体内容 -->
    <DynamicContent></DynamicContent>
  </div>
</template>
<script>
import { News, DynamicContent } from "@index/views/home/<USER>";
export default {
  components: {
    News,
    DynamicContent,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
.home-content-box {
  background-image: url("~@index/assets/images/index/bg.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
