<template>
  <div class="dynamic-content">
    <!-- 后续可拓展选择不同主体内容 -->
    <DailyServices />
    <!-- 此处需动态遍历标题下应展示在首页的不同主体内容模块 -->
    <template v-for="item in homePageList">
      <component :componentInfo="item" :is="item.componentName" @handCardItem="handCardItem" @handleMore="handleMore" />
    </template>
    <FriendLinks />
  </div>
</template>

<script>
import { DailyServices, FriendLinks, ServiceGuide, ServiceGuideTwo, PublicInfo, PublicInfoTwo } from "@index/views/home/<USER>";

import { getBlockLink } from "@index/api/index/index";

import { safeDownloadFile } from "@index/utils/download";

// 目前首页根据后期接口动态展示的有 服务指南和公示信息两种大方向的数据格式布局
// 后续在这两种格式里，各自新增不同风格ui布局供选择
const dynamicComponents = {
  ServiceGuide,
  PublicInfo,
  ServiceGuideTwo,
  PublicInfoTwo,
};
const dynamicComponentsList = [ServiceGuide, PublicInfo, ServiceGuideTwo, PublicInfoTwo];
export default {
  components: {
    DailyServices,
    FriendLinks,
    ...dynamicComponents,
  },
  data() {
    return {
      dynamicComponents,
      dynamicComponentsList,
      homePageList: [],
    };
  },
  created() {
    this.getBlockLink();
  },
  methods: {
    async getBlockLink() {
      const response = await getBlockLink();
      if (response.code === 0 && Array.isArray(response.data)) {
        this.homePageList = response.data
          .filter((item) => item.childrenSourceDataList.length)
          .map((item, index) => {
            // 内层uuid为菜单id，外层已被覆盖，如有用到外层，再说
            item = { ...item, ...item.sourceData };
            item.children = item.childrenSourceDataList;
            delete item.childrenSourceDataList;
            item.componentName = this.dynamicComponentsList[index % this.dynamicComponentsList.length];
            return item;
          });
      } else {
        this.homePageList = [];
      }
    },
    handCardItem(componentInfo, tabIndex, index) {
      let tabs = componentInfo.children;
      // 判断子类型是什么，根据不同类型跳转不同功能
      let itemFather = tabs[tabIndex];
      let item = tabs[tabIndex].children[index];
      if (item.kind === 2) {
        window.open(item.hyperlinks, "_blank");
      } else if (itemFather.kind === 5) {
        safeDownloadFile({
          url: item.attachmentUrl,
          fileName: item.fileName,
        });
      } else {
        this.$store.commit("saveViewListDetailUuid", item.uuid);
        this.handleMore(componentInfo, tabIndex);
      }
    },
    handleMore(componentInfo, tabIndex) {
      let data = componentInfo.children[tabIndex];
      let dataFather = componentInfo;
      this.activeUuid = data.uuid;
      this.$store.commit("saveActiveUuid", this.activeUuid);
      this.$store.commit("saveActiveData", [data, dataFather]);
      // 后续修改成新网页跳转
      this.$router
        .push({
          name: "viewDetail",
        })
        .catch((err) => {});
      // 打开新窗口
      // const route = this.$router.resolve({
      //   name: "viewDetail",
      // });
      // window.open(route.href, "_blank");
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";
// 大的模块布局控左右， 小的模块布局控上下
.dynamic-content {
  padding: 0 110px;
}
</style>
