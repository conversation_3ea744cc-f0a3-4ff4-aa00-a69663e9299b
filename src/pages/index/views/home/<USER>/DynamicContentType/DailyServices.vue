<template>
  <div class="daily-services">
    <SubTitle title="日常服务" icon="icon-xingzhuang" />
    <div class="services-container">
      <div v-for="(category, catIndex) in serviceData" :key="category.uuid" class="service-category">
        <div class="category-header">
          <img class="category-icon" :src="location + category.categorySvg" />
          <div class="category-title">{{ category.categoryName }}</div>
        </div>
        <div class="category-items">
          <div v-for="(item, itemIndex) in category.appList" :key="item.uuid" class="category-item ellipsis" @click="handleItemClick(item)">
            {{ item.appName }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { SubTitle } from "@index/components";

import { getSubAppList } from "@index/api/index/index";

export default {
  name: "DailyServices",
  components: {
    SubTitle,
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + window.g.ApiUrl,
      serviceData: [],
    };
  },
  created() {
    this.getSubAppList();
  },
  methods: {
    formatUrl(url) {
      if (!url) return "";
      // 如果URL不是以http://或https://开头，则添加https://
      if (!/^https?:\/\//i.test(url)) {
        return "https://" + url;
      }
      return url;
    },
    handleItemClick(item) {
      // 判断是否为移动设备
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // 根据设备类型选择跳转URL
      let targetUrl = isMobile ? item.mobileUrl : item.pcUrl;
      // 如果URL存在则在新窗口打开
      if (targetUrl) {
        targetUrl = this.formatUrl(targetUrl);
        window.open(targetUrl, "_blank");
      }
    },
    async getSubAppList() {
      const response = await getSubAppList();
      if (response.code === 0 && Array.isArray(response.data)) {
        this.serviceData = response.data;
      } else {
        this.serviceData = [];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";

.daily-services {
  margin-bottom: 30px;
  width: 100%;

  .sub-title {
    margin-left: 40px;
    margin-top: 32px;
  }
}

.services-container {
  margin: 40px auto;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 40px;
}

.service-category {
  flex: 1;
  width: 395px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 24px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: calc(100% - 26px);
  padding: 22px 0 20px 26px;
}

.category-icon {
  width: 56px;
  height: 56px;
  margin-right: 16px;
  object-fit: contain;
}

.category-title {
  font-size: 24px;
  color: #333333;
  font-weight: bold;
}

.category-items {
  width: calc(100% - 100px);
  height: 378px;
  overflow: hidden;
  padding: 0 50px;
  margin-bottom: 22px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-item {
  width: 100%;
  height: 47px;
  background: #f2f6fe;
  border-radius: 8px;
  line-height: 47px;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: var(--themeMainColor);
    color: #ffffff;
  }

  &.active {
    background-color: var(--themeMainColor);
    color: #ffffff;
    font-weight: 500;
  }
}
</style>
