<template>
  <div class="friend-links-wrapper">
    <!-- <SubTitle title="友情链接" :iconLocal="require('@index/assets/images/Home/friendLinkIcon.png')"></SubTitle> -->
    <SubTitle title="友情链接" icon="icon-youqinglianjie"></SubTitle>
    <div class="links">
      <div v-for="link in links" :key="link.sid" class="link-card" @click="btnClick(link.linkUrl)">
        <img v-if="link.linkSvg" class="link-svg" :src="location + link.linkSvg" />
        <i v-else-if="link.linkIcon" class="iconfont link-icon-font" :class="link.linkIcon"></i>
        <!-- <svg class="icon" aria-hidden="true" v-else-if="link.linkIcon">
          <use :xlink:href="`#${link.linkIcon}`"></use>
        </svg> -->
        <span v-else class="link-icon-font">🔗</span>
        <a :href="formatUrl(link.linkUrl)" target="_blank" class="link-menu">{{ link.linkName }}</a>
      </div>
    </div>
  </div>
</template>
<script>
import { SubTitle } from "@index/components";

import { getFriendLink } from "@index/api/index/index";
export default {
  name: "FriendLinks",
  components: {
    SubTitle,
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + window.g.ApiUrl,
      links: [],
    };
  },
  created() {
    this.fetchFriendLinks();
  },
  methods: {
    // 格式化URL，确保有http/https前缀
    formatUrl(url) {
      if (!url) return "";
      // 如果URL不是以http://或https://开头，则添加https://
      if (!/^https?:\/\//i.test(url)) {
        return "https://" + url;
      }
      return url;
    },
    async fetchFriendLinks() {
      const response = await getFriendLink();
      if (response.code === 0 && Array.isArray(response.data)) {
        this.links = response.data;
      } else {
        this.links = [];
      }
    },
    btnClick(linkUrl) {
      window.open(this.formatUrl(linkUrl), "_blank");
    },
  },
};
</script>
<style scoped>
.friend-links-wrapper {
  padding: 40px 0 50px;
}
.links {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 40px;
  margin: 0 auto;
  margin-top: 35px;
}
.icon {
  width: 30px;
  height: 30px;
}
.link-card {
  width: 172px;
  height: 76px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  box-shadow: 0px 1px 3px 0px rgba(102, 102, 102, 0.09);
  border-radius: 7px;
  border: 1px solid #f2f4f5;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.link-card:hover {
  box-shadow: 0px 1px 3px 0px rgba(102, 102, 102, 0.3);
  transform: translateY(-5px);
}
.link-svg {
  width: 30px;
  height: 25px;
  margin-right: 12px;
}
.link-icon-font {
  font-size: 26px;
  margin-right: 12px;
  color: var(--themeMainColor);
}
.link-menu {
  font-size: 22px;
  color: var(--themeMainColor);
  text-decoration: none;
}
.loading {
  text-align: center;
  margin-top: 35px;
  font-size: 16px;
  color: #999;
}
</style>
