<template>
  <div class="public-info">
    <SubTitle :title="componentInfo.blockName" :icon="componentInfo.blockIcon" :iconSvg="componentInfo.blockSvg" />
    <div
      class="info-columns"
      :class="
        componentInfo.children.length === 2
          ? 'gap75'
          : componentInfo.children.length === 3
          ? 'gap150'
          : componentInfo.children.length > 4
          ? 'gap15'
          : 'gap50'
      "
    >
      <div class="info-col" v-for="(item, tabIndex) in tabs" :key="item.uuid">
        <div
          class="col-title"
          :style="{
            borderTop: `2px solid ${borderColor[tabIndex % borderColor.length]}`,
            backgroundColor: `${bgColor[tabIndex % bgColor.length]}`,
          }"
        >
          {{ item.menuName }}
        </div>
        <ul class="lab-list">
          <li
            class="lab-item active"
            @click="handCardItem(tabIndex, index)"
            v-for="(lab, index) in getItemsForTab(tabIndex)"
            :key="lab.uuid || index"
          >
            <span class="ellipsis">{{ lab.title || lab.menuName }}</span>
            <i class="el-icon-arrow-right"></i>
          </li>
        </ul>
        <SubTitle v-if="item.children.length > 5" class="read-more" :showMore="true" @handleMore="handleMore(tabIndex)" />
      </div>
    </div>
  </div>
</template>
<script>
import { SubTitle } from "@index/components";
import contentLoader from "@index/views/home/<USER>/mixins/contentLoader";

const borderColor = ["#314099", "#F7C959", "#F75959", "#3B30D3", "#17A241"];
const bgColor = ["#e8eaf3", "#fdf9ee", "#fbeeed", "#e9e8f8", "#e9f5eb"];

export default {
  name: "PublicInfo",
  components: {
    SubTitle,
  },
  mixins: [contentLoader],
  props: {
    componentInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      borderColor,
      bgColor,
    };
  },
  async created() {
    // 一次性获取所有数据
    await this.initTabsData(true);
  },
  methods: {
    handCardItem(tabIndex, index) {
      this.$emit("handCardItem", this.componentInfo, tabIndex, index);
    },
    handleMore(tabIndex) {
      this.$emit("handleMore", this.componentInfo, tabIndex);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";
.public-info {
  width: 100%;
  padding: 32px 0 0;
}
.public-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  .icon {
    margin-right: 8px;
  }
  .public-info-title {
    font-size: 24px;
    font-weight: 700;
    color: #2d5af1;
  }
}
.info-columns {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  flex-wrap: wrap;
}
.gap15 {
  gap: 15px;
}
.gap50 {
  gap: 50px;
}
.gap75 {
  gap: 75px;
}
.gap150 {
  gap: 150px;
}
.info-col {
  background: #f5f7fa;
  border-radius: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-width: 50%;
  height: 467px;
  background: #ffffff;
  box-shadow: 0px 4px 7px 0px rgba(102, 102, 102, 0.16);
  cursor: pointer;
  position: relative;
  .col-title {
    height: 63px;
    line-height: 63px;
    color: #333;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    padding: 0 32px;
  }
  .lab-list {
    list-style: none;
    height: 315px;
    overflow: hidden;
  }
  .lab-list li {
    height: 63px;
    font-size: 22px;
    color: #333;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eaeaea;
    justify-content: space-between;
    padding: 0 32px;
  }
  .el-icon-arrow-right {
    font-size: 22px;
    font-weight: 700;
  }
  .lab-item:hover {
    color: var(--themeMainColor);
  }
}
.info-col:hover {
  transform: translateY(-20px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}
.read-more {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
}
</style>
