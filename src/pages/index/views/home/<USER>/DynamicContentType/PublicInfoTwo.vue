<template>
  <div class="public-info">
    <SubTitle :title="componentInfo.blockName" :icon="componentInfo.blockIcon" :iconSvg="componentInfo.blockSvg" />
    <div class="info-columns">
      <div class="info-col" v-for="(item, tabIndex) in tabs" :key="item.uuid">
        <div class="col-menu" :style="{ backgroundColor: borderColor[tabIndex % borderColor.length] }">
          <span>{{ item.menuAbbreviation || item.menuName }}</span>
        </div>
        <div class="col-timeline">
          <div class="timeline-line" :style="{ backgroundColor: borderColor[tabIndex % borderColor.length] }"></div>
          <div
            class="timeline-item"
            @click="handCardItem(tabIndex, index)"
            v-for="(lab, index) in getItemsForTab(tabIndex).slice(0, 5)"
            :key="lab.uuid || index"
          >
            <div class="timeline-content ellipsis2">{{ lab.title || lab.menuName }}</div>
          </div>
          <SubTitle v-if="getItemsForTab(tabIndex).length > 5" class="read-more" :showMore="true" @handleMore="handleMore(tabIndex)" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { SubTitle } from "@index/components";
import contentLoader from "@index/views/home/<USER>/mixins/contentLoader";

const borderColor = ["#394299", "#F7B959", "#C73333", "#00A0E9", "#6BC16D"];
const bgColor = ["#e8eaf3", "#fdf9ee", "#fbeeed", "#e9e8f8", "#e9f5eb"];

export default {
  name: "PublicInfoTwo",
  components: {
    SubTitle,
  },
  mixins: [contentLoader],
  props: {
    componentInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      borderColor,
      bgColor,
    };
  },
  async created() {
    // 一次性获取所有数据
    await this.initTabsData(true);
  },
  methods: {
    handCardItem(tabIndex, index) {
      this.$emit("handCardItem", this.componentInfo, tabIndex, index);
    },
    handleMore(tabIndex) {
      this.$emit("handleMore", this.componentInfo, tabIndex);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";
.public-info {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 32px 0 0;
}
.info-columns {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  gap: 20px;
  flex-wrap: wrap;
}
.info-col {
  position: relative;
  flex: 1;
  padding: 0;
  min-width: 16%;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  .col-menu {
    width: 51px;
    position: absolute;
    left: 0;
    top: 0;
    height: 118px;
    color: white;
    writing-mode: vertical-lr;
    display: flex;
    align-items: center;
    border-radius: 5px;
    padding-top: 15px;
    span {
      font-size: 24px;
      font-weight: bold;
      letter-spacing: 3px;
    }
  }
  .col-timeline {
    position: relative;
    margin-left: 50px;
    margin-top: 6px;
    padding: 10px 10px 20px;
    flex: 1;
    height: 360px;
    overflow: hidden;
    .timeline-line {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 1px;
    }

    .timeline-item {
      height: 50px;
      position: relative;
      margin-bottom: 20px;
      padding-left: 20px;
      font-size: 22px;
      color: #333333;
      cursor: pointer;
    }
    .timeline-item:hover {
      color: var(--themeMainColor);
    }
    .read-more {
      bottom: 10px;
      left: 20px;
      position: absolute;
    }
  }
}
.info-col:hover {
  // transform: translateY(-20px);
  transform: scale(1.05);
}
</style>
