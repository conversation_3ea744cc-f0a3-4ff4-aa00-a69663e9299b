<template>
  <div class="service-guide">
    <div class="bg"></div>
    <SubTitle :title="componentInfo.blockName" :showMore="tabsList.length > 12"
      :icon="componentInfo.blockIcon" :iconSvg="componentInfo.blockSvg" @handleMore="handleMore(activeTab)" />
    <div class="service-content">
      <div class="tabs">
        <div class="tabs-item" v-for="(tab, index) in tabs" :key="index" :class="{ active: index === activeTab }"
          @click="handTabsItem(index)">
          <img :src="location + tab.menuSvg" class="title-icon" v-if="tab.menuSvg" />
          <i class="iconfont " :class="tab.menuIcon" v-else-if="tab.menuIcon"></i>
          <!-- <svg class="icon" aria-hidden="true" v-else-if="tab.menuIcon">
            <use :xlink:href="`#${tab.menuIcon}`"></use>
          </svg> -->
          {{ tab.menuName }}
        </div>
      </div>
      <div class="main-content">
        <div class="left-img">
          <img v-if="coverImage" :src="location + coverImage" />
          <img v-else src="@index/assets/images/Home/empty.png" alt="" />
        </div>
        <div class="right-content">
          <div class="card-list">
            <div class="card-main" v-for="(item, index) in tabsList" :key="item.title || index"
              @click="handCardItem(activeTab, index)">
              <CardItem :item="item"></CardItem>
            </div>
          </div>
          <!-- <SubTitle v-if="tabsList.length > 8" class="sub-title" :showMore="true" @handleMore="handleMore(activeTab)"/> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { SubTitle, CardItem } from "@index/components";
import contentLoader from "@index/views/home/<USER>/mixins/contentLoader";

export default {
  name: "ServiceGuide",
  components: {
    SubTitle,
    CardItem,
  },
  mixins: [contentLoader],
  props: {
    componentInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl + "/",
      activeTab: 0,
      coverImage: "",
      tabsList: [],
    };
  },
  async created() {
    // 按需加载当前选中tab的数据
    await this.initTabsData(false, 0);
    if (this.tabs.length) {
      this.coverImage = this.tabs[0].coverImage;
      this.tabsList = this.getItemsForTab(0);
    }
  },
  methods: {
    async handTabsItem(index) {
      this.activeTab = index;
      const currentTab = this.tabs[index];
      this.coverImage = currentTab.coverImage;

      // 当kind类型为4或者5时，分别调取文章列表或者下载菜单接口
      if ((currentTab.kind === 4 || currentTab.kind === 5) && (!currentTab.children || currentTab.children.length === 0)) {
        await this.fetchTabData(currentTab);
      }
      this.tabsList = this.getItemsForTab(index);
    },
    handCardItem(tabIndex, index) {
      this.$emit("handCardItem", this.componentInfo, tabIndex, index);
    },
    handleMore(tabIndex) {
      this.$emit("handleMore", this.componentInfo, tabIndex);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";

/* 小的模块布局控上下 */
.service-guide {
  padding: 32px 0 0;
  width: 100%;
  position: relative;
}
.bg {
  width: 1900px; // 滚动条占比
  height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-image: url("~@index/assets/images/Home/singlebg.png");
  z-index: -1;
}
.service-content {
  border: 1px solid #dbe3ec;
  margin-top: 46px;
  background-color: #ffffff;
}
.tabs {
  width: 100%;
  height: 105px;
  display: flex;
  gap: 64px;
  justify-content: space-evenly;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #dbe3ec;
}
.tabs-item {
  cursor: pointer;
  padding: 0 15px 0;
  color: #222;
  display: flex;
  align-items: center;
  transition: background 0.2s, color 0.2s;
  font-size: 24px;
  color: #333333;
  .iconfont {
    font-size: 30px;
    width: 30px;
    margin-right: 13px;
    color: var(--themeMainColor);
  }
  .icon {
    width: 30px;
    height: 30px;
  }
  .title-icon {
    width: 23px;
    margin-right: 13px;
  }
}
.tabs .active {
  position: relative;
}
.tabs .active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--themeMainColor);
  transition: background 0.2s ease;
}
.tabs .active::before {
  content: "";
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid var(--themeMainColor);
}
.main-content {
  display: flex;
  border-radius: 12px;
  box-shadow: 0 2px 16px 0 rgba(59, 108, 255, 0.08);
  min-height: 350px;
  padding: 48px 35px;
  gap: 70px;
}
.left-img {
  flex: 0 0 340px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.left-img img {
  width: 437px;
  height: 337px;
  object-fit: cover;
  box-shadow: 0 2px 8px 0 rgba(59, 108, 255, 0.1);
}
.right-content {
  flex: 1;
  position: relative;
}
.card-list {
  width: 100%;
  max-height: 350px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
}
.card-main {
  width: 510px;
  height: 50px;
  margin-top: 10px;
  font-size: 22px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f2f3fb;
  cursor: pointer;
  position: relative;
  .arrow {
    width: 25px;
    height: 15px;
  }
}
.card-main:nth-child(1),.card-main:nth-child(2) {
  margin-top: 0px;
}
.card-main:nth-child(2n-1) {
  margin-right: 80px;
}
.sub-title {
  position: absolute;
  bottom: 0px;
  left: 0;
}
</style>
