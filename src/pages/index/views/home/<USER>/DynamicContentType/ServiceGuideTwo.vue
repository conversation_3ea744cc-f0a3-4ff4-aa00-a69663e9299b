<template>
  <div class="service-guide">
    <SubTitle :title="componentInfo.blockName" :icon="componentInfo.blockIcon" :iconSvg="componentInfo.blockSvg" />
    <div v-if="tabs.length > 4" class="service-box">
      <div class="service-card">
        <SubTitle
          class="sub-title"
          :fs18="true"
          :title="tabs[0].menuName"
          :showMore="tabs[0].children && tabs[0].children.length > 6"
          :icon="tabs[0].menuIcon"
          :iconSvg="tabs[0].menuSvg"
        />
        <div class="card-list">
          <div class="card-main" v-for="(item, index) in getItemsForTab(0)" :key="index" @click="handCardItem(0, index)">
            <CardItem :item="item"></CardItem>
          </div>
        </div>
      </div>
      <div class="service-card-right">
        <div class="service-card-righ-main" v-for="(tab, tabIndex) in tabs.slice(1, 5)" :key="'tab-' + tabIndex">
          <SubTitle
            class="sub-title"
            :fs18="true"
            :title="tab.menuName"
            :showMore="tab.children && tab.children.length > 4"
            :icon="tab.menuIcon"
            :iconSvg="tab.menuSvg"
          />
          <div class="card-list-right">
            <div
              class="card-main card-main-right"
              v-for="(item, index) in getItemsForTab(tabIndex + 1)"
              :key="index"
              :class="{ active: index === activeCard }"
              @click="handCardItem(tabIndex + 1, index)"
            >
              <CardItem :item="item"></CardItem>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="service-grid">
      <div class="service-card-exp" v-for="(tab, tabIndex) in tabs" :key="'tab-' + tabIndex">
        <SubTitle
          :fs18="true"
          :title="tab.menuName"
          :showMore="tab.children && tab.children.length > 6"
          icon="@index/assets/images/labicon.png"
          @handleMore="handleMore(tabIndex)"
        />
        <div class="card-list">
          <div class="card-main " v-for="(item, index) in getItemsForTab(tabIndex)" :key="index" @click="handCardItem(tabIndex, index)">
            <CardItem :item="item"></CardItem>
          </div>
        </div>
      </div>
    </div>
    <SubTitle v-if="tabs.length > 5" class="read-more" :showMore="true" @handleMore="handleMore(0)" />
  </div>
</template>
<script>
import { SubTitle, CardItem } from "@index/components";
import contentLoader from "@index/views/home/<USER>/mixins/contentLoader";

export default {
  name: "ServiceGuideTwo",
  components: {
    SubTitle,
    CardItem,
  },
  mixins: [contentLoader],
  props: {
    componentInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      activeCard: -1,
    };
  },
  async created() {
    // 一次性获取所有数据
    await this.initTabsData(true);
  },
  methods: {
    handCardItem(tabIndex, index) {
      this.$emit("handCardItem", this.componentInfo, tabIndex, index);
    },
    handleMore(tabIndex) {
      this.$emit("handleMore", this.componentInfo, tabIndex);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";
.service-guide {
  padding: 32px 0 0;
  width: 100%;
}

.service-box {
  margin-top: 20px;
  display: flex;
  gap: 13px;
}
.service-grid {
  // display: grid;
  // grid-template-columns: repeat(4, 1fr);
  // grid-template-rows: repeat(4, auto);
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
}
.service-card {
  width: 422px;
  height: 486px;
  padding: 27px 34px;
  background: #ffffff;
  box-shadow: 0px 4px 7px 0px rgba(102, 102, 102, 0.16);
  border-radius: 11px;
  ::v-deep .title-text {
    font-size: 24px;
  }
}
.service-card-exp {
  flex: 1;
  height: 486px;
  padding: 27px 34px;
  background: #ffffff;
  box-shadow: 0px 4px 7px 0px rgba(102, 102, 102, 0.16);
  border-radius: 11px;
}
.service-card-right {
  width: 1197px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.service-card-righ-main {
  width: 540px;
  height: 206px;
  background: #ffffff;
  box-shadow: 0px 4px 7px 0px rgba(102, 102, 102, 0.16);
  border-radius: 11px;
  padding: 27px 18px;
  overflow: hidden;
  ::v-deep .title-text {
    font-size: 24px;
  }
}
.service-icon {
  margin-right: 10px;

  i {
    font-size: 24px;
    color: var(--themeMainColor);
  }
}
.card-list {
  width: 100%;
  height: 450px;
  overflow: hidden;
}
.card-list-right {
  height: 150px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 10px;
}
.card-main {
  width: 100%;
  height: 60px;
  margin-top: 13px;
  font-size: 16px;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f2f3fb;
  cursor: pointer;
  position: relative;
}
.card-main-right {
  width: 48%;
}
.read-more {
  margin: 20px 20px 0 0;
}
</style>
