<template>
  <div class="news-main-container">
    <!-- 后续可拓展选择不同新闻栏 -->
    <img class="news-left" src="@index/assets/images/Home/newsLeft.png" alt="" />
    <img class="news-right" src="@index/assets/images/Home/newsRight.png" alt="" />
    <el-row type="flex" justify="space-between" class="news-container">
      <el-col :span="11.5">
        <WorkDynamicTypeOne id="WorkDynamicType" :newslist="newslist" @handleMore="handleMore" @handleItemClick="handleItemClick" />
      </el-col>
      <el-col :span="12">
        <NoticeTypeOne
          id="NoticeType"
          :newsGridHeight="newsGridHeight"
          :noticeListItemHeight="noticeListItemHeight"
          :noticelist="noticelist"
          @handleMore="handleMore"
          @handleItemClick="handleItemClick"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { WorkDynamicTypeOne, WorkDynamicTypeTwo, NoticeTypeOne, NoticeTypeTwo } from "@index/views/home/<USER>";

import { getNews } from "@index/api/index/index";

const arrayCalcu = [
  { idType: "WorkDynamicType", classType: ".news-grid", info: "newsGridHeight" },
  { idType: "NoticeType", classType: ".notice-list-item", info: "noticeListItemHeight" },
];
export default {
  name: "News",
  components: {
    WorkDynamicTypeOne,
    WorkDynamicTypeTwo,
    NoticeTypeOne,
    NoticeTypeTwo,
  },
  data() {
    return {
      newslist: [],
      noticelist: [],
      newsGridHeight: 0,
      noticeListItemHeight: 0,
    };
  },
  computed: {},
  methods: {
    // 获取某id下的某个class高度，包含height&marginBottom 计算出该盒子设计稿初始高度
    calculateNewsGridHeight(arrayCalcu) {
      let { idType, classType, info } = arrayCalcu[0];
      if (arrayCalcu.length > 1) {
        this.calculateNewsGridHeight(arrayCalcu.slice(1));
      }
      let WorkDynamicType = document.getElementById(idType);
      if (WorkDynamicType) {
        let newsGrid = WorkDynamicType.querySelector(classType);
        if (newsGrid) {
          let marginBottom = window.getComputedStyle(newsGrid).marginBottom;
          let newsGridHeight = newsGrid.offsetHeight;
          const innerWidth = window.innerWidth;
          this[info] = (newsGridHeight + Number(marginBottom.split("px")[0])) * (1920 / innerWidth);
        }
      }
    },
    // 获取新闻数据的通用方法
    fetchNewsByKind(kind, targetList, callback = null) {
      let params = {
        pageNum: 1,
        pageSize: 10,
        kind,
        orderItems: "publishDate,sid",
        orderRule: "desc",
      };
      getNews(params).then((res) => {
        if (res.code == 0 && res.data) {
          res.data.map((item, index) => {
            res.data[index].publishDate = this.$moment(item.publishDate).format("YYYY-MM-DD");
          });
          this[targetList] = res.data;

          // 执行回调函数（如果有）
          if (callback) {
            callback();
          }
        }
      });
    },
    // 获取通知公告
    getNews() {
      this.fetchNewsByKind(3, 'noticelist', () => {
        setTimeout(() => {
          this.calculateNewsGridHeight(arrayCalcu);
        }, 100);
      });
    },
    // 获取工作动态
    getNews2() {
      this.fetchNewsByKind(5, 'newslist');
    },
    handleMore(type) {
      const route = this.$router.resolve({
        name: "viewDetail",
      });
      this.$store.commit("viewDetailFlag", type);
      window.open(route.href, "_blank");
    },
    handleItemClick(data, type) {
      this.$store.commit("viewDetailFlag", type);
      this.$store.commit("setIndexdata", data);
      // 打开新窗口
      const route = this.$router.resolve({
        name: "viewDetail",
      });
      window.open(route.href, "_blank");
    },
  },
  mounted() {
    this.getNews();
    this.getNews2();
    // 添加窗口大小变化监听
    window.addEventListener("resize", () => {
      this.calculateNewsGridHeight(arrayCalcu);
    });
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.calculateNewsGridHeight);
  },
};
</script>

<style lang="scss" scoped>
.news-main-container {
  display: flex;
  width: 100%;
  position: relative;
  .news-left {
    width: 119px;
    height: 370px;
    position: absolute;
    left: 0;
    top: 48px;
  }
  .news-right {
    width: 77px;
    height: 305px;
    position: absolute;
    right: 0;
    top: 71px;
  }
  .news-container {
    width: 100%;
    padding: 20px 110px 17px;
  }
}
</style>
