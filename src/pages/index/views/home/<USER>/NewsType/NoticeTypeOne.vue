<template>
  <div class="news-section">
    <SubTitle :title="title" icon="icon-tongzhigonggao" :showMore="true" @handleMore="handleMore(title)"></SubTitle>
    <div class="notice-list" :style="{ '--realViewLength': realViewLength }">
      <div
        v-for="(item, index) in ulList"
        :key="'noticelist-' + index"
        class="notice-list-item"
        :class="!index && play ? 'toUp' : ''"
        @click="handleItemClick(item, title)"
      >
        <div class="notice-date">
          <span class="notice-day">{{ $moment(item.publishDate).format("DD") }}</span>
          <span class="notice-month">{{ $moment(item.publishDate).format("YYYY-MM") }}</span>
        </div>
        <div class="notice-content">
          <div class="notice-title ellipsis">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { SubTitle } from "@index/components";

import _ from "lodash";

export default {
  name: "NoticeTypeOne",
  components: {
    SubTitle,
  },
  props: {
    newsGridHeight: {
      type: Number,
      default: 0,
    },
    noticeListItemHeight: {
      type: Number,
      default: 0,
    },
    noticelist: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    noticelist(val) {
      if (val.length > 0) {
        this.ulList = _.cloneDeep(val);
        // 暂时不轮播
        // setInterval(this.startPlay, 2000);
      }
    },
    newsGridHeight(val) {
      if (val) {
        this.realViewLength = Math.round(val / (this.noticeListItemHeight || 66));
      } else {
        this.realViewLength = this.noticeViewLength;
      }
    },
  },
  data() {
    return {
      title: "通知公告",
      realViewLength: 5,
      noticeViewLength: 5,
      ulList: [],
      play: false,
    };
  },
  created() {
    // if (window.g.noticeViewLength) {
    //   this.noticeViewLength = window.g.noticeViewLength;
    // }
  },
  methods: {
    handleMore(type) {
      this.$emit("handleMore", type);
    },
    handleItemClick(data, type) {
      this.$emit("handleItemClick", data, type);
    },
    startPlay() {
      let that = this;
      that.play = true; //开始播放
      setTimeout(() => {
        that.ulList.push(that.ulList[0]); //将第一条数据塞到最后一个
        that.ulList.shift(); //删除第一条数据
        that.play = false; //暂停播放,此处修改，保证每一次都会有动画显示
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.news-section {
  width: 100%;
}
.notice-list {
  margin-top: 27px;
  overflow: hidden;
  // height: 360px;
  height: calc(var(--realViewLength) * 66px);
  .toUp {
    margin-top: -66px; //向上移
    transition: all 0.5s;
  }
}
.notice-list-item {
  height: 60px;
  background: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 8px;
  cursor: pointer;
  margin-bottom: 6px;
}
.notice-date {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  width: 60px;
  height: 100%;
  font-family: Adobe Heiti Std;
  font-size: 14px;
  color: #999999;
  margin-right: 50px;
}
.notice-day {
  font-size: 32px;
  color: #cc9933;
  padding-right: 5px;
}
.notice-month {
  font-size: 14px;
  color: #999999;
}
.notice-content {
  flex: 1;
}
.notice-title {
  font-size: 22px;
  color: #324050;
  font-weight: 500;
  height: 70px;
  line-height: 70px;
}
.notice-title:hover {
  color: var(--themeMainColor);
}
</style>
