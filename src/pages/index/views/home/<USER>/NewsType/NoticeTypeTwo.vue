<template>
  <div class="news-section">
    <SubTitle :title="title" icon="icon-tongzhigonggao" :showMore="true" @handleMore="handleMore(title)"></SubTitle>
    <div class="notice-list" :style="{ '--realViewLength': realViewLength }">
      <div
        v-for="(item, index) in ulList"
        :key="'noticelist-' + index"
        class="notice-list-item"
        :class="!index && play ? 'toUp' : ''"
        @click="handleItemClick(item, title)"
      >
        <div class="notice-title ellipsis">{{ item.title }}</div>
        <div class="notice-date">{{ item.publishDate }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { SubTitle } from "@index/components";

import _ from "lodash";

export default {
  name: "NoticeTypeTwo",
  components: {
    SubTitle,
  },
  props: {
    newsGridHeight: {
      type: Number,
      default: 0,
    },
    noticeListItemHeight: {
      type: Number,
      default: 0,
    },
    noticelist: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    noticelist(val) {
      if (val.length > 0) {
        this.ulList = _.cloneDeep(val);
        // 暂时不轮播
        // this.startPlayType = setInterval(this.startPlay, 2000);
      }
    },
    newsGridHeight(val) {
      if (val) {
        this.realViewLength = Math.round(val / (this.noticeListItemHeight || 83));
      } else {
        this.realViewLength = this.noticeViewLength;
      }
    },
  },
  computed: {},
  data() {
    return {
      startPlayType: null,
      title: "通知公告",
      realViewLength: 4,
      noticeViewLength: 4,
      ulList: [],
      play: false,
    };
  },
  created () { },
  beforeDestroy () {
    clearInterval(this.startPlayType);
  },
  methods: {
    handleMore(type) {
      this.$emit("handleMore", type);
    },
    handleItemClick(data, type) {
      this.$emit("handleItemClick", data, type);
    },
    startPlay() {
      let that = this;
      that.play = true; //开始播放
      setTimeout(() => {
        that.ulList.push(that.ulList[0]); //将第一条数据塞到最后一个
        that.ulList.shift(); //删除第一条数据
        that.play = false; //暂停播放,此处修改
      }, 1000);
    },
    //只要对第一行进行滚动，下面的自动会跟着往上移动。
    isScroll(index) {
      return index == 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.news-section {
  width: 100%;
}
.notice-list {
  overflow: hidden;
  height: calc(var(--realViewLength) * 82px);
  .toUp {
    margin-top: -83px;
    transition: all 0.5s;
  }
}
.notice-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 82px;
  cursor: pointer;
  border-bottom: 1px solid #d9d9d9;
}

.notice-title {
  flex: 1;
  font-size: 22px;
  color: #333333;
  font-weight: 500;
}
.notice-title:hover {
  color: var(--themeMainColor);
}
.notice-date {
  color: #666666;
  font-size: 18px;
  margin-left: 20px;
}
</style>
