<template>
  <div class="news-section">
    <SubTitle :title="title" icon="icon-gongzuodongtai" :showMore="true" @handleMore="handleMore(title)"></SubTitle>
    <div class="news-carousel news-grid">
      <div class="carousel-image-container">
        <el-carousel :interval="2000" arrow="always" :loop="true">
          <el-carousel-item class="swiper-item" v-for="(item, index) in newslist" :key="index">
            <el-image
              @click="handleItemClick(item, title)"
              class="swiper-item-img"
              :src="item.newsImage ? location + item.newsImage : require('@index/assets/images/Home/news.png')"
              fit="cover"
              alt
              ></el-image>
            <div @click="handleItemClick(item, title)" class="carousel-overlay">
              <div class="overlay-content">
                <div class="overlay-title">{{ item.title }}</div>
                <div class="overlay-date">{{ item.publishDate }}</div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="news-list">
        <div class="news-items-container">
          <div
            class="news-item"
            :class="!index && play ? 'toUp' : ''"
            v-for="(item, index) in newslist2"
            :key="'item-' + index"
            @click="handleItemClick(item, title)"
          >
            <div class="news-date">
              <span class="news-year">{{ $moment(item.publishDate).format("YYYY") }}</span>
              <span class="news-day">{{ $moment(item.publishDate).format("MM/DD") }}</span>
            </div>
            <div class="news-title ellipsis2">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { SubTitle } from "@index/components";

import _ from "lodash";
export default {
  name: "WorkDynamicTypeOne",
  components: {
    SubTitle,
  },
  props: {
    newslist: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      title: "工作动态",
      newslist2: [],
      location: window.location.protocol + "//" + window.location.host + window.g.ApiUrl + "/",
      play: false,
      intervalId: null,
    };
  },
  watch: {
    newslist(val) {
      if (val.length > 0) {
        this.newslist2 = _.cloneDeep(val);
        clearInterval(this.intervalId);
        this.intervalId = setInterval(this.startPlay, 1500);
      }
    },
  },
  methods: {
    handleMore(type) {
      this.$emit("handleMore", type);
    },
    handleItemClick(data, title) {
      this.$emit("handleItemClick", data, title);
    },
    startPlay() {
      let that = this;
      that.play = true; //开始播放
      setTimeout(() => {
        that.newslist2.push(that.newslist2[0]); //将第一条数据塞到最后一个
        that.newslist2.shift(); //删除第一条数据
        that.play = false; //暂停播放,此处修改，保证每一次都会有动画显示。 一定要修改属性，只有修改属性这样才能触发控件刷新冲毁!!!!
      }, 1000);
    },
  },
  mounted() {},
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-carousel__container {
  width: 430px;
  height: 320px;
  .el-carousel__arrow {
    width: 41px;
    height: 41px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.7);
    .el-icon-arrow-left,
    .el-icon-arrow-right {
      display: none;
    }
  }
  .el-carousel__arrow::before {
    content: "";
    width: 10px;
    height: 10px;
    border-style: solid;
    border-width: 2px 2px 0 0;
    border-color: #666;
    position: absolute;
    left: 38%;
    top: 33%;
    transform: translate(-50%, -50%);
  }
  .el-carousel__arrow--left {
    left: 5px;
  }
  .el-carousel__arrow--right {
    right: 5px;
  }
  .el-carousel__arrow--left::before {
    transform: rotate(-135deg);
  }
  .el-carousel__arrow--right::before {
    left: 35%;
    transform: rotate(45deg);
  }
}
// ::v-deep .el-carousel__indicator,
::v-deep .el-carousel__indicators--horizontal {
  display: none;
  .el-carousel__indicator--horizontal {
    .el-carousel__button {
      width: 13px;
      height: 13px;
      background: #ffffff;
      border-radius: 50%;
      opacity: 1;
    }
  }
}
.wrapper {
  position: relative;
  width: 100%;
  height: 320px;
}
.swiper-item {
  cursor: pointer;
}
.swiper-item-img {
  width: 100%;
  height: 320px;
  border-radius: 10px;
}
.news-section {
  width: 100%;
  border-radius: 12px;
}
.news-carousel {
  display: flex;
  align-items: center;
  margin: 27px 0 18px;
  position: relative;
}
.carousel-image-container {
  position: relative;
  width: 430px;
  height: 320px;
  border-radius: 10px;
  margin-right: 12px;
  overflow: hidden;
}
.carousel-arrow {
  position: absolute;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  cursor: pointer;
  z-index: 2;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s;
}
.carousel-arrow:hover {
  background: rgba(255, 255, 255, 0.9);
}
.carousel-arrow.left {
  left: 20px;
}
.carousel-arrow.right {
  right: 20px;
}
.carousel-arrow .arrow-icon {
  display: block;
  width: 10px;
  height: 10px;
  border-style: solid;
  border-width: 2px 2px 0 0;
  border-color: #666;
}
.carousel-arrow.left .arrow-icon {
  transform: rotate(-135deg);
  margin-left: 3px;
}
.carousel-arrow.right .arrow-icon {
  transform: rotate(45deg);
  margin-right: 3px;
}
.carousel-img {
  width: 430px;
  height: 320px;
  object-fit: cover;
  border-radius: 8px;
}
.carousel-noimg {
  width: 430px;
  height: 320px;
  background: #e4e2e2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b6b4b4;
  font-size: 18px;
  border-radius: 8px;
}
.carousel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 90px;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2;
}

.overlay-content {
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  box-sizing: border-box;
  border-radius: 0 0 10px 10px;
}

.overlay-title {
  color: #fff;
  font-size: 22px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overlay-date {
  color: #e0e0e0;
  font-size: 16px;
  margin-top: 12px;
}
.news-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 369px;
  height: 320px;
  overflow: hidden;
  position: relative;
}
.news-items-container {
  position: relative;
  height: 100%;
  width: 100%;
  .toUp {
    margin-top: -165px; // 向上移距离等于内部盒子+margin-bottom
    transition: all 0.5s;
  }
}
.news-item {
  padding: 20px;
  cursor: pointer;
  border-radius: 10px;
  width: 330px;
  height: 115px;
  background: #ffffff;
  margin-bottom: 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}
.news-date {
  display: flex;
  align-items: baseline;
  margin-bottom: 15px;
}
.news-year {
  font-size: 18px;
  color: #333333;
  margin-right: 10px;
}
.news-day {
  width: 73px;
  height: 24px;
  background: var(--themeMainColor);
  font-size: 18px;
  color: white;
  text-align: center;
  line-height: 24px;
}
.news-title {
  font-weight: 400;
  font-size: 22px;
  color: #333333;
  line-height: 43px;
}
.news-subtitle {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* Common transitions */
.slide-next-enter-to,
.slide-next-leave,
.slide-prev-enter-to,
.slide-prev-leave {
  opacity: 1;
  transform: translateX(0);
}

/* Slide up transitions */
.slide-up-move {
  transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-up-enter {
  transform: translateY(152px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-152px);
  opacity: 0;
}
</style>
