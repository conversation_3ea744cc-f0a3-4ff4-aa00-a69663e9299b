<template>
  <div class="news-section">
    <SubTitle :title="title" icon="icon-gongzuodongtai" :showMore="true" @handleMore="handleMore(title)"></SubTitle>
    <div class="news-grid">
      <div class="grid-main-image" v-if="newslist.length" @click="handleItemClick(newslist[0], title)">
        <img class="main-image" :src="location + newslist[0].newsImage" />
        <div class="image-overlay">
          <div class="overlay-content">
            <div class="overlay-title">{{ newslist[0].title }}</div>
            <div class="overlay-date">{{ newslist[0].publishDate }}</div>
          </div>
        </div>
      </div>
      <div class="grid-small-images" v-if="newslist.length > 1">
        <div class="small-image-container" v-for="(item, index) in newslist.slice(1, 3)" :key="index" @click="handleItemClick(item, title)">
          <img class="small-image"
          :src="item.newsImage ? location + item.newsImage : require('@index/assets/images/Home/news.png')"
          />
          <div class="image-overlay">
            <div class="overlay-content">
              <div class="overlay-title">{{ item.title }}</div>
              <div class="overlay-date">{{ item.publishDate }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { SubTitle } from "@index/components";

export default {
  name: "WorkDynamicTypeTwo",
  components: {
    SubTitle,
  },
  props: {
    newslist: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      title: "工作动态",
      location: window.location.protocol + "//" + window.location.host + window.g.ApiUrl + "/",
    };
  },
  methods: {
    handleMore(type) {
      this.$emit("handleMore", type);
    },
    handleItemClick(data, type) {
      this.$emit("handleItemClick", data, type);
    },
  },
};
</script>

<style lang="scss" scoped>
.news-section {
  width: 100%;
  border-radius: 12px;
}

.news-grid {
  display: flex;
  margin: 27px 0 18px;
  gap: 10px;
}

.grid-main-image {
  position: relative;
  width: 498px;
  height: 469px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.grid-small-images {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 470px;
  cursor: pointer;
}

.small-image-container {
  position: relative;
  width: 305px;
  height: 230px;
  background: #333333;
  border-radius: 10px;
  overflow: hidden;
}

.small-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2;
  border-radius: 0 0 10px 10px;
}

.overlay-content {
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
}

.overlay-title {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.overlay-date {
  color: #e0e0e0;
  font-size: 14px;
  margin-top: 5px;
}
</style>
