import { getMenuArticlePage, getMenuDownloadPage } from "@index/api/index/index";

export default {
  data() {
    return {
      tabs: [],
      loading: false,
      location: document.location.protocol + "//" + document.location.host + window.g.ApiUrl,
    };
  },
  methods: {
    /**
     * 初始化所有tabs数据
     * @param {Boolean} immediate - 是否立即加载所有数据，为true时一次性加载全部，为false时仅加载当前选中的tab
     * @param {Number} activeIndex - 当前选中的tab索引，仅当immediate为false时有效
     */
    async initTabsData(immediate = true, activeIndex = 0) {
      if (!this.componentInfo || !this.componentInfo.children) return;

      this.tabs = this.componentInfo.children || [];

      if (immediate) {
        // 一次性加载所有tabs数据
        await this.initAllTabsData();
      } else {
        // 仅加载当前选中的tab数据
        const currentTab = this.tabs[activeIndex];
        if (currentTab && (currentTab.kind === 4 || currentTab.kind === 5)) {
          await this.fetchTabData(currentTab);
        }
      }
    },

    /**
     * 初始化所有tabs数据
     */
    async initAllTabsData() {
      const promises = this.tabs.map((tab) => {
        if (tab.kind === 4 || tab.kind === 5) {
          return this.fetchTabData(tab);
        }
        return Promise.resolve();
      });

      await Promise.all(promises);
    },

    /**
     * 根据tab的kind类型获取对应的数据
     * @param {Object} tab - tab对象
     */
    async fetchTabData(tab) {
      if (!tab) return;

      try {
        let response;
        const params = {
          menuUuid: tab.uuid,
          pageNum: 1,
          pageSize: 10,
        };

        if (tab.kind === 4) {
          // 调用文章列表接口
          response = await getMenuArticlePage(params);
        } else if (tab.kind === 5) {
          // 调用下载菜单接口
          response = await getMenuDownloadPage(params);
        }

        if (response && response.data) {
          // 将接口返回的数据保存到tab的children中
          tab.children = response.data;
        } else {
          tab.children = [];
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        tab.children = [];
      }
    },

    /**
     * 获取指定tab的子项列表
     * @param {Number} index - tab索引
     * @returns {Array} 子项列表
     */
    getItemsForTab(index) {
      const tab = this.tabs[index];
      return tab?.children || [];
    },
  }
};