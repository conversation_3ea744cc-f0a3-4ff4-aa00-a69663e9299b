import contentLoader from '../mixins/contentLoader';
import componentConfig from '../config/componentConfig';

/**
 * 高阶组件工厂函数 - 为组件添加内容加载能力
 * @param {Object} component - 需要增强的组件
 * @returns {Object} - 增强后的组件
 */
export default function withContentLoader(component) {
  // 获取组件名称
  const componentName = component.name;
  
  // 判断组件的加载策略
  const isImmediate = componentConfig.loadingStrategy.immediateLoad.includes(componentName);
  
  // 创建新的created钩子
  const newCreated = async function() {
    // 调用原始的created钩子
    const originalCreated = component.created || function() {};
    
    // 根据配置决定加载策略
    await this.initTabsData(isImmediate, 0);
    
    // 执行原始created钩子
    originalCreated.call(this);
    
    // 如果是ServiceGuide这样的组件，需要额外处理
    if (componentConfig.loadingStrategy.lazyLoad.includes(componentName) && this.tabs.length) {
      this.coverImage = this.tabs[0].coverImage;
      this.tabsList = this.getItemsForTab(0);
    }
  };
  
  // 创建新的component定义
  return {
    ...component,
    mixins: [...(component.mixins || []), contentLoader],
    created: newCreated
  };
} 