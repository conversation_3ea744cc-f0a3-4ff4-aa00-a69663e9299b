<template>
  <div class="nav-bar" :style="{ opacity: navOpacity }" v-show="navOpacity">
    <!-- 后续可拓展选择不同导航栏 -->
    <NavBarTypeOne
      :activeUuid="activeUuid"
      :menudatas="menudatas"
      @handleSelect="handleSelect"
      @getMenu="getMenu"
      @handleFirstMenu="handleFirstMenu"
      @handleMenu="handleMenu"
    ></NavBarTypeOne>
  </div>
</template>
<script>
import { NavBarTypeOne } from "@index/views/index/developCom";

import { getMenuTree } from "@index/api/index/index";

export default {
  components: {
    NavBarTypeOne,
  },
  data() {
    return {
      navOpacity: 1,
      activeUuid: "1",
      menudatas: [],
    };
  },
  watch: {
    "$store.state.activeUuid": {
      handler(val) {
        this.activeUuid = val;
      },
    },
  },
  mounted() {
    this.activeUuid = this.$store.state.activeUuid || "1";
    window.addEventListener("scroll", this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    handleSelect(key) {
      // 目前仅仅点击首页
      this.activeUuid = key;
      this.$store.commit("initClear");
      this.$store.commit("saveActiveUuid", this.activeUuid);
      if (key == 1 && this.$route.path !== "/home") {
        this.$router.push({ name: "home" });
      }
    },
    getMenu() {
      getMenuTree().then((res) => {
        if (res.code == 0) {
          this.menudatas = res.data;
          // 更新当前活跃菜单状态
          this.updateActiveMenuState();
        }
      });
    },
    // 更新当前活跃菜单状态
    updateActiveMenuState() {
      const activeUuid = this.$store.state.activeUuid;

      // 如果当前不在首页，需要更新活跃菜单数据（后台更新菜单时 前台也需要更新）
      if (activeUuid !== "1") {
        // 查找当前活跃菜单是否为一级菜单
        const activeParentMenu = this.menudatas.find((item) => item.uuid === activeUuid);

        if (activeParentMenu) {
          // 如果是一级菜单，直接处理
          this.handleFirstMenu(activeParentMenu);
          return;
        }

        // 查找当前活跃菜单是否为二级菜单
        for (const parentMenu of this.menudatas) {
          if (!parentMenu.children || parentMenu.children.length === 0) continue;

          const activeChildMenu = parentMenu.children.find((child) => child.uuid === activeUuid);
          if (activeChildMenu) {
            // 如果是二级菜单，处理对应的菜单
            this.handleMenu(activeChildMenu, parentMenu);
            return;
          }
        }

        // 如果没有找到匹配的菜单，默认选择首页
        this.handleSelect(1);
      }
    },
    //点击父菜单
    handleFirstMenu(data) {
      if (data.kind === 1) {
        if (data.children?.length > 0) {
          this.handleMenu(data.children[0], data);
        } else {
          this.$message.warning("该菜单没有子菜单");
        }
      } else {
        // 若navbar kind不是1，则子级即是父级 ，例如当前菜单为文章列表菜单，点击跳转详情文章列表
        this.handleMenu(data, data);
      }
    },
    //点击子菜单
    handleMenu(data, dataFather) {
      console.log(data, dataFather);
      // 1-导航菜单,2-超链接,3-富文本,4-文章列表,5-下载
      // 目前该地方应该只会有kind为1导航菜单
      // 下级菜单展示方式（仅当 kind=1 时有效）：1-普通导航无点击,2-横向滑动分类,3-平铺内容区
      if (data.kind == 2) {
        if (data.hyperlinks != "") {
          window.open(data.hyperlinks);
        }
      } else {
        this.activeUuid = data.uuid;
        this.$store.commit("saveActiveUuid", this.activeUuid);
        this.$store.commit("saveActiveData", [data, dataFather]);
        this.$router
          .push({
            name: "viewDetail",
          })
          .catch((err) => {});
      }
    },
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      if (scrollTop > 400) {
        this.navOpacity = 0;
      } else {
        this.navOpacity = 1 - scrollTop / 400;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.nav-bar {
  position: fixed;
  top: 0;
  width: 100%;
  transition: opacity 0.3s ease;
  z-index: 9;
}
</style>
<style lang="scss" scoped></style>
