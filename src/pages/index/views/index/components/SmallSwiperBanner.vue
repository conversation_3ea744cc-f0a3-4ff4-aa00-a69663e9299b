<template>
  <div class="wrapper">
    <el-carousel :interval="2000" arrow="always" :loop="true">
      <el-carousel-item class="swiper-item" v-for="(item, index) in bannerArr" :key="index">
        <el-image class="swiper-item-img" :src="location + item.imageUrl" fit="cover" alt></el-image>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import { getbanners } from "@index/api/index/index";

export default {
  name: "SwiperBanner",
  props: {
    banStyle: {
      type: Object,
      default() {
        return {
          bannerHeight: "374px",
        };
      },
    },
  },
  data() {
    return {
      bannerArr: [],
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl + "/",
    };
  },
  methods: {
    getallbanners() {
      let params = {
        category: 1,
        orderItems: "orderNum",
        orderRule: "desc",
      };
      getbanners(params).then((res) => {
        if (res.code == 0) {
          this.bannerArr = res.data;
        }
      });
    },
  },
  created() {
    this.getallbanners();
  },
};
</script>
<style lang="scss" scoped>
@import "@index/css/global.scss";
::v-deep .el-carousel__container {
  height: 377px;
  z-index: -1;
}
::v-deep .el-carousel--horizontal {
  z-index: -1;
}
// ::v-deep .el-carousel__indicator,
::v-deep .el-carousel__indicators--horizontal {
  .el-carousel__indicator--horizontal {
    .el-carousel__button {
      width: 13px;
      height: 13px;
      background: #ffffff;
      border-radius: 50%;
      opacity: 1;
    }
  }
  .is-active > .el-carousel__button {
    background: var(--themeMainColor);
    opacity: 1;
  }
}
.wrapper {
  position: relative;
  width: 100%;
  height: 377px;
  z-index: 0;
}
.swiper-item-img {
  width: 100%;
  height: 377px;
}
</style>
