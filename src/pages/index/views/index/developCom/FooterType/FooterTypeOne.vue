<template>
  <div class="footer-wrapper">
    <div class="footer-main">
      <div class="footer-contact">
        <div class="footer-contact-title">联系我们</div>
        <div class="footer-contact-info">
          <div class="footer-contact-info-col left">
            <div class="footer-contact-row">地址: 湖州市学士路1号</div>
            <div class="footer-contact-row">联系电话: 0572-2116277(党委办公室)</div>
            <div class="footer-contact-row">技术支持:0572-2119208</div>
          </div>
          <div class="footer-contact-info-col right">
            <div class="footer-contact-row">邮编: 313000</div>
            <div class="footer-contact-row">0572-2117000(招生咨询热线)</div>
          </div>
        </div>
      </div>
      <div class="footer-center">
        <LogoWithName
          :logo="require('@index/assets/images/image.png')"
          :name="'实验室与设备管理处'"
          :enName="'Laboratory and Equipment Management Office'"
        />
      </div>
      <div class="footer-social">
        <img src="@index/assets/images/Footer/weibo.png" alt="weibo" />
        <img src="@index/assets/images/Footer/wechat.png" alt="wechat" />
        <img src="@index/assets/images/Footer/tiktok.png" alt="douyin" />
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-bottom-content">
        <span>浙ICP备xxxxxxxxxxx号-xxx</span>
        <span>浙公网安备 xxxxxxxxxxxx号</span>
        <span>版权所有：湖州学院</span>
      </div>
    </div>
    <img class="footer-bg" src="@index/assets/images/Footer/bg.png" alt="" />
  </div>
</template>
<script>
import { LogoWithName } from "@index/components";
export default {
  name: "FooterOne",
  components: {
    LogoWithName,
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.footer-wrapper {
  background: var(--themeMainColor);
  padding: 43px 0 31px;
  position: relative;
}
.footer-main {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0 110px;
  margin-bottom: 90px;
}
.footer-contact {
  color: #fff;
  width: 600px;
  .footer-contact-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 24px;
  }
  .footer-contact-info {
    display: flex;
    flex-direction: row;
    gap: 20px;
    font-size: 14px;
    .footer-contact-info-col {
      display: flex;
      flex-direction: column;
      &.left {
        min-width: 270px;
      }
      &.right {
        min-width: 220px;
      }
    }
    .footer-contact-row {
      margin-bottom: 16px;
      color: #fff;
      font-weight: bold;
      letter-spacing: 0.5px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.footer-center {
  align-self: flex-end;
  margin-bottom: 10px;
}
.footer-social {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  gap: 32px;
  flex: 1;
  margin-left: 140px;
  margin-top: 20px;
  img {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
  }
}
.footer-bottom {
  text-align: center;
  padding: 0 110px;
  .footer-bottom-content {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    span {
      margin: 0 16px;
    }
  }
}
.footer-bg {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 1489px;
  height: 166px;
}
</style>
