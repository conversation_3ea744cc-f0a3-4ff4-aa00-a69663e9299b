<template>
  <div class="footer-wrapper">
    <div class="content">
      <iframe :srcdoc="iframeContent" frameborder="0" style="width: 100%; height: 100%;"></iframe>
    </div>
  </div>
</template>
<script>
import { templatePreview } from "@index/api/index/index";
import { decode } from "@admin/utils/base64";
export default {
  name: "FooterSecond",
  components: {},
  data() {
    return {
      iframeContent: "",
      content: "",
    };
  },
  created() {
    this.init();
  },
  mounted() {
    // 添加窗口大小变化监听
    window.addEventListener("resize", () => {
      this.updateIframeContent();
    });
  },
  methods: {
    // 获取CSS变量值的方法
    getCSSVariableValue(variableName) {
      // 获取当前主题
      const currentTheme = window.g?.themeColorStyle || "浅蓝";

      // 创建一个临时元素来获取当前主题下的CSS变量值
      const tempElement = document.createElement("div");
      tempElement.setAttribute("data-theme", currentTheme);
      tempElement.style.position = "absolute";
      tempElement.style.visibility = "hidden";
      document.body.appendChild(tempElement);

      // 获取计算后的样式值
      const computedValue = getComputedStyle(tempElement)
        .getPropertyValue(variableName)
        .trim();

      // 清理临时元素
      document.body.removeChild(tempElement);

      return computedValue;
    },

    // 修改为转换px到rem的方法
    convertPxToRem(htmlContent) {
      // 假设设计稿宽度为1920px
      const designWidth = 1920;

      // 使用正则表达式查找所有的px值
      const pxRegex = /(\d+(\.\d+)?)px/g;

      // 转换为rem
      return htmlContent.replace(pxRegex, (match, pxValue) => {
        // 将px值转换为rem值
        const remValue = parseFloat(pxValue) / 100;
        return remValue.toFixed(4) + "rem";
      });
    },

    // 更新iframe内容的方法
    updateIframeContent() {
      if (!this.content) return;

      // 添加动态设置根字体大小的脚本
      const fontSizeScript = `
      <script>
        function setRootFontSize() {
          const width = window.innerWidth;
          // 根据不同设备宽度设置不同的根字体大小
          document.documentElement.style.fontSize = width * 0.052 + 'px';
        }
        window.addEventListener('DOMContentLoaded', setRootFontSize);
        window.addEventListener('resize', setRootFontSize);
      <\/script>
      `;

      // 获取当前项目的主题色
      const themeMainColor = this.getCSSVariableValue("--themeMainColor");
      const themeMainColorDeep = this.getCSSVariableValue("--themeMainColorDeep");
      const themeMainColorLucency = this.getCSSVariableValue("--themeMainColorLucency");

      // 替换指定宽度并添加动态字体大小脚本
      let newContent = this.content;
      // 转换px为rem
      newContent = this.convertPxToRem(newContent);
      // 替换宽度
      newContent = newContent.replace(/width:\s*1440px/g, `width: ${window.innerWidth * 0.88}px`);

      // 替换--themeMainColor为实际的颜色值
      if (themeMainColor) {
        newContent = newContent.replace(/--themeMainColor:\s*[^;]+;/g, `--themeMainColor: ${themeMainColor};`);
      }
      if (themeMainColorDeep) {
        newContent = newContent.replace(/--themeMainColorDeep:\s*[^;]+;/g, `--themeMainColorDeep: ${themeMainColorDeep};`);
      }
      if (themeMainColorLucency) {
        newContent = newContent.replace(/--themeMainColorLucency:\s*[^;]+;/g, `--themeMainColorLucency: ${themeMainColorLucency};`);
      }
      // 在</head>标签前插入字体大小脚本
      newContent = newContent.replace("</head>", `${fontSizeScript}</head>`);

      // 添加html标签的样式
      newContent = newContent.replace("<html", '<html data-dp="1" style="font-size: 99px;"');

      this.iframeContent = newContent;
    },

    async init() {
      const postData = {
        templateKind: 3,
      };
      const res = await templatePreview(postData);
      if (res.code === 0) {
        const content = decode(res.data);
        this.content = content;
        this.updateIframeContent();
      }
      // 注释掉的原始代码保留作参考
      // let content = decode(
      //   "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",
      // );
      // this.content = content;
      // this.replaceSpecificNumber(content, "1440px", window.innerWidth * 0.88 + "px");
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-wrapper {
  background: var(--themeMainColorDeep);
  margin: 0;
}
.content {
  display: flex;
  height: 325px;
  flex-direction: column;
}
</style>
