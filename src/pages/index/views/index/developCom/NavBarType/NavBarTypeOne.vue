<template>
  <div class="navbarbox">
    <div class="navbar-content pubwidth">
      <!-- 左侧 logo+标题+英文 -->
      <LogoWithName :logo="require('@index/assets/images/image.png')" :name="'实验室与设备管理处'"
        :enName="'Laboratory and Equipment Management Office'" />
      <!-- 右侧菜单 -->
      <div class="navbar-right">
        <ul class="custom-menu">
          <li :class="{ active: activeUuid === '1' }" @click="handleSelect('1')">
            首页
            <div class="line w95" v-show="activeUuid === '1'"></div>
            <div class="activeBg w95" v-show="activeUuid === '1'"></div>
          </li>
          <li v-for="item in menudatas" :key="item.uuid" class="custom-menu-item" @mouseenter="showSubMenu = item.uuid"
            @mouseleave="showSubMenu = null">
            <div :class="{ active: activeUuid === item.uuid }" @click="handleFirstMenu(item)">
              {{ item.menuName }}
              <div class="line"
                v-show="activeUuid === item.uuid || (item.children && item.children.findIndex((item2) => item2.uuid === activeUuid) !== -1)">
              </div>
              <div class="activeBg"
                v-show="activeUuid === item.uuid || (item.children && item.children.findIndex((item2) => item2.uuid === activeUuid) !== -1)">
              </div>
            </div>
            <ul v-show="item.children && item.children.length && showSubMenu === item.uuid" class="submenu">
              <div class="expec"></div>
              <div class="normal" v-for="a in item.children" :key="a.uuid" @click.stop="handleMenu(a, item)"
                :class="{ active: activeUuid === a.uuid }">
                {{ a.menuName }}
              </div>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { LogoWithName } from "@index/components";

export default {
  components: {
    LogoWithName,
  },
  props: {
    activeUuid: {
      type: String,
      default: "1",
    },
    menudatas: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showSubMenu: null,
    };
  },
  methods: {
    handleSelect(key) {
      this.$emit("handleSelect", key);
    },
    //获取所有导航栏的数据
    getMenu() {
      this.$emit("getMenu");
    },
    //点击父菜单
    handleFirstMenu(data) {
      this.$emit("handleFirstMenu", data);
    },
    //点击子菜单
    handleMenu(data, dataFather) {
      this.$emit("handleMenu", data, dataFather);
    },
  },
  created() {
    this.getMenu();
  },
};
</script>
<style scoped lang="less"></style>
<style lang="scss" scoped>
@import "@index/css/global.scss";
.navbarbox {
  height: 100px;
  background: var(--themeMainColorLucency);
  padding-left: 110px;
  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }
  .navbar-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.custom-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  align-items: center;
  background: none;
  .custom-menu-item,
  > li {
    position: relative;
    color: white;
    font-size: 22px;
    text-align: center;
    cursor: pointer;
    height: 100%;
    margin: 0 20px;
    white-space: nowrap;
    .line {
      width: 100%;
      height: 4px;
      padding:0 10px;
      position: absolute;
      top: 60px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #ffffff;
    }
    .line::before {
      content: "";
      position: absolute;
      top: -6px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 10px solid #f0f0f0;
    }

    .activeBg {
      width: 100%;
      height: 97px;
      padding:0 10px;
      opacity: 0.7;
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(0deg, #F7F9FF 0%, rgba(255, 255, 255, 0.34) 98%);
    }
    .w95 {
      width: 95px;
      padding: 0;
    }
  }
  .submenu {
    min-width: 126px;
    border-radius: 6px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 18px;
    overflow: hidden;
    .normal {
      height: 47px;
      line-height: 47px;
      color: #333;
      text-align: center;
      background: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      &:hover,
      &.active {
        position: relative;
        color: #FFC224;
        background: var(--themeMainColor);
        background: color-mix(in srgb, var(--themeMainColor) 80%, transparent);
      }
    }

    .expec {
      border-bottom: 4px solid var(--themeSecondColor);
      height: 38px !important;
    }
  }
}
</style>
