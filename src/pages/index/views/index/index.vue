<template>
  <!-- 页面以views文件 index.vue为home入口 components为组件 developCom为功能拓展组件，组件以大写驼峰命名， -->
  <div>
    <NavBar></NavBar>
    <SmallSwiperBanner v-show="isHome"></SmallSwiperBanner>
    <NavBg v-show="!isHome"></NavBg>
    <router-view></router-view>
    <Footer></Footer>
    <BackTop></BackTop>
  </div>
</template>
<script>
import { NavBar, NavBg, SmallSwiperBanner, Footer } from "@index/views/index/components";
import { BackTop } from "@index/components";
export default {
  components: {
    NavBar,
    NavBg,
    SmallSwiperBanner,
    Footer,
    BackTop,
  },
  data() {
    return {};
  },
  computed: {
    isHome() {
      return this.$route.path.includes("home");
    },
  },
  created() {},
  methods: {},
};
</script>
<style lang="scss" scoped></style>
