<template>
  <div class="detail-module-type">
    <!-- 后续可拓展选择不同二级菜单样式 -->
    <SecondTitle :title="viewDetailFlag" :showTabs="false" :showDetail="showDetail" @btnClickBack="btnClickBack"></SecondTitle>
    <!-- 列表视图 -->
    <div v-show="!showDetail">
      <ViewNewsType
        :viewList="viewList"
        :viewType="viewDetailFlag"
        :currentPage="pagination.pageNum"
        :pageSize="pagination.pageSize"
        :total="total"
        @itemClick="handleItemClick"
        v-if="viewDetailFlag === '通知公告' || viewDetailFlag === '工作动态'"
      ></ViewNewsType>
      <div class="pagination-container" v-if="viewDetailFlag === '通知公告' || viewDetailFlag === '工作动态'">
        <Paging :pagination="pagination" @Pagebar="handlePagination"></Paging>
      </div>
    </div>
    <!-- 通知公告和工作动态点击后的详情内容 -->
    <NewsDetailView
      v-if="showDetail"
      :key="currentNewsDetail ? currentNewsDetail.uuid : 'no-detail'"
      :newsDetail="currentNewsDetail"
      :articleList="viewList"
      :currentPage="pagination.pageNum"
      :pageSize="pagination.pageSize"
      :total="total"
      :loadingDetail="loadingDetail"
      @backToList="backToList"
      @goToPrev="handleGoToPrev"
      @goToNext="handleGoToNext"
      @loadNextPage="handleLoadNextPage"
    ></NewsDetailView>
  </div>
</template>
<script>
import { Paging } from "@index/components";
import { SecondTitle, ViewNewsType, NewsDetailView } from "@index/views/viewDetail/developCom";

import { getNews, getNewsDetail } from "@index/api/index/index";

export default {
  name: "HomeDetailMolule",
  components: {
    SecondTitle,
    ViewNewsType,
    Paging,
    NewsDetailView,
  },
  data() {
    return {
      viewDetailFlag: this.$store.state.viewDetailFlag,
      viewList: [],
      total: 0,
      pagination: {
        pageNum: 1,
        pageSize: 10,
        pageCount: 0,
      },
      showDetail: false,
      currentNewsDetail: {},
      // 缓存当前页和下一页的数据，避免重复请求
      cachedData: {},
      // 缓存详情数据，避免重复请求
      cachedDetailData: {},
      // 是否正在加载详情
      loadingDetail: false,
      // 缓存过期时间（30分钟，单位：毫秒）
      cacheExpiration: 30 * 60 * 1000,
    };
  },
  computed: {
    newsType() {
      return this.viewDetailFlag === "通知公告" ? 3 : 5;
    },
  },
  created() {
    // 目前进入的仅有通知公告和工作动态
    this.getNews(this.newsType).then(() => {
      // 获取完列表数据后检查是否有直接进入详情的数据
      this.checkDirectDetail();
    });
  },
  methods: {
    // 检查是否从首页直接进入详情
    checkDirectDetail() {
      // 从store中获取索引数据
      const indexData = this.$store.state.indexdata;
      if (indexData && Object.keys(indexData).length > 0) {
        // 如果有索引数据，表示是从首页直接点击进入的
        this.findAndShowDetail(indexData);
        // 清除store中的数据，避免影响下次访问
        this.$store.commit("setIndexdata", null);
      }
    },
    // 在获取的列表中查找匹配的详情并显示
    findAndShowDetail(targetItem) {
      if (!targetItem || !targetItem.uuid) return;
      // 先在当前页查找
      let foundItem = this.viewList.find((item) => item.uuid === targetItem.uuid);
      if (foundItem) {
        // 找到了匹配项，获取详情并显示
        this.fetchNewsDetail(foundItem);
      } else {
        // 未找到匹配项，可能在其他页，直接通过API获取详情
        this.fetchNewsDetail(targetItem);
        // 遍历获取所有分页数据以便支持上一条/下一条功能
        this.loadAllPagesForNavigation(targetItem.uuid);
      }
    },
    // 获取新闻详情
    fetchNewsDetail(item) {
      if (!item || !item.uuid) return;

      // 显示加载状态
      this.loadingDetail = true;

      // 检查缓存中是否已有该详情，并且未过期
      const now = Date.now();
      if (
        this.cachedDetailData[item.uuid] &&
        this.cachedDetailData[item.uuid].cacheTime &&
        now - this.cachedDetailData[item.uuid].cacheTime < this.cacheExpiration
      ) {
        this.setCurrentNewsDetail(this.cachedDetailData[item.uuid]);
        this.loadingDetail = false;
        return;
      }

      // 调用详情接口
      getNewsDetail({ uuid: item.uuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 处理日期格式
            const detailData = { ...res.data };
            detailData.publishDate = this.$moment(detailData.publishDate).format("YYYY-MM-DD");

            // 缓存详情数据，并添加缓存时间戳
            detailData.cacheTime = Date.now();
            this.cachedDetailData[item.uuid] = detailData;

            // 设置当前详情
            this.setCurrentNewsDetail(detailData);
          } else {
            // 接口失败时，使用列表数据作为备选
            this.setCurrentNewsDetail(item);
          }
          this.loadingDetail = false;
        })
        .catch((err) => {
          console.error("获取详情失败:", err);
          // 接口失败时，使用列表数据作为备选
          this.setCurrentNewsDetail(item);
          this.loadingDetail = false;
        });
    },
    // 加载所有分页以找到目标项所在页，支持导航功能
    loadAllPagesForNavigation(targetUuid) {
      if (!targetUuid) return;
      // 获取总页数
      const totalPages = Math.ceil(this.total / this.pagination.pageSize);
      if (totalPages <= 1) return; // 只有一页不需要处理
      // 先检查第一页（已加载）是否包含目标项，确定目标项在哪一页
      const itemInCurrentPage = this.viewList.findIndex((item) => item.uuid === targetUuid);
      if (itemInCurrentPage >= 0) return; // 已经在当前页，不需要额外加载
      // 如果不在当前页，则可能需要遍历所有页来找到目标项
      // 注意: 这种做法在文章很多时效率较低，理想情况是后端提供直接获取文章位置的API

      // 为减少请求数量，可以采用二分查找的策略
      // 但这里为简单起见，我们先尝试加载第二页看目标项是否在第二页
      // 实际应用中，可以根据业务需求优化这部分逻辑

      // 预加载第2页以支持下一条功能
      if (totalPages >= 2) {
        this.preloadPage(2);
      }
    },

    // 预加载指定页
    preloadPage(pageNum) {
      const cacheKey = `${this.viewDetailFlag}_${pageNum}`;
      if (this.cachedData[cacheKey]) return; // 已缓存，不重复加载
      let params = {
        pageNum: pageNum,
        pageSize: this.pagination.pageSize,
        kind: this.newsType,
        orderItems: "publishDate,sid",
        orderRule: "desc",
      };

      getNews(params).then((res) => {
        if (res.code == 0 && res.data) {
          // 处理日期格式
          const processedData = [...res.data];
          processedData.forEach((item, index) => {
            if (item.publishDate && item.publishDate != "") {
              processedData[index].publishDate = this.$moment(item.publishDate).format("YYYY-MM-DD");
            }
          });

          // 缓存数据
          this.cachedData[cacheKey] = {
            list: processedData,
            count: res.count || 0,
          };
        }
      });
    },

    getNews(type, page = null) {
      const pageNum = page || this.pagination.pageNum;

      // 如果已缓存此页数据，则直接使用
      const cacheKey = `${type}_${pageNum}`;
      if (this.cachedData[cacheKey]) {
        this.handleNewsData(this.cachedData[cacheKey], pageNum);
        return Promise.resolve(this.cachedData[cacheKey]);
      }

      // 否则请求新数据
      let params = {
        pageNum: pageNum,
        pageSize: this.pagination.pageSize,
        kind: type,
        orderItems: "publishDate,sid",
        orderRule: "desc",
      };

      return getNews(params).then((res) => {
        if (res.code == 0) {
          if (res.data) {
            // 处理日期格式
            const processedData = [...res.data];
            processedData.forEach((item, index) => {
              if (item.publishDate && item.publishDate != "") {
                processedData[index].publishDate = this.$moment(item.publishDate).format("YYYY-MM-DD");
              }
            });

            // 缓存数据
            this.cachedData[cacheKey] = {
              list: processedData,
              count: res.count || 0,
            };

            // 如果是当前页，更新视图
            if (pageNum === this.pagination.pageNum) {
              this.handleNewsData(this.cachedData[cacheKey], pageNum);
            }

            return this.cachedData[cacheKey];
          }
        }
        return null;
      });
    },
    // 处理新闻数据
    handleNewsData(data, pageNum) {
      if (data) {
        this.viewList = data.list;
        this.total = data.count;
        this.pagination.pageCount = data.count;

        // 如果是切换页面，可能需要更新分页信息
        if (pageNum !== this.pagination.pageNum) {
          this.pagination.pageNum = pageNum;
        }
      }
    },
    handlePagination(data) {
      this.pagination.pageNum = data.pageNum;
      this.pagination.pageSize = data.pageSize;
      this.getNews(this.newsType);
    },
    // 处理新闻项点击
    handleItemClick(item) {
      this.fetchNewsDetail(item);
    },
    // 设置当前新闻详情 - 统一处理详情设置逻辑
    setCurrentNewsDetail(item) {
      if (!item) return;

      // 强制Vue刷新视图
      this.currentNewsDetail = {};
      this.$nextTick(() => {
        this.currentNewsDetail = item;
        this.showDetail = true;
      });
    },
    // 返回列表
    backToList() {
      this.showDetail = false;
      this.currentNewsDetail = {};
    },
    // 处理前往上一篇文章
    handleGoToPrev(prevArticle) {
      if (!prevArticle) return;

      // 获取上一篇文章的详情
      this.fetchNewsDetail(prevArticle);
    },
    // 处理前往下一篇文章
    handleGoToNext(nextArticle) {
      if (!nextArticle) return;

      // 获取下一篇文章的详情
      this.fetchNewsDetail(nextArticle);
    },
    // 处理加载下一页
    handleLoadNextPage() {
      const nextPage = this.pagination.pageNum + 1;

      // 预加载下一页数据
      this.getNews(this.newsType, nextPage).then((res) => {
        if (res && res.list && res.list.length > 0) {
          // 更新分页信息
          this.pagination.pageNum = nextPage;
          this.viewList = res.list;
          this.pagination.pageCount = res.count;

          // 自动跳转到下一页的第一篇文章
          this.fetchNewsDetail(res.list[0]);
        }
      });
    },
    btnClickBack() {
      this.showDetail = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-module-type {
  padding: 0px 0 80px;
}
.pagination-container {
  padding: 0 110px;
  position: relative;
}
</style>
