<template>
  <div class="detail-module-type">
    <!-- 菜单的类型：1导航菜单（1下级菜单平铺，2下级菜单为超链接列表）、2超链接、3富文本、4文章列表，5下载菜单 -->
    <!-- 后续可拓展选择不同二级菜单样式 目前仅有一种 -->
    <SecondTitle
      v-if="menuType === 2"
      :title="menuName"
      :activeUuid="activeUuid"
      :showTabs="kindFather === 1 && submenuStyleFather === 1"
      :tabs="tabs"
      @tabItemClick="tabItemClick"
    ></SecondTitle>
    <ThirdTitle v-if="menuType === 3" :activeUuid="activeUuidChild" :tabs="tabs" @tabItemClick="tabItemClick"></ThirdTitle>
    <!-- 1-普通导航（点进去后无二级菜单） -->
    <!-- 3富文本、4文章列表，5下载菜单 -->
    <!-- 首页公示信息进入，选用文章列表同级组件 kind4 -->
    <ViewDetail v-if="kind === 3 && !showEmpty" class="view-detail" :viewDetailContent="viewDetailContent"></ViewDetail>
    <ArticlesList v-else-if="kind === 4" :viewArticlesList="viewArticlesList" @setDefaultShowPag="setDefaultShowPag"></ArticlesList>
    <DownLoadList v-else-if="kind === 5" :viewDownLoadList="viewDownLoadList"></DownLoadList>
    <!-- 2-下级菜单平铺 -->
    <SecondMenuModule :menuType="3" v-else-if="kind === 1 && submenuStyle === 1 && menuType === 2"></SecondMenuModule>
    <!-- 3-下级菜单为超链接列表 -->
    <HyperLinkList v-else-if="kind === 1 && submenuStyle === 2" :viewHyperLinkList="viewHyperLinkList"></HyperLinkList>
    <Paging v-if="showPag" :pagination="pagination" @Pagebar="handlePagination"></Paging>
    <div v-if="showEmpty" class="empty">
      <img src="@index/assets/images/empty.png" alt="" />
      <span>暂无内容</span>
    </div>
  </div>
</template>
<script>
import { SecondTitle, ViewDetail, ArticlesList, DownLoadList, HyperLinkList, ThirdTitle } from "@index/views/viewDetail/developCom";
import { Paging } from "@index/components";
import SecondMenuModule from "./SecondMenuModule";

import { getMenuArticlePage, getMenuDownloadPage, getRichTextDetail } from "@index/api/index/index";
export default {
  name: "SecondMenuModule",
  components: {
    SecondTitle,
    ViewDetail,
    ArticlesList,
    DownLoadList,
    HyperLinkList,
    Paging,
    SecondMenuModule,
    ThirdTitle,
  },
  props: {
    menuType: {
      type: Number,
      default: 2,
    },
  },
  computed: {
    takeChild() {
      return this.menuType === 2 ? "" : "Child";
    },
    tabs() {
      return this.$store.state[`activeDataFather${this.takeChild}`]?.children || [];
    },
    submenuStyleFather() {
      return this.$store.state[`activeDataFather${this.takeChild}`].submenuStyle;
    },
    kindFather() {
      return this.$store.state[`activeDataFather${this.takeChild}`].kind;
    },
    submenuStyle() {
      return this.$store.state[`activeData${this.takeChild}`].submenuStyle;
    },
    kind() {
      //kind 1-导航菜单,2-超链接,3-富文本,4-文章列表,5-下载
      return this.$store.state[`activeData${this.takeChild}`].kind;
    },
    menuName() {
      return this.$store.state[`activeDataFather${this.takeChild}`].menuName;
    },
    activeData() {
      return this.$store.state[`activeData${this.takeChild}`];
    },
    activeDataFather() {
      return this.$store.state[`activeDataFather${this.takeChild}`];
    },
    showPag() {
      return (
        this.defaultShowPag && ((this.kind === 4 && this.viewArticlesList.length) || (this.kind === 5 && this.viewDownLoadList.length))
      );
    },
    showEmpty() {
      return (
        (this.kind === 1 && this.submenuStyle === 1 && this.menuType === 2 && !this.activeData.children.length) ||
        (this.kind === 1 && this.submenuStyle === 2 && !this.viewHyperLinkList.length) ||
        (this.kind === 3 && !this.viewDetailContent.content) ||
        (this.kind === 4 && !this.viewArticlesList.length) ||
        (this.kind === 5 && !this.viewDownLoadList.length)
      );
    },
  },
  watch: {
    "$store.state.activeUuid": {
      handler(val) {
        if (this.menuType === 2) {
          this.activeUuid = val;
          // 更新activeUuid时，初始化数据
          this.initMessage();
          this.initInfo(this.activeData);
        }
      },
    },
    "$store.state.activeUuidChild": {
      handler(val) {
        if (this.menuType === 3) {
          this.activeUuidChild = val;
          this.initMessage();
          this.initInfo(this.activeData);
        }
      },
    },
  },
  data() {
    return {
      defaultShowPag: true,
      activeUuid: "",
      activeUuidChild: "",
      viewMenuList: [],
      viewDetailContent: {},
      viewArticlesList: [],
      viewDownLoadList: [],
      viewHyperLinkList: [],
      viewThirdMenuList: [],
      pagination: {
        pageNum: 1,
        pageSize: 10,
        pageCount: 0,
      },
    };
  },
  created() {
    this[`activeUuid${this.takeChild}`] = this.$store.state[`activeUuid${this.takeChild}`];
    this.initInfo(this.$store.state[`activeData${this.takeChild}`]);
  },
  methods: {
    initMessage() {
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
    },
    setDefaultShowPag(type) {
      this.defaultShowPag = type;
    },
    tabItemClick(tab, indx) {
      // kind为2的超链接仅做跳转处理，无需其他操作
      if (tab.kind === 2) {
        window.open(tab.hyperlinks);
        return;
      }
      this.$store.commit(`saveActiveUuid${this.takeChild}`, tab.uuid);
      this.$store.commit(`saveActiveData${this.takeChild}`, [tab]);
    },
    // 处理分页事件
    handlePagination(data) {
      this.pagination.pageNum = data.pageNum;
      this.pagination.pageSize = data.pageSize;
      // 根据当前类型重新获取数据
      this.initInfo(this.activeData);
    },
    // 同时根据其kind类型调取不同接口获取对应数据
    initInfo(data) {
      // 1超链接列表 2超链接  3富文本、4文章列表，5下载菜单
      if (data.kind === 1) {
        if (data.submenuStyle === 1) {
          this.getChildMenu(data);
        } else if (data.submenuStyle === 2) {
          this.getHyperLinkList();
        }
      } else if (data.kind === 2) {
        window.open(data.hyperlinks);
      } else if (data.kind === 3) {
        this.getDetailContent(data.uuid);
      } else if (data.kind === 4) {
        this.getArticlesList(data.uuid);
      } else if (data.kind === 5) {
        this.getDownLoadList(data.uuid);
      }
    },
    getChildMenu(data) {
      if (data.children?.length) {
        this.$store.commit("saveActiveUuidChild", data.children[0].uuid);
        this.$store.commit("saveActiveDataChild", [data.children[0], data]);
      }
    },
    getHyperLinkList() {
      this.viewHyperLinkList = this.activeData.children || [];
    },
    getArticlesList(uuid) {
      let params = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        menuUuid: uuid,
        orderItems: "publishDate",
        orderRule: "desc",
      };
      getMenuArticlePage(params).then((res) => {
        if (res.code == 0) {
          if (res.data && res.data.length) {
            res.data.map((item, index) => {
              if (item.publishDate && item.publishDate != "") {
                res.data[index].publishDate = this.$moment(item.publishDate).format("YYYY-MM-DD");
              }
            });
            this.viewArticlesList = res.data;
            // 获取总数，可能在响应中的不同位置，这里假设在res.total中
            this.pagination.pageCount = res.count;
          } else {
            this.viewArticlesList = [];
          }
        }
      });
    },
    getDownLoadList(uuid) {
      let params = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize,
        menuUuid: uuid,
        orderItems: "publishDate",
        orderRule: "desc",
      };
      getMenuDownloadPage(params).then((res) => {
        if (res.code == 0) {
          if (res.data && res.data.length) {
            res.data.map((item, index) => {
              if (item.publishDate && item.publishDate != "") {
                res.data[index].publishDate = this.$moment(item.publishDate).format("YYYY-MM-DD");
              }
            });
            this.viewDownLoadList = res.data;
            // 获取总数
            this.pagination.pageCount = res.count;
          } else {
            this.viewDownLoadList = [];
          }
        }
      });
    },
    getDetailContent(uuid) {
      getRichTextDetail({ uuid }).then((res) => {
        if (res.code == 0) {
          if (res.data) {
            this.viewDetailContent = res.data.article;
          } else {
            this.viewDetailContent = {};
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-module-type {
  margin-bottom: 100px;
}
.view-detail {
  padding: 20px 120px;
}
.empty {
  padding: 0 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: 164px;
    height: 164px;
  }
  span {
    margin-top: 6px;
    font-size: 16px;
    color: #000000;
  }
}
</style>
