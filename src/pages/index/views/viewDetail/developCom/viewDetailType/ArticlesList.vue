<template>
  <!-- 文章列表ui布局 -->
  <div class="view-menu-type">
    <div class="content-list" v-if="viewList">
      <div v-for="item in viewArticlesList" :key="item.uuid" class="content-item" @click="handleItemClick(item.uuid)">
        <div class="item-left">
          <div class="date-number">{{ $moment(item.publishDate).format("DD") }}</div>
          <div class="date-month">{{ $moment(item.publishDate).format("YYYY-MM") }}</div>
        </div>
        <div class="item-right">
          <div class="item-title ellipsis">{{ item.title }}</div>
          <div class="item-desc ellipsis2">{{ item.articleSummary }}</div>
        </div>
      </div>
    </div>
    <NewsDetailView
      v-else
      class="view-detail"
      :newsDetail="currentArticleDetail"
      :articleList="viewArticlesList"
      :currentPage="1"
      :pageSize="viewArticlesList.length"
      :total="viewArticlesList.length"
      :loadingDetail="loadingDetail"
      @backToList="handleBack"
      @goToNext="handleGoToNext"
      @goToPrev="handleGoToPrev"
      @loadNextPage="handleLoadNextPage"
    ></NewsDetailView>
  </div>
</template>
<script>
import { NewsDetailView } from "@index/views/viewDetail/developCom/viewHomeDetailType";

import { getMenuArticleDetail } from "@index/api/index/index";

export default {
  name: "ArticlesList",
  components: {
    NewsDetailView,
  },
  props: {
    viewArticlesList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loadingDetail: false,
      viewList: true,
      currentArticleDetail: {},
      currentArticleIndex: -1,
    };
  },
  computed: {},
  watch: {
    viewList(val) {
      this.$emit("setDefaultShowPag", val);
    },
    "$store.state.activeUuidChild": {
      handler(val) {
        this.viewList = true;
      },
    },
  },
  created() {
    let uuid = this.$store.state.saveViewListDetailUuid;
    if (uuid) {
      this.$store.commit("saveViewListDetailUuid", "");
      this.handleItemClick(uuid);
    }
  },
  methods: {
    handleItemClick(uuid) {
      this.viewList = false;
      this.getMenuArticleDetail(uuid);
      // 记录当前文章在列表中的索引，用于支持下一条功能
      this.currentArticleIndex = this.viewArticlesList.findIndex((item) => item.uuid === uuid);
    },
    handleBack() {
      this.viewList = true;
    },
    // 处理前往上一篇文章
    handleGoToPrev(prevArticle) {
      if (!prevArticle) return;
      this.getMenuArticleDetail(prevArticle.uuid);
    },
    // 处理前往下一篇文章
    handleGoToNext(nextArticle) {
      if (!nextArticle) return;
      this.getMenuArticleDetail(nextArticle.uuid);
    },
    // 处理加载下一页 - 在当前场景中，所有文章已经在viewArticlesList中，不需要额外加载
    handleLoadNextPage() {
      // 如果当前是最后一篇，可以在这里处理
      console.log("已经是最后一篇文章");
    },
    getMenuArticleDetail(uuid) {
      this.loadingDetail = true;
      getMenuArticleDetail({ uuid }).then((res) => {
        this.loadingDetail = false;
        if (res.code == 0) {
          if (res.data) {
            // 构建文章详情对象，包含NewsDetailView组件所需的属性
            // 处理日期格式
            this.currentArticleDetail = { ...res.data };
            this.currentArticleDetail.publishDate = this.$moment(this.currentArticleDetail.publishDate).format("YYYY-MM-DD");
            this.$forceUpdate();
          } else {
            this.currentArticleDetail = {};
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.view-menu-type {
  width: 100%;
  position: relative;
  .content-list {
    margin: 0 auto;
    padding: 0 110px 54px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    .content-item {
      background: #ffffff;
      padding: 24px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: transform 0.2s;
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }
      .item-left {
        width: 90px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .date-number {
          font-size: 32px;
          font-weight: 500;
          color: var(--themeSecondColor);
          line-height: 1;
          margin-right: 7px;
        }
        .date-month {
          margin-top: 8px;
          font-size: 14px;
          color: #999;
        }
      }
      .item-right {
        flex: 1;
        padding-left: 40px;
        .item-title {
          font-size: 18px;
          color: #333;
          font-weight: 500;
          margin-bottom: 12px;
        }
        .item-desc {
          font-size: 16px;
          color: #333;
        }
      }
    }
  }
  .view-detail {
    margin: 0 auto;
    padding: 20px 110px;
  }
}
</style>
