<template>
  <!-- 下载菜单ui布局 -->
  <div class="view-detail-type-one">
    <div class="content-list">
      <div v-for="(item, idx) in viewDownLoadList" :key="item.uuid" class="content-item">
        <div class="item-title ellipsis">{{ item.title }}</div>
        <div class="item-meta">
          <span class="item-date">
            <i class="el-icon-date"></i>
            {{ item.publishDate }}
          </span>
          <!-- <span class="item-download-count">
            <i class="el-icon-folder-opened"></i>
            下载次数：{{ item.downloadCount }}
          </span> -->
        </div>
        <button class="download-btn" @click="handleDownload(item)">
          <i class="el-icon-download"></i>
          下载
        </button>
      </div>
    </div>
  </div>
</template>
<script>
import { safeDownloadFile } from "@index/utils/download";

export default {
  name: "DownLoadList",
  props: {
    viewDownLoadList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showActive: -1,
      location: window.location.protocol + "//" + window.location.host + window.g.ApiUrl + "/",
    };
  },
  methods: {
    handleDownload(item) {
      safeDownloadFile({
        url: item.attachmentUrl,
        fileName: item.fileName,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.view-detail-type-one {
  width: 100%;
  position: relative;
  .title {
    height: 40px;
    font-size: 42px;
    color: #ffffff;
    margin: 0 0 55px 113px;
  }
  .tabs {
    // width: 962px;
    height: 80px;
    border-radius: 0 40px 40px 0;
    display: flex;
    width: fit-content;
    gap: 48px;
    margin-bottom: 50px;
    font-size: 20px;
    color: #ffffff;
    position: relative;
    padding: 0 200px 0 110px;
    background: var(--themeMainColor);
    .tab-item {
      min-width: 80px;
      line-height: 80px;
      text-align: center;
      cursor: pointer;
      position: relative;
      .tab-active {
        width: 33px;
        height: 14px;
        position: absolute;
        left: 50%;
        bottom: 0px;
        transform: translateX(-50%);
        overflow: hidden;
        .arc-container {
          position: absolute;
          left: 50%;
          top: 0;
          transform: translateX(-50%);
          width: 90%;
          height: 100%;
          background-color: #ffffff;
        }
        .arc {
          position: absolute;
          right: 30%;
          bottom: 0;
          width: 50px;
          height: 50px;
          background-color: var(--themeMainColor);
          border-radius: 50%;
        }
        .right {
          transform: rotate(90deg);
          left: 30%;
        }
      }
    }
  }
  // .tabs::before {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   width: 1062px;
  //   height: 80px;
  //   background: var(--themeMainColor);
  //   border-radius: 0 40px 40px 0;
  // }
  .content-list {
    margin: 0 156px 84px 110px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    .content-item {
      height: 106px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item-title {
        flex: 1;
        font-size: 18px;
        color: #333333;
        font-weight: 500;
        transition: all 0.2s;
        height: 106px;
        line-height: 106px;
        border-bottom: #e9e9e9;
        border-left: 5px solid var(--themeSecondColor);
        padding: 0 24px;
        cursor: pointer;
      }
      .item-title:hover {
        font-size: 22px;
        color: var(--themeMainColor);
      }
      .item-meta {
        width: 400px;
        display: flex;
        align-items: center;
        gap: 32px;
        font-size: 14px;
        color: #888;
        .el-icon-date,
        .el-icon-folder-opened {
          margin-right: 6px;
          color: #b3b3b3;
        }
      }
      .download-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        background: var(--themeMainColor);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 0 15px;
        height: 44px;
        font-size: 18px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        i {
          font-size: 22px;
        }
        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}
.breadcrumb {
  position: absolute;
  padding: 15px 110px;
  font-size: 16px;
  color: #4c4c4c;
  right: 0;
  top: 150px;
  i {
    color: var(--themeMainColor);
    margin-right: 5px;
  }
}
</style>
