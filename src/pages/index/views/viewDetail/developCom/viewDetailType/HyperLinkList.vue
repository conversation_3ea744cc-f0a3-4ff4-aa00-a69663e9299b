<template>
  <!-- 超链接列表ui类型 -->
  <div class="view-menu-type">
    <div class="content-list">
      <ul>
        <li v-for="(item, idx) in viewHyperLinkList" :key="item.uuid">
          <span class="bullet"></span>
          <span class="item-title ellipsis" @click="btnClick(item)">{{ item.menuName }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: "HyperLinkList",
  props: {
    viewHyperLinkList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showActive: -1,
    };
  },
  methods: {
    formatUrl(url) {
      if (!url) return "";
      // 如果URL不是以http://或https://开头，则添加https://
      if (!/^https?:\/\//i.test(url)) {
        return "https://" + url;
      }
      return url;
    },
    btnClick (item) {
      let targetUrl = this.formatUrl(item.hyperlinks);
      window.open(targetUrl, "_blank");
    },
  },
};
</script>
<style lang="scss" scoped>
.view-menu-type {
  width: 100%;
  padding: 20px 0;
  position: relative;
  .content-list {
    padding: 20px 100px 20px 200px;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 30px 20px;

      li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
        position: relative;
        .bullet {
          background: var(--themeMainColor);
          width: 10px;
          height: 10px;
          position: absolute;
          top: 50%;
          transform: translate(-150%, -50%);
        }

        .item-title {
          font-size: 22px;
          color: #333333;
          line-height: 22px;
          cursor: pointer;
          flex-grow: 1;

          &:hover {
            color: var(--themeMainColor);
          }
        }
      }
    }
  }
}
.breadcrumb {
  position: absolute;
  padding: 15px 110px;
  font-size: 16px;
  color: #4c4c4c;
  right: 0;
  top: 150px;
  i {
    color: var(--themeMainColor);
    margin-right: 5px;
  }
}
</style>
