<template>
  <div class="second-title">
    <div class="title">{{ title }}</div>
    <div class="tabs" v-if="showTabs">
      <div
        @mouseenter="showActive = idx"
        @mouseleave="showActive = -1"
        v-for="(tab, idx) in tabs"
        :key="tab.key"
        :class="['tab-item', { active: tab.uuid === activeUuid }]"
        @click="tabItemClick(tab, idx)"
      >
        {{ tab.menuName }}
        <!-- <div class="tabs-bg" v-if="tab.uuid === activeUuid || showActive === idx"></div> -->
        <!-- <div class="tab-active" v-if="tab.uuid === activeUuid || showActive === idx">
          <div class="arc-container">
            <div class="arc left"></div>
            <div class="arc right"></div>
          </div>
        </div> -->
      </div>
    </div>
    <div class="tabs2" v-else></div>
    <div class="breadcrumb">
      <i class="el-icon-s-home"></i>
      <span>
        <span @click="goHome" style="cursor: pointer;">首页</span>
        <span @click="btnClickBack" style="cursor: pointer;">&nbsp;-&nbsp;{{ title }}</span>
        <span v-if="showDetail">&nbsp;>&nbsp;正文</span>
        <span v-else-if="showTabs">{{ " - " + tabs.find((item) => item.uuid === activeUuid).menuName }}</span>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  name: "SecondTitle",
  props: {
    title: {
      type: String,
    },
    showTabs: {
      type: Boolean,
      default: true,
    },
    showDetail: {
      type: Boolean,
      default: false,
    },
    tabs: {
      type: Array,
      default: () => [],
    },
    activeUuid: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showActive: -1,
    };
  },
  methods: {
    tabItemClick(tab, idx) {
      this.$emit("tabItemClick", tab, idx);
    },
    btnClickBack() {
      this.$emit("btnClickBack");
    },
    goHome() {
      this.$router.push("/");
      this.$store.commit("saveActiveUuid", "1");
      // this.$store.commit("initClear");
    },
  },
};
</script>
<style lang="scss" scoped>
.second-title {
  position: relative;
  margin-top: -130px;
}
.title {
  height: 40px;
  font-size: 42px;
  color: #ffffff;
  margin: 0 0 55px 113px;
}
.tabs {
  // width: 962px;
  height: 80px;
  border-radius: 0 40px 40px 0;
  display: flex;
  width: fit-content;
  gap: 48px;
  margin-bottom: 50px;
  font-size: 22px;
  color: #ffffff;
  position: relative;
  padding: 0 200px 0 110px;
  background: var(--themeMainColor);
  z-index: 101;
  .tab-item {
    padding: 0 20px;
    min-width: 80px;
    line-height: 80px;
    text-align: center;
    cursor: pointer;
    position: relative;
      font-size: 22px;

    .tab-active {
      width: 33px;
      height: 14px;
      position: absolute;
      left: 50%;
      bottom: 0px;
      transform: translateX(-50%);
      overflow: hidden;
      .arc-container {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
        width: 90%;
        height: 100%;
        background-color: #ffffff;
      }
      .arc {
        position: absolute;
        right: 30%;
        bottom: 0;
        width: 50px;
        height: 50px;
        background-color: var(--themeMainColor);
        border-radius: 50%;
      }
      .right {
        transform: rotate(90deg);
        left: 30%;
      }
    }
    .tabs-bg {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      padding:0 10px;
      background-color: #ffffff;
      font-weight: bold;
      color: var(--themeMainColor);
    }
  }
  .active {
    font-weight: bold;
    background-color: #ffffff;
    color: var(--themeMainColor);
  }
}
.tabs2 {
  height: 80px;
  margin-bottom: 50px;
}
.breadcrumb {
  position: absolute;
  padding: 15px 110px;
  font-size: 16px;
  color: #4c4c4c;
  right: 0;
  top: 150px;
  i {
    color: var(--themeMainColor);
    margin-right: 3px;
  }
}
</style>
