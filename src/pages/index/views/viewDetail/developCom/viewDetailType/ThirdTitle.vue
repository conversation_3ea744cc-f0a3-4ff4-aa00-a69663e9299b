<template>
  <div class="tabs">
    <div
      class="tabs-item"
      v-for="(tab, index) in tabs"
      :key="index"
      :class="{ active: activeUuid === tab.uuid }"
      @click="tabItemClick(tab, index)"
    >
      {{ tab.menuName }}
    </div>
  </div>
</template>
<script>
export default {
  name: "ThirdTitle",
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
    activeUuid: {
      type: String,
      default: "",
    },
  },
  created() {
    let uuid = this.$store.state.saveViewListDetailUuid;
    if (uuid) {
      this.$store.commit("saveViewListDetailUuid", "");
      let findIndex = this.tabs.findIndex((item) => item.uuid === uuid);
      if (findIndex !== -1) {
        setTimeout(() => {
          this.tabItemClick(this.tabs[findIndex], findIndex);
        }, 0);
      }
    }
  },
  methods: {
    tabItemClick(tab, idx) {
      this.$emit("tabItemClick", tab, idx);
    },
  },
};
</script>
<style lang="scss" scoped>
.tabs {
  height: 60px;
  display: flex;
  gap: 64px;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #dcdcdc;
  margin: 0 110px;
  margin-bottom: 45px;
  i {
    font-size: 30px;
    width: 30px;
    margin-right: 13px;
    color: var(--themeMainColor);
  }
}
.tabs-item {
  cursor: pointer;
  padding: 0 15px 0;
  color: #222;
  display: flex;
  align-items: center;
  transition: background 0.2s, color 0.2s;
  font-size: 18px;
  color: #333333;
}
.tabs .active {
  position: relative;
}
.tabs .active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--themeMainColor);
  transition: background 0.2s ease;
}
.tabs .active::before {
  content: "";
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid var(--themeMainColor);
}
</style>
