<template>
  <div class="detail-container">
    <div class="view-detail-view">
      <el-row>
        <div class="view-detail-header">
          <h1 class="view-title">{{ viewDetailContent.title }}</h1>
          <div class="view-meta">
            <span class="view-date">发布时间：{{ viewDetailContent.publishDate }}</span>
            <!-- <span class="view-views">浏览次数：{{ viewDetailContent.viewCount || 0 }}</span> -->
          </div>
        </div>
        <Editor style="overflow-y: hidden;" :value="viewDetailContent.content" :defaultConfig="{ readOnly: true }" mode="simple" />
      </el-row>
    </div>
  </div>
</template>

<script>
import { Editor } from "@wangeditor/editor-for-vue";

export default {
  name: "ViewDetail",
  components: {
    Editor,
  },
  props: {
    viewDetailContent: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl + "/",
    };
  },
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
@import "@index/css/global.scss";
.detail-container {
  padding: 0 115px;
}
.view-detail-view {
  padding: 0 80px 54px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.2);
  .view-detail-header {
    padding: 30px 0;
    border-bottom: 1px solid #e9e9e9;
    text-align: center;

    .view-title {
      font-size: 22px;
      color: #333333;
      margin-bottom: 25px;
    }

    .view-meta {
      font-size: 16px;
      color: #333333;
      text-align: left;
      .view-date,
      .view-views {
        display: inline-block;
        margin-right: 52px;
      }
    }
  }
}
</style>
