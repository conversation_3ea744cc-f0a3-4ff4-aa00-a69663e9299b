<template>
  <div class="detail-container">
    <div class="news-detail-view">
      <el-row>
        <el-col v-loading="loadingDetail" :span="24">
          <div class="news-detail-header">
            <h1 class="news-title">{{ newsDetail.title }}</h1>
            <div class="news-meta">
              <span class="news-date">发表时间：{{ newsDetail.publishDate }}</span>
              <!-- <span class="news-views">浏览次数：{{ newsDetail.viewCount || 0 }}</span> -->
            </div>
          </div>
          <div class="news-content">
            <Editor style="overflow-y: hidden;" :value="newsDetail.content" :defaultConfig="{ readOnly: true }" mode="simple" />
          </div>
        </el-col>
      </el-row>
      <div class="footer">
        <div class="navigation-buttons">
          <div v-if="prevArticle" class="footer-prev" @click="goToPrevArticle">上一条：{{ prevArticle.title }}</div>
          <div v-else class="footer-prev disabled">已经是第一条</div>
          <div v-if="nextArticle" class="footer-next" @click="goToNextArticle">下一条：{{ nextArticle.title }}</div>
          <div v-else class="footer-next disabled">已经是最后一条</div>
        </div>
        <div class="back-button" @click="backToList">
          返回列表
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Editor } from "@wangeditor/editor-for-vue";
export default {
  name: "NewsDetailView",
  components: {
    Editor,
  },
  props: {
    newsDetail: {
      type: Object,
      default: null,
    },
    // 所有文章列表数据
    articleList: {
      type: Array,
      default: () => [],
    },
    // 当前页码
    currentPage: {
      type: Number,
      default: 1,
    },
    // 每页数量
    pageSize: {
      type: Number,
      default: 10,
    },
    // 总条数
    total: {
      type: Number,
      default: 0,
    },
    loadingDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + this.$myStore.baseUrl + "/",
    };
  },
  computed: {
    // 当前文章在列表中的索引
    currentArticleIndex() {
      if (!this.newsDetail || !this.articleList.length) return -1;
      return this.articleList.findIndex((item) => item.uuid === this.newsDetail.uuid);
    },

    // 计算上一篇文章
    prevArticle() {
      if (this.currentArticleIndex <= 0) return null;
      return this.articleList[this.currentArticleIndex - 1];
    },

    // 计算下一篇文章
    nextArticle() {
      if (!this.newsDetail || !this.articleList.length) return null;

      // 如果找到了当前文章并且不是列表中的最后一个，返回下一篇
      if (this.currentArticleIndex > -1 && this.currentArticleIndex < this.articleList.length - 1) {
        return this.articleList[this.currentArticleIndex + 1];
      }

      // 如果是当前页的最后一篇，且不是总数据的最后一篇
      const currentTotal = this.currentPage * this.pageSize;
      if (this.currentArticleIndex === this.articleList.length - 1 && currentTotal < this.total) {
        // 需要获取下一页的第一条
        return null; // 由父组件负责加载下一页数据
      }
      return null;
    },

    // 是否是列表中的第一篇
    isFirstArticle() {
      return this.currentArticleIndex <= 0;
    },

    // 是否是列表中的最后一篇
    isLastArticle() {
      if (!this.newsDetail || !this.articleList.length) return true;

      const isLastInCurrentPage = this.currentArticleIndex === this.articleList.length - 1;
      const isLastOverall = this.currentPage * this.pageSize >= this.total;

      return isLastInCurrentPage && isLastOverall;
    },
  },
  methods: {
    backToList() {
      this.$emit("backToList");
    },

    goToPrevArticle() {
      if (this.prevArticle) {
        // 如果有上一篇文章，发送事件通知父组件切换
        this.$emit("goToPrev", this.prevArticle);
      }
    },

    goToNextArticle() {
      if (this.nextArticle) {
        // 如果有下一篇文章，发送事件通知父组件切换
        this.$emit("goToNext", this.nextArticle);
      } else if (!this.isLastArticle) {
        // 如果当前是本页最后一篇但不是总体最后一篇，需要获取下一页
        this.$emit("loadNextPage");
      }
    },
  },
  // 监听 newsDetail 的变化，确保组件能够响应数据变化
  watch: {
    newsDetail(newVal, oldVal) {
      if (newVal && oldVal && newVal.uuid !== oldVal.uuid) {
        // 当详情切换时，可以在这里做一些额外处理
        console.log("文章详情已更新:", newVal.title);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@index/css/global.scss";
.detail-container {
  padding: 0 115px;
}
.news-detail-view {
  padding: 0 80px 54px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.2);
  .news-detail-header {
    padding: 30px 0;
    border-bottom: 1px solid #e9e9e9;
    text-align: center;

    .news-title {
      font-size: 22px;
      color: #333333;
      margin-bottom: 25px;
    }

    .news-meta {
      font-size: 16px;
      color: #333333;
      text-align: left;
      .news-date,
      .news-views {
        display: inline-block;
        margin-right: 52px;
      }
    }
  }

  .news-content {
    padding: 30px 0;
    min-height: 200px;
    border-bottom: 1px solid #e9e9e9;
    .news-text {
      font-size: 16px;

      ::v-deep(img) {
        max-width: 100%;
        height: auto;
        margin: 20px auto;
        display: block;
      }

      ::v-deep(p) {
        margin-bottom: 15px;
        text-indent: 2em;
      }

      ::v-deep(h2) {
        font-size: 22px;
        font-weight: bold;
        margin: 25px 0 15px;
        color: var(--themeMainColor);
      }

      ::v-deep(h3) {
        font-size: 20px;
        font-weight: bold;
        margin: 20px 0 15px;
      }

      ::v-deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;

        th,
        td {
          border: 1px solid #ddd;
          padding: 8px 12px;
        }

        th {
          background-color: #f6f6f6;
        }
      }
    }
  }
}
.footer {
  margin-top: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navigation-buttons {
  display: flex;
  gap: 10px;
  max-width: 70%;
}

.footer-prev,
.footer-next {
  font-size: 16px;
  color: #333333;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 200px;

  &:hover {
    color: var(--themeMainColor);
  }

  &.disabled {
    color: #999;
    cursor: not-allowed;
  }
}

.back-button {
  width: 96px;
  height: 34px;
  background: var(--themeMainColor);
  border-radius: 4px;
  font-size: 18px;
  color: #ffffff;
  text-align: center;
  line-height: 34px;
  cursor: pointer;
}
</style>
