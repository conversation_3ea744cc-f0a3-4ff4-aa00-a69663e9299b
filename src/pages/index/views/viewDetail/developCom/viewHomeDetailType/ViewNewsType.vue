<template>
  <div class="view-detail">
    <div class="news-list">
      <newsItem
        v-for="(item, index) in viewList"
        :key="index"
        :item="item"
        :showImage="viewType === '工作动态'"
        @itemClick="handleItemClick"
      ></newsItem>
    </div>
  </div>
</template>

<script>
import { NewsItem } from "@index/components";

export default {
  name: "ViewNewsType",
  components: {
    NewsItem,
  },
  props: {
    viewList: {
      type: Array,
      default: () => [],
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    total: {
      type: Number,
      default: 0,
    },
    viewType: {
      type: String,
      default: "通知公告",
    },
  },
  data() {
    return {
      activeTab: 0,
      showActive: -1,
      tabs: [],
    };
  },
  methods: {
    tabItemClick(tab, indx) {
      this.activeTab = indx;
    },
    handleItemClick(item) {
      this.$emit("itemClick", item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@index/css/global.scss";

.view-detail {
  width: 100%;
  position: relative;
  min-height: 100px;

  .news-list {
    margin: 0 auto;
    padding: 20px 110px 60px;
  }
}
</style>
