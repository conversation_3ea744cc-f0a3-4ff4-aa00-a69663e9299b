<template>
  <div class="view-detail-type-five">
    <div class="news-list">
      <newsItem v-for="(item, index) in viewList" :key="index" :item="item" :showImage="false" @itemClick="handleItemClick"></newsItem>
    </div>
  </div>
</template>

<script>
import { SecondTitle, NewsItem } from "@index/components";

export default {
  name: "viewDetailTypeFive",
  components: {
    SecondTitle,
    NewsItem,
  },
  props: {
    viewList: {
      type: Array,
      default: () => [],
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    total: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      activeTab: 0,
      showActive: -1,
      tabs: [],
    };
  },
  methods: {
    tabItemClick(tab, indx) {
      this.activeTab = indx;
    },
    handleItemClick(item) {
      this.$emit("itemClick", item);
    },
    handlePageChange(pageNum, pageSize) {
      this.$emit("pageChange", {
        pageNum: pageNum,
        page: pageSize,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@index/css/global.scss";

.view-detail-type-five {
  width: 100%;
  position: relative;
  min-height: 100px;

  .news-list {
    margin: 0 auto;
    padding: 20px 110px 60px;
  }
}
</style>
