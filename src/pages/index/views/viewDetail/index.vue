<template>
  <div class="viewDetail">
    <HomeDetailMolule v-if="showHomeModule"></HomeDetailMolule>
    <SecondMenuModule v-else></SecondMenuModule>
  </div>
</template>
<script>
import { SecondMenuModule, HomeDetailMolule } from "@index/views/viewDetail/components";

export default {
  components: {
    SecondMenuModule, // 顶部菜单栏部分配置在首页时，进入的模块
    HomeDetailMolule, // 顶部菜单栏没有时候进入的模块 例：工作动态，通知公告
  },
  computed: {
    showHomeModule() {
      return this.$store.state.activeUuid === "1";
    },
  },
};
</script>
<style lang="scss" scoped>
.viewDetail {
  z-index: 999;
}
</style>
