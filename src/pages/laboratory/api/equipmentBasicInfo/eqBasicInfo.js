import request from '@laboratory/utils/request'

// 涉及到字典的字段 调取laboratory/basicMessage.js里的根据字典类型获取字典数据 getDictAll后面同理
// 例 classNumCode	String	是	分类号	字典[1008]
// 使用人列表
export function getAccNo(params) {
  return request({
    url: '/account',
    method: 'get',
    params
  })
}
// 出参字段说明
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ accNo
// └─ logonName	登录名
// └─ trueName	姓名
// └─ accNoTrueName	组合名

// 获取设备验收信息列表
export function deviceAcceptanceList(params) {
  return request({
    url: '/device/acceptance-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// devSn	Integer	否	设备编号（仪器编号）
// assetSn	String	否	资产编号
// devName	String	否	仪器名称
// devSource	String	否	仪器来源
// countryCode	String	否	国别码
// presentSituationCode	String	否	现状码
// directionOfUse	String	否	使用方向
// devKindUuid	String	否	仪器类型
// priceMin	Long	否	单价最小值
// priceMax	Long	否	单价最大值
// devModel	String	否	型号
// devSpecification	String	否	规格
// acceptanceStatus	Integer	否	验收状态（0 - 无效 1 - 草稿 2 - 待验收 3 - 验收中 4 - 已验收 5 - 验收不通过）
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ devId	Integer	设备id
// └─ uuid	String	设备uuid
// └─ devSn	Integer	设备编号（仪器编号）
// └─ devName	String	仪器名称
// └─ assetSn	String	资产编号
// └─ classNumCode	String	教育部分类号
// └─ classNumName	String	教育部分类号名称
// └─ devModel	String	型号
// └─ devSpecification	String	规格
// └─ price	Long	单价 单位分
// 如果页面展示元，需要除以100
// └─ devSource	String	仪器来源
// └─ devSourceName	String	仪器来源名称
// └─ countryCode	String	国别码
// └─ countryCodeName	String	国别码名称
// └─ presentSituationCode	String	现状码
// └─ presentSituationName	String	现状码名称
// └─ purchaseDate	LocalDate	购置日期
// └─ directionOfUse	String	使用方向
// └─ directionOfUseName	String	使用方向名称
// └─ kindId	Integer	设备类型ID
// └─ devKindName	String	设备类型名称
// └─ imgUrl	String	设备照片
// └─ deptId	Integer	单位ID（学院ID）
// └─ deptName	String	单位名称（单位名称）
// └─ acceptanceStatus	Integer	验收状态（0 - 无效 1 - 草稿 2 - 待验收 3 - 验收中 4 - 已验收 5 - 验收不通过）
// 获取设备基础信息列表
export function deviceBasicList (params) {
  return request({
    url: '/device/basic-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// beginDate	String	否	开始时间
// endDate	String	否	结束时间
// devSn	Integer	否	设备编号（仪器编号）
// assetSn	String	否	资产编号
// devName	String	否	仪器名称
// devSource	String	否	仪器来源
// countryCode	String	否	国别码
// presentSituationCode	String	否	现状码
// directionOfUse	String	否	使用方向
// devKindUuid	String	否	仪器类型
// devKindId	Integer	否	仪器类型ID 通过devKindUuid获取
// priceMin	Long	否	单价最小值
// priceMax	Long	否	单价最大值
// devModel	String	否	型号
// devSpecification	String	否	规格
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ devId	Integer	设备id
// └─ uuid	String	设备uuid
// └─ devSn	Integer	设备编号（仪器编号）
// └─ devName	String	设备名称（仪器名称）
// └─ assetSn	String	资产编号
// └─ imgUrl	String	设备照片
// └─ devModel	String	型号
// └─ devSpecification	String	规格
// └─ devKindName	String	设备类型
// └─ roomId	Integer	设备所在房间id
// └─ roomName	String	设备所在房间名称
// └─ floorName	String	所设备所在楼层
// └─ floorNo	String	所在层楼号
// └─ buildingId	Integer	楼宇id
// └─ buildingName	String	设备所在楼宇
// └─ campusId	Integer	校区id
// └─ campusName	String	校区名称
// └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
// └─ deptId	Integer	单位ID（学院ID）
// └─ deptName	String	单位名称（学院名称）
// └─ price	Long	单价 单位分
// 如果页面展示元，需要除以100
// └─ devSource	String	设备来源
// └─ devSourceName	String	设备来源名称
// └─ countryCode	String	国别码
// └─ countryCodeName	String	国别码名称
// └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
// └─ presentSituationCode	String	现状码
// └─ presentSituationName	String	现状码名称
// └─ directionOfUse	String	使用方向
// └─ directionOfUseName	String	使用方向名称
// └─ classNumCode	String	教育部设备分类号
// └─ classNumName	String	教育部设备分类号名称
// └─ kindId	Integer	设备类型ID
// └─ factoryName	String	厂商名称
// └─ factoryNumber	String	出厂号
// └─ factoryDate	LocalDateTime	出厂日期 格式为 yyyy - MM - dd HH: mm: ss
// 如果没有时分秒 则默认加上 00:00:00
// └─ factoryContact	String	厂商联系方式
// └─ warrantyDate	LocalDate	保修日期（保留期限） 格式为 yyyy - MM - dd
// └─ serviceLife	Integer	使用年限
// └─ usingAccNo	Integer	使用人ID（使用就是管理人，谁使用谁管理）
// └─ usingLogonName	String	使用人学工号
// └─ usingTrueName	String	使用人真实姓名
// └─ usingPersonName	String	使用人姓名 格式化后的姓名
// └─ usingDeptId	Integer	使用单位ID
// └─ usingDeptName	String	使用单位名称
// └─ searchTag	String	设备标签名称 中间逗号”,”隔开
// └─ tagId	String	rfid标签
// └─ latitude	BigDecimal	纬度（GPS标签）
// └─ longitude	BigDecimal	经度（GPS标签）
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 新增验收设备信息
export function deviceSaveForAcceptance (data) {
  return request({
    url: '/device/save-for-acceptance',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// devSn	Integer	是	设备编号（仪器编号）
// devName	String	是	仪器名称
// assetSn	String	是	资产编号
// classNumCode	String	是	分类号	字典[1008]
// devModel	String	是	型号
// devSpecification	String	是	规格
// price	Long	是	单价 单位分，如果页面展示元需要 * 100	最小值为 0
// devSource	String	是	仪器来源	字典[1011]
// countryCode	String	是	国别码
// presentSituationCode	String	是	现状码	字典[1012]
// purchaseDate	LocalDate	是	购置日期
// directionOfUse	String	是	使用方向	字典[1013]
// kindId	Integer	是	设备类型ID
// technicalParameters	String	否	技术参数
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	String

// 更新设备待验收/基础信息
export function deviceUpdate (data) {
  return request({
    url: '/device/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	设备uuid
// devSn	Integer	是	设备编号（仪器编号）
// devName	String	是	仪器名称
// assetSn	String	是	资产编号
// classNumCode	String	是	分类号
// devModel	String	是	型号
// devSpecification	String	是	规格
// price	Long	是	单价 单位分，如果页面展示元需要 * 100	最小值为 0
// devSource	String	是	仪器来源
// countryCode	String	是	国别码
// presentSituationCode	String	是	现状码
// purchaseDate	LocalDate	是	购置日期
// directionOfUse	String	是	使用方向
// kindId	Integer	是	设备类型ID
// technicalParameters	String	否	技术参数
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 更新设备扩展信息
export function deviceUpdateExtension (data) {
  return request({
    url: '/device/update-extension',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	设备uuid
// deptId	Integer	否	所属单位id
// roomId	Integer	是	所属房间id
// storageLocation	String	否	存放地点
// factoryName	String	否	厂商名称
// factoryNumber	String	否	出厂号
// factoryDate	LocalDateTime	否	出厂日期 格式为 yyyy - MM - dd HH: mm: ss
// 如果没有时分秒 则默认加上 00:00:00
// factoryContact	String	否	厂商联系方式
// warrantyDate	LocalDate	是	保修日期（保留期限） 格式为 yyyy - MM - dd
// serviceLife	Integer	是	使用年限
// usingAccNo	Integer	否	使用人ID（使用人就是管理人，谁使用谁管理）
// usingDeptId	Integer	否	使用单位ID
// searchTag	String	否	设备标签名称 中间逗号”,”隔开
// tagId	String	否	rfid标签
// latitude	BigDecimal	否	纬度（GPS标签）
// longitude	BigDecimal	否	经度（GPS标签）
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 设备详情
export function deviceDetail (params) {
  return request({
    url: '/device/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	DeviceDetailVO
// └─ devId	Integer	设备id
// └─ uuid	String	设备uuid
// └─ devSn	Integer	设备编号
// └─ devName	String	设备名称
// └─ assetSn	String	资产编号
// └─ imgUrl	String	设备照片
// └─ devModel	String	设备型号
// └─ devSpecification	String	设备规格
// └─ devKindName	String	设备类型
// └─ roomId	Integer	设备所在房间id
// └─ roomName	String	设备所在房间名称
// └─ floorName	String	所设备所在楼层
// └─ floorNo	String	所在层楼号
// └─ buildingId	Integer	楼宇id
// └─ buildingName	String	设备所在楼宇
// └─ campusId	Integer	校区id
// └─ campusName	String	校区名称
// └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
// └─ deptId	Integer	部门id（学院id）
// └─ deptName	String	部门名称（学院名称）
// └─ price	Long	单价（单位分）
// └─ devSource	String	设备来源
// └─ devSourceName	String	设备来源名称
// └─ countryCode	String	国别码
// └─ countryCodeName	String	国别码名称
// └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
// └─ presentSituationCode	String	现状码
// └─ presentSituationName	String	现状码名称
// └─ directionOfUse	String	使用方向
// └─ directionOfUseName	String	使用方向名称
// └─ classNumCode	String	教育部设备分类号
// └─ classNumName	String	教育部设备分类号名称
// └─ kindId	Integer	设备类型ID
// └─ factoryName	String	厂商名称
// └─ factoryNumber	String	出厂号
// └─ factoryDate	LocalDateTime	出厂日期 格式为 yyyy - MM - dd HH: mm: ss
// 如果没有时分秒 则默认加上 00:00:00
// └─ factoryContact	String	厂商联系方式
// └─ warrantyDate	LocalDate	保修日期（保留期限） 格式为 yyyy - MM - dd
// └─ serviceLife	Integer	使用年限
// └─ usingAccNo	Integer	使用人ID（使用就是管理人，谁使用谁管理）
// └─ usingLogonName	String	使用人学工号
// └─ usingTrueName	String	使用人真实姓名
// └─ usingPersonName	String	使用人姓名 格式化后的姓名
// └─ usingDeptId	Integer	使用单位ID
// └─ usingDeptName	String	使用单位名称
// └─ searchTag	String	设备标签名称 中间逗号”,”隔开
// └─ tagId	String	rfid标签
// └─ latitude	BigDecimal	纬度（GPS标签）
// └─ longitude	BigDecimal	经度（GPS标签）
// └─ acceptanceStatus	Integer	验收状态（0 - 无效 1 - 草稿 2 - 待验收 3 - 验收中 4 - 已验收 5 - 验收不通过）
// └─ acceptanceTime	LocalDateTime	验收时间
// └─ technicalParameters	String	技术参数
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 设备验收
export function devTechDocAcceptanceDevice (data) {
  return request({
    url: '/dev-tech-doc/acceptance-device',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 验收成功示例
// {
//     "code": 0,
//     "message": "请求成功!"
// }
// 验收失败，缺少技术资料示例
// {
//     "code": 1,
//     "message": "配置验收清单,目录资料内容1改硬件,类型6内容"
// }
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	String

// 仪器设备技术资料列表
export function devTechDocPage (params) {
  return request({
    url: '/dev-tech-doc/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderKey	String	否	排序字段(此处兼容老的排序)
// orderModel	String	否	排序模式（此处兼容老的排序）
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// beginDate	String	否	开始时间
// endDate	String	否	结束时间
// devUuid	String	是	仪器设备uuid
// devId	Integer	否	仪器设备ID 根据仪器设备uuid查询
// fileName	String	否	文件名称
// contentName	String	否	技术资料内容名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	技术资料ID
// └─ uuid	String	唯一值
// └─ devId	Integer	仪器设备id
// └─ fileId	Integer	文件id（common_file表file_id）
// └─ attachmentUrl	String	文件地址
// └─ fileName	String	原始文件名（含扩展名）
// └─ fileSize	Long	文件大小(字节)
// └─ fileType	String	文件扩展名
// └─ techDocContentId	Integer	技术资料内容id
// └─ techDocContentName	String	技术资料内容名称
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

// 新增仪器设备技术资料
export function devTechDocSave (data) {
  return request({
    url: '/dev-tech-doc/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// devUuid	String	是	仪器设备uuid
// attachmentUrl	String	是	文件地址
// techDocContentId	Integer	是	技术资料内容id
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除仪器设备技术资料（支持单个和多个）
export function devTechDocDelete (data) {
  return request({
    url: '/dev-tech-doc/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 获取设备下技术资料列表
export function devTechDocList (params) {
  return request({
    url: '/dev-tech-doc/list',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	设备uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	技术资料ID
// └─ uuid	String	唯一值
// └─ devId	Integer	仪器设备id
// └─ fileId	Integer	文件id（common_file表file_id）
// └─ attachmentUrl	String	文件地址
// └─ fileName	String	原始文件名（含扩展名）
// └─ fileSize	Long	文件大小(字节)
// └─ fileType	String	文件扩展名
// └─ techDocContentId	Integer	技术资料内容id
// └─ techDocContentName	String	技术资料内容名称
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间