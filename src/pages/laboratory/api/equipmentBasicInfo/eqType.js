import request from '@laboratory/utils/request'
import request_import from '@laboratory/utils/request_import';
// 仪器设备类型列表
export function devKindList(params) {
  return request({
    url: '/dev/kind/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// devKindName	String	否	设备类型名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ uuid	String	设备类型唯一标识
// └─ devKindId	Integer	设备类型id
// └─ devKindName	String	设备类型名称
// └─ kindClassName	String	设备类型 分类名称
// └─ maxUse	Integer	最大使用人数
// └─ formatDevKindName	String	格式化后的实验室名称
// └─ orderNum	Integer	排序
// └─ appNo	Integer	应用编号

// 新增仪器设备类型
export function devKindSave (data) {
  return request({
    url: '/dev/kind/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// devKindName	String	是	设备类型名称
// orderNum	Integer	否	排序序号
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Integer	设备类型id

// 更新仪器设备类型
export function devKindUpdate (data) {
  return request({
    url: '/dev/kind/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// devKindName	String	是	设备类型名称
// orderNum	Integer	否	排序序号
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除仪器设备类型
export function devKindDelete (data) {
  return request({
    url: '/dev/kind/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	UuidDTO	否	uuid传输对象
// └─ uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 仪器设备类型选项
export function devKindOptions (params) {
  return request({
    url: '/dev/kind/options',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ uuid	String	设备类型唯一标识
// └─ devKindId	Integer	设备类型id
// └─ devKindName	String	设备类型名称
// └─ orderNum	Integer	排序