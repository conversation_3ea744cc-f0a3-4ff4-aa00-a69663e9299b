import request from '@laboratory/utils/request'
import request_import from '@laboratory/utils/request_import';
// 设备技术资料分类列表
export function devTechDocCategoryList(params) {
  return request({
    url: '/dev-tech-doc/category/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// categoryName	String	否	分类名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	技术资料分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	技术资料分类名称
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// └─ logonName	String	更新人学工号
// └─ trueName	String	更新人真实姓名

// 新增设备技术资料分类
export function devTechDocCategorySave (data) {
  return request({
    url: '/dev-tech-doc/category/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// categoryName	String	是	技术资料分类名称
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer	分类id

// 更新设备技术资料分类
export function devTechDocCategoryUpdate (data) {
  return request({
    url: '/dev-tech-doc/category/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// categoryName	String	是	技术资料分类名称
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 删除设备技术资料分类
export function devTechDocCategoryDelete (data) {
  return request({
    url: '/dev-tech-doc/category/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 设备技术资料分类选项
export function devTechDocCategoryOptions (params) {
  return request({
    url: '/dev-tech-doc/category/options',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	技术资料分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	技术资料分类名称
// └─ orderNum	Integer	排序字段

// 技术资料内容列表
export function devTechDocContentPage (params) {
  return request({
    url: '/dev-tech-doc/content/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// contentName	String	否	内容名称
// categoryUuid	String	否	分类UUID
// categoryName	String	否	标识分类名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ contentId	Integer	技术资料内容id
// └─ uuid	String	唯一值
// └─ contentName	String	技术资料内容名称
// └─ categoryId	Integer	分类id
// └─ categoryName	String	分类名称
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// └─ logonName	String	更新人学工号
// └─ trueName	String	更新人真实姓名

// 新增技术资料内容
export function devTechDocContentSave (data) {
  return request({
    url: '/dev-tech-doc/content/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// contentName	String	是	技术资料内容名称
// categoryId	Integer	是	分类id
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

// 更新技术资料内容
export function devTechDocContentUpdate (data) {
  return request({
    url: '/dev-tech-doc/content/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// contentName	String	是	技术资料内容名称
// categoryId	Integer	是	分类id
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除技术资料内容
export function devTechDocContentDelete (data) {
  return request({
    url: '/dev-tech-doc/content/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 技术资料内容选项列表
export function devTechDocContentOptions (params) {
  return request({
    url: '/dev-tech-doc/content/options',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ contentId	Integer	技术资料内容id
// └─ uuid	String	唯一值
// └─ contentName	String	技术资料内容名称
// └─ orderNum	Integer	排序字段

// 获取技术资料分类及内容列表
export function devTechDocContentList (params) {
  return request({
    url: '/dev-tech-doc/content/list',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	技术资料分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	技术资料分类名称
// └─ orderNum	Integer	排序字段
// └─ contentList	List	技术资料内容列表
//     └─ contentId	Integer	技术资料内容id
//     └─ uuid	String	唯一值
//     └─ contentName	String	技术资料内容名称
//     └─ orderNum	Integer	排序字段

// 仪器设备类型技术资料关联列表 (用于基本信息里某设备上传技术资料时的展示)
export function devKindDocRelationList (params) {
  return request({
    url: '/dev-kind-doc/relation/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// devKindUuid	String	是	仪器设备类型uuid
// contentName	String	否	技术资料内容名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	自增id
// └─ uuid	String	关联关系uuid
// └─ devKindId	Integer	设备类型id
// └─ techDocContentId	Integer	技术资料内容id
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ techDocContent	DevTechDocContentVO	技术资料内容基本信息
//     └─ contentId	Integer	技术资料内容id
//     └─ uuid	String	唯一值
//     └─ contentName	String	技术资料内容名称
//     └─ categoryId	Integer	分类id
//     └─ categoryName	String	分类名称
//     └─ kind	Integer	类型
//     └─ property	Integer	属性
//     └─ orderNum	Integer	排序字段
//     └─ createBy	Integer	创建人ID
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ updateBy	Integer	更新人ID
//     └─ updateByName	String	更新人名称
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ memo	String	备注
//     └─ logonName	String	更新人学工号
//     └─ trueName	String	更新人真实姓名

// 保存仪器设备类型技术资料关联关系
export function devKindDocRelationSave (data) {
  return request({
    url: '/dev-kind-doc/relation/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// devKindUuid	String	是	设备类型UUID
// techDocContentIds	Set	否	技术资料内容ID集合
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 仪器设备类型关联的技术资料内容ID列表
export function devKindDocRelationTechDocContentIds (params) {
  return request({
    url: '/dev-kind-doc/relation/tech-doc-content-ids',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// devKindId	Integer	是	仪器设备类型ID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
