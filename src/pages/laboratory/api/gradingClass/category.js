import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

//实验室安全类型列表
export function labSafetyCategoryList (params) {
  return request({
    url: '/lab-safety/category/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// categoryName	String	否	类型名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	实验室类型
// └─ detailedExplanation	String	详细说明
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// └─ logonName	String	更新人学工号
// └─ trueName	String	更新人真实姓名

//保存实验室安全类型
export function labSafetyCategorySave (data) {
  return request({
    url: '/lab-safety/category/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// categoryName	String	是	实验室类型
// detailedExplanation	String	是	详细说明
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

//更新实验室安全类型
export function labSafetyCategoryUpdate (data) {
  return request({
    url: '/lab-safety/category/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// categoryName	String	是	实验室类型
// detailedExplanation	String	是	详细说明
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//删除实验室安全类型
export function labSafetyCategoryDelete (data) {
  return request({
    url: '/lab-safety/category/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//实验室安全类型选项
export function labSafetyCategoryOptions () {
  return request({
    url: '/lab-safety/category/options',
    method: 'get',
    XAPIVersion: '2.0.0'
  })
}
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	实验室类型

//导出实验室安全类型
export function labSafetyCategoryExport (params) {
  return request_import({
    url: '/lab-safety/category/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}