import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

// 涉及到字典的字段 调取laboratory/basicMessage.js里的根据字典类型获取字典数据 getDictAll后面同理
// 例 roomLabCategory	String	否	实验室类别	字典[1014]
// 实验中心分级列表
export function labSafetyLabCenterList (params) {
  return request({
    url: '/lab-safety/lab-center/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ labCenterInfo	RoomKindVO	实验中心信息
//     └─ roomKindId	Integer	实验中心id
//     └─ uuid	String	实验中心唯一值
//     └─ roomKindName	String	实验中心名称
//     └─ roomKindKind	Integer	实验中心分类
//     └─ roomKindProp	Integer	实验中心属性
//     └─ roomKindImage	String	实验中心图片（封面）
//     └─ roomKindDesc	String	实验中心介绍
//     └─ roomKindSummary	String	实验中心简介
//     └─ roomLabCount	Integer	实验室数量
//     └─ orderNum	Integer	排序
//     └─ roomCategory	String	实验室类别
//     └─ roomCategoryName	String	实验室类别名称
//     └─ roomType	String	实验室类型
//     └─ roomTypeName	String	实验室类型名称
//     └─ roomKindLevel	Integer	实验中心级别
//     └─ deptId	Integer	所属部门id
//     └─ deptName	String	所属部门名称
//     └─ contactInfo	String	联系方式
//     └─ roomKindAddress	String	实验中心地址
//     └─ responsiblePerson	String	负责人
//     └─ safetyOfficer	String	安全负责人
//     └─ apiVersion	String	api版本
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ memo	String	备注
// └─ labSafetyRiskLevelSummaryList	List	实验室风险等级汇总
//     └─ scoreLevelId	Integer	分值等级id
//     └─ scoreLevelName	String	分级分值名称
//     └─ displayValue	String	展示值
//     └─ displayColors	String	展示颜色
//     └─ orderNum	Integer	排序字段
//     └─ labCount	Integer	实验室数量
// └─ gradedLabCount	Integer	已分级实验室数量
// └─ ungradedLabCount	Integer	未分级实验室数量
// └─ totalLabCount	Integer	实验室总数

// 实验室分级列表
export function labSafetyLabList (params) {
  return request({
    url: '/lab-safety/lab/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// scoreLevelUuid	String	否	分级分值uuid
// scoreLevelName	String	否	分级分值名称
// roomLabName	String	否	实验室名称
// responsiblePerson	String	否	实验室管理员
// categoryName	String	否	实验室分类
// labCenterUuid	String	否	实验中心uuid
// deptUuid	String	否	学院uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ labInfo	LabInfoVO	实验室信息
//     └─ roomLabId	Integer	实验室id
//     └─ uuid	String	唯一值
//     └─ roomLabSn	String	实验室编号
//     └─ roomLabName	String	实验室名称
//     └─ roomLabNameEn	String	实验室英文名称
//     └─ roomLabCategory	String	实验室类别
//     └─ roomLabCategoryName	String	实验室类别名称
//     └─ roomLabType	String	实验室类型
//     └─ roomLabTypeName	String	实验室类型名称
//     └─ deptId	Integer	学院id
//     └─ deptName	String	学院名称
//     └─ roomLabDesc	String	实验室介绍
//     └─ roomLabImage	String	实验室图片
//     └─ roomKindId	Integer	实验室类型(中心)id
//     └─ roomLabSummary	String	实验室简介
//     └─ hazardLevel	Integer	实验室危险等级
//     └─ hazardScore	Integer	实验室危险评分
//     └─ status	Integer	状态：0:未评估 1：已评估
//     └─ orderNum	Integer	排序
//     └─ kind	Integer	类型
//     └─ property	Integer	属性
//     └─ establishmentDate	LocalDate	建立日期
//     └─ usageStatus	Integer	使用状态 1:在用 2:停用
//     └─ labAcademic	String	实验室所属学科
//     └─ labAcademicName	String	实验室所属学科名称
//     └─ contactInfo	String	联系方式
//     └─ labAddress	String	实验室地址
//     └─ responsiblePerson	String	负责人
//     └─ safetyOfficer	String	安全负责人
//     └─ apiVersion	String	接口版本
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ memo	String	备注
//     └─ roomArea	BigDecimal	房间面积
//     └─ roomCount	Integer	房间数量
// └─ scoreLevelId	Integer	分值等级id
// └─ scoreLevelName	String	分级分值名称
// └─ displayValue	String	展示值
// └─ displayColors	String	展示颜色
// └─ categoryId	Integer	分类id
// └─ categoryName	String	实验室分类
// └─ assessStatus	Integer	评估状态 0:未评估 1：已评估
// 实验室详情
export function labSafetyLabDetail (params) {
  return request({
    url: '/lab-safety/lab/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	LabSafetyLabDetailVO
// └─ labInfo	LabInfoVO	实验室信息
//     └─ roomLabId	Integer	实验室id
//     └─ uuid	String	唯一值
//     └─ roomLabSn	String	实验室编号
//     └─ roomLabName	String	实验室名称
//     └─ roomLabNameEn	String	实验室英文名称
//     └─ roomLabCategory	String	实验室类别
//     └─ roomLabCategoryName	String	实验室类别名称
//     └─ roomLabType	String	实验室类型
//     └─ roomLabTypeName	String	实验室类型名称
//     └─ deptId	Integer	学院id
//     └─ deptName	String	学院名称
//     └─ roomLabDesc	String	实验室介绍
//     └─ roomLabImage	String	实验室图片
//     └─ roomKindId	Integer	实验室类型(中心)id
//     └─ roomLabSummary	String	实验室简介
//     └─ hazardLevel	Integer	实验室危险等级
//     └─ hazardScore	Integer	实验室危险评分
//     └─ status	Integer	状态：0:未评估 1：已评估
//     └─ orderNum	Integer	排序
//     └─ kind	Integer	类型
//     └─ property	Integer	属性
//     └─ establishmentDate	LocalDate	建立日期
//     └─ usageStatus	Integer	使用状态 1:在用 2:停用
//     └─ labAcademic	String	实验室所属学科
//     └─ labAcademicName	String	实验室所属学科名称
//     └─ contactInfo	String	联系方式
//     └─ labAddress	String	实验室地址
//     └─ responsiblePerson	String	负责人
//     └─ safetyOfficer	String	安全负责人
//     └─ apiVersion	String	接口版本
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ memo	String	备注
//     └─ roomArea	BigDecimal	房间面积
//     └─ roomCount	Integer	房间数量
// └─ scoreLevelId	Integer	分值等级id
// └─ scoreLevelName	String	分级分值名称
// └─ displayValue	String	展示值
// └─ displayColors	String	展示颜色
// └─ categoryId	Integer	分类id
// └─ categoryName	String	实验室分类
// 实验室分类评定结果保存
export function labSafetyCategoryRateSave (data) {
  return request({
    url: '/lab-safety/category-rating/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labUuid	String	是	实验室uuid
// categoryUuid	String	是	实验室分类uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void
// 保存实验室警示标志信息
export function labWarningSignSave (data) {
  return request({
    url: '/lab/warning-sign/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室uuid
// signId	List	是	安全标识id列表 一个都不选传[] 不能不传
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void
// 获取实验室警示标志列表
export function labSafetyWarningSignList (params) {
  return request({
    url: '/lab/warning-sign/list',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ signId	Integer	标识id
// └─ uuid	String	唯一值
// └─ signName	String	标识名称
// └─ signImagePath	String	标识图片路径
// └─ orderNum	Integer	标识顺序
// └─ categoryId	Integer	标识分类ID，外键关联 lab_safety_sign_category 表
// └─ categoryName	String	标识分类名称
// └─ categoryOrderNum	Integer	标识分类顺序

// 保存实验室防护要点信息
export function labProtectionPointSave (data) {
  return request({
    url: '/lab/protection-point/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室uuid
// protectionPointId	List	是	防护要点id列表 一个都不选传[] 不能不传
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 获取实验室防护要点列表
export function labProtectionPointList (params) {
  return request({
    url: '/lab/protection-point/list',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ protectionPointId	Integer	防护要点id
// └─ uuid	String	唯一值
// └─ protectionPointName	String	防护要点名称
// └─ pointType	String	防护要点类型
// └─ orderNum	Integer	排序字段
// └─ signId	Integer	安全标识id
// └─ signName	String	安全标识名称
// └─ signImagePath	String	安全标识图片路径

// 保存实验室安全(风险点)评估记录
export function labSafetyAssessmentSave (data) {
  return request({
    url: '/lab-safety/assessment/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室uuid
// categoryLevelIds	List	是	安全类目等级id集合
// hazardIds	List	是	安全风险源id集合 一个都不选传[] 不能不传
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 实验当前安全等级(风险点)详情
export function labSafetyCurrentLevelDetail (params) {
  return request({
    url: '/lab-safety/current-level/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabSafetyCurrentLevelVO
// └─ assessmentStatus	Boolean	实验风险评估状态 true - 已评估 false - 未评估
// └─ currentLevelId	Integer	实验室当前安全等级id
// └─ uuid	String	唯一值
// └─ labId	Integer	实验室id
// └─ assessmentId	Integer	安全评估记录id
// └─ hazardJson	List	风险源json
//     └─ hazardId	Integer	风险源id
//     └─ uuid	String	唯一值
//     └─ hazardCategoryId	Integer	风险源分类id
//     └─ hazardName	String	风险源名称
//     └─ hazardScore	Integer	风险分值
//     └─ orderNum	Integer	排序字段
//     └─ hazardCategoryName	String	风险源分类名称
//     └─ categoryOrderNum	Integer	风险源分类排序字段
// └─ safetyCategoryLevelJson	List	安全类目等级json
//     └─ categoryLevelId	Integer	分级类目id
//     └─ uuid	String	唯一值
//     └─ scoreLevelId	Integer	分级分值id
//     └─ categoryLevelBasis	String	分级依据
//     └─ orderNum	Integer	排序字段
//     └─ hazardCategoryName	String	危险源分类名称
//     └─ scoreLevelName	String	分级分值名称
//     └─ displayValue	String	展示值
//     └─ displayColors	String	展示颜色
//     └─ scoreLevelOrderNum	Integer	分级分值排序
// └─ hazardScore	Integer	风险评分
// └─ scoreLevelId	Integer	安全分级分值id
// └─ scoreLevelName	String	分级分值名称
// └─ displayValue	String	展示值
// └─ displayColors	String	展示颜色