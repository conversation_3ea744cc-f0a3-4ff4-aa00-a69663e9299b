import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

// 安全分级类目列表
export function labSafetyCategoryLevelList (params) {
  return request({
    url: '/lab-safety/category-level/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// scoreLevelName	String	否	级别名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ categoryLevelId	Integer	分级类目id
// └─ uuid	String	唯一值
// └─ scoreLevelId	Integer	分级分值id
// └─ scoreLevelName	String	分级分值名称
// └─ categoryLevelBasis	String	分级依据
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

//保存安全分级类目
export function labSafetyCategoryLevelSave (data) {
  return request({
    url: '/lab-safety/category-level/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// scoreLevelId	Integer	是	分级分值id
// categoryLevelBasis	String	是	分级依据
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

//更新安全分级类目
export function labSafetyCategoryLevelUpdate (data) {
  return request({
    url: '/lab-safety/category-level/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// scoreLevelId	Integer	是	分级分值id
// categoryLevelBasis	String	是	分级依据
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//删除安全分级类目
export function labSafetyCategoryLevelDelete (data) {
  return request({
    url: '/lab-safety/category-level/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//安全分级类目选项
export function labSafetyCategoryLevelOptionsList () {
  return request({
    url: '/lab-safety/category-level/options',
    method: 'get',
    XAPIVersion: '2.0.0'
  })
}
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ categoryLevelId	Integer	分级类目id
// └─ uuid	String	唯一值
// └─ scoreLevelId	Integer	分级分值id
// └─ scoreLevelName	String	分级分值名称
// └─ categoryLevelBasis	String	分级依据

//导出分级类目
export function labSafetyCategoryLevelExport (params) {
  return request_import({
    url: '/lab-safety/category-level/export',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}

//安全分值等级列表
export function labSafetyScoreLevelList (params) {
  return request({
    url: '/lab-safety/score-level/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// scoreLevelName	String	否	分值名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ scoreLevelId	Integer	分值等级id
// └─ uuid	String	唯一值
// └─ scoreLevelName	String	分值等级名称
// └─ maxScore	Integer	最大值
// └─ minScore	Integer	最小值
// └─ displayValue	String	展示值
// └─ displayColors	String	展示颜色
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

//保存安全分级分值
export function labSafetyScoreLevelSave (data) {
  return request({
    url: '/lab-safety/score-level/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// scoreLevelName	String	是	分级分值名称
// maxScore	Integer	是	最大值	-1表示无限大
// minScore	Integer	是	最小值
// displayValue	String	是	展示值
// displayColors	String	是	展示颜色
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

//更新安全分级分值
export function labSafetyScoreLevelUpdate (data) {
  return request({
    url: '/lab-safety/score-level/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// scoreLevelName	String	是	分级分值名称
// maxScore	Integer	是	最大值	-1表示无限大
// minScore	Integer	是	最小值
// displayValue	String	是	展示值
// displayColors	String	是	展示颜色
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//删除安全分级分值
export function labSafetyScoreLevelDelete (data) {
  return request({
    url: '/lab-safety/score-level/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//安全分级分值选项
export function labSafetyScoreLevelOptionsList () {
  return request({
    url: '/lab-safety/score-level/options',
    method: 'get',
    XAPIVersion: '2.0.0'
  })
}
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ scoreLevelId	Integer	分值等级id
// └─ uuid	String	唯一值
// └─ scoreLevelName	String	分值等级名称
// └─ displayValue	String	展示值
// └─ displayColors	String	展示颜色

//导出安全分级分值
export function labSafetyScoreLevelExport (params) {
  return request_import({
    url: '/lab-safety/score-level/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}

//危险源分类列表
export function labSafetyHazardCategoryList (params) {
  return request({
    url: '/lab-safety/hazard-category/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderKey	String	否	排序字段(此处兼容老的排序)
// orderModel	String	否	排序模式（此处兼容老的排序）
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// beginDate	String	否	开始时间
// endDate	String	否	结束时间
// hazardCategoryName	String	否	危险源分类名称
// uuids	List	否	危险源分类UUID列表
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ hazardCategoryId	Integer	风险源分类id
// └─ uuid	String	唯一值
// └─ hazardCategoryName	String	危险源分类名称
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// └─ logonName	String	学工号
// └─ trueName	String	真实姓名

//保存危险源分类
export function labSafetyHazardCategorySave (data) {
  return request({
    url: '/lab-safety/hazard-category/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// hazardCategoryName	String	是	危险源分类名称
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

//更新危险源分类
export function labSafetyHazardCategoryUpdate (data) {
  return request({
    url: '/lab-safety/hazard-category/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// hazardCategoryName	String	是	危险源分类名称
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//删除危险源分类
export function labSafetyHazardCategoryDelete (data) {
  return request({
    url: '/lab-safety/hazard-category/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//危险源分类选项列表
export function labSafetyHazardCategoryOptionsList () {
  return request({
    url: '/lab-safety/hazard-category/options',
    method: 'get',
    XAPIVersion: '2.0.0'
  })
}
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ hazardCategoryId	Integer	风险源分类id
// └─ uuid	String	唯一值
// └─ hazardCategoryName	String	危险源分类名称

//导出危险源分类数据
export function labSafetyHazardCategoryExport (params) {
  return request_import({
    url: '/lab-safety/hazard-category/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}

//危险源列表
export function labSafetyHazardList (params) {
  return request({
    url: '/lab-safety/hazard/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// hazardCategoryUuid	String	否	风险源分类UUID
// hazardCategoryName	String	否	风险源分类名称
// hazardName	String	否	风险源名称
// hazardScoreMin	Integer	否	风险分值范围最小值
// hazardScoreMax	Integer	否	风险分值范围最大值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ hazardId	Integer	风险源id
// └─ uuid	String	唯一值
// └─ hazardCategoryId	Integer	风险源分类id
// └─ hazardCategoryName	String	风险源分类名称
// └─ hazardName	String	风险源名称
// └─ hazardScore	Integer	风险分值
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// └─ logonName	String	学工号
// └─ trueName	String	真实姓名

//保存危险源信息
export function labSafetyHazardSave (data) {
  return request({
    url: '/lab-safety/hazard/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// hazardCategoryId	Integer	是	风险源分类id
// hazardName	String	是	风险源名称
// hazardScore	Integer	是	风险分值	最小值为 0
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

//更新危险源信息
export function labSafetyHazardUpdate (data) {
  return request({
    url: '/lab-safety/hazard/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// hazardCategoryId	Integer	是	风险源分类id
// hazardName	String	是	风险源名称
// hazardScore	Integer	是	风险分值	最小值为 0
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//删除危险源信息
export function labSafetyHazardDelete (data) {
  return request({
    url: '/lab-safety/hazard/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

//危险源选项列表
export function labSafetyHazardOptionsList () {
  return request({
    url: '/lab-safety/hazard/options',
    method: 'get',
    XAPIVersion: '2.0.0'
  })
}
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ hazardId	Integer	风险源id
// └─ uuid	String	唯一值
// └─ hazardCategoryId	Integer	风险源分类id
// └─ hazardCategoryName	String	风险源分类名称
// └─ hazardName	String	风险源名称
// └─ hazardScore	Integer	风险分值

//导出危险源数据
export function labSafetyHazardExport (params) {
  return request_import({
    url: '/lab-safety/hazard/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}