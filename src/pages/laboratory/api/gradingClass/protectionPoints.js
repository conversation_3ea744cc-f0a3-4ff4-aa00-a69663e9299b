import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

// 涉及到字典的字段 调取laboratory/basicMessage.js里的根据字典类型获取字典数据 getDictAll后面同理
// 例 roomLabCategory	String	否	实验室类别	字典[1014]

// 安全防护要点列表
export function labSafetyprotectionPointList (params) {
  return request({
    url: '/lab-safety/protection-point/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// protectionPointName	String	否	防护要点名称
// pointType	String	否	防护要点类型
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ protectionPointId	Integer	防护要点id
// └─ uuid	String	唯一值
// └─ protectionPointName	String	防护要点名称
// └─ pointType	String	防护要点类型
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// └─ signId	Integer	安全标识id
// └─ signName	String	安全标识名称
// └─ signImagePath	String	安全标识图片路径

// 保存安全防护要点
export function labSafetySignCategorySave (data) {
  return request({
    url: '/lab-safety/protection-point/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// protectionPointName	String	是	防护要点名称
// pointType	String	是	防护要点类型
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Integer

// 更新安全防护要点
export function labSafetySignCategoryUpdate (data) {
  return request({
    url: '/lab-safety/protection-point/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// protectionPointName	String	是	防护要点名称
// pointType	String	是	防护要点类型
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 删除安全防护要点
export function labSafetySignCategoryDelete (data) {
  return request({
    url: '/lab-safety/protection-point/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 安全防护要点选项列表
export function labSafetyProtectionPointOptionsList (params) {
  return request({
    url: '/lab-safety/protection-point/options',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// protectionPointName	String	否	防护要点名称
// pointType	String	否	防护要点类型
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ protectionPointId	Integer	防护要点id
// └─ uuid	String	唯一值
// └─ protectionPointName	String	防护要点名称
// └─ pointType	String	防护要点类型

// 导出安全防护要点信息
export function labSafetyProtectionPointExport (params) {
  return request_import({
    url: '/lab-safety/protection-point/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// protectionPointName	String	否	防护要点名称
// pointType	String	否	防护要点类型
// uuids	List	否	防护要点UUID列表
// 出参
// 此接口无任何出参

// 保存防护要点与安全标识的关联关系
export function labSafetyProtectionPointSignRelationSave (data) {
  return request({
    url: '/lab-safety/protection-point-sign-relation/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	防护要点UUID
// signId	Integer	是	安全标识id
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void