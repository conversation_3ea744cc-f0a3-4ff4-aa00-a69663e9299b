import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

// 涉及到字典的字段 调取laboratory/basicMessage.js里的根据字典类型获取字典数据 getDictAll后面同理
// 例 roomLabCategory	String	否	实验室类别	字典[1014]

// 安全标识分类列表
export function labSafetySignCategoryList (params) {
  return request({
    url: '/lab-safety/sign-category/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// categoryName	String	否	标识分类名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	标识分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	标识分类名称
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 保存安全标识分类
export function labSafetySignCategorySave (data) {
  return request({
    url: '/lab-safety/sign-category/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// categoryName	String	是	标识分类名称
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Integer

// 更新安全标识分类
export function labSafetySignCategoryUpdate (data) {
  return request({
    url: '/lab-safety/sign-category/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	否	唯一值
// categoryName	String	是	标识分类名称
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 删除安全标识分类
export function labSafetySignCategoryDelete (data) {
  return request({
    url: '/lab-safety/sign-category/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 安全标识分类选项列表
export function labSafetySignCategoryOptionsList (params) {
  return request({
    url: '/lab-safety/sign-category/options',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ categoryId	Integer	标识分类id
// └─ uuid	String	唯一值
// └─ categoryName	String	标识分类名称

// 安全标识列表
export function labSafetySignList (params) {
  return request({
    url: '/lab-safety/sign/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// signName	String	否	标识名称
// categoryUuid	String	否	标识分类UUID
// categoryName	String	否	标识分类名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ signId	Integer	标识id
// └─ uuid	String	唯一值
// └─ signName	String	标识名称
// └─ signImagePath	String	标识图片路径
// └─ categoryId	Integer	标识分类ID，外键关联 lab_safety_sign_category 表
// └─ categoryName	String	标识分类名称
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ orderNum	Integer	排序字段
// └─ createBy	Integer	创建人ID
// └─ gmtCreate	LocalDateTime	创建时间
// └─ updateBy	Integer	更新人ID
// └─ updateByName	String	更新人名称
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 保存安全标识
export function labSafetySignSave (data) {
  return request({
    url: '/lab-safety/sign/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// signName	String	是	标识名称
// signImagePath	String	否	标识图片路径
// categoryId	Integer	是	标识分类ID，外键关联 lab_safety_sign_category 表
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Integer

// 更新安全标识
export function labSafetySignUpdate (data) {
  return request({
    url: '/lab-safety/sign/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	否	唯一值
// signName	String	是	标识名称
// signImagePath	String	否	标识图片路径
// categoryId	Integer	是	标识分类ID，外键关联 lab_safety_sign_category 表
// kind	Integer	否	类型
// property	Integer	否	属性
// orderNum	Integer	否	排序字段
// memo	String	否	备注
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void

// 删除安全标识
export function labSafetySignDelete (data) {
  return request({
    url: '/lab-safety/sign/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	Void
// 安全标识选项列表
export function labSafetySignOptionsList (params) {
  return request({
    url: '/lab-safety/sign/options',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// signName	String	否	标识名称
// categoryUuid	String	否	标识分类UUID
// categoryName	String	否	标识分类名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ signId	Integer	标识id
// └─ uuid	String	唯一值
// └─ signName	String	标识名称
// └─ signImagePath	String	标识图片路径
// └─ categoryId	Integer	标识分类ID，外键关联 lab_safety_sign_category 表
// └─ categoryName	String	标识分类名称
// 导出安全标识库信息
export function labSafetySignExport (params) {
  return request_import({
    url: '/lab-safety/sign/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// signName	String	否	标识名称
// categoryUuid	String	否	标识分类UUID
// categoryName	String	否	标识分类名称
// uuids	List	否	标识UUID列表
// 导出安全标识分类信息
export function labSafetySignCategoryExport (params) {
  return request_import({
    url: '/lab-safety/sign-category/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// categoryName	String	否	标识分类名称
// uuids	List	否	标识分类UUID列表