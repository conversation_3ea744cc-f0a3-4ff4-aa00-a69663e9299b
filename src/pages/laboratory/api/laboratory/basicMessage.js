import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

// 获取实验中心列表
export function labCenterPage (params) {
  return request({
    url: '/lab-center/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	gmtCreate, roomKindId
// orderRule	String	否	排序规则（, 号分割）	desc
// roomKindName	String	否	实验中心名称	物理
// deptId	Integer	否	所属部门id	1
// deptName	String	否	所属部门名称	教务处
// roomKindLevel	Integer	否	实验中心级别	1	1:校级 2: 院级
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ roomKindId	Integer	实验室中心id
// └─ uuid	String	实验室中心唯一值
// └─ roomKindName	String	实验中心名称
// └─ roomKindImage	String	实验中心图片（封面）
// └─ roomKindDesc	String	实验中心介绍
// └─ roomKindSummary	String	实验中心简介
// └─ roomLabCount	Integer	实验室数量
// └─ orderNum	Integer	排序
// └─ roomCategory	String	实验室类别
// └─ roomCategoryName	String	实验室类别名称
// └─ roomType	String	实验室类型
// └─ roomTypeName	String	实验室类型名称
// └─ roomKindLevel	Integer	实验中心级别
// └─ deptId	Integer	所属部门id
// └─ deptName	String	所属部门名称
// └─ contactInfo	String	联系方式
// └─ roomKindAddress	String	实验中心地址
// └─ responsiblePerson	String	负责人
// └─ safetyOfficer	String	安全负责人
// └─ apiVersion	String	api版本
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 新增实验中心
export function labCenterSave (data) {
  return request({
    url: '/lab-center/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// roomKindName	String	是	实验中心名称
// roomKindKind	Integer	否	实验中心分类	预留字段
// roomKindProp	Integer	否	实验中心属性	预留字段
// roomCategory	String	否	实验室类别	字典[1014]
// roomType	String	否	实验室类型	字典[1015]
// roomKindDesc	String	否	实验中心介绍
// roomKindImage	String	否	实验中心图片
// orderNum	Integer	否	排序
// memo	String	否	备注
// apiVersion	String	否	api版本
// roomKindLevel	Integer	是	实验中心级别	1:校级 2: 院级
// deptId	Integer	是	所属部门id
// contactInfo	String	否	联系方式
// roomKindAddress	String	否	实验中心地址
// responsiblePerson	String	否	负责人
// safetyOfficer	String	否	安全负责人
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	RoomKindVO
// └─ roomKindId	Integer	实验室中心id
// └─ uuid	String	实验室中心唯一值
// └─ roomKindName	String	实验中心名称
// └─ roomKindKind	Integer	实验中心分类
// └─ roomKindProp	Integer	实验中心属性
// └─ roomKindImage	String	实验中心图片（封面）
// └─ orderNum	Integer	排序
// └─ roomCategory	String	实验室类别
// └─ roomType	String	实验室类型
// └─ roomKindLevel	Integer	实验中心级别
// └─ deptId	Integer	所属部门id
// └─ contactInfo	String	联系方式
// └─ roomKindAddress	String	实验中心地址
// └─ responsiblePerson	String	负责人
// └─ safetyOfficer	String	安全负责人
// └─ apiVersion	String	api版本
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 获取实验室中心详情
export function labCenterDetail (params) {
  return request({
    url: '/lab-center/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// uuid	String	是	实验室中心UUID
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	RoomKindVO
// └─ roomKindId	Integer	实验室中心id
// └─ uuid	String	实验室中心唯一值
// └─ roomKindName	String	实验中心名称
// └─ roomKindImage	String	实验中心图片（封面）
// └─ roomKindDesc	String	实验中心介绍
// └─ roomKindSummary	String	实验中心简介
// └─ roomLabCount	Integer	实验室数量
// └─ orderNum	Integer	排序
// └─ roomCategory	String	实验室类别
// └─ roomCategoryName	String	实验室类别名称
// └─ roomType	String	实验室类型
// └─ roomTypeName	String	实验室类型名称
// └─ roomKindLevel	Integer	实验中心级别
// └─ deptId	Integer	所属部门id
// └─ deptName	String	所属部门名称
// └─ contactInfo	String	联系方式
// └─ roomKindAddress	String	实验中心地址
// └─ responsiblePerson	String	负责人
// └─ safetyOfficer	String	安全负责人
// └─ apiVersion	String	api版本
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 更新实验中心
export function labCenterUpdate (data) {
  return request({
    url: '/lab-center/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// roomKindName	String	是	实验中心名称
// roomKindKind	Integer	否	实验中心分类	预留字段
// roomKindProp	Integer	否	实验中心属性	预留字段
// roomCategory	String	否	实验室类别	字典[1014]
// roomType	String	否	实验室类型	字典[1015]
// roomKindDesc	String	否	实验中心介绍
// roomKindImage	String	否	实验中心图片
// orderNum	Integer	否	排序
// memo	String	否	备注
// apiVersion	String	否	api版本
// roomKindLevel	Integer	是	实验中心级别	1:校级 2: 院级
// deptId	Integer	是	所属部门id
// contactInfo	String	否	联系方式
// roomKindAddress	String	否	实验中心地址
// responsiblePerson	String	否	负责人
// safetyOfficer	String	否	安全负责人
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 更新实验中心介绍
export function updateCenterDesc (data) {
  return request({
    url: '/lab-center/update-introduction',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	唯一值
// roomKindDesc	String	是	实验中心介绍
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 删除实验中心
export function labCenterDelete (data) {
  return request({
    url: '/lab-center/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid传输对象
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 实验中心人员绑定信息列表
export function labCenterPersonBindPage (params) {
  return request({
    url: '/lab-center/personnel-binding/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	id
// orderRule	String	否	排序规则（, 号分割）	desc
// labCenterUuid	String	是
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	绑定关系id
// └─ uuid	String	绑定关系唯一值
// └─ resourceType	Integer	资源类型（1: 实验中心, 2: 实验室, 3: 房间）
// └─ resourceId	Integer	资源id
// └─ sysRole	String	系统角色
// └─ sysRoleName	String	系统角色名称
// └─ personnelType	Integer	人员类型（1: 专任, 2: 兼任）
// └─ assignedDate	LocalDate	分配日期
// └─ endDate	LocalDate	结束日期（可选）
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ labPersonInfo	LabPersonInfoVO	人员信息
//     └─ accNo	Integer	用户id
//     └─ logonName	String	学工号
//     └─ trueName	String	姓名
//     └─ sex	Integer	性别 0.保密 1.男 2.女
//     └─ birthday	Integer	出生日期
//     └─ deptId	Integer	部门id
//     └─ deptName	String	部门名称
//     └─ personPhoto	String	人员照片
//     └─ personnelAcademic	String	所属学科
//     └─ personnelAcademicName	String	所属学科名称
//     └─ technicalPosition	String	专业技术职务
//     └─ technicalPositionName	String	专业技术职务名称
//     └─ educationalLevel	String	文化程度
//     └─ educationalLevelName	String	文化程度名称
//     └─ expertCategory	String	专家类别
//     └─ expertCategoryName	String	专家类别名称
//     └─ degree	String	学位
//     └─ degreeName	String	学位名称
//     └─ phone	String	联系电话
//     └─ email	String	邮箱

//实验中心人员列表
export function labCenterPersonPage (params) {
  return request({
    url: '/lab-center/query/personnel-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	id
// orderRule	String	否	排序规则（, 号分割）	desc
// labCenterUuid	String	是	实验中心UUID	1937093874623377408
// personKeyword	String	否	人员信息搜索关键字	张三
// sysRole	String	否	系统角色	lab_center_admin
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	绑定关系id
// └─ uuid	String	绑定关系唯一值
// └─ resourceType	Integer	资源类型（1: 实验中心, 2: 实验室, 3: 房间）
// └─ resourceId	Integer	资源id
// └─ sysRole	String	系统角色
// └─ sysRoleName	String	系统角色名称
// └─ personnelType	Integer	人员类型（1: 专任, 2: 兼任）
// └─ position	String	职位
// └─ assignedDate	LocalDate	分配日期
// └─ endDate	LocalDate	结束日期（可选）
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ labPersonInfo	LabPersonInfoVO	人员信息
//     └─ accNo	Integer	用户id
//     └─ logonName	String	学工号
//     └─ trueName	String	姓名
//     └─ sex	Integer	性别 0.保密 1.男 2.女
//     └─ birthday	Integer	出生日期
//     └─ deptId	Integer	部门id
//     └─ deptName	String	部门名称
//     └─ personPhoto	String	人员照片
//     └─ personnelAcademic	String	所属学科
//     └─ personnelAcademicName	String	所属学科名称
//     └─ technicalPosition	String	专业技术职务
//     └─ technicalPositionName	String	专业技术职务名称
//     └─ educationalLevel	String	文化程度
//     └─ educationalLevelName	String	文化程度名称
//     └─ expertCategory	String	专家类别
//     └─ expertCategoryName	String	专家类别名称
//     └─ degree	String	学位
//     └─ degreeName	String	学位名称
//     └─ phone	String	联系电话
//     └─ email	String	邮箱
// 保存实验中心人员绑定信息
export function labCenterPersonBindSave (data) {
  return request({
    url: '/lab-center/personnel-binding/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// personInfo	LabPersonInfoDTO	否	人员信息
// └─ accNo	Integer	是	用户id
// └─ personPhoto	String	否	人员照片
// └─ personnelAcademic	String	否	所属学科	字典[1003]
// └─ technicalPosition	String	否	专业技术职务	字典[1010]
// └─ educationalLevel	String	否	文化程度	字典[1009]
// └─ expertCategory	String	否	专家类别	字典[1016]
// └─ degree	String	否	学位	字典[1028]
// └─ position	String	否	职称
// └─ phone	String	否	联系电话
// └─ email	String	否	邮箱
// sysRole	String	是	系统角色
// personnelType	Integer	是	人员类型
// assignedDate	LocalDate	否	分配日期	默认当天
// endDate	LocalDate	否	结束日期	任职结束日期（预留）
// labCenterUuid	String	是	实验室中心UUID
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除实验中心人员绑定信息
export function labCenterPersonBindDelete (data) {
  return request({
    url: '/lab-center/personnel-binding/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 更新实验中心人员绑定信息
export function labCenterPersonBindUpdate (data) {
  return request({
    url: '/lab-center/personnel-binding/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	绑定关系uuid
// personInfo	LabPersonInfoDTO	是	人员信息
// └─ accNo	Integer	是	用户id	不允许修改
// └─ personPhoto	String	否	人员照片
// └─ personnelAcademic	String	否	所属学科
// └─ technicalPosition	String	否	专业技术职务
// └─ educationalLevel	String	否	文化程度
// └─ expertCategory	String	否	专家类别
// └─ degree	String	否	学位
// └─ phone	String	否	联系电话
// └─ email	String	否	邮箱
// sysRole	String	是	系统角色
// personnelType	Integer	是	人员类型 1:专任 2: 兼任
// position	String	否	职位
// assignedDate	LocalDate	否	分配日期
// endDate	LocalDate	否	结束日期
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 获取实验室人员信息列表(学工号)
export function labPersonInfoList (params) {
  return request({
    url: '/lab/person-info/list',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// key	String	否	搜索关键字	张三	范围学工号姓名
// num	Integer	否	返回数量 0.全部 其他.指定数量 指定数量最多1000条	10
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ accNo	Integer	用户id
// └─ logonName	String	学工号
// └─ trueName	String	姓名
// └─ sex	Integer	性别 0.保密 1.男 2.女
// └─ birthday	Integer	出生日期
// └─ deptId	Integer	部门id
// └─ deptName	String	部门名称
// └─ personPhoto	String	人员照片
// └─ personnelAcademic	String	所属学科
// └─ technicalPosition	String	专业技术职务
// └─ educationalLevel	String	文化程度
// └─ expertCategory	String	专家类别
// └─ phone	String	联系电话
// └─ email	String	邮箱

// 实验中心人员系统角色字典数据
export function labCenterPersonRoleDict (params) {
  return request({
    url: '/lab-center/personnel-binding/role-dict',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}

// 获取部门列表
export function getDeptList (params) {
  return request({
    url: '/accDept/getAll',
    method: 'get',
    params,
  })
}
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ deptId	Integer	部门id
// └─ deptName	String	部门名称
// └─ email	Integer	uuid

//根据字典类型获取字典数据
export function getDictAll (params) {
  return request({
    url: '/codingTable/getAll',
    method: 'get',
    params
  })
}
// 请求参数:
// 参数名	是否必须	类型	示例	说明
// codeType	是	number	1010	用户名
// 返回参数说明:
// 参数名称	参数类型	示例值	备注
// code	number	0
// message	string	查询成功!
// data	array	[object Object],[object Object]	字典数据
// count	number	61

// data、children
// 参数名称	参数类型	示例值	备注
// id	number	451	字典id
// name	string	四级飞行机械员	字典名称
// uuid	string	1504360028751929346	字典uuid
// codeValue	string	495	字典值
// parentUuid	string	1504348213716328450	字典父级uuid
// parentName	string	飞行技术人员（机械）	字典父级名称
// children	array[]	下级字典

// 实验中心绑定的实验室列表
export function labCenterBindLabPage (params) {
  return request({
    url: '/lab-center/lab-binding/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// roomLabSn	String	否	实验室编号
// roomLabName	String	否	实验室名称
// deptName	String	否	学院名称
// deptUuid	String	否	学院uuid
// deptId	Integer	否	学院id 根据uuid换取
// labCenterName	String	否	实验中心名称
// labCenterUuid	String	否	实验中心uuid
// labCenterId	Integer	否	实验中心id 根据uuid换取
// roomLabCategory	String	否	实验室类别 字典[1014]
// roomLabType	String	否	实验室类型 字典[1015]
// responsiblePerson	String	否	负责人
// usageStatus	Integer	否	使用状态 1:在用 2: 停用
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	自增id
// └─ bindingUuid	String	绑定关系uuid
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ labInfo	LabInfoVO	绑定的实验室信息
//     └─ roomLabId	Integer	实验室id
//     └─ uuid	String	唯一值
//     └─ roomLabSn	String	实验室编号
//     └─ roomLabName	String	实验室名称
//     └─ roomLabNameEn	String	实验室英文名称
//     └─ roomLabCategory	String	实验室类别
//     └─ roomLabCategoryName	String	实验室类别名称
//     └─ roomLabType	String	实验室类型
//     └─ roomLabTypeName	String	实验室类型名称
//     └─ deptId	Integer	学院id
//     └─ deptName	String	学院名称
//     └─ roomLabDesc	String	实验室介绍
//     └─ roomLabImage	String	实验室图片
//     └─ roomKindId	Integer	实验室类型(中心)id
//     └─ roomLabSummary	String	实验室简介
//     └─ hazardLevel	Integer	实验室危险等级
//     └─ hazardScore	Integer	实验室危险评分
//     └─ status	Integer	状态：0:未评估 1：已评估
//     └─ orderNum	Integer	排序
//     └─ kind	Integer	类型
//     └─ property	Integer	属性
//     └─ establishmentDate	LocalDate	建立日期
//     └─ usageStatus	Integer	使用状态 1:在用 2: 停用
//     └─ labAcademic	String	实验室所属学科
//     └─ labAcademicName	String	实验室所属学科名称
//     └─ contactInfo	String	联系方式
//     └─ labAddress	String	实验室地址
//     └─ responsiblePerson	String	负责人
//     └─ safetyOfficer	String	安全负责人
//     └─ apiVersion	String	接口版本
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ memo	String	备注
//     └─ roomArea	BigDecimal	房间面积
//     └─ roomCount	Integer	房间数量

// 保存实验中心绑定的实验室信息
export function labCenterBindLabSave (data) {
  return request({
    url: '/lab-center/lab-binding/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "labCenterUuid": "实验中心uuid33",
//     "labIds": [
//       955,
//       820
//     ]
// }
// 入参字段说明
// 字段	类型	必填	含义
// labCenterUuid	String	是	实验中心uuid
// labIds	Set	否	实验室id集合 传空表示解绑所有实验室
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除实验中心绑定的实验室
export function labCenterBindLabDelete (data) {
  return request({
    url: '/lab-center/lab-binding/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "uuidSet": [
//     "uuid 集合46",
//     "uuid 集合119"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 实验中心绑定的实验室ID集合
export function labCenterBindLabIds (params) {
  return request({
    url: '/lab-center/lab-binding/lab-ids',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验中心UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Set

//实验中心规章制度列表
export function labCenterRegulationPage (params) {
  return request({
    url: '/lab-center/regulation/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（,号分割）
// orderRule	String	否	排序规则（,号分割）
// labCenterUuid	String	是	实验中心uuid
// title	String	否	标题
// fileName	String	否	文件名称
// regulationType	Integer	否	规章类型 1校级文件 2上级文件
// beginDate	String	否	更新开始时间	格式: yyyy-MM-dd
// endDate	String	否	更新结束时间	格式: yyyy-MM-dd
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ regulationId	Integer	规则制度ID
// └─ uuid	String	唯一值
// └─ labId	Integer	实验室id
// └─ fileId	Integer	文件id（common_file表file_id）
// └─ title	String	用户可见标题
// └─ subtitle	String	副标题
// └─ tags	String	逗号分隔标签
// └─ regulationType	Integer	规则制度类型 1:校级 2: 上级（国家级）
// └─ attachmentUrl	String	文件地址
// └─ fileName	String	原始文件名（含扩展名）
// └─ fileSize	Long	文件大小(字节)
// └─ fileType	String	文件扩展名
// └─ publishStatus	Integer	发布状态：1 - 草稿，2 - 发布
// └─ publishDate	LocalDateTime	发布时间（可预设未来时间）
// └─ downloadCount	Integer	下载次数
// └─ memo	String	备注
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

// 新增实验中心规章制度
export function labCenterRegulationSave (data) {
  return request({
    url: '/lab-center/regulation/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "labCenterUuid": "实验中心uuid23",
//   "regulationType": 801,
//   "title": "用户可见标题 （预留）108",
//   "tags": "标签 多个标签逗号分隔（预留）2",
//   "attachmentUrl": "文件地址42"
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labCenterUuid	String	是	实验中心uuid
// regulationType	Integer	是	规章类型 规章类型 1校级文件 2上级文件
// title	String	否	用户可见标题 （预留）	默认会取文件名
// tags	String	否	标签 多个标签逗号分隔（预留）
// attachmentUrl	String	是	文件地址
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除实验中心规章制度
export function labCenterRegulationDelete (data) {
  return request({
    url: '/lab-center/regulation/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "uuidSet": [
//     "uuid 集合24",
//     "uuid 集合9"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 实验中心设备绑定列表
export function labCenterDeviceBindPage (params) {
  return request({
    url: '/lab-center/device-binding/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// labCenterUuid	String	是	实验室中心UUID
// assetSn	String	否	资产编号
// devName	String	否	设备名称
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
// 实验中心设备列表
export function labCenterDevicePage (params) {
  return request({
    url: '/lab-center/query/device-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	devId
// orderRule	String	否	排序规则（, 号分割）	desc
// labCenterUuid	String	是	实验中心UUID	1937093874623377408
// assetSn	String	否	资产编号	11
// devName	String	否	设备名称	计算机
// presentSituationCode	String	否	现状码	00	字典[1012]
// devKindName	String	否	设备类型
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称

// 实验中心设备详情
export function labCenterDeviceDetail (params) {
  return request({
    url: '/lab-center/query/device-detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// uuid	String	是	设备uuid	1595656005587439616
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabBindingDeviceVO
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
//     └─ classNumCode	String	教育部设备分类号
//     └─ classNumName	String	教育部设备分类号名称
// 设备列表(含绑定状态 是否在实验中心/实验室下绑定)
export function devicePage (params) {
  return request({
    url: '/lab/device/binding-status-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	devIds
// orderRule	String	否	排序规则（, 号分割）	desc
// campusName	String	否	校区名称	朝晖
// buildingName	String	否	楼宇名称	健
// floorName	String	否	楼层名称	1
// roomName	String	否	房间名称	机房
// roomIds	Set	否	房间id集合	3
// devSn	String	否	设备编号	2
// assetSn	String	否	资产编号	982
// devName	String	否	设备名称	机
// devKindName	String	否	设备类型名称	计算机
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	String	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ floorNo	String	所在层楼号
//     └─ buildingId	Integer	楼宇id
//     └─ buildingName	String	设备所在楼宇
//     └─ campusId	Integer	校区id
//     └─ campusName	String	校区名称
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
//     └─ classNumCode	String	教育部设备分类号
//     └─ classNumName	String	教育部设备分类号名称
//     └─ gmtCreate	LocalDateTime	创建时间
//     └─ gmtModified	LocalDateTime	更新时间
//     └─ memo	String	备注

// 保存实验中心设备绑定关系
export function labCenterDeviceBindSave (data) {
  return request({
    url: '/lab-center/device-binding/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "labCenterUuid": "实验室中心uuid93",
//     "devIds": [
//       521,
//       239
//     ]
// }
// 入参字段说明
// 字段	类型	必填	含义
// labCenterUuid	String	是	实验室中心uuid
// devIds	Set	否	设备ID集合 至少包含一个设备ID
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 实验中心设备绑定详情
export function labCenterDeviceBindDetail (params) {
  return request({
    url: '/lab-center/device-binding/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	设备绑定关系UUID
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabBindingDeviceVO
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称

// 删除实验中心设备绑定关系
export function labCenterDeviceBindDelete (data) {
  return request({
    url: '/lab-center/device-binding/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "uuidSet": [
//     "uuid 集合11",
//     "uuid 集合106"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 实验中心绑定的设备ID列表
export function labCenterDeviceBindDevIds (params) {
  return request({
    url: '/lab-center/device-binding/dev-ids',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labCenterUuid	String	是	实验中心UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List

// 设备类型下拉框
export function getApiDevKinAll (params) {
  return request({
    url: '/device/getApiDevKinAll',
    method: 'get',
    params,
  })
}
// 入参字段说明
// 参数名	是否必须	类型	示例值	说明
// devKindName	否	string	计算	设备名称模糊搜索

// 返回参数说明:
// 参数名称	参数类型	示例值	备注
// code	number	0	0：请求成功，其他请求失败
// message	string	查询成功
// data	array
//   uuid	string	fdec1f63d015441e9f498ff0b3ea8030	设备类型唯一标识
//   devKindId	number	206	设备类型id
//   devKindName	string	实验设备	设备类型名称
//   maxUse	number	1	最大使用人数
//   formatDevKindName	string	实验设备|人数:1	格式化的设备类型名称
// count	number	3

// 导出实验中心设备绑定关系
export function labCenterDeviceBindExport (params) {
  return request_import({
    url: '/lab-center/device-binding/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例
// labCenterUuid	String	是	实验中心uuid	1937093874623377408
// roomLabSn	String	否	实验室编号
// roomLabName	String	否	实验室名称
// deptName	String	否	学院名称
// deptUuid	String	否	学院uuid
// deptId	Integer	否	学院id 根据uuid换取
// roomLabCategory	String	否	实验室类别 字典
// roomLabType	String	否	实验室类型 字典
// responsiblePerson	String	否	负责人
// usageStatus	Integer	否	使用状态 1:在用 2: 停用
// 出参字段说明
// 此接口无任何出参，直接返回文件流
// 实验中心设备导入绑定接口
export function labCenterDeviceBindImport (data) {
  return request({
    url: '/lab-center/device-binding/import',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "labCenterUuid": "实验中心uuid110",
//   "assetSnList": [
//     "资产编号集合 至少包含一个资产编号95",
//     "资产编号集合 至少包含一个资产编号62"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labCenterUuid	String	是	实验中心uuid
// assetSnList	List	否	资产编号集合 至少包含一个资产编号
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	ExcelImportVO
// └─ failList	List	失败列表
//     └─ assetSn	String	资产编号
//     └─ errorMsg	String	错误原因

// 实验中心概览信息
export function labCenterSummary (params) {
  return request({
    url: '/lab-center/query/summary',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验中心UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabCenterSummaryVO
// └─ personnelCount	Long	人员总数
// └─ labCount	Long	实验室总数
// └─ deviceCount	Long	仪器设备总数
// └─ regulationCount	Long	规章制度总数