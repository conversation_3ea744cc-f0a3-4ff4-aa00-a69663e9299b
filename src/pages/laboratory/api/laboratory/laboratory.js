import request from '@laboratory/utils/request';
import request_import from '@laboratory/utils/request_import';

// 涉及到字典的字段 调取laboratory/basicMessage.js里的根据字典类型获取字典数据 getDictAll后面同理
// 例 roomLabCategory	String	否	实验室类别	字典[1014]

// 实验室基本信息列表
export function labInfoPage (params) {
  return request({
    url: '/lab-info/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// roomLabSn	String	否	实验室编号
// roomLabName	String	否	实验室名称
// deptName	String	否	学院名称
// deptUuid	String	否	学院uuid
// deptId	Integer	否	学院id 根据uuid换取
// labCenterName	String	否	实验中心名称
// labCenterUuid	String	否	实验中心uuid
// labCenterId	Integer	否	实验中心id 根据uuid换取
// roomLabCategory	String	否	实验室类别	字典[1014]
// roomLabType	String	否	实验室类型	字典[1015]
// responsiblePerson	String	否	负责人
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ roomLabId	Integer	实验室id
// └─ uuid	String	唯一值
// └─ roomLabSn	String	实验室编号
// └─ roomLabName	String	实验室名称
// └─ roomLabNameEn	String	实验室英文名称
// └─ roomLabCategory	String	实验室类别
// └─ roomLabCategoryName	String	实验室类别名称
// └─ roomLabType	String	实验室类型
// └─ roomLabTypeName	String	实验室类型名称
// └─ deptId	Integer	学院id
// └─ roomLabDesc	String	实验室介绍
// └─ roomLabImage	String	实验室图片
// └─ roomKindId	Integer	实验室类型(中心)id
// └─ roomLabSummary	String	实验室简介
// └─ hazardLevel	Integer	实验室危险等级
// └─ hazardScore	Integer	实验室危险评分
// └─ status	Integer	状态：0:未评估 1：已评估
// └─ orderNum	Integer	排序
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ establishmentDate	LocalDate	建立日期
// └─ usageStatus	Integer	使用状态 1:在用 2: 停用
// └─ labAcademic	String	实验室所属学科
// └─ labAcademicName	String	实验室所属学科名称
// └─ contactInfo	String	联系方式
// └─ labAddress	String	实验室地址
// └─ responsiblePerson	String	负责人
// └─ safetyOfficer	String	安全负责人
// └─ apiVersion	String	接口版本
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 保存实验室基本信息
export function labInfoSave (data) {
  return request({
    url: '/lab-info/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// roomLabSn	String	是	实验室编号
// roomLabName	String	是	实验室名称
// roomLabNameEn	String	否	实验室英文名称
// roomLabCategory	String	否	实验室类别	字典 [1014]
// roomLabType	String	否	实验室类型	字典 [1015]
// deptId	Integer	是	学院id
// roomLabImage	String	否	实验室图片
// roomKindId	Integer	否	实验室类型(中心)id
// orderNum	Integer	否	排序
// kind	Integer	否	类型
// property	Integer	否	属性
// establishmentDate	LocalDate	是	建立日期
// usageStatus	Integer	是	使用状态 1:在用 2:停用
// labAcademic	String	否	实验室所属学科 字典 [1003]
// contactInfo	String	否	联系方式
// labAddress	String	否	实验室地址
// responsiblePerson	String	否	负责人（同实验室专职人员）
// safetyOfficer	String	否	安全负责人
// apiVersion	String	否	接口版本
// memo	String	否	备注 长度不能超过200
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabInfoVO
// └─ roomLabId	Integer	实验室id
// └─ uuid	String	唯一值
// └─ roomLabSn	String	实验室编号
// └─ roomLabName	String	实验室名称
// └─ roomLabNameEn	String	实验室英文名称
// └─ roomLabCategory	String	实验室类别
// └─ roomLabType	String	实验室类型
// └─ deptId	Integer	学院id
// └─ roomLabDesc	String	实验室介绍
// └─ roomLabImage	String	实验室图片
// └─ roomKindId	Integer	实验室类型(中心)id
// └─ roomLabSummary	String	实验室简介
// └─ hazardLevel	Integer	实验室危险等级
// └─ hazardScore	Integer	实验室危险评分
// └─ status	Integer	状态：0:未评估 1：已评估
// └─ orderNum	Integer	排序
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ establishmentDate	LocalDate	建立日期
// └─ usageStatus	Integer	使用状态 1:在用 2: 停用
// └─ labAcademic	String	实验室所属学科
// └─ contactInfo	String	联系方式
// └─ labAddress	String	实验室地址
// └─ responsiblePerson	String	负责人
// └─ safetyOfficer	String	安全负责人
// └─ apiVersion	String	接口版本
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注

// 更新实验室基本信息
export function labInfoUpdate (data) {
  return request({
    url: '/lab-info/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室UUID
// roomLabSn	String	是	实验室编号
// roomLabName	String	是	实验室名称
// roomLabNameEn	String	否	实验室英文名称
// roomLabCategory	String	否	实验室类别	字典[1014]
// roomLabType	String	否	实验室类型	字典[1015]
// deptId	Integer	是	学院id
// roomLabImage	String	否	实验室图片
// roomKindId	Integer	否	实验室类型(中心)id
// orderNum	Integer	否	排序
// kind	Integer	否	类型
// property	Integer	否	属性
// establishmentDate	LocalDate	是	建立日期
// usageStatus	Integer	是	使用状态 1:在用 2: 停用
// labAcademic	String	否	实验室所属学科 字典 [1003]
// contactInfo	String	否	联系方式
// labAddress	String	否	实验室地址
// responsiblePerson	String	否	负责人（同实验室专职人员）
// safetyOfficer	String	否	安全负责人
// apiVersion	String	否	接口版本
// memo	String	否	备注 长度不能超过200
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 更新实验室简介信息
export function labInfoSummaryUpdate (data) {
  return request({
    url: '/lab-info/update-introduction',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	实验室UUID
// roomLabDesc	String	否	实验室介绍
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 删除实验室基本信息
export function labInfoDelete (data) {
  return request({
    url: '/lab-info/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	uuid唯一值
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 获取实验室基本信息详情
export function labInfoDetail (params) {
  return request({
    url: '/lab-info/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义
// uuid	String	是	实验室UUID
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabInfoVO
// └─ roomLabId	Integer	实验室id
// └─ uuid	String	唯一值
// └─ roomLabSn	String	实验室编号
// └─ roomLabName	String	实验室名称
// └─ roomLabNameEn	String	实验室英文名称
// └─ roomLabCategory	String	实验室类别
// └─ roomLabType	String	实验室类型
// └─ deptId	Integer	学院id
// └─ roomLabDesc	String	实验室介绍
// └─ roomLabImage	String	实验室图片
// └─ roomKindId	Integer	实验室类型(中心)id
// └─ roomLabSummary	String	实验室简介
// └─ hazardLevel	Integer	实验室危险等级
// └─ hazardScore	Integer	实验室危险评分
// └─ status	Integer	状态：0:未评估 1：已评估
// └─ orderNum	Integer	排序
// └─ kind	Integer	类型
// └─ property	Integer	属性
// └─ establishmentDate	LocalDate	建立日期
// └─ usageStatus	Integer	使用状态 1:在用 2: 停用
// └─ labAcademic	String	实验室所属学科
// └─ contactInfo	String	联系方式
// └─ labAddress	String	实验室地址
// └─ responsiblePerson	String	负责人
// └─ safetyOfficer	String	安全负责人
// └─ apiVersion	String	接口版本
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ memo	String	备注
// 实验室人员系统角色字典数据
export function labPersonRoleDict () {
  return request({
    url: '/lab/personnel-binding/role-dict',
    method: 'get',
    XAPIVersion: '2.0.0'
  })
}
// 入参
// 此接口无任何入参
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ label	String	字典标签
// └─ value	String	字典值

// 实验室人员绑定信息分页列表
export function labPersonBindPage (params) {
  return request({
    url: '/lab/personnel-binding/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	id
// orderRule	String	否	排序规则（, 号分割）	desc
// labUuid	String	是	实验室UUID
// personKeyword	String	否	人员信息搜索条件	张三	范围: logonName、 trueName
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	绑定关系id
// └─ uuid	String	绑定关系唯一值
// └─ resourceType	Integer	资源类型（1: 实验中心, 2: 实验室, 3: 房间）
// └─ resourceId	Integer	资源id
// └─ sysRole	String	系统角色
// └─ sysRoleName	String	系统角色名称
// └─ personnelType	Integer	人员类型（1: 专任, 2: 兼任）
// └─ assignedDate	LocalDate	分配日期
// └─ endDate	LocalDate	结束日期（可选）
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间
// └─ labPersonInfo	LabPersonInfoVO	人员信息
// └─ labPersonInfo	LabPersonInfoVO	人员信息
//     └─ accNo	Integer	用户id
//     └─ logonName	String	学工号
//     └─ trueName	String	姓名
//     └─ sex	Integer	性别 0.保密 1.男 2.女
//     └─ birthday	Integer	出生日期
//     └─ deptId	Integer	部门id
//     └─ deptName	String	部门名称
//     └─ personPhoto	String	人员照片
//     └─ personnelAcademic	String	所属学科
//     └─ personnelAcademicName	String	所属学科名称
//     └─ technicalPosition	String	专业技术职务
//     └─ technicalPositionName	String	专业技术职务名称
//     └─ educationalLevel	String	文化程度
//     └─ educationalLevelName	String	文化程度名称
//     └─ expertCategory	String	专家类别
//     └─ expertCategoryName	String	专家类别名称
//     └─ degree	String	学位
//     └─ degreeName	String	学位名称
//     └─ phone	String	联系电话
//     └─ email	String	邮箱

// 保存实验室人员绑定信息
export function labPersonBindSave (data) {
  return request({
    url: '/lab/personnel-binding/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// personInfo	LabPersonInfoDTO	否	人员信息
// └─ accNo	Integer	是	用户id
// └─ personPhoto	String	否	人员照片
// └─ personnelAcademic	String	否	所属学科	字典[1003]
// └─ technicalPosition	String	否	专业技术职务	字典[1010]
// └─ educationalLevel	String	否	文化程度	字典[1009]
// └─ expertCategory	String	否	专家类别	字典[1016]
// └─ degree	String	否	学位	字典[1028]
// └─ phone	String	否	联系电话
// └─ email	String	否	邮箱
// sysRole	String	是	系统角色
// personnelType	Integer	是	人员类型
// position	String	否	职位
// assignedDate	LocalDate	否	分配日期	默认当天 格式: yyyy - MM - dd
// endDate	LocalDate	否	结束日期	任职结束日期（预留）格式: yyyy - MM - dd
// labUuid	String	是	实验室UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 更新实验室人员绑定信息
export function labPersonBindUpdate (data) {
  return request({
    url: '/lab/personnel-binding/update',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	绑定关系uuid
// personInfo	LabPersonInfoDTO	是	人员信息
// └─ accNo	Integer	是	用户id	不允许修改
// └─ personPhoto	String	否	人员照片
// └─ personnelAcademic	String	否	所属学科
// └─ technicalPosition	String	否	专业技术职务
// └─ educationalLevel	String	否	文化程度
// └─ expertCategory	String	否	专家类别
// └─ degree	String	否	学位
// └─ phone	String	否	联系电话
// └─ email	String	否	邮箱
// sysRole	String	是	系统角色
// personnelType	Integer	是	人员类型 1:专任 2: 兼任
// position	String	否	职位
// assignedDate	LocalDate	否	分配日期
// endDate	LocalDate	否	结束日期
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除实验室人员绑定信息
export function labPersonBindDelete (data) {
  return request({
    url: '/lab/personnel-binding/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "uuidSet": [
//     "uuid 集合73",
//     "uuid 集合8"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void


//实验室技术资料列表（规章制度）
export function labRegulationPage (params) {
  return request({
    url: '/lab/regulation/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// labUuid	String	是	实验室uuid
// title	String	否	标题
// fileName	String	否	文件名称
// beginDate	String	否	更新开始时间	格式: yyyy - MM - dd
// endDate	String	否	更新结束时间	格式: yyyy - MM - dd
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ regulationId	Integer	规则制度ID
// └─ uuid	String	唯一值
// └─ labId	Integer	实验室id
// └─ fileId	Integer	文件id（common_file表file_id）
// └─ title	String	用户可见标题
// └─ subtitle	String	副标题
// └─ tags	String	逗号分隔标签
// └─ attachmentUrl	String	文件地址
// └─ fileName	String	原始文件名（含扩展名）
// └─ fileSize	Long	文件大小(字节)
// └─ fileType	String	文件扩展名
// └─ publishStatus	Integer	发布状态：1 - 草稿，2 - 发布
// └─ publishDate	LocalDateTime	发布时间（可预设未来时间）
// └─ downloadCount	Integer	下载次数
// └─ memo	String	备注
// └─ gmtCreate	LocalDateTime	创建时间
// └─ gmtModified	LocalDateTime	更新时间

// 新增实验室技术资料(规章制度)
export function labRegulationSave (data) {
  return request({
    url: '/lab/regulation/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "labUuid": "实验室uuid37",
//   "title": "用户可见标题 （预留）96",
//   "tags": "标签 多个标签逗号分隔（预留）124",
//   "attachmentUrl": "文件地址82"
// }
// 入参字段说明
// 字段	类型	必填	含义
// labUuid	String	是	实验室uuid
// title	String	否	用户可见标题 （预留）	默认会取文件名
// tags	String	否	标签 多个标签逗号分隔（预留）
// attachmentUrl	String	是	文件地址
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void

// 删除实验室技术资料(规章制度)
export function labRegulationDelete (data) {
  return request({
    url: '/lab/regulation/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "uuidSet": [
//     "uuid 集合42",
//     "uuid 集合2"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 实验室设备绑定列表
export function labDeviceBindPage (params) {
  return request({
    url: '/lab/device-binding/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// labUuid	String	是	实验室UUID
// assetSn	String	否	资产编号
// devName	String	否	设备名称
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
// 保存实验室设备绑定关系
export function labDeviceBindSave (data) {
  return request({
    url: '/lab/device-binding/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "labUuid": "实验室UUID11",
//     "devIds": [
//       624,
//       486
//     ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labUuid	String	是	实验室UUID
// devIds	Set	否	设备ID集合 至少包含一个设备ID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 实验室设备绑定详情
export function labDeviceBindDetail (params) {
  return request({
    url: '/lab/device-binding/detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	设备绑定关系UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabBindingDeviceVO
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
// 删除实验室设备绑定关系
export function labDeviceBindDelete (data) {
  return request({
    url: '/lab/device-binding/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 实验室绑定的设备ID列表
export function labDeviceBindDevIds (params) {
  return request({
    url: '/lab/device-binding/dev-ids',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// 实验室房间已绑定列表
export function labRoomBindPage (params) {
  return request({
    url: '/lab/room-binding/page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// beginDate	String	否	开始时间
// labUuid	String	是	实验室uuid
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ roomId	Integer	房间id
// └─ labId	Integer	实验室id
// └─ roomInfo	RoomInfoV2VO	实验室基本信息
//     └─ roomId	Integer	房间id
//     └─ uuid	String	房间uuid
//     └─ roomSn	String	房间号
//     └─ roomName	String	实验室名称
//     └─ tags	String	房间标签
//     └─ imgUrl	Integer	房间照片
//     └─ campusName	String	校区名称
//     └─ buildingName	String	楼宇名称
//     └─ floorName	String	楼层名称
//     └─ roomFloor	String	房间所属楼层 纯数字表示
//     └─ area	Integer	面积(平方米 * 100 保留两位小数，即实际保存时没有小数点，展示时除以100)
// 实验室绑定的房间ID列表
export function labRoomBindDevIds (params) {
  return request({
    url: '/lab/room-binding/room-ids',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List

// 保存实验室房间绑定关系
export function labRoomBindSave (data) {
  return request({
    url: '/lab/room-binding/save',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "labUuid": "实验室UUID102",
//   "roomIds": [
//     78,
//     867
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labUuid	String	是	实验室UUID
// roomIds	Set	否	房间ID集合 至少包含一个房间ID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 删除实验室房间绑定关系
export function labRoomBindDelete (data) {
  return request({
    url: '/lab/room-binding/delete',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例(RequestBody)
// {
//   "uuidSet": [
//     "uuid 集合53",
//     "uuid 集合31"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuidSet	Set	是	uuid 集合
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	Void
// 实验室已绑定的设备列表
export function labDevicePage (params) {
  return request({
    url: '/lab/query/device-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	devId
// orderRule	String	否	排序规则（, 号分割）	desc
// labUuid	String	是	实验室UUID	1937093874623377408
// assetSn	String	否	资产编号	11
// devName	String	否	设备名称	计算机
// presentSituationCode	String	否	现状码	00	字典[1012]
// devKindName	String	否	设备类型	计算机
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
//     └─ classNumCode	String	教育部设备分类号
//     └─ classNumName	String	教育部设备分类号名称
// 实验室设备详情
export function labDeviceDetail (params) {
  return request({
    url: '/lab/query/device-detail',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// uuid	String	是	设备uuid	1595656005587439616
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabBindingDeviceVO
// └─ id	Integer	自增id
// └─ uuid	String	绑定关系uuid
// └─ devId	Integer	设备id
// └─ resourceType	Integer	资源类型（所属对象类型）1:实验中心 2:实验室 3: 房间
// └─ resourceId	Integer	资源id
// └─ resourceName	String	资源名称（所属对象名称）
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ device	DeviceV2VO	设备信息
//     └─ devId	Integer	设备id
//     └─ uuid	String	设备uuid
//     └─ devSn	Integer	设备编号
//     └─ devName	String	设备名称
//     └─ assetSn	String	资产编号
//     └─ imgUrl	Integer	设备照片
//     └─ devModel	String	设备型号
//     └─ devSpecification	String	设备规格
//     └─ devKindName	String	设备类型
//     └─ roomId	Integer	设备所在房间id
//     └─ roomName	String	设备所在房间名称
//     └─ floorName	String	所设备所在楼层
//     └─ buildingName	String	设备所在楼宇
//     └─ storageLocation	String	设备存放地点 楼宇 - 楼层 - 房间
//     └─ deptId	Integer	部门id（学院id）
//     └─ deptName	String	部门名称（学院名称）
//     └─ price	Long	单价（单位分）
//     └─ devSource	String	设备来源
//     └─ devSourceName	String	设备来源名称
//     └─ countryCode	String	国别码
//     └─ countryCodeName	String	国别码名称
//     └─ purchaseDate	LocalDate	购置日期 格式为 yyyy - MM - dd
//     └─ presentSituationCode	String	现状码
//     └─ presentSituationName	String	现状码名称
//     └─ directionOfUse	String	使用方向
//     └─ directionOfUseName	String	使用方向名称
//     └─ classNumCode	String	教育部设备分类号
//     └─ classNumName	String	教育部设备分类号名称
// 房间列表（包含绑定状态 实验中心/实验室用）
export function roomPage (params) {
  return request({
    url: '/lab/room/binding-status-page',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// pageNum	Long	否	页码	1
// pageSize	Long	否	页面容量	10
// orderItems	String	否	参与排序的字段（, 号分割）	roomId
// orderRule	String	否	排序规则（, 号分割）	desc
// campusName	String	否	校区名称	朝晖
// buildingName	String	否	楼宇名称	健
// floorName	String	否	楼层名称	1
// roomSn	String	否	房间号	101
// roomName	String	否	房间名称	101
// tags	String	否	房间标签	机房, 会议室	多个, 号分割
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	List
// └─ roomId	Integer	房间id
// └─ labId	Integer	实验室id
// └─ bindingStatus	Integer	绑定状态 0 - 未绑定 1 - 已绑定
// └─ roomInfo	RoomInfoV2VO	实验室基本信息
//     └─ roomId	Integer	房间id
//     └─ uuid	String	房间uuid
//     └─ roomSn	String	房间号
//     └─ roomName	String	实验室名称
//     └─ tags	String	房间标签
//     └─ imgUrl	Integer	房间照片
//     └─ campusId	Integer	校区ID
//     └─ campusName	String	校区名称
//     └─ buildingId	Integer	楼宇id
//     └─ buildingName	String	楼宇名称
//     └─ floorName	String	楼层名称
//     └─ roomFloor	String	房间所属楼层 纯数字表示
//     └─ area	Integer	面积(平方米 * 100 保留两位小数，即实际保存时没有小数点，展示时除以100)

// 实验室树
export function roomTree (params) {
  return request({
    url: '/room/tree',
    method: 'get',
    params,
  })
}
// 返回参数:
// 参数名	类型	必选	描述
// count	long	N
// data	List	N
// └─id	String	N	唯一值
// └─name	String	N	名称
// └─parentId	String	N	父级id
// └─value	Integer	N	值
// └─level	Integer	N	层级1:校区2:楼宇3:楼层4:房间
// └─children	List	N	子节点
// code	int	N	返回码0:成功,100:参数异常,500:服务器异常
// message	String	N	响应消息

// 实验室部门中心树
export function deptTree (params) {
  return request({
    url: '/lab/query/dept-tree',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	List
// └─ nodeKey	String	节点唯一标识 解决id如果本身不是树结构的情况
// └─ id	Integer	id
// └─ name	String	名字
// └─ uuid	String	uuid
// └─ parentUuid	String	上级uuid
// └─ type	Integer	根据事件构造树的场景来区分
// └─ children	List	下级
//     └─ nodeKey	String	节点唯一标识 解决id如果本身不是树结构的情况
//     └─ id	Integer	id
//     └─ name	String	名字
//     └─ uuid	String	uuid
//     └─ parentUuid	String	上级uuid
//     └─ type	Integer	根据事件构造树的场景来区分

// 房间导出文件接口
export function labRoomExport (params) {
  return request_import({
    url: '/lab/room-binding/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	示例	其他参考信息
// labUuid	String	是	实验室uuid	1939924316942700544
// campusName	String	否	校区名称	朝晖
// buildingName	String	否	楼宇名称	健
// floorName	String	否	楼层名称	1
// roomSn	String	否	房间编号	101
// roomName	String	否	房间名称	101
// 此接口无任何出参，直接返回文件流
// 房间号导入绑定接口
export function labRoomImportant (data) {
  return request({
    url: '/lab/room-binding/import',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "labUuid": "实验室UUID34",
//   "roomSnList": [
//     "房间编号集合 至少包含一个房间编号96",
//     "房间编号集合 至少包含一个房间编号126"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labUuid	String	是	实验室UUID
// roomSnList	List	否	房间编号集合 至少包含一个房间编号
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0:成功, 100:参数异常, 500:服务器异常
// message	String	响应消息
// data	ExcelImportVO
// └─ failList	List	失败列表
//     └─ roomSn	String	房间号
//     └─ errorMsg	String	错误原因

// 实验室设备导出文件接口
export function labDeviceExport (params) {
  return request_import({
    url: '/lab/device-binding/export',
    method: 'get',
    params,
    responseType: 'blob',
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// pageNum	Long	否	页码
// pageSize	Long	否	页面容量
// orderItems	String	否	参与排序的字段（, 号分割）
// orderRule	String	否	排序规则（, 号分割）
// labUuid	String	是	实验室UUID
// assetSn	String	否	资产编号
// devName	String	否	设备名称
// 此接口无任何出参，直接返回文件流
// 实验室设备导入绑定接口
export function labDeviceImportant (data) {
  return request({
    url: '/lab/device-binding/import',
    method: 'post',
    data,
    XAPIVersion: '2.0.0'
  })
}
// 入参示例 (RequestBody)
// {
//   "labUuid": "实验室UUID118",
//   "assetSnList": [
//     "资产编号集合 至少包含一个资产编号100",
//     "资产编号集合 至少包含一个资产编号64"
//   ]
// }
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// labUuid	String	是	实验室UUID
// assetSnList	List	否	资产编号集合 至少包含一个资产编号
// /出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	ExcelImportVO
// └─ failList	List	失败列表
//     └─ assetSn	String	资产编号
//     └─ errorMsg	String	错误原因

// 实验室概览中心
export function labSummary (params) {
  return request({
    url: '/lab/query/summary',
    method: 'get',
    params,
    XAPIVersion: '2.0.0'
  })
}
// 入参字段说明
// 字段	类型	必填	含义	其他参考信息
// uuid	String	是	实验室UUID
// 出参字段说明
// 字段	类型	含义	其他参考信息
// code	int	返回码 0: 成功, 100: 参数异常, 500: 服务器异常
// message	String	响应消息
// data	LabSummaryVO
// └─ personnelCount	Long	人员总数
// └─ roomCount	Long	房间总数
// └─ deviceCount	Long	仪器设备总数
// └─ regulationCount	Long	规章制度（技术资料）总数
