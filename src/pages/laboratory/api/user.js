import request from '@laboratory/utils/request';

export function login (data) {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}

export function getlistbyrole (roleId) {
  return request({
    url: '/function/listByRoleId',
    method: 'get',
    params: { roleId }
  })
}

export function logout () {
  return request({
    url: '/login/logout',
    method: 'get'
  })
}


//判断是否为统一身份认证登录
export function loginType () {
  return request({
    url: '/login/getLoginType',
    method: 'get'
  })
}

//获取当前账号的应用数据
export function getAppList () {
  return request({
    url: '/function/getAppList',
    method: 'get'
  })
}

//切换应用下的菜单栏
export function changeApp (params) {
  return request({
    url: '/function/changeApp',
    method: 'get',
    params
  })
}



