<template>
  <el-cascader
    v-model="selectedValue"
    :options="options"
    :props="mergedProps"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :size="size"
    :loading="loading"
    :style="{ width: width }"
    :showAllLevels="false"
    filterable
    @change="handleChange"
  ></el-cascader>
</template>

<script>
import { getDictAll } from "@laboratory/api/laboratory/basicMessage";
import { processTreeData, mergeCascaderProps, defaultCascaderProps } from "@laboratory/utils/dictUtils";

export default {
  name: "DictCascader",
  props: {
    // 字典类型编码
    codeType: {
      type: [Number, String],
      required: true,
    },
    // 选中的值
    value: {
      type: [String, Number, Array],
      default: "",
    },
    // 级联选择器配置
    props: {
      type: Object,
      default: () => ({}),
    },
    // 占位文本
    placeholder: {
      type: String,
      default: "请选择",
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true,
    },
    // 尺寸
    size: {
      type: String,
      default: "small",
    },
    // 宽度
    width: {
      type: String,
      default: "100%",
    },
  },
  data() {
    return {
      options: [],
      loading: false,
      selectedValue: this.value,
    };
  },
  computed: {
    // 合并默认配置和自定义配置
    mergedProps() {
      return mergeCascaderProps(defaultCascaderProps, this.props);
    },
  },
  watch: {
    value(val) {
      this.selectedValue = val;
    },
    selectedValue(val) {
      this.$emit("input", val);
    },
    codeType: {
      handler(val) {
        if (val) {
          this.fetchDictData();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取字典数据
    fetchDictData() {
      this.loading = true;
      getDictAll({ codeType: this.codeType })
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 处理字典数据，移除空的children字段
            this.options = processTreeData(res.data);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 处理选择变更
    handleChange(val) {
      this.$emit("change", val);
    },
  },
};
</script>
