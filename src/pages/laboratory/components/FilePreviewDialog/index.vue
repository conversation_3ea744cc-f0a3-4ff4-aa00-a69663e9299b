<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    class="file-preview-dialog"
    @close="handleClose"
  >
    <div class="preview-container">
      <!-- 文件信息头部 -->
      <div class="file-info-header" v-if="fileInfo">
        <div class="file-meta">
          <i :class="getFileIcon(fileInfo.fileName)" class="file-icon"></i>
          <div class="file-details">
            <h4 class="file-name">{{ fileInfo.fileName || "未知文件" }}</h4>
            <div class="file-meta-info">
              <span class="file-size" v-if="fileInfo.fileSize">{{ formatFileSize(fileInfo.fileSize) }}</span>
              <span class="file-type">{{ getFileTypeLabel(fileInfo.fileName) }}</span>
            </div>
          </div>
        </div>
        <div class="file-actions">
          <el-button icon="el-icon-download" @click="handleDownload" v-if="fileInfo.fileUrl || fileInfo.attachmentUrl">
            下载
          </el-button>
        </div>
      </div>

      <!-- 预览内容区域 -->
      <div class="preview-content">
        <!-- 图片预览 -->
        <div v-if="currentFileType === 'image'" class="image-preview-container">
          <div class="image-wrapper" ref="imageContainer">
            <img :src="previewUrl" :alt="fileInfo.fileName" class="preview-image" @load="initImageViewer" @error="handleImageError" />
          </div>
        </div>

        <!-- PDF预览 -->
        <div v-else-if="currentFileType === 'pdf'" class="pdf-preview-container">
          <iframe :src="previewUrl" class="preview-iframe" frameborder="0" @error="handlePdfError"></iframe>
        </div>

        <!-- 不支持预览的文件类型 -->
        <div v-else class="unsupported-preview">
          <div class="unsupported-content">
            <i class="el-icon-warning-outline unsupported-icon"></i>
            <h3>暂不支持在线预览</h3>
            <p>该文件类型暂不支持在线预览，请下载后查看</p>
            <el-button type="primary" icon="el-icon-download" @click="handleDownload" v-if="fileInfo.fileUrl || fileInfo.attachmentUrl">
              下载文件
            </el-button>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-loading-spinner></el-loading-spinner>
          <p>正在加载文件...</p>
        </div>

        <!-- 错误状态 -->
        <div v-if="error" class="error-container">
          <i class="el-icon-warning-outline error-icon"></i>
          <h3>文件加载失败</h3>
          <p>{{ error }}</p>
          <el-button @click="retryLoad">重试</el-button>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button
        type="primary"
        icon="el-icon-download"
        @click="handleDownload"
        v-if="fileInfo.fileUrl || fileInfo.attachmentUrl"
      >
        下载文件
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import Viewer from "viewerjs";
import "viewerjs/dist/viewer.css";
import { getFileType, getFileIcon, formatFileSize, isImageFile, isPdfFile } from "@laboratory/utils/fileUtils";

export default {
  name: "FilePreviewDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    fileInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      error: null,
      viewer: null,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    dialogTitle() {
      return this.fileInfo.fileName ? `文件预览 - ${this.fileInfo.fileName}` : "文件预览";
    },
    previewUrl() {
      return this.fileInfo.fileUrl || this.fileInfo.attachmentUrl || "";
    },
    currentFileType() {
      return getFileType(this.fileInfo.fileName);
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.error = null;
        this.loading = false;
        this.$nextTick(() => {
          if (this.currentFileType === "image") {
            this.initImageViewer();
          }
        });
      } else {
        this.destroyViewer();
      }
    },
    fileInfo: {
      handler() {
        this.error = null;
        this.loading = false;
      },
      deep: true,
    },
  },
  beforeDestroy() {
    this.destroyViewer();
  },
  methods: {
    // 初始化图片查看器
    initImageViewer() {
      this.$nextTick(() => {
        this.destroyViewer();

        const imageContainer = this.$refs.imageContainer;
        if (imageContainer && this.currentFileType === "image") {
          this.viewer = new Viewer(imageContainer, {
            inline: false,
            button: true,
            navbar: false,
            title: true,
            toolbar: {
              zoomIn: true,
              zoomOut: true,
              oneToOne: true,
              reset: true,
              prev: false,
              play: false,
              next: false,
              rotateLeft: true,
              rotateRight: true,
              flipHorizontal: true,
              flipVertical: true,
            },
            tooltip: true,
            movable: true,
            zoomable: true,
            rotatable: true,
            scalable: true,
            transition: true,
            fullscreen: true,
            keyboard: true,
            backdrop: true,
            loading: true,
            loop: false,
            interval: 5000,
            minZoomRatio: 0.1,
            maxZoomRatio: 10,
            zoomRatio: 0.1,
            zoomOnTouch: true,
            zoomOnWheel: true,
            slideOnTouch: true,
            toggleOnDblclick: true,
            ready() {
              console.log("图片查看器已准备就绪");
            },
            show() {
              console.log("图片查看器已显示");
            },
            shown() {
              console.log("图片查看器显示完成");
            },
            hide() {
              console.log("图片查看器已隐藏");
            },
            view() {
              console.log("图片查看中");
            },
            viewed() {
              console.log("图片查看完成");
            },
          });
        }
      });
    },

    // 销毁图片查看器
    destroyViewer() {
      if (this.viewer) {
        this.viewer.destroy();
        this.viewer = null;
      }
    },

    // 处理图片加载错误
    handleImageError() {
      this.error = "图片加载失败，请检查文件路径是否正确";
    },

    // 处理PDF加载错误
    handlePdfError() {
      this.error = "PDF文件加载失败，请检查文件路径是否正确";
    },

    // 重试加载
    retryLoad() {
      this.error = null;
      this.loading = true;

      setTimeout(() => {
        this.loading = false;
        if (this.currentFileType === "image") {
          this.initImageViewer();
        }
      }, 1000);
    },

    // 处理下载
    handleDownload() {
      const downloadUrl = this.fileInfo.fileUrl || this.fileInfo.attachmentUrl;
      if (!downloadUrl) {
        this.$message.warning("该文件暂无可下载的链接");
        return;
      }

      try {
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = this.fileInfo.fileName || "文件";
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success("开始下载文件");
      } catch (error) {
        console.error("下载文件失败:", error);
        this.$message.error("下载文件失败");
      }
    },

    // 处理关闭
    handleClose() {
      this.dialogVisible = false;
      this.destroyViewer();
    },

    // 获取文件图标
    getFileIcon,

    // 格式化文件大小
    formatFileSize,

    // 获取文件类型标签
    getFileTypeLabel(fileName) {
      const type = getFileType(fileName);
      const typeLabels = {
        image: "图片文件",
        pdf: "PDF文档",
        document: "文档文件",
        other: "其他文件",
      };
      return typeLabels[type] || "未知类型";
    },
  },
};
</script>

<style lang="scss" scoped>
.file-preview-dialog {
  .preview-container {
    .file-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #ebeef5;
      margin-bottom: 20px;

      .file-meta {
        display: flex;
        align-items: center;

        .file-icon {
          font-size: 24px;
          color: #2050d1;
          margin-right: 12px;
        }

        .file-details {
          .file-name {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }

          .file-meta-info {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #909399;

            .file-size,
            .file-type {
              padding: 2px 6px;
              background: #f5f7fa;
              border-radius: 4px;
            }
          }
        }
      }
    }

    .preview-content {
      position: relative;

      .image-preview-container {
        display: flex;
        justify-content: center;
        align-items: center;

        .image-wrapper {
          max-width: 300px;
          display: flex;
          justify-content: center;
          align-items: center;

          .preview-image {
            max-width: 300px;
            cursor: pointer;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }
        }
      }

      .pdf-preview-container {
        .preview-iframe {
          max-width: 300px;
          width: 100%;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
        }
      }

      .unsupported-preview {
        display: flex;
        justify-content: center;
        align-items: center;

        .unsupported-content {
          text-align: center;
          color: #909399;

          .unsupported-icon {
            font-size: 64px;
            color: #e6a23c;
            margin-bottom: 16px;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: #606266;
          }

          p {
            margin: 0 0 20px 0;
            color: #909399;
          }
        }
      }

      .loading-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        max-width: 300px;
        color: #909399;

        p {
          margin-top: 16px;
        }
      }

      .error-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        max-width: 300px;
        text-align: center;

        .error-icon {
          font-size: 64px;
          color: #f56c6c;
          margin-bottom: 16px;
        }

        h3 {
          margin: 0 0 8px 0;
          color: #f56c6c;
        }

        p {
          margin: 0 0 20px 0;
          color: #909399;
        }
      }
    }
  }
}

// 覆盖 viewer.js 的样式
v-deep .viewer-container {
  z-index: 9000 !important;
}
// :global(.viewer-container) {
//   z-index: 3000 !important;
// }

// :global(.viewer-backdrop) {
//   background-color: rgba(0, 0, 0, 0.8) !important;
// }

// :global(.viewer-toolbar) {
//   background-color: rgba(0, 0, 0, 0.8) !important;
// }
</style>
