<template>
  <div class="multi-file-uploader">
    <!-- 文件选择区域 -->
    <div class="file-select-area">
      <input ref="fileInput" type="file" :multiple="multiple" :accept="accept" @change.passive="handleFileChange" style="display: none" />
      <slot name="trigger">
        <el-button icon="el-icon-upload" @click.native.stop="triggerFileInput" :disabled="fileList.length === limit">
          {{ buttonText }}
        </el-button>
      </slot>
      <div class="upload-tip" v-if="tip">
        <slot name="tip">{{ tip }}</slot>
      </div>
    </div>

    <!-- 文件列表区域 -->
    <div class="file-list" v-if="fileList.length > 0">
      <slot name="file-list" :files="fileList">
        <el-table :data="fileList" style="width: 100%">
          <el-table-column prop="index" label="序号" align="center" width="80">
            <template slot-scope="scope">{{ scope.$index + 1 }}</template>
          </el-table-column>
          <el-table-column prop="name" label="文件名称" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="120">
            <template slot-scope="scope">
              <div style="width: 100%;display: flex;align-items: center;justify-content: center;position: relative;">
                <i class="icon" :class="getIconType(scope.row.status)"></i>
                {{ getStatusText(scope.row.status) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <div
                v-if="scope.row.status === 'ready' || scope.row.status === 'error'"
                class="delete-btn"
                @click.stop="removeFile(scope.$index)"
              >
                删除
              </div>
            </template>
          </el-table-column>
        </el-table>
      </slot>
    </div>
  </div>
</template>

<script>
import { uploadFile } from "@laboratory/api/upload";

export default {
  name: "MultiFileUploader",
  props: {
    // 使用 v-model 绑定的文件列表
    value: {
      type: Array,
      default: () => [],
    },
    // 上传接口地址 (为兼容现有结构保留，实际不再使用)
    action: {
      type: String,
      default: "",
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 最大文件数量
    limit: {
      type: Number,
      default: 10,
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: "",
    },
    // 文件大小限制（MB）
    fileSizeLimit: {
      type: Number,
      default: 50,
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: "选择文件",
    },
    // 提示文本
    tip: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      fileList: [],
      uploadingCount: 0,
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val && Array.isArray(val)) {
          this.fileList = [...val];
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 触发文件选择
    triggerFileInput() {
      this.$refs.fileInput.click();
    },

    // 处理文件选择变化
    handleFileChange(e) {
      const files = e.target.files;

      if (!files || files.length === 0) {
        return;
      }
      let filesList = Array.from(files);
      // 检查是否超出文件数量限制
      if (this.fileList.length + filesList.length > this.limit) {
        this.$emit("on-exceed", filesList.length);
        this.$message.warning(`最多只能上传${this.limit}个文件`);
        filesList.length = this.limit - this.fileList.length; // 截断超出部分
        // return;
      }
      // 使用 setTimeout 防止处理大量文件时阻塞UI
      setTimeout(() => {
        // 处理文件
        filesList.forEach((file) => {
          // 检查文件大小
          if (file.size > this.fileSizeLimit * 1024 * 1024) {
            this.$message.warning(`文件 ${file.name} 大小超过限制 (${this.fileSizeLimit}MB)`);
            return;
          }
          // 添加到文件列表
          this.fileList.push({
            uid: Date.now() + this.fileList.length,
            name: file.name,
            size: file.size,
            type: file.type,
            status: "ready", // ready, uploading, success, error
            percentage: 0,
            raw: file,
            url: "",
          });
        });
        // 重置文件输入，以便可以重复选择相同的文件
        this.$refs.fileInput.value = null;

        // 只在添加文件后更新父组件的值
        this.$emit("input", [...this.fileList]);
      }, 0);
    },

    // 移除文件
    removeFile(index) {
      if (this.fileList[index] && this.fileList[index].status !== "uploading") {
        this.fileList.splice(index, 1);
        // 只在移除文件后更新父组件的值
        this.$emit("input", [...this.fileList]);
      }
    },

    // 上传单个文件
    uploadFile(file) {
      if (!file || file.status === "success") {
        return Promise.resolve(file);
      }

      if (file.status === "uploading") {
        return Promise.reject(new Error("文件正在上传中"));
      }

      // 更新文件状态
      file.status = "uploading";
      file.percentage = 0;

      // 创建表单数据
      const formData = new FormData();
      formData.append("file", file.raw);
      // 使用项目提供的上传API
      return uploadFile(formData).then((res) => {
        if (res.code === 0) {
          file.status = "success";
          file.url = res.data;
          this.$emit("on-success", res, file);
          return file;
        } else {
          file.status = "error";
          this.$emit("on-error", new Error(res.message || "上传失败"), file);
          return Promise.reject(new Error(res.message || "上传失败"));
        }
      });
    },

    // 上传所有文件
    submitUpload() {
      if (this.fileList.length === 0) {
        return Promise.resolve([]);
      }

      // 过滤出待上传的文件
      const pendingFiles = this.fileList.filter((file) => file.status === "ready" || file.status === "error");

      if (pendingFiles.length === 0) {
        return Promise.resolve(this.fileList);
      }

      // 使用分批处理上传，每批处理3个文件，避免同时上传太多文件导致阻塞
      const batchSize = 3;
      const batches = [];

      for (let i = 0; i < pendingFiles.length; i += batchSize) {
        const batch = pendingFiles.slice(i, i + batchSize);
        batches.push(batch);
      }

      // 逐批上传文件
      return new Promise((resolve) => {
        const results = [];

        const processBatch = (index) => {
          if (index >= batches.length) {
            // 所有批次处理完成后，更新父组件的值
            this.$emit("input", [...this.fileList]);
            resolve(results);
            return;
          }

          const batchPromises = batches[index].map((file) => this.uploadFile(file));

          Promise.all(batchPromises)
            .then((batchResults) => {
              results.push(...batchResults);
              // 使用 setTimeout 避免长时间占用主线程
              setTimeout(() => processBatch(index + 1), 0);
            })
            .catch(() => {
              // 即使有错误也继续处理下一批
              setTimeout(() => processBatch(index + 1), 0);
            });
        };

        processBatch(0);
      });
    },

    // 上传指定文件
    submit(file) {
      const targetFile = this.fileList.find((f) => f.uid === file.uid);
      if (targetFile) {
        return this.uploadFile(targetFile);
      }
      return Promise.reject(new Error("文件不存在"));
    },

    // 清空文件列表
    clearFiles() {
      this.fileList = [];
      this.$emit("input", []);
    },

    // 获取状态类型
    getIconType(status) {
      switch (status) {
        case "success":
          return "el-icon-success";
        case "error":
          return "el-icon-warning";
        case "uploading":
          return "el-icon-loading";
        default:
          return "el-icon-time";
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "success":
          return "上传成功";
        case "error":
          return "上传失败";
        case "uploading":
          return "上传中";
        default:
          return "待上传";
      }
    },
  },
  mounted() {
    // 不再需要手动添加事件监听器，因为已经在模板中绑定了事件
    // 改用 @click.native.stop 来阻止事件冒泡并直接触发原生事件
  },
  beforeDestroy() {
    // 不再需要移除事件监听器
  },
};
</script>

<style lang="scss" scoped>
.multi-file-uploader {
  .file-select-area {
    margin-bottom: 15px;

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }
  }

  .file-list {
    margin-top: 15px;
  }
  .icon {
    position: absolute;
    font-size: 16px;
    color: #8a8b8a;
    left: 13px;
    top: 2;
  }
  .el-icon-success {
    color: #3ca273;
  }
  .el-icon-warning {
    color: #d2585d;
  }
  // .el-icon-loading {
  // }
  .el-icon-time {
    color: #8a8b8a;
  }
  .delete-btn {
    color: #d2585d;
    font-size: 14px;
    width: 100%;
    cursor: pointer;

    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}
::v-deep .el-table td.el-table__cell {
  border: none !important;
}
::v-deep .el-table__body-wrapper {
  overflow-x: hidden;
  border-left: 1px dashed #dcdcdc !important;
  border-right: 1px dashed #dcdcdc !important;
}
::v-deep .el-table td.el-table__cell,
::v-deep .el-table td.el-table__cell,
::v-deep .el-table th.el-table__cell.is-leaf {
  border-bottom: 1px dashed #dcdcdc !important;
}
</style>
