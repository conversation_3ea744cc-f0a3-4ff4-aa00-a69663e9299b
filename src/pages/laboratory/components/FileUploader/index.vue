<template>
  <div class="file-uploader">
    <!-- 图片上传模式 -->
    <template v-if="type === 'image'">
      <el-upload
        class="avatar-uploader"
        :accept="accept"
        action="#"
        :http-request="uploadFile"
        :show-file-list="false"
        :before-upload="beforeImageUpload"
      >
        <template v-if="value">
          <div class="image-container">
            <img :src="baseUrl + value" class="avatar" />
            <div class="delete-icon" @click.stop="handleImageRemove">
              <i class="el-icon-delete"></i>
            </div>
          </div>
        </template>
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
      <div class="upload-tip" v-if="tip">{{ tip }}</div>
    </template>

    <!-- 文件上传模式 -->
    <template v-else>
      <el-upload
        class="file-upload"
        action="#"
        :http-request="uploadFile"
        :file-list="fileList"
        :limit="limit"
        :before-upload="beforeFileUpload"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :show-file-list="showFileList"
      >
        <el-button :disabled="fileList.length >= limit"  type="primary">{{ clickName }}</el-button>
        <div slot="tip" class="upload-tip" v-if="tip">{{ tip }}</div>
      </el-upload>
    </template>
  </div>
</template>

<script>
import { uploadimg, uploadFile as uploadFileApi } from "@laboratory/api/upload";

export default {
  name: "FileUploader",
  props: {
    // 双向绑定的值
    value: {
      type: String,
      default: "",
    },
    // 上传类型: image 或 file
    type: {
      type: String,
      default: "image",
    },
    // 自定义上传接口
    uploadApi: {
      type: Function,
      default: null,
    },
    // 文件大小限制（MB）
    maxSize: {
      type: Number,
      default: function() {
        return this.type === "image" ? 2 : 50;
      },
    },
    // 提示文字
    tip: {
      type: String,
      default: function() {
        return this.type === "image" ? "建议上传尺寸：32px * 32px" : "请上传文件";
      },
    },
    // 允许的文件类型
    accept: {
      type: String,
      default: function() {
        return this.type === "image" ? "image/*" : "";
      },
    },
    // 最大上传数量
    limit: {
      type: Number,
      default: 1,
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    clickName: {
      type: String,
      default: '点击上传'
    }
  },
  data() {
    return {
      baseUrl: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      fileList: [],
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        // 当value变化时，更新fileList
        if (this.type !== "image" && val) {
          // 处理多文件情况
          if (Array.isArray(val)) {
            // 如果value是数组，则表示多文件
            this.fileList = val.map((item, index) => {
              return {
                name: this.$attrs.fileNames?.[index] || `文件${index + 1}`,
                url: item,
              };
            });
          } else {
            // 单文件情况
            const fileName = this.$attrs.fileName || "文件";
            // 检查fileList是否已包含此文件，避免重复添加
            const existingFile = this.fileList.find((file) => file.url === val);
            if (!existingFile) {
              this.fileList = [
                {
                  name: fileName,
                  url: val,
                },
              ];
            }
          }
        } else if (!val) {
          this.fileList = [];
        }
      },
    },
  },
  methods: {
    // 上传图片前的验证
    beforeImageUpload(file) {
      const isImage = file.type.indexOf("image/") === 0;
      const isLtMax = file.size / 1024 / 1024 < this.maxSize;

      if (!isImage) {
        this.$message.error(`上传图标只能是图片格式!`);
        return false;
      }
      if (!isLtMax) {
        this.$message.error(`上传图标大小不能超过 ${this.maxSize}MB!`);
        return false;
      }
      return true;
    },

    // 上传文件前的验证
    beforeFileUpload(file) {
      const isLtMax = file.size / 1024 / 1024 < this.maxSize;

      if (!isLtMax) {
        this.$message.error(`上传文件大小不能超过 ${this.maxSize}MB!`);
        return false;
      }

      // 如果设置了accept属性，则进行类型检查
      if (this.accept && this.accept !== "*") {
        const fileType = file.type;
        const acceptTypes = this.accept.split(",");
        const isValidType = acceptTypes.some((type) => {
          if (type.endsWith("/*")) {
            // 处理类似 image/* 的情况
            const mainType = type.split("/")[0];
            return fileType.startsWith(mainType + "/");
          }
          return type === fileType;
        });

        if (!isValidType) {
          this.$message.error(`上传文件格式不正确!`);
          return false;
        }
      }

      return true;
    },

    // 统一的上传处理方法
    uploadFile(param) {
      const formData = new FormData();
      formData.append("file", param.file);

      const loading = this.$loading({
        lock: true,
        text: "上传中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 根据类型选择默认上传API
      let defaultApi = this.type === "image" ? uploadimg : uploadFileApi;
      // 使用传入的上传API或默认的API
      const uploadFunction = this.uploadApi || defaultApi;

      uploadFunction(formData)
        .then((response) => {
          loading.close();
          if (response.code === 0) {
            // 通过事件将上传成功的路径传递给父组件
            this.$emit("input", response.data);

            // 如果是文件类型，更新文件列表
            if (this.type !== "image") {
              this.fileList = [
                {
                  name: param.file.name,
                  url: response.data,
                },
              ];
            }
            // 触发上传成功事件
            this.$emit(
              "upload-success",
              {
                url: response.data,
                name: param.file.name,
                size: param.file.size,
                type: param.file.type,
              },
              this.fileList,
            );

            this.$message({
              type: "success",
              message: "上传成功",
            });
          }
        })
        .catch(() => {
          loading.close();

          // 上传失败时清理文件列表，避免显示失败的文件
          if (this.type !== "image") {
            this.fileList = [];
          }

          // 触发上传失败事件
          this.$emit("upload-error", {
            name: param.file.name,
            size: param.file.size,
            type: param.file.type,
          });

        });
    },

    // 移除文件
    handleRemove() {
      this.fileList = [];
      this.$emit("input", "");
      this.$emit("remove");
    },

    // 处理超出限制的情况
    handleExceed() {
      this.$message.error(`最多只能上传 ${this.limit} 个文件`);
    },

    // 处理图片删除
    handleImageRemove(e) {
      // 阻止事件冒泡，防止触发上传操作
      e && e.stopPropagation();

      // 清空图片路径
      this.$emit("input", "");

      // 触发删除事件
      this.$emit("remove");

      this.$message({
        type: "success",
        message: "图片已删除",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.avatar-uploader {
  width: 100px;
  background-color: #f5f7fa;
}
.avatar-uploader /deep/ .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &:hover {
    border-color: #2050d1;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}
.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
.file-upload {
  // /deep/ .el-upload-list {
  //   margin-top: 10px;
  // }
}
.image-container {
  position: relative;
  width: 100px;
  height: 100px;
  overflow: hidden;

  &:hover .delete-icon {
    opacity: 1;
  }

  .delete-icon {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;

    i {
      color: #fff;
      font-size: 20px;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }
}
</style>
