<template>
  <div class="icon-selector">
    <el-popover placement="bottom" width="600" trigger="click" v-model="popoverVisible">
      <template slot="reference">
        <el-input v-model="selectedIcon" placeholder="请选择图标" clearable @clear="clearIcon"></el-input>
      </template>
      <div class="icon-selector-container">
        <div class="icon-search">
          <el-input v-model="iconSearch" placeholder="搜索图标" prefix-icon="el-icon-search" clearable></el-input>
        </div>
        <div class="icon-list">
          <div v-for="icon in filteredIcons" :key="icon" class="icon-item" @click="selectIcon(icon)">
            <i :class="'iconfont ' + icon"></i>
            <span class="icon-name">{{ icon.replace("", "") }}</span>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: "IconSelector",
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      selectedIcon: this.value,
      popoverVisible: false,
      iconSearch: "",
      iconList: [],
    };
  },
  computed: {
    filteredIcons() {
      if (!this.iconSearch) {
        return this.iconList;
      }

      const search = this.iconSearch.toLowerCase();
      return this.iconList.filter((icon) => icon.toLowerCase().includes(search));
    },
  },
  watch: {
    value(val) {
      this.selectedIcon = val;
    },
    selectedIcon(val) {
      this.$emit("input", val);
    },
  },
  methods: {
    selectIcon(icon) {
      this.selectedIcon = icon;
      this.popoverVisible = false;
      this.$emit("change", icon);
    },
    clearIcon() {
      this.selectedIcon = "";
      this.$emit("change", "");
    },
    async fetchIconFont() {
      // 获取所有 <link> 标签
      // const links = document.querySelectorAll('link[rel="stylesheet"]');

      // // 遍历并匹配目标链接
      // links.forEach(link => {
      //   if (link.href.includes('font_3009528_vk2bm5i4n7b')) {
      //     const hrefString = link.getAttribute('href');
      //     console.log(hrefString); // 输出：//at.alicdn.com/t/c/font_3009528_vk2bm5i4n7b.css
      //   }
      // });
      if (!window.g.iconFontUrl) return;
      try {
        // 尝试直接从阿里图标库获取图标信息
        const response = await fetch(window.g.iconFontUrl);
        const cssText = await response.text();

        // 使用正则表达式匹配所有图标类名
        const iconRegex = /\.icon-([a-zA-Z0-9-_]+):before\s*{/g;
        let match;
        const icons = [];

        while ((match = iconRegex.exec(cssText)) !== null) {
          icons.push("icon-" + match[1]);
        }

        if (icons.length > 0) {
          this.iconList = icons;
          console.log("通过请求加载了", icons.length, "个图标");
        } else {
          // 使用备用正则表达式
          const alternativeRegex = /\.iconfont\.icon-([a-zA-Z0-9-_]+):before/g;
          while ((match = alternativeRegex.exec(cssText)) !== null) {
            icons.push("icon-" + match[1]);
          }

          if (icons.length > 0) {
            this.iconList = icons;
            console.log("通过备用正则加载了", icons.length, "个图标");
          } else {
            // 如果仍然无法匹配，使用预定义的图标
            this.useDefaultIcons();
          }
        }
      } catch (error) {
        console.error("获取图标库失败:", error);
        this.useDefaultIcons();
      }
    },

    useDefaultIcons() {
      console.warn("使用预定义图标列表");
      // 预定义一些常用的阿里图标类名
      this.iconList = [
        // "iconfont icon-home",
        // "iconfont icon-user",
        // "iconfont icon-setting",
        // "iconfont icon-search",
        // "iconfont icon-message",
        // "iconfont icon-edit",
        // "iconfont icon-delete",
        // "iconfont icon-add",
        // "iconfont icon-close",
        // "iconfont icon-check",
        // "iconfont icon-info",
        // "iconfont icon-warning",
        // "iconfont icon-error",
        // "iconfont icon-success",
        // "iconfont icon-file",
        // "iconfont icon-folder",
        // "iconfont icon-link",
        // "iconfont icon-share",
        // "iconfont icon-download",
        // "iconfont icon-upload",
        // "iconfont icon-camera",
        // "iconfont icon-image",
        // "iconfont icon-video",
        // "iconfont icon-audio",
        // "iconfont icon-location",
        // "iconfont icon-phone",
        // "iconfont icon-mail",
        // "iconfont icon-chat",
        // "iconfont icon-calendar",
        // "iconfont icon-time",
      ];
    },
  },
  mounted() {
    this.fetchIconFont();
  },
};
</script>

<style lang="less" scoped>
.icon-selector {
  width: 100%;
}

.icon-selector-container {
  .icon-search {
    margin-bottom: 10px;
  }

  .icon-list {
    height: 300px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .icon-item {
      width: 85px;
      height: 85px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      transition: all 0.3s;
      padding: 8px 5px;

      &:hover {
        background-color: #f0f9eb;
        border-color: #c2e7b0;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      i {
        font-size: 28px;
        margin-bottom: 8px;
        color: #606266;
      }

      .icon-name {
        font-size: 12px;
        color: #909399;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        text-align: center;
        padding: 0 5px;
      }
    }
  }
}

// 让滚动条看起来更美观
.icon-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.icon-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.icon-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.icon-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
