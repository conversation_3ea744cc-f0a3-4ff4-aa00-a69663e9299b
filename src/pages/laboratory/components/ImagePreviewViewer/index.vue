<template>
  <div class="image-preview-viewer">
    <!-- 图片预览容器 -->
    <el-image v-if="imageUrl" :src="imageUrl" :preview-src-list="[imageUrl]" style="display: none;" ref="imagePreview" />
  </div>
</template>

<script>
export default {
  name: "ImagePreviewViewer",
  data() {
    return {
      imageUrl: "",
    };
  },
  methods: {
    // 预览图片
    preview(url, fileName = "") {
      this.imageUrl = url;
      this.$nextTick(() => {
        if (this.$refs.imagePreview) {
          // 触发el-image的预览功能
          this.$refs.imagePreview.clickHandler();
        }
      });
    },

    // 判断是否为图片文件
    isImageFile(fileName) {
      if (!fileName) return false;
      const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"];
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf("."));
      return imageExtensions.includes(extension);
    },
  },
};
</script>

<style scoped>
.image-preview-viewer {
  display: none;
}
</style>
