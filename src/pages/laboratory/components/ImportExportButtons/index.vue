<template>
  <div class="import-export-buttons">
    <!-- 导入按钮 -->
    <el-upload
      v-if="showImport"
      ref="uploadRef"
      :action="''"
      :auto-upload="false"
      :show-file-list="false"
      :before-upload="handleBeforeUpload"
      :on-change="handleFileChange"
      accept=".xlsx,.xls"
      style="display: inline-block; margin-left: 8px;"
    >
      <el-button type="primary" icon="el-icon-upload2" :loading="importLoading">
        {{ importButtonText }}
      </el-button>
    </el-upload>
    <!-- 导出按钮 -->
    <el-button
      v-if="showExport"
      type="success"

      icon="el-icon-download"
      :loading="exportLoading"
      @click="handleExport"
      style="margin-left: 8px;"
    >
      {{ exportButtonText }}
    </el-button>
    <!-- 模板下载按钮 -->
    <el-button
      v-if="showTemplate"
      type="info"

      icon="el-icon-document"
      @click="handleDownloadTemplate"
      style="margin-left: 8px;"
    >
      {{ templateButtonText }}
    </el-button>

    <!-- 导入确认对话框 -->
    <el-dialog title="确认导入" :visible.sync="importDialogVisible" width="500px" :close-on-click-modal="false">
      <div class="import-confirm-content">
        <div class="file-info">
          <p>
            <strong>文件名：</strong>
            {{ selectedFile && selectedFile.name }}
          </p>
          <p>
            <strong>文件大小：</strong>
            {{ formatFileSize(selectedFile && selectedFile.size) }}
          </p>
          <p>
            <strong>解析结果：</strong>
            共找到
            <span class="highlight">{{ parsedData.length }}</span>
            条{{ fieldName }}数据
          </p>
        </div>

        <div class="data-preview" v-if="parsedData.length > 0">
          <p><strong>数据预览：</strong></p>
          <div class="preview-list">
            <el-tag v-for="(item, index) in previewData" :key="index" style="margin: 2px;">
              {{ item }}
            </el-tag>
            <!-- <span v-if="parsedData.length > 10" class="more-indicator">... 还有 {{ parsedData.length - 10 }} 条数据</span> -->
          </div>
        </div>

        <!-- 导入失败信息显示 -->
        <div class="import-result" v-if="importResult && importResult.failList && importResult.failList.length > 0">
          <p><strong>导入结果：</strong></p>
          <div class="fail-list">
            <p class="fail-summary">
              成功：
              <span class="success-count">{{ importResult.successCount || 0 }}</span>
              条， 失败：
              <span class="fail-count">{{ importResult.failList.length }}</span>
              条
            </p>
            <div class="fail-details" v-if="importResult.failList.length > 0">
              <p><strong>失败详情：</strong></p>
              <div class="fail-items">
                <div v-for="(item, index) in importResult.failList" :key="index" class="fail-item">
                  <span class="asset-sn">{{ item[Object.keys(item)[0]] }}</span>
                  ：
                  <span class="error-msg">{{ item[Object.keys(item)[1]] }}</span>
                </div>
                <!-- <div v-if="importResult.failList.length > 5" class="more-fails">
                  ... 还有 {{ importResult.failList.length - 5 }} 条失败记录
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelImport">取 消</el-button>
        <el-button type="primary" @click="confirmImport" :loading="importLoading">
          确认导入
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DeviceImportExport from "@laboratory/utils/DeviceImportExport";

export default {
  name: "ImportExportButtons",
  props: {
    // 控制按钮显示
    showImport: {
      type: Boolean,
      default: true,
    },
    showExport: {
      type: Boolean,
      default: true,
    },
    showTemplate: {
      type: Boolean,
      default: true,
    },
    // 实验室/实验中心UUID
    uuid: {
      type: String,
      default: "",
    },
    // 导入/导出uuid参数字段名
    uuidName: {
      type: String,
      default: "",
    },
    // 字段名称，默认为"资产编号"
    fieldName: {
      type: String,
      default: "资产编号",
    },
    // 模板文件名
    templateName: {
      type: String,
      default: "设备导入模板",
    },
    // 示例数据
    exampleData: {
      type: Array,
      default: () => ["ASSET001", "ASSET002"],
    },
    // 导出按钮文本
    exportButtonText: {
      type: String,
      default: "导出",
    },
    // 导入按钮文本
    importButtonText: {
      type: String,
      default: "导入",
    },
    // 模板下载按钮文本
    templateButtonText: {
      type: String,
      default: "下载模板",
    },
    // 导出文件前缀
    exportFilePrefix: {
      type: String,
      default: "设备列表",
    },
    // 导入参数字段名
    importParamField: {
      type: String,
      default: "assetSnList",
    },
    // 导出API函数 - 当showExport为true时才需要
    exportApi: {
      type: Function,
      default: null,
    },
    // 导入API函数 - 当showImport为true时才需要
    importApi: {
      type: Function,
      default: null,
    },
    fileType: {
      type: String,
      default: "xlsx",
    },
  },
  data() {
    return {
      exportLoading: false,
      importLoading: false,
      importDialogVisible: false,
      selectedFile: null,
      parsedData: [],
      importResult: null, // 导入结果
    };
  },
  computed: {
    // 预览数据（最多显示10条）
    previewData() {
      return this.parsedData;
      // return this.parsedData.slice(0, 10);
    },
  },
  methods: {
    // 处理导出
    async handleExport() {
      try {
        if (!this.exportApi) {
          this.$message.error("导出功能未配置");
          return;
        }

        this.exportLoading = true;

        // 构造导出参数
        const params = this.buildExportParams();

        // 调用传入的导出API
        const response = await this.exportApi(params);
        console.log(response, "response");
        // 处理文件下载
        if (response && (response.data || response instanceof Blob)) {
          const filename = `${this.exportFilePrefix}_${DeviceImportExport.formatDate()}.${this.fileType}`;
          const fileData = response.data || response;
          DeviceImportExport.downloadFile(fileData, filename);
          this.$message.success("导出成功");
          this.$emit("export-success");
        } else {
          throw new Error("导出数据为空");
        }
      } catch (error) {
        console.error("导出失败:", error);
        this.$emit("export-error", error);
      } finally {
        this.exportLoading = false;
      }
    },

    // 处理文件上传前的验证
    handleBeforeUpload(file) {
      // 验证文件类型
      if (!DeviceImportExport.validateFileType(file)) {
        this.$message.error("请选择Excel文件（.xlsx或.xls格式）");
        return false;
      }

      // 验证文件大小（限制10MB）
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error("文件大小不能超过10MB");
        return false;
      }

      return false; // 阻止自动上传
    },

    // 处理文件选择
    async handleFileChange(file) {
      try {
        this.selectedFile = file.raw;

        // 解析Excel文件
        this.parsedData = await DeviceImportExport.parseExcelFile(file.raw, this.fieldName);

        // 显示确认对话框
        this.importDialogVisible = true;
      } catch (error) {
        console.error("文件解析失败:", error);
        this.$message.error(error.message || "文件解析失败");
        this.resetUpload();
      }
    },

    // 确认导入
    async confirmImport() {
      try {
        if (!this.importApi) {
          this.$message.error("导入功能未配置");
          return;
        }

        this.importLoading = true;
        this.importResult = null; // 重置导入结果

        // 构造导入参数
        const params = this.buildImportParams();

        // 调用传入的导入API
        const response = await this.importApi(params);

        if (response.code === 0) {
          this.$message.success(`成功导入${this.parsedData.length}条设备数据`);
          this.importDialogVisible = false;
          this.resetUpload();
          this.$emit("import-success");
        } else if (response.code === 2) {
          // 检查是否有失败记录
          if (response.data && response.data.failList && response.data.failList.length > 0) {
            this.importResult = {
              successCount: this.parsedData.length - response.data.failList.length,
              failList: response.data.failList,
            };
            this.$message.warning(`导入完成，成功${this.importResult.successCount}条，失败${response.data.failList.length}条`);
          } else {
            this.$message.success(`成功导入${this.parsedData.length}条设备数据`);
            this.importDialogVisible = false;
            this.resetUpload();
          }
          this.$emit("import-success", {
            total: this.parsedData.length,
            success: this.importResult ? this.importResult.successCount : this.parsedData.length,
            failed: this.importResult ? this.importResult.failList.length : 0,
            failList: this.importResult ? this.importResult.failList : [],
          });
        }
      } catch (error) {
        console.error("导入失败:", error);
        this.$emit("import-error", error);
      } finally {
        this.importLoading = false;
      }
    },

    // 取消导入
    cancelImport() {
      this.importDialogVisible = false;
      this.resetUpload();
    },

    // 重置上传状态
    resetUpload() {
      this.selectedFile = null;
      this.parsedData = [];
      this.importResult = null;
      if (this.$refs.uploadRef) {
        this.$refs.uploadRef.clearFiles();
      }
    },

    // 下载模板
    handleDownloadTemplate() {
      try {
        DeviceImportExport.generateTemplate(this.templateName, this.fieldName, this.exampleData);
        this.$message.success("模板下载成功");
        this.$emit("template-download");
      } catch (error) {
        console.error("模板下载失败:", error);
        this.$message.error("模板下载失败");
      }
    },

    // 构造导出参数
    buildExportParams() {
      const params = {};

      // 如果提供了uuid和uuidName，则添加到参数中
      if (this.uuid && this.uuidName) {
        params[this.uuidName] = this.uuid;
      }

      return params;
    },

    // 构造导入参数
    buildImportParams() {
      // 验证必要参数
      if (!this.parsedData || this.parsedData.length === 0) {
        throw new Error("没有可导入的数据");
      }

      const params = {
        [this.importParamField]: this.parsedData,
      };

      // 如果提供了uuid和uuidName，则添加到参数中
      if (this.uuid && this.uuidName) {
        params[this.uuidName] = this.uuid;
      }

      return params;
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return "0 Bytes";
      return DeviceImportExport.formatFileSize(bytes);
    },
  },
};
</script>

<style lang="scss" scoped>
.import-export-buttons {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.import-confirm-content {
  .file-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;

    p {
      margin: 5px 0;
      font-size: 14px;

      .highlight {
        color: #2050d1;
        font-weight: bold;
      }
    }
  }

  .data-preview {
    .preview-list {
      max-height: 120px;
      overflow-y: auto;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 10px;
      background-color: #fafafa;

      .more-indicator {
        color: #909399;
        font-size: 12px;
        font-style: italic;
      }
    }
  }

  .import-result {
    margin-top: 15px;
    padding: 15px;
    background-color: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 4px;

    .fail-summary {
      margin: 5px 0;
      font-size: 14px;

      .success-count {
        color: #52c41a;
        font-weight: bold;
      }

      .fail-count {
        color: #ff4d4f;
        font-weight: bold;
      }
    }

    .fail-details {
      margin-top: 10px;

      .fail-items {
        max-height: 120px;
        overflow-y: auto;
        border: 1px solid #ffd591;
        border-radius: 4px;
        padding: 10px;
        background-color: #fffbf0;

        .fail-item {
          margin: 3px 0;
          font-size: 12px;

          .asset-sn {
            color: #1890ff;
            font-weight: bold;
          }

          .error-msg {
            color: #ff4d4f;
          }
        }

        .more-fails {
          color: #909399;
          font-size: 12px;
          font-style: italic;
          margin-top: 5px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
