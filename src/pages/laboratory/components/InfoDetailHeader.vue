<template>
  <div class="info-detail-header" v-loading="loading">
    <div class="section-title">
      <span>基本信息</span>
    </div>

    <div class="header-content">
      <div class="header-image" :style="imageStyle">
        <img :src="imagePrefix + imagePath" alt="图片" v-if="imagePath" />
        <img src="@laboratory/assets/image/labCover.png" alt="图片" v-else />
        <!-- <div class="image-placeholder" v-else>
          <i class="el-icon-picture-outline"></i>
        </div> -->
      </div>
      <div class="header-info">
        <h2 class="header-title">{{ title }}
          <slot name=title-style></slot>
        </h2>
        <slot name="dept">
          <div class="center-dept">{{ centerDetail.deptName }}</div>
        </slot>
        <div class="info-row">
          <div class="info-item" v-for="(item, index) in infoItems" :key="index">
            <i :class="item.icon" v-if="showIcon && item.icon"></i>
            <el-tooltip
              :content="`${item.label}：${item.value}`"
              placement="top"
              :disabled="!isTextOverflow(item.label, item.value)"
              effect="light"
              :enterable="false"
            >
              <span class="ellipsis2" style="display: flex;"><div>{{ item.label }}：</div><div style="white-space: pre-wrap;">{{ item.value }}</div></span>
            </el-tooltip>
            <!-- <div v-if="item.label === '地址'" class="location">
              <i class="el-icon-map-location"></i>
              查看定位
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "InfoDetailHeader",
  props: {
    centerDetail: {
      type: Object,
      default: () => ({}),
    },
    // 标题（如实验中心名称）
    title: {
      type: String,
      required: true,
    },
    // 图片路径
    imagePath: {
      type: String,
      default: "",
    },
    // 图片前缀（服务器地址等）
    imagePrefix: {
      type: String,
      default: "",
    },
    // 信息对象数组，包含图标和内容
    infoItems: {
      type: Array,
      default: () => [],
      // 每个元素格式: { icon: 'el-icon-xxx', label: '标签名', value: '值' }
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 图片宽度
    imageWidth: {
      type: String,
      default: "200px",
    },
    // 图片高度
    imageHeight: {
      type: String,
      default: "200px",
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 计算图片样式
    imageStyle() {
      return {
        width: this.imageWidth,
        height: this.imageHeight,
      };
    },
  },
  methods: {
    // 判断文本是否溢出
    isTextOverflow(label, value) {
      const text = `${label}：${value}`;
      // 创建一个临时元素来测量文本宽度
      const tempElement = document.createElement('span');
      tempElement.style.visibility = 'hidden';
      tempElement.style.position = 'absolute';
      tempElement.style.fontSize = '14px';
      tempElement.style.fontFamily = getComputedStyle(document.body).fontFamily;
      tempElement.textContent = text;
      document.body.appendChild(tempElement);

      const textWidth = tempElement.offsetWidth;
      document.body.removeChild(tempElement);

      // info-item 的宽度是 270px，减去图标和边距大约 250px 可用
      return textWidth > 250;
    },
  },
};
</script>

<style lang="scss" scoped>
.info-detail-header {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    color: #333333;
    &::before {
      content: "";
      display: block;
      width: 4px;
      height: 16px;
      background-color: #2468f2;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .header-content {
    display: flex;
    // padding: 10px;
  }

  .header-image {
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    margin-right: 20px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;

      i {
        font-size: 48px;
        color: #c0c4cc;
      }
    }
  }

  .header-info {
    flex: 1;

    .header-title {
      font-size: 18px;
      color: #333333;
      margin: 0;
    }
    .center-dept {
      display: inline-block;
      width: fit-content;
      min-width: auto;
      max-width: 160px;
      height: 23px;
      line-height: 23px;
      text-align: center;
      background: #ecf2fe;
      border-radius: 3px;
      font-size: 14px;
      color: #0052d9;
      margin: 7px 0 24px;
      padding: 0 6px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      box-sizing: border-box;
    }
    .info-row {
      display: flex;
      flex-wrap: wrap;
      .info-item {
        width: 270px;
        display: flex;
        align-items: center;
        margin-right: 30px;
        margin-bottom: 15px;
        color: #333333;
        font-size: 14px;
        white-space: nowrap;
        i {
          margin-right: 8px;
        }
        .location {
          margin-left: 10px;
          cursor: pointer;
          color: #0052d9;
          i {
            color: #0052d9;
            margin-right: 0;
          }
        }
      }
      .info-item:last-child {
        flex-basis: 100%;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .info-detail-header {
    .header-content {
      flex-direction: column;
    }

    .header-image {
      width: 100% !important;
      margin-right: 0;
      margin-bottom: 20px;
    }
  }
}
</style>
