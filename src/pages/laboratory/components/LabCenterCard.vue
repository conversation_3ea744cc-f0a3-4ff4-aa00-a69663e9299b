<template>
  <div class="center-card">
    <!-- 添加状态文本的具名插槽，如果没有使用插槽则显示默认文本 -->
    <!-- 后续修改，不需要角标，功能保留 -->
    <slot name="status-text">
      <div class="card-icon" v-if="showIcon">
        <i class="iconfont icon-bianyuan" :class="item.roomKindLevel == 1 ? 'icon-warning' : 'icon-success'" />
        <div class="text">
          {{ item.roomKindLevel === 1 ? "校级" : "院级" }}
        </div>
      </div>
    </slot>
    <div class="card-main">
      <div class="card-image">
        <img v-if="item.roomKindImage" :src="imageSrc + item.roomKindImage" alt="图片" />
        <img src="@laboratory/assets/image/labCover.png" alt="图片" v-else />
        <!-- <div v-else class="image-placeholder">
          <i class="el-icon-picture-outline"></i>
        </div> -->
      </div>
      <div class="card-content">
        <div class="center-name" :class="ellipsisNum === 1 ? 'ellipsis' : 'ellipsis2 '">{{ item.roomKindName }}</div>
        <div v-if="showIcon2 || viewDeptName">
          <slot name="showIcon2" v-if="showIcon2">
            <div class="center-dept" style="margin-right: 11px;">{{ item.roomKindLevel === 1 ? "校级" : "院级" }}</div>
          </slot>
          <div class="center-dept" v-if="viewDeptName">{{ item.deptName }}</div>
        </div>
        <div class="center-dept2" v-else-if="!ellipsis"></div>
        <div class="center-info">
          <!-- 动态渲染信息项 -->
          <div class="info-item" v-for="(infoItem, index) in displayInfoItems" :key="index">
            <i :class="infoItem.icon || getDefaultIcon(infoItem.label)"></i>
            <span>{{ infoItem.label }}：{{ infoItem.value }}</span>
          </div>
          <!-- 额外信息插槽 -->
          <slot name="extra-info"></slot>
        </div>
      </div>
    </div>
    <!-- 中间层 -->
    <div>
      <slot name="middle-layer">
      </slot>
    </div>
    <div class="card-actions" v-if="actions || $slots.actions">
      <!-- 默认按钮 -->
      <slot name="actions">
        <template>
          <el-button plain type="danger" icon="el-icon-delete" @click="handleAction('delete')">删除</el-button>
          <el-button plain type="primary" icon="el-icon-edit" @click="handleAction('edit')">编辑</el-button>
          <el-button type="primary" @click="handleAction('detail')">查看详情</el-button>
        </template>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "LabCenterCard",
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    imageSrc: {
      type: String,
      default: "",
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    showIcon2: {
      type: Boolean,
      default: false,
    },
    // 新增 infoItems 属性
    infoItems: {
      type: Array,
      default: () => [],
    },
    // 控制是否使用自定义信息项
    useCustomInfoItems: {
      type: Boolean,
      default: false,
    },
    // 控制最大显示的信息项数量
    maxInfoItems: {
      type: Number,
      default: 3, // 默认显示3个
    },
    viewDeptName: {
      type: Boolean,
      default: true,
    },
    actions: {
      type: Boolean,
      default: true,
    },
    ellipsis: {
      type: Number,
      default: 0,
    }
  },
  computed: {
    ellipsisNum () {
      return this.ellipsis || this.viewDeptName || this.showIcon2 ? 1 : 2
    },
    // 计算最终显示的信息项
    displayInfoItems() {
      let items = [];

      // 如果提供了自定义信息项并且useCustomInfoItems为true，则使用它们
      if (this.infoItems.length > 0 && this.useCustomInfoItems) {
        items = this.infoItems;
      } else {
        // 否则使用默认信息项
        // 似乎不需要默认的
        items = [
          // {
          //   label: "负责人",
          //   value: this.item.responsiblePerson || "-",
          //   icon: "iconfont icon-gerenzhongxin_0",
          // },
          // {
          //   label: "联系方式",
          //   value: this.item.contactInfo || "-",
          //   icon: "iconfont icon-call",
          // },
          // {
          //   label: "地址",
          //   value: this.item.roomKindAddress || "-",
          //   icon: "iconfont icon-a-bianzu5",
          // },
        ];
      }
      // 如果设置了最大显示数量限制，则截取
      if (this.maxInfoItems > 0 && items.length > this.maxInfoItems) {
        return items.slice(0, this.maxInfoItems);
      }
      return items;
    },
  },
  methods: {
    handleAction(action) {
      this.$emit("action", action, this.item);
    },
    // 根据标签名获取默认图标
    getDefaultIcon(label) {
      const iconMap = {
        "负责人": "iconfont icon-gerenzhongxin_0",
        "联系方式": "iconfont icon-call",
        "地址": "iconfont icon-a-bianzu5",
        "所属部门": "el-icon-office-building",
        "中心类型": "el-icon-s-grid",
        "中心级别": "el-icon-medal",
        "安全负责人": "el-icon-warning",
        "创建时间": "el-icon-date",
      };

      return iconMap[label] || "el-icon-info";
    },
  },
};
</script>

<style lang="scss" scoped>
.center-card {
  width: calc(33.333% - 20px);
  margin: 10px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  position: relative;

  .card-icon {
    width: 50px;
    height: 37px;
    position: absolute;
    right: 0;
    top: 0;
    transform: scaleX(-1);
    .text {
      width: 100%;
      transform: scaleX(-1);
      color: #ffffff;
      text-align: center;
      margin-left: -4px;
      font-size: 14px;
      line-height: 1.6;
    }
    .iconfont {
      font-size: 32px;
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  @media screen and (max-width: 1650px) {
    width: calc(50% - 20px);
  }

  @media screen and (max-width: 1200px) {
    width: calc(100% - 20px);
  }

  .card-main {
    display: flex;
    flex-direction: row;
    min-height: 160px; // 调整与InfoDetailHeader更一致
    overflow: hidden;

    @media screen and (max-width: 768px) {
      flex-direction: column;
      height: auto;
    }
  }

  .card-image {
    border-radius: 12px;
    position: relative;
    width: 160px;
    height: 160px;
    flex-shrink: 0;
    overflow: hidden;
    background-color: #f5f7fa;
    margin: 18px 0 18px 22px;

    @media screen and (max-width: 768px) {
      width: 160px;
      height: 160px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }

    .image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;

      i {
        font-size: 48px;
        color: #c0c4cc;
      }
    }
  }

  .card-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .center-name {
      font-size: 18px; // 与InfoDetailHeader保持一致
      font-weight: bold;
      margin-bottom: 5px;
      color: #333333; // 与InfoDetailHeader保持一致
    }
    .ellipsis2 {
      height: 40px;
    }
    .center-dept {
      display: inline-block;
      width: fit-content;
      min-width: auto;
      max-width: 160px;
      height: 23px;
      line-height: 23px;
      text-align: center;
      background: #ecf2fe;
      border-radius: 3px;
      font-size: 14px;
      color: #0052d9;
      margin: 10px 0 15px;
      padding: 0 6px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      box-sizing: border-box;
    }
    .center-dept2 {
      height: 20px;
    }
    .center-info {
      flex: 1;
      overflow: hidden;

      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 14px; // 与InfoDetailHeader保持一致
        color: #333333; // 与InfoDetailHeader保持一致
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }

        i {
          margin-right: 8px;
          font-size: 14px;
          flex-shrink: 0;
          margin-top: 2px;
        }

        span {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }
      }
    }
  }

  .card-actions {
    display: flex;
    justify-content: flex-end;
    padding: 10px 16px;
    margin-top: auto;
    position: relative;
    border-top: 1px solid rgba(220, 220, 220, 0.24); // 替代伪元素的边框

    .el-button {
      margin: 0 10px;
      font-size: 12px;
      padding: 6px 8px;

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
