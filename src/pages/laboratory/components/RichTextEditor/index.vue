<template>
  <div class="rich-text-editor-container">
    <!-- 富文本编辑器 -->
    <div style="border: 1px solid #ccc;">
      <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
      <Editor
        style="height: 300px; overflow-y: hidden;"
        v-model="content"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="onEditorCreated"
      />
    </div>
  </div>
</template>

<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { uploadimg, uploadvideo, uploadFile } from "@laboratory/api/upload";

export default {
  name: "RichTextEditor",
  components: {
    Editor,
    Toolbar,
  },
  props: {
    // 编辑器内容值
    value: {
      type: String,
      default: "",
    },
    // 编辑器高度
    height: {
      type: Number,
      default: 300,
    },
    // 编辑器占位符文本
    placeholder: {
      type: String,
      default: "请输入内容...",
    },
    // 自定义工具栏配置
    customToolbarConfig: {
      type: Object,
      default: () => ({}),
    },
    // 自定义编辑器配置
    customEditorConfig: {
      type: Object,
      default: () => ({}),
    },
    // 图片上传大小限制 (MB)
    imageSizeLimit: {
      type: Number,
      default: 10,
    },
    // 视频上传大小限制 (MB)
    videoSizeLimit: {
      type: Number,
      default: 50,
    },
    // 附件上传大小限制 (MB)
    attachmentSizeLimit: {
      type: Number,
      default: 50,
    },
  },
  data() {
    return {
      editor: null,
      content: this.value,
      mode: "default",
      // 编辑器URL前缀
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      // 工具栏配置
      toolbarConfig: {
        // toolbarKeys: ["bold", "underline", "italic", "color", "bgColor", "uploadImage", "uploadVideo", "emotion"],
        // 插入附件按钮
        insertKeys: {
          index: 23,
          keys: ["uploadAttachment"],
        },
      },
      // 编辑器配置
      editorConfig: {
        placeholder: this.placeholder,
        MENU_CONF: {
          // 上传图片配置
          uploadImage: {
            customUpload: this.customUploadImage,
          },
          // 上传视频配置
          uploadVideo: {
            customUpload: this.customUploadVideo,
          },
          // 上传附件配置
          uploadAttachment: {
            customUpload: this.customUploadAttachment,
          },
        },
      },
    };
  },
  watch: {
    // 监听外部传入的值变化
    value(newVal) {
      if (this.content !== newVal) {
        this.content = newVal;
      }
    },
    // 监听内部值变化，触发v-model更新
    content(newVal) {
      this.$emit("input", newVal);
    },
    // 监听props中高度变化
    height(newVal) {
      if (this.editor) {
        const editorElem = document.querySelector(".w-e-text-container");
        if (editorElem) {
          editorElem.style.height = `${newVal}px`;
        }
      }
    },
    // 监听自定义工具栏配置
    customToolbarConfig: {
      handler(newVal) {
        this.mergeToolbarConfig(newVal);
      },
      deep: true,
    },
    // 监听自定义编辑器配置
    customEditorConfig: {
      handler(newVal) {
        this.mergeEditorConfig(newVal);
      },
      deep: true,
    },
  },
  methods: {
    // 编辑器创建完成事件
    onEditorCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal()，否则会报错
      this.$emit("editor-created", this.editor);

      // 设置编辑器高度
      if (this.height !== 300) {
        const editorElem = document.querySelector(".w-e-text-container");
        if (editorElem) {
          editorElem.style.height = `${this.height}px`;
        }
      }
    },

    // 自定义上传图片
    customUploadImage(file, insertFn) {
      // 检查文件大小是否超限
      const isLtMax = file.size / 1024 / 1024 < this.imageSizeLimit;
      if (!isLtMax) {
        this.$message.error(`上传图片大小不能超过 ${this.imageSizeLimit}MB!`);
        return Promise.reject(`上传图片大小不能超过 ${this.imageSizeLimit}MB!`);
      }

      const formData = new FormData();
      formData.append("file", file);

      return uploadimg(formData).then((res) => {
        if (res.code === 0) {
          const url = this.location + res.data;
          insertFn(url, file.name, url);
          this.$emit("upload-success", {
            type: "image",
            file,
            url,
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 自定义上传视频
    customUploadVideo(file, insertFn) {
      // 检查文件大小是否超限
      const isLtMax = file.size / 1024 / 1024 < this.videoSizeLimit;
      if (!isLtMax) {
        this.$message.error(`上传视频大小不能超过 ${this.videoSizeLimit}MB!`);
        return Promise.reject(`上传视频大小不能超过 ${this.videoSizeLimit}MB!`);
      }

      const formData = new FormData();
      formData.append("file", file);

      return uploadvideo(formData).then((res) => {
        if (res.code === 0) {
          const url = this.location + res.data;
          insertFn(url, file.name);
          this.$emit("upload-success", {
            type: "video",
            file,
            url,
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 自定义上传附件
    customUploadAttachment(file, insertFn) {
      // 检查文件大小是否超限
      const isLtMax = file.size / 1024 / 1024 < this.attachmentSizeLimit;
      if (!isLtMax) {
        this.$message.error(`上传附件大小不能超过 ${this.attachmentSizeLimit}MB!`);
        return Promise.reject(`上传附件大小不能超过 ${this.attachmentSizeLimit}MB!`);
      }

      const formData = new FormData();
      formData.append("file", file);

      return uploadFile(formData).then((res) => {
        if (res.code === 0) {
          const url = this.location + res.data;
          insertFn(file.name, url);
          this.$emit("upload-success", {
            type: "attachment",
            file,
            url,
          });
        } else {
          this.$message.error(res.message);
        }
      });
    },

    // 合并工具栏配置
    mergeToolbarConfig(customConfig) {
      this.toolbarConfig = {
        ...this.toolbarConfig,
        ...customConfig,
      };
    },

    // 合并编辑器配置
    mergeEditorConfig(customConfig) {
      // 深度合并配置
      this.editorConfig = this.deepMerge(this.editorConfig, customConfig);
    },

    // 深度合并对象
    deepMerge(target, source) {
      const result = { ...target };

      for (const key in source) {
        if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
          result[key] = this.deepMerge(target[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }

      return result;
    },

    // 获取编辑器实例
    getEditor() {
      return this.editor;
    },

    // 清空编辑器内容
    clear() {
      if (this.editor) {
        this.editor.clear();
        this.content = "";
      }
    },

    // 设置编辑器内容
    setContent(html) {
      this.content = html;
    },
  },
  beforeDestroy() {
    // 组件销毁时，销毁编辑器
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.rich-text-editor-container {
  width: 100%;
}
</style>

<style></style>
