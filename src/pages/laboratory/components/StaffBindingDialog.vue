<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="811px" :close-on-click-modal="false" @closed="handleClosed">
    <el-form ref="staffForm" :model="staffForm" :rules="rules" label-width="80px" >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学工号" prop="logonName">
            <el-select
              v-model="staffForm.logonName"
              filterable
              remote
              :remote-method="handleLogonNameSearch"
              :loading="searchLoading"
              placeholder="请输入学工号或姓名搜索"
              clearable
              style="width: 100%"
              @change="handleLogonNameChange"
            >
              <el-option v-for="item in logonNameOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="trueName">
            <el-input v-model="staffForm.trueName" placeholder="自动带出" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="staffForm.sex" disabled>
              <el-radio :label="1">男</el-radio>
              <el-radio :label="2">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生年月" prop="birthday">
            <el-date-picker
              v-model="staffForm.birthday"
              type="date"
              placeholder="自动带出"
              style="width: 100%"
              value-format="yyyy-MM-dd"
              disabled
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单位" prop="deptName">
            <el-input v-model="staffForm.deptName" placeholder="自动带出" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属学科" prop="personnelAcademic">
            <dict-cascader v-model="staffForm.personnelAcademic" :code-type="1003" :loading="dictLoading"></dict-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专业技术职务" prop="technicalPosition">
            <dict-cascader v-model="staffForm.technicalPosition" :code-type="1010" :loading="dictLoading"></dict-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文化程度" prop="educationalLevel">
            <dict-cascader v-model="staffForm.educationalLevel" :code-type="1009" :loading="dictLoading"></dict-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专家类别" prop="expertCategory">
            <dict-cascader v-model="staffForm.expertCategory" :code-type="1016" :loading="dictLoading"></dict-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学位" prop="degree">
            <dict-cascader v-model="staffForm.degree" :code-type="1028" :loading="dictLoading"></dict-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职称" prop="position">
            <el-input v-model="staffForm.position" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="phone">
            <el-input v-model="staffForm.phone" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="staffForm.email" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上传照片">
            <file-uploader
              v-model="staffForm.personPhoto"
              type="image"
              :maxSize="2"
              tip="支持JPG、PNG格式，不超过2MB"
              class="photo-uploader"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="人员类型" prop="personnelType">
            <el-select v-model="staffForm.personnelType" placeholder="请选择" clearable style="width: 100%">
              <el-option label="专任" :value="1"></el-option>
              <el-option label="兼任" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="系统角色" prop="sysRole">
            <el-select v-model="staffForm.sysRole" placeholder="请选择" clearable style="width: 100%">
              <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" :loading="submitLoading">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading">确认绑定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  labCenterPersonBindSave,
  labCenterPersonRoleDict,
  labPersonInfoList,
  getDictAll,
  labCenterPersonBindUpdate,
} from "@laboratory/api/laboratory/basicMessage";
import { labPersonBindSave, labPersonBindUpdate, labPersonRoleDict } from "@laboratory/api/laboratory/laboratory";
import FileUploader from "@laboratory/components/FileUploader/index.vue";
import DictCascader from "./DictCascader.vue";

export default {
  name: "StaffBindingDialog",
  components: {
    FileUploader,
    DictCascader,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    centerUuid: {
      type: String,
      default: "",
    },
    labUuid: {
      type: String,
      default: "",
    },
    currentUuid: {
      type: String,
      default: "",
    },
    resourceType: {
      type: Number,
      default: 1, // 1: 实验中心, 2: 实验室
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      staffForm: {
        accNo: null,
        logonName: "",
        trueName: "",
        sex: 1,
        birthday: "",
        deptId: null,
        deptName: "",
        personPhoto: "",
        personnelAcademic: "",
        technicalPosition: "",
        educationalLevel: "",
        expertCategory: "",
        phone: "",
        email: "",
        degree: "",
        position: "",
        sysRole: "",
        personnelType: null,
      },
      rules: {
        logonName: [{ required: true, message: "请选择学工号", trigger: "change" }],
        personnelType: [{ required: true, message: "请选择人员类型", trigger: "change" }],
        sysRole: [{ required: true, message: "请选择系统角色", trigger: "change" }],
        email: [{ pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: "请输入正确的邮箱格式", trigger: "blur" }],
        // phone: [{ pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号格式", trigger: "blur" }],
      },
      logonNameOptions: [],
      personInfoMap: {},
      roleOptions: [],
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      searchKey: "",
      searchLoading: false,
      dictLoading: false, // 字典数据加载状态
      submitLoading: false, // 表单提交加载状态
      first: true,
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? "编辑人员" : "绑定人员";
    },
    // 动态API映射
    personApi() {
      return {
        1: {
          // 实验中心
          roleDict: labCenterPersonRoleDict,
          saveApi: labCenterPersonBindSave,
          updateApi: labCenterPersonBindUpdate,
          uuidField: "labCenterUuid",
        },
        2: {
          // 实验室
          roleDict: labPersonRoleDict,
          saveApi: labPersonBindSave,
          updateApi: labPersonBindUpdate,
          uuidField: "labUuid",
        },
      }[this.resourceType];
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      this.fetchPersonInfoList();
      if (val) {
        this.$nextTick(() => {
          this.$refs.staffForm && this.$refs.staffForm.clearValidate();
          if (this.isEdit && this.editData) {
            this.initEditForm();
          }
        });
      }
    },
  },
  created() {
    this.fetchRoleOptions();
  },
  methods: {
    handleLogonNameChange() {
      if (this.personInfoMap[this.staffForm.logonName]) {
        const personInfo = this.personInfoMap[this.staffForm.logonName];
        this.staffForm = {
          ...this.staffForm,
          accNo: personInfo.accNo,
          trueName: personInfo.trueName,
          sex: personInfo.sex,
          birthday: personInfo.birthday,
          deptId: personInfo.deptId,
          deptName: personInfo.deptName,
          personPhoto: personInfo.personPhoto || "",
          personnelAcademic: personInfo.personnelAcademic || "",
          technicalPosition: personInfo.technicalPosition || "",
          educationalLevel: personInfo.educationalLevel || "",
          expertCategory: personInfo.expertCategory || "",
          phone: personInfo.phone || "",
          email: personInfo.email || "",
          degree: personInfo.degree || "",
        };
      }
    },
    fetchPersonInfoList(key = "") {
      this.searchLoading = true;
      const params = {
        key: key,
        num: 9999,
      };

      labPersonInfoList(params)
        .then((res) => {
          if (res.code === 0) {
            this.logonNameOptions = [];
            this.personInfoMap = {};

            if (res.data && res.data.length > 0) {
              res.data.forEach((person) => {
                this.logonNameOptions.push({
                  label: `${person.logonName} - ${person.trueName}`,
                  value: person.logonName,
                });

                this.personInfoMap[person.logonName] = person;
              });
            }
          }
        })
        .finally(() => {
          this.searchLoading = false;
        });
    },

    handleLogonNameSearch(query) {
      if (query) {
        this.searchKey = query;
        this.fetchPersonInfoList(query);
      } else {
        this.searchKey = "";
        this.fetchPersonInfoList();
      }
    },

    fetchRoleOptions() {
      this.personApi.roleDict().then((res) => {
        if (res.code === 0) {
          this.roleOptions = res.data;
          this.$emit("roleOptions", res.data);
        }
      });
    },

    initEditForm() {
      if (this.editData.labPersonInfo) {
        const { labPersonInfo, sysRole, personnelType, position } = this.editData;
        this.staffForm = {
          accNo: labPersonInfo.accNo || null,
          logonName: labPersonInfo.logonName || "",
          trueName: labPersonInfo.trueName || "",
          sex: labPersonInfo.sex || 1,
          birthday: labPersonInfo.birthday || "",
          deptId: labPersonInfo.deptId || null,
          deptName: labPersonInfo.deptName || "",
          personPhoto: labPersonInfo.personPhoto || "",
          personnelAcademic: labPersonInfo.personnelAcademic || "",
          technicalPosition: labPersonInfo.technicalPosition || "",
          educationalLevel: labPersonInfo.educationalLevel || "",
          expertCategory: labPersonInfo.expertCategory || "",
          phone: labPersonInfo.phone || "",
          email: labPersonInfo.email || "",
          degree: labPersonInfo.degree || "",
          sysRole: sysRole || "",
          personnelType: personnelType || null,
          position: position || "",
        };

        this.$forceUpdate();
      }
    },

    submitForm() {
      this.submitLoading = true;
      this.$refs.staffForm.validate((valid) => {
        if (valid) {
          const personInfo = {
            accNo: this.staffForm.accNo,
            personPhoto: this.staffForm.personPhoto,
            personnelAcademic: this.staffForm.personnelAcademic,
            technicalPosition: this.staffForm.technicalPosition,
            educationalLevel: this.staffForm.educationalLevel,
            expertCategory: this.staffForm.expertCategory,
            degree: this.staffForm.degree,
            phone: this.staffForm.phone,
            email: this.staffForm.email,
          };

          const params = {
            personInfo,
            sysRole: this.staffForm.sysRole,
            personnelType: this.staffForm.personnelType,
            position: this.staffForm.position,
          };

          // 根据资源类型设置不同的UUID字段
          params[this.personApi.uuidField] = this.currentUuid;
          // 根据是否是编辑模式和资源类型选择不同的接口
          if (this.isEdit && this.editData) {
            // 编辑模式
            const updateParams = {
              ...params,
              uuid: this.editData.uuid,
            };
            this.personApi
              .updateApi(updateParams)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success("编辑成功");
                  this.dialogVisible = false;
                  this.$emit("success");
                }
              })
              .finally(() => {
                this.submitLoading = false;
              });
          } else {
            // 新增模式
            this.personApi
              .saveApi(params)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success("绑定成功");
                  this.dialogVisible = false;
                  this.$emit("success");
                }
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        } else {
          this.submitLoading = false;
          return false;
        }
      });
    },

    handleClosed() {
      this.$emit("update:visible", false);
      this.$emit("closed");
      this.resetForm();
    },

    resetForm() {
      this.staffForm = {
        accNo: null,
        logonName: "",
        trueName: "",
        sex: 1,
        birthday: "",
        deptId: null,
        deptName: "",
        personPhoto: "",
        personnelAcademic: "",
        technicalPosition: "",
        educationalLevel: "",
        expertCategory: "",
        phone: "",
        email: "",
        degree: "",
        position: "",
        sysRole: "",
        personnelType: null,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  padding: 10px 20px 20px;
}

::v-deep .el-button--small {
  padding: 8px 15px;
  font-size: 12px;
}
</style>
