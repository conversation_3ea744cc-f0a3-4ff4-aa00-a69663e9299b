<template>
  <div class="dynamic-table">
    <el-table
      ref="table"
      :data="tableData"
      border
      stripe
      v-loading="loading"
      :style="{ width: screenWidth ? `${screenWidth}px` : 'auto' }"
      :max-height="400"
      @sort-change="handleSortChange"
      @select="handleSelectionChange"
      @select-all="handleSelectionChange"
    >
      <!-- :row-class-name="tableRowClassName" -->
      <!-- @selection-change="handleSelectionChange" -->
      <!-- 动态生成列 -->
      <el-table-column
        v-for="(column, index) in columns"
        :key="index"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :minWidth="column.minWidth"
        :sortable="column.sortable || false"
        :formatter="column.formatter"
        :fixed="column.fixed || false"
        :align="column.align || 'center'"
        :type="column.type"
        :selectable="column.type === 'selection' ? selectable : undefined"
      >
        <!-- 自定义列内容 -->
        <template v-if="column.slotName" v-slot="{ row }">
          <slot :name="column.slotName" :row="row"></slot>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="showPagination"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.currentPage"
      :page-sizes="pagination.pageSizes"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: "TablePagetion",
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => [],
    },
    // 列配置
    columns: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every((item) => {
          return item.prop;
        });
      },
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => ({
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 20, 50, 100],
        total: 0,
      }),
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // 行选择函数，用于控制哪些行可以被选择
    selectable: {
      type: Function,
      default: null,
    },
  },
  methods: {
    // tableRowClassName({ row, rowIndex }) {
    //   this.$emit("tableRowClassName", { row, rowIndex }, (className) => {
    //     return className;
    //   });
    //   return "gray-row";
    // },
    handleSizeChange(val) {
      this.$emit("page-size-change", val);
    },
    handleCurrentChange(val) {
      this.$emit("current-page-change", val);
    },
    handleSortChange({ column, prop, order }) {
      this.$emit("sort-change", { column, prop, order });
    },
    handleSelectionChange(selection) {
      this.$emit("handleSelectionChange", selection);
    },
    // 清空选择
    clearSelection() {
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
    },
    // 切换行选择状态
    toggleRowSelection(row, selected) {
      if (this.$refs.table) {
        this.$refs.table.toggleRowSelection(row, selected);
      }
    },
  },
};
</script>

<style scoped>
.dynamic-table {
  margin-top: 20px;
}
.el-pagination {
  margin-top: 15px;
  text-align: right;
}
</style>
