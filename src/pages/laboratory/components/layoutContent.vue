<template>
  <div class="layout-content">
    <!-- 搜索区域 -->
    <div class="search-section" v-if="$slots.search">
      <slot name="search"></slot>
    </div>
    <!-- 中心区域 -->
    <div class="center-list">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "layoutContent",
  created() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.layout-content {
  width: 100%;
  background-color: #ffffff;
  .search-section {
    min-height: 75px;
    background-color: #fff;
    padding: 20px 10px;
    border-radius: 3px;
    border-bottom: 1px solid #dcdcdc;
  }
  .center-list {
    padding: 20px 16px;
  }
}
</style>
