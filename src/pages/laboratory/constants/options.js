/**
 * 实验中心类型选项
 */
export const CENTER_TYPE_OPTIONS = [
  { value: 1, label: "实体" },
  { value: 2, label: "虚拟" }
];

/**
 * 实验中心级别选项
 */
export const CENTER_LEVEL_OPTIONS = [
  { value: 1, label: "校级" },
  { value: 2, label: "院级" }
];

/**
 * 规章制度类型选项
 */
export const REGULATION_TYPE_OPTIONS = [
  { value: 1, label: "校级文件" },
  { value: 2, label: "上级文件" }
];

/**
 * 根据值获取标签
 * @param {Array} options 选项数组
 * @param {String|Number} value 值
 * @returns {String} 对应的标签
 */
export function getLabelByValue (options, value) {
  const option = options.find(item => item.value == value);
  return option ? option.label : "-";
}