<template>
  <section class="app-main">
    <sidebar class="sidebar-container" />
    <div class="main-container">
      <transition name="fade-transform" mode="out-in">
        <router-view :key="key" />
      </transition>
    </div>
  </section>
</template>

<script>
import Sidebar from "./Sidebar";
export default {
  name: "AppMain",
  components: {
    Sidebar,
  },
  computed: {
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style scoped>
.app-main {
  min-height: calc(100vh - 300px);
  box-sizing: border-box;
  width: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
}
.main-container {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 2px 0px 0px rgba(255, 255, 255, 0.36);
  border-radius: 3px;
  margin: 0px 0 0 7px;
  height: calc(100vh - 265px);
  overflow-y: auto;
}
</style>

<style lang="scss"></style>
