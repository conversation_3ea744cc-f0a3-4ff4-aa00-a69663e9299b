<template>
  <div class="fixed-header">
    <div class="navbar">
      <div class="left-menu">
        <LogoWithName
          :logo="require('@laboratory/assets/image/logo.png')"
          :name="'实验室与设备管理处'"
          :enName="'Laboratory and Equipment Management Office'"
        />
      </div>
      <div class="right-menu">
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <div class="user-avatar">{{ trueName.substr(0, 1) }}</div>
            <div class="name">{{ logonName }}</div>
            <div class="line"></div>
            <div class="name">{{ trueName }}</div>
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <!-- <el-dropdown-item @click.native="backsite">前往预约端</el-dropdown-item> -->
            <!-- divided -->
            <el-dropdown-item @click.native="logout"><span style="display: block">退出登录</span></el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
          <div class="avatar-wrapper">
            <div class="line"></div>
            <i style="color: #ffffff;font-size: 24px;margin-right: 3px;" class="iconfont icon-shezhi" />
            <div class="name">系统设置</div>
          </div>
      </div>
    </div>
  </div>
</template>

<script>
import LogoWithName from "@laboratory/components/LogoWithName";
import { mapGetters } from "vuex";
import Hamburger from "@laboratory/components/Hamburger";
import store from "@laboratory/store";
import { logout } from "@laboratory/api/login";

export default {
  data() {
    return {
      trueName: store.getters.name,
      logonName: store.getters.logonName,
      activeName: "",
      tabRoutes: [],
      AppList: [],
    };
  },
  components: {
    LogoWithName,
    Hamburger,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar"]),
  },
  methods: {
    //统一认证退出登录
    logout() {
      let json = {
        callbackPage: window.location.protocol + "//" + window.location.host + window.g.thirdurl + "/laboratory.html",
      };
      logout(json).then((res) => {
        if (res.code == 0) {
          if (res.data.isThirdLogout == 1) {
            window.location.href = res.data.thirdLogoutUrl;
          } else {
            this.$store
              .dispatch("user/logout")
              .then(() => {
                this.$router.go(0);
                // this.$router.replace(`/login?redirect=${this.$route.fullPath}`);
              })
              .catch(() => {
                this.$router.go(0);
                // this.$router.replace(`/login?redirect=${this.$route.fullPath}`);
              });
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    //返回预约端
    backsite() {
      let a = window.location.href;
      if (a.indexOf("back") != -1) {
        window.location.href = "labplatform.html";
      } else {
        window.location.href = "index.html";
      }
    },
  },
  created () {
  },
};
</script>
<style lang="less" scoped>
.navbar {
  height: 68px;
  overflow: hidden;
  position: relative;
  background: #0052d9;
  box-shadow: 0px 0px 9px 0px rgba(64, 64, 64, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 80px;

  .left-menu {
    display: flex;
    align-items: center;
    height: 100%;
    flex: 1;
  }

  .right-menu {
    display: flex;
    align-items: center;
    height: 100%;

    .avatar-container {
      cursor: pointer;
      height: 100%;

    }
    .avatar-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      height: 100%;

      .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.8);
        color: #0066cc;
        font-weight: bold;
        font-size: 18px;
        margin-right: 10px;
      }
      .line {
        width: 2px;
        height: 30px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 1px;
        margin: 0 16px;
      }
      .name {
        color: white;
        font-size: 14px;
        font-weight: 500;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        font-size: 18px;
        color: #ffffff;
      }
    }
  }
}

/deep/ .user-dropdown {
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .el-dropdown-menu__item {
    font-size: 14px;
    padding: 12px 20px;

    &:hover {
      background-color: #f5f5f7;
    }
  }
}
</style>
