<template>
  <div class="lab-navigation">
    <!-- 左箭头 -->
    <div class="nav-arrow prev" :class="{ disabled: !canGoPrev }" @click="goToPrevPage">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" />
      </svg>
    </div>
    <!-- 主导航栏 -->
    <div class="main-nav">
      <!-- 导航容器 -->
      <div class="nav-container" ref="navContainer">
        <div class="nav-wrapper" :style="{ transform: `translateX(-${currentPage * containerWidth}px)` }">
          <!-- 每一页作为一个完整的容器 -->
          <div v-for="(page, pageIndex) in paginatedSections" :key="pageIndex" class="nav-page"
            :style="{ width: containerWidth + 'px' }">
            <div v-for="(section, sectionIndex) in page" :key="section.id || sectionIndex" class="nav-section"
              :class="{ 'section-expanded': isExpanded[getGlobalSectionIndex(pageIndex, sectionIndex)] }">
              <h3 class="section-title">{{ section.title }}</h3>
              <div class="nav-items" :class="{ 'has-overflow': section.items.length > 3 }"
                @mouseenter="handleMouseEnter(getGlobalSectionIndex(pageIndex, sectionIndex))"
                @mouseleave="handleMouseLeave(getGlobalSectionIndex(pageIndex, sectionIndex))"
                >
                <!-- 基础显示的前3个项目 -->
                <div v-for="(item, itemIndex) in section.items.slice(0, 3)" :key="item.id" class="nav-item"
                  @click="handleNavClick(item)">
                  <div class="nav-icon">
                    <svg class="iconfont" aria-hidden="true">
                      <use :xlink:href="`#${item.iconClass}`"></use>
                    </svg>
                  </div>
                  <span class="nav-text" :class="activeSystem === item.system ? 'active' : ''">{{ item.text }}</span>
                </div>

                <!-- 展开指示器 -->
                <div v-if="section.items.length > 3" class="expand-indicator">
                  <i class="expand-icon iconfont icon-xiala"
                    :class="{ 'expanded': isExpanded[getGlobalSectionIndex(pageIndex, sectionIndex)] }"></i>

                  <!-- <svg class="expand-icon"
                    :class="{ 'expanded': isExpanded[getGlobalSectionIndex(pageIndex, sectionIndex)] }">
                    <use xlink:href="#icon-xiala"></use>
                  </svg> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右箭头 -->
    <div class="nav-arrow next" :class="{ disabled: !canGoNext }" @click="goToNextPage">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
      </svg>
    </div>

    <!-- 悬浮展开层 - 保持与原始nav-section一致的样式 -->
    <div v-if="expandedSection !== null" class="nav-expanded-overlay" @mouseleave="handleOverlayMouseLeave"
    >
      <div class="nav-expanded-content" :style="expandedContentStyle" @mouseenter="handleExpandedContentMouseEnter">
        <div class="expanded-nav-section">
          <h3 class="section-title" style="opacity: 0;">{{ expandedSection.title }}</h3>
          <div class="nav-items expanded-nav-items">
            <div v-for="item in expandedSection.items" :key="item.id" class="nav-item" @click="handleNavClick(item)">
              <div class="nav-icon">
                <svg class="iconfont" aria-hidden="true">
                  <use :xlink:href="`#${item.iconClass}`"></use>
                </svg>
              </div>
              <span class="nav-text" :class="activeSystem === item.system ? 'active' : ''">{{ item.text }}</span>
            </div>
            <!-- 收起指示器 -->
            <div class="expand-indicator">
              <i class="expand-icon expanded iconfont icon-xiala"></i>
              <!-- <svg class="expand-icon expanded">
                <use xlink:href="#icon-xiala"></use>
              </svg> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { asyncRoutes } from "@laboratory/router";
export default {
  name: "LabNavigation",
  data() {
    return {
      activeSystem: sessionStorage.getItem("currentSystem") || "basicInfoMange",
      hoveredSection: null,
      isExpanded: {}, // 记录每个section的展开状态
      expandedSection: null, // 当前展开的section数据
      expandedSectionIndex: null, // 当前展开的section索引
      expandedContentStyle: {}, // 展开内容的位置样式
      mouseLeaveTimer: null, // 鼠标离开延迟定时器
      currentPage: 0, // 当前页码
      screenWidth: window.innerWidth, // 屏幕宽度
      containerWidth: 0, // 容器宽度
      navSections: [
        {
          id: "lab",
          title: "实验室",
          items: [
            {
              id: "basic-info",
              text: "基本信息管理",
              iconClass: "icon-jichuxinxi",
              system: "basicInfoMange",
            },
            {
              id: "project",
              text: "项目管理",
              iconClass: "icon-xiangmuguanli",
            },
            {
              id: "performance",
              text: "绩效管理",
              iconClass: "icon-jixiaoguanli",
            },
            {
              id: "tech-achievement",
              text: "技术与成果",
              iconClass: "icon-jishuyuchengguo",
            },
          ],
        },
        {
          id: "equipment",
          title: "仪器设备",
          items: [
            {
              id: "equipment-management",
              text: "设备管理",
              iconClass: "icon-shebeiguanli",
              system: "equipmentBasicInfo",
            },
            {
              id: "equipment-purchase",
              text: "设备采购",
              iconClass: "icon-shebeigongxiang",
              system: "equipmentPurchase"
            },
            {
              id: "equipment-sharing",
              text: "设备共享",
              iconClass: "icon-shebeigongxiang",
            },
          ],
        },
        {
          id: "safety",
          title: "实验安全",
          items: [
            {
              id: "safety-info",
              text: "安全信息",
              iconClass: "icon-anquanxinxi",
            },
            {
              id: "safety-access",
              text: "安全准入",
              iconClass: "icon-anquanzhunru",
            },
            {
              id: "safety-check",
              text: "安全检查",
              iconClass: "icon-anquanjiancha",
            },
            {
              id: "classification",
              text: "分级分类",
              iconClass: "icon-fenjifenlei",
              system: "gradingClass",
            },
          ],
        },
        {
          id: "materials",
          title: "材料采购",
          items: [
            {
              id: "dangerous-goods",
              text: "危化品",
              iconClass: "icon-weihuapin",
            },
            {
              id: "chemicals",
              text: "普通试剂",
              iconClass: "icon-putongshiji",
            },
            {
              id: "consumables",
              text: "易耗品",
              iconClass: "icon-putongshiji",
            },
          ],
        },
        {
          id: "system",
          title: "系统应用",
          items: [
            {
              id: "audit-center",
              text: "审核中心",
              iconClass: "icon-shebeigongxiang",
            },
            {
              id: "ioTMiddleStation",
              text: "物联中台",
              iconClass: "icon-shujudaping",
            },
            {
              id: "dataStatistics",
              text: "数据统计",
              iconClass: "icon-shujutongji",
            },
            {
              id: "largeDataScreen",
              text: "数据大屏",
              iconClass: "icon-shujudaping",
            },
          ],
        },
      ],
    };
  },
  mounted() {
    // 根据当前路由设置activeSystem
    this.setActiveSystemByRoute();
    // 监听窗口大小变化
    window.addEventListener("resize", this.handleResize);
    // 初始化容器宽度
    this.$nextTick(() => {
      this.updateContainerWidth();
      // 延迟再次更新，确保DOM完全渲染
      setTimeout(() => {
        this.updateContainerWidth();
      }, 100);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    // 清理定时器
    if (this.mouseLeaveTimer) {
      clearTimeout(this.mouseLeaveTimer);
    }
  },
  computed: {
    // 根据屏幕宽度动态计算每页显示的section数量
    itemsPerPage() {
      if (this.screenWidth >= 1920) return 5;
      if (this.screenWidth >= 1600) return 4;
      if (this.screenWidth >= 1200) return 3;
      return 2;
    },
    // 总页数
    totalPages() {
      return Math.ceil(this.navSections.length / this.itemsPerPage);
    },
    // 是否可以向前翻页
    canGoPrev() {
      return this.currentPage > 0;
    },
    // 是否可以向后翻页
    canGoNext() {
      return this.currentPage < this.totalPages - 1;
    },
    // 分页后的sections
    paginatedSections() {
      const pages = [];
      for (let i = 0; i < this.navSections.length; i += this.itemsPerPage) {
        pages.push(this.navSections.slice(i, i + this.itemsPerPage));
      }
      return pages;
    },
  },
  watch: {
    "$route" (value) {
      // 监听路由变化，自动设置activeSystem
      this.setActiveSystemByRoute();
    },
  },
  methods: {
    handleNavClick(item) {
      if (!item.system) return;
      this.activeSystem = item.system;
      // 根据system切换路由并跳转到对应system下的第一个路由
      this.$store.dispatch("permission/switchRoutesBySystem", item.system).then((filteredRoutes) => {
        // 获取该system下的第一个路由路径
        if (filteredRoutes && filteredRoutes.length > 0) {
          const firstRoute = filteredRoutes[0];
          let targetPath = firstRoute.path;

          // 如果有redirect，使用redirect路径
          if (firstRoute.redirect) {
            targetPath = firstRoute.redirect;
          } else if (firstRoute.children && firstRoute.children.length > 0) {
            // 如果有子路由，使用第一个子路由的完整路径
            const firstChild = firstRoute.children[0];
            targetPath = firstRoute.path + "/" + firstChild.path;
          }
          this.$router.push(targetPath);
        }
      });
    },

    setActiveSystemByRoute() {
      // 根据当前路由路径判断应该激活哪个system
      const currentPath = this.$route.path;
      this.activeSystem = currentPath.split("/").filter(Boolean)[0];
      sessionStorage.setItem("currentSystem", currentPath.split("/").filter(Boolean)[0]);
      this.$store.commit('permission/SET_ROUTES_BY_SYSTEM', { routes: asyncRoutes, system: this.activeSystem });
    },

    // 获取全局section索引
    getGlobalSectionIndex(pageIndex, sectionIndex) {
      let globalIndex = 0;
      for (let i = 0; i < pageIndex; i++) {
        globalIndex += this.paginatedSections[i].length;
      }
      return globalIndex + sectionIndex;
    },

    // 鼠标进入导航区域
    handleMouseEnter(globalSectionIndex) {
      // 清除之前的延迟隐藏定时器
      if (this.mouseLeaveTimer) {
        clearTimeout(this.mouseLeaveTimer);
        this.mouseLeaveTimer = null;
      }

      const section = this.navSections[globalSectionIndex];
      if (section && section.items.length > 3) {
        this.$set(this.isExpanded, globalSectionIndex, true);
        this.expandedSectionIndex = globalSectionIndex;
        this.expandedSection = section;

        // 计算展开内容的位置
        this.$nextTick(() => {
          this.calculateExpandedPosition(globalSectionIndex);
        });
      }
    },

    // 鼠标离开导航区域
    handleMouseLeave(globalSectionIndex) {
      // 延迟隐藏，给用户时间移动到展开的内容上
      this.mouseLeaveTimer = setTimeout(() => {
        this.$set(this.isExpanded, globalSectionIndex, false);
        if (this.expandedSectionIndex === globalSectionIndex) {
          this.expandedSection = null;
          this.expandedSectionIndex = null;
        }
      }, 150);
    },

    // 鼠标进入展开的内容区域
    handleExpandedContentMouseEnter() {
      // 清除延迟隐藏定时器
      if (this.mouseLeaveTimer) {
        clearTimeout(this.mouseLeaveTimer);
        this.mouseLeaveTimer = null;
      }
    },

    // 鼠标离开展开的覆盖层
    handleOverlayMouseLeave() {
      // 保存当前的expandedSectionIndex用于清理状态
      const currentExpandedIndex = this.expandedSectionIndex;
      this.expandedSection = null;
      this.expandedSectionIndex = null;
      // 清理展开状态
      if (currentExpandedIndex !== null) {
        this.$set(this.isExpanded, currentExpandedIndex, false);
      }
    },

    // 计算展开内容的位置
    calculateExpandedPosition(globalSectionIndex) {
      // 找到对应的nav-section元素
      const navSections = document.querySelectorAll(".nav-section");
      let targetSection = null;

      // 根据当前页面和section索引找到对应的DOM元素
      let currentIndex = 0;
      for (let pageIndex = 0; pageIndex < this.paginatedSections.length; pageIndex++) {
        for (let sectionIndex = 0; sectionIndex < this.paginatedSections[pageIndex].length; sectionIndex++) {
          if (currentIndex === globalSectionIndex) {
            // 计算在当前可见页面中的位置
            const visiblePageSections = document.querySelectorAll(`.nav-page:nth-child(${this.currentPage + 1}) .nav-section`);
            const sectionInPage = sectionIndex;
            if (visiblePageSections[sectionInPage]) {
              targetSection = visiblePageSections[sectionInPage];
            }
            break;
          }
          currentIndex++;
        }
        if (targetSection) break;
      }

      if (targetSection) {
        const rect = targetSection.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 计算展开内容的尺寸
        const itemsCount = this.expandedSection.items.length;
        const rows = Math.ceil(itemsCount / 3);
        const contentHeight = Math.max(200, rows * 90 + 80); // 动态高度
        const contentWidth = rect.width; // 使用原始nav-section的宽度

        // 计算最佳位置
        let left = rect.left;
        let top = rect.top;

        // 水平位置调整
        if (left + contentWidth > viewportWidth - 20) {
          left = viewportWidth - contentWidth - 20;
        }
        if (left < 20) {
          left = 20;
        }

        // 垂直位置调整
        if (top + contentHeight > viewportHeight - 20) {
          top = Math.max(20, viewportHeight - contentHeight - 20);
        }

        this.expandedContentStyle = {
          position: "fixed",
          left: `${left}px`,
          top: `${top + 10}px`,
          width: `${contentWidth}px`,
          minHeight: `${contentHeight - 80}px`,
          zIndex: 9999,
        };
      }
    },

    // 处理窗口大小变化
    handleResize() {
      this.screenWidth = window.innerWidth;
      // 延迟更新容器宽度，等待CSS响应式变化完成
      setTimeout(() => {
        this.updateContainerWidth();
        // 如果当前页超出了新的总页数，调整到最后一页
        if (this.currentPage >= this.totalPages) {
          this.currentPage = Math.max(0, this.totalPages - 1);
        }
      }, 50);
    },

    // 更新容器宽度
    updateContainerWidth() {
      if (this.$refs.navContainer) {
        const newWidth = this.$refs.navContainer.offsetWidth;
        if (newWidth > 0) {
          this.containerWidth = newWidth;
        }
      }
    },

    // 向前翻页
    goToPrevPage() {
      if (this.canGoPrev) {
        this.currentPage--;
      }
    },

    // 向后翻页
    goToNextPage() {
      if (this.canGoNext) {
        this.currentPage++;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-navigation {
  width: 100%;
  height: 146px;
  margin-bottom: 7px;
  position: relative;
}

.main-nav {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0px;
  align-items: stretch;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.nav-section {
  position: relative; // 重要：为绝对定位的nav-items提供定位上下文
  display: flex;
  flex-direction: column;
  padding: 10px 0;
  transition: all 0.3s ease;
  // flex: 1;
  min-width: 0; /* 防止flex项目收缩问题 */

  /* 响应式宽度调整 */
  @media (min-width: 1920px) {
    width: 20%; /* 5个section时每个占20% */
  }
  @media (min-width: 1600px) and (max-width: 1919px) {
    width: 25%; /* 4个section时每个占25% */
  }
  @media (min-width: 1200px) and (max-width: 1599px) {
    width: 33.333%; /* 3个section时每个占33.333% */
  }
  @media (max-width: 1199px) {
    width: 50%; /* 2个section时每个占50% */
  }
}

/* 导航容器 */
.nav-container {
  flex: 1;
  overflow: hidden;
  position: relative;

  /* 保持原有overflow设置，不做任何修改 */
}

.nav-wrapper {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  height: 100%;

  /* 保持原有设置 */
}

.nav-page {
  display: flex;
  flex-shrink: 0;
  align-items: stretch;
  height: 100%;
  gap: 12px;

  /* 保持原有设置 */
}

/* 走马灯箭头 */
.nav-arrow {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: absolute;
  top: 50%;
  z-index: 100; /* 降低z-index，确保在展开的nav-items之下 */
  background: #fff;
  color: #0052d9;

  &.prev {
    left: -40px;
  }

  &.next {
    right: -40px;
  }

  &:hover:not(.disabled) {
    background: #fff;
    color: #0052d9;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #3d7ef7;
    color: #ffffff;
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.5);
  }

  svg {
    transition: transform 0.2s ease;
  }

  &:hover:not(.disabled) svg {
    transform: scale(1.1);
  }
}

.section-title {
  height: 18px;
  font-size: 18px;
  font-weight: 400;
  color: #333333;
  margin: 0 0 8px 0;
  padding-bottom: 6px;
  white-space: nowrap;
}

.nav-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: min-content;
  gap: 10px;
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  padding: 10px;
  // box-shadow: 0px 0px 16px 0px rgba(102, 102, 102, 0.15);
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  height: 90px;
  overflow: hidden;
  width: 100%;
  flex: 1;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  min-width: 90px;
  width: 100%; /* 确保在grid中占满格子 */
  // height: 70px;
}

.nav-item:hover,
.active {
  color: #0052d9;
  transform: translateY(-2px);
}

.nav-icon {
  margin-bottom: 9px;
  border-radius: 5px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  .iconfont {
    width: 46px;
    height: 46px;
    font-size: 46px;
  }
}

.nav-item:hover .nav-icon {
  transform: scale(1.05);
}

.nav-text {
  font-size: 16px;
  text-align: center;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.nav-item:hover .nav-text {
  color: #0052d9;
}

/* 展开指示器样式 */
.expand-indicator {
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  // width: 14px;
  // height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: none;

  .expand-icon {
    width: 20px;
    height: 14px;
    font-size: 8px;
    color: #000000;
    transition: transform 0.3s ease;
    // transform: rotate(90deg); /* 向下箭头 */

    &.expanded {
      transform: rotate(-180deg); /* 向上箭头 */
    }
  }
}

.nav-expanded-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none; /* 允许点击穿透到下层元素 */
  z-index: 9999; /* 确保在所有元素之上 */
  overflow: visible; /* 确保内容不被裁剪 */
}

.nav-expanded-content {
  pointer-events: auto; /* 恢复展开内容的交互 */
  // animation: expandIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top left;
  max-width: 90vw; /* 防止在小屏幕上超出视口 */
  max-height: 80vh; /* 防止内容过高 */
  overflow-y: auto; /* 内容过多时允许滚动 */
}

/* 展开的nav-section样式 - 与原始保持一致 */
.expanded-nav-section {
  position: relative;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.expanded-nav-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: min-content;
  gap: 15px 20px;
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0px 0px 16px 0px rgba(102, 102, 102, 0.15);
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
  min-height: 120px; /* 动态高度以适应所有项目 */
  overflow: visible;
  width: 100%;
}

/* 展开动画 */
@keyframes expandIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 确保不换行 */
* {
  box-sizing: border-box;
}
</style>
