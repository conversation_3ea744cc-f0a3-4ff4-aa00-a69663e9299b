<template>
  <div class="app-wrapper">
    <NavBar />
    <div class="navbar-bottom-bg"></div>
    <div class="main-container">
      <NavMenu />
      <app-main />
    </div>
  </div>
</template>

<script>
import { NavBar, NavMenu, AppMain } from "./components";

export default {
  name: "Layout",
  data() {
    return {
      version: window.g.version,
      // AdminversionTitle:window.g.AdminversionTitle
    };
  },
  components: {
    NavBar,
    NavMenu,
    AppMain,
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
// @import "@laboratory/styles/mixin.scss";
// @import "@laboratory/styles/variables.scss";

.app-wrapper {
  // @include clearfix;
  position: relative;
  height: auto;
  width: 100%;
  background: #f8f8f8;
  // background-color: red;
}
.navbar-bottom-bg {
  width: 100%;
  height: 141px;
  background: url("~@laboratory/assets/bg1.jpg") center / 100% 100% no-repeat;
}
.main-container {
  margin: -100px 80px 0;
}
@media (max-width: 768px) {
  .main-container {
    margin: -120px 10px 0;
  }
}
@media (max-width: 1440px) {
  .main-container {
    margin: -110px 30px 0;
  }
}
@media (max-width: 1640px) {
  .main-container {
    margin: -100px 50px 0;
  }
}
</style>
