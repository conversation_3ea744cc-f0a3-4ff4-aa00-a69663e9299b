import "babel-polyfill";
import Vue from "vue";
import App from "./App";
import store from "@laboratory/store/index";
import router from "@laboratory/router/index";
import "@laboratory/icons"; // 第三方icon
import install from "@laboratory/utils/preventReClick";
Vue.use(install);

import "../../../node_modules/normalize.css/normalize.css"; // 一个现代的替代CSS重置
import "./styles/index.scss"; // 全局的css样式

//引入element-UI(中文/英文)
import ElementUI from "element-ui";
// import "element-ui/lib/theme-chalk/index.css"; // 全局主题修改 element-ui.scss
// import locale from 'element-ui/lib/locale/lang/en'
// Vue.use(ElementUI); //中文
// Vue.use(ElementUI, { locale })//英
Vue.use(ElementUI, { size: 'small' })
//引入时间插件
import Moment from "moment";
Moment.locale("zh-cn");
Vue.prototype.$moment = Moment;
import "./permission";

// 富文本编辑器
import VueQuillEditor from "vue-quill-editor";
import "../../../node_modules/quill/dist/quill.core.css";
import "../../../node_modules/quill/dist/quill.snow.css";
import "../../../node_modules/quill/dist/quill.bubble.css";
Vue.use(VueQuillEditor);
import myStore from '@laboratory/views/Store';
Vue.prototype.$myStore = myStore; // 挂载到 Vue 实例上

//数组传参
import qs from "qs";
Vue.prototype.$qs = qs;

Vue.filter("formatDate", function(value, str) {
  return Moment(value).format(str);
});

import 'font-awesome/css/font-awesome.css'
// 富文本编辑器样式
import '@wangeditor/editor/dist/css/style.css';
// 注册附件上传插件(配合wangeditor使用)
import { Boot } from "@wangeditor/editor";
import attachmentModule from "@wangeditor/plugin-upload-attachment";
Boot.registerModule(attachmentModule);
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }

Vue.config.productionTip = false;

// 全局指令
Vue.directive("allow", {
  inserted: (el, binding, vnode) => {
    let optionsList = vnode.context.$route.meta.options;
    if (optionsList && !optionsList.includes(binding.value)) {
      if (window.g.AuthorityTest) {
        el.parentNode.removeChild(el);
      }
    }
  },
});

new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});
