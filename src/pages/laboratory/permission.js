import router, { resetRouter } from './router/index'
import store from './store/index'
import axios from 'axios'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // 一个进度条的插件
import 'nprogress/nprogress.css' // progress bar style
import getPageTitle from '@laboratory/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] //  免登陆白名单

// 辅助函数：检查是否在白名单中
function isInWhiteList (path) {
  return whiteList.indexOf(path) !== -1
}

// 辅助函数：检查用户是否已登录
function isUserLoggedIn (haslogin) {
  return haslogin === true || haslogin === 'true'
}

// 辅助函数：处理未登录用户
async function handleUnauthenticatedUser (to, next) {
  if (isInWhiteList(to.path)) {
    next()
  } else {
    await store.dispatch('user/resetToken')
    next(`/login?redirect=${to.path}`)
    NProgress.done()
  }
}

// 辅助函数：处理动态路由生成
async function handleDynamicRoutes (to, next) {
  try {
    if (store.state.user.roles) {
      const roles = store.state.user.roles
      const accessRoutes = await store.dispatch('permission/generateRoutes', sessionStorage.getItem("activeName"), roles)

      router.addRoutes(accessRoutes)

      if (isInWhiteList(to.path)) {
        next({ ...to, replace: true })
      } else {
        next({ ...to, replace: true }) // 统一使用 replace: true 避免导航冲突
      }
    } else {
      await handleUnauthenticatedUser(to, next)
    }
  } catch (error) {
    console.error('动态路由生成失败:', error)
    await handleUnauthenticatedUser(to, next)
  }
}

router.beforeEach(async (to, from, next) => {
  try {
    // 开始进度条
    NProgress.start()

    // 设置页面标题
    document.title = getPageTitle(to.meta.title)

    // 确定用户是否已登录
    const haslogin = store.state.user.isLogin

    if (isUserLoggedIn(haslogin)) {
      // 用户已登录
      if (to.path === '/login') {
        // 如果已登录，则重定向到主页
        next({ path: '/', replace: true })
        NProgress.done()
      } else {
        // 检查是否已有权限路由
        const hasPermissionRoutes = store.getters.permission_routes && store.getters.permission_routes.length > 0

        if (hasPermissionRoutes) {
          // 已有权限路由，直接放行
          next()
        } else {
          // 没有权限路由，需要生成动态路由
          await handleDynamicRoutes(to, next)
        }
      }
    } else {
      // 用户未登录
      await handleUnauthenticatedUser(to, next)
    }
  } catch (error) {
    console.error('路由守卫执行失败:', error)
    // 兜底处理
    if (isInWhiteList(to.path)) {
      next()
    } else {
      try {
        await store.dispatch('user/resetToken')
      } catch (resetError) {
        console.error('重置token失败:', resetError)
      }
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

// 设置响应拦截
axios.interceptors.response.use(res => {
  if (res.data.code === 500) {
    store.commit('setIsLogin', false)
    sessionStorage.setItem("isLogin", false)
    // 修复：在拦截器中无法使用 to 和 next，改为使用 router.push
    router.push('/login').catch(err => {
      // 捕获可能的导航错误
      if (err.name !== 'NavigationDuplicated') {
        console.error('导航到登录页失败:', err)
      }
    })
    NProgress.done()
  }
  return res
}, error => {
  console.error('请求错误:', error)
  return Promise.reject(error)
})