import Layout from '@laboratory/layout'

export const basicInfoMangeRouter = [
  // 实验室 开始
  // 基本信息管理
  // {
  //   path: '/basicInfoMange/generalInfo',
  //   // name: 'generalInfoRoot',
  //   component: Layout,
  //   system: 'basicInfoMange',
  //   redirect: '/basicInfoMange/generalInfo/generalInfo',
  //   children: [
  //     {
  //       path: 'generalInfo',
  //       name: 'generalInfo',
  //       component: () => import('@basicInfoMange/generalInfo/generalInfo'),
  //       meta: { title: '实验平台综合信息' }
  //     }
  //   ]
  // },
  {
    path: '/basicInfoMange/platformServices',
    // name: 'platformServices',
    component: Layout,
    system: 'basicInfoMange',
    meta: { title: '实验室平台与服务' },
    children: [
      {
        path: 'centerManage',
        name: 'centerManage',
        component: () => import('@basicInfoMange/platformServices/centerManage/centerManage'),
        meta: {
          title: '实验中心管理',
          // keepAlive: true
        },

      },
      {
        path: 'centerDetail/:uuid',
        name: 'centerDetail',
        component: () => import('@basicInfoMange/platformServices/centerManage/centerDetail'),
        meta: { title: '实验中心详情', activeMenu: '/basicInfoMange/platformServices/centerManage' },
        hidden: true
      },
      {
        path: 'centerAdd',
        name: 'centerAdd',
        component: () => import('@basicInfoMange/platformServices/centerManage/centerAdd'),
        meta: { title: '实验中心新建', activeMenu: '/basicInfoMange/platformServices/centerManage' },
        hidden: true
      },
      {
        path: 'centerEdit/:uuid',
        name: 'centerEdit',
        component: () => import('@basicInfoMange/platformServices/centerManage/centerAdd'),
        meta: { title: '实验中心编辑', activeMenu: '/basicInfoMange/platformServices/centerManage' },
        hidden: true
      }
    ]
  },
  {
    path: '/basicInfoMange/basicInfo',
    // name: 'basicInfo',
    component: Layout,
    system: 'basicInfoMange',
    meta: { title: '实验室基本信息' },
    children: [
      {
        path: 'labManager',
        name: 'labManager',
        component: () => import('@basicInfoMange/basicInfo/labManager/labManager'),
        meta: { title: '实验室管理' }
      },
      {
        path: 'labDetail/:uuid',
        name: 'labDetail',
        component: () => import('@basicInfoMange/basicInfo/labManager/labDetail'),
        meta: { title: '实验室详情', activeMenu: '/basicInfoMange/basicInfo/labManager' },
        hidden: true
      },
      {
        path: 'labAdd',
        name: 'labAdd',
        component: () => import('@basicInfoMange/basicInfo/labManager/labAdd'),
        meta: { title: '实验室新建', activeMenu: '/basicInfoMange/basicInfo/labManager' },
        hidden: true
      },
      {
        path: 'labEdit/:uuid',
        name: 'labEdit',
        component: () => import('@basicInfoMange/basicInfo/labManager/labAdd'),
        meta: { title: '实验室编辑', activeMenu: '/basicInfoMange/basicInfo/labManager' },
        hidden: true
      },
    ]
  },
]