import Layout from '@laboratory/layout'

export const equipmentBasicInfoRouter = [
  // 仪器设备基本信息
  {
    path: '/equipmentBasicInfo/eqBasicInfo',
    component: Layout,
    system: 'equipmentBasicInfo',
    meta: { title: '仪器设备基本信息' },
    children: [
      {
        path: 'eqInfo',
        name: 'eqInfo',
        component: () => import('@equipmentBasicInfo/eqInfo/eqInfo'),
        meta: { title: '仪器信息' }
      },
      {
        path: 'eqAdd',
        name: 'eqAdd',
        component: () => import('@equipmentBasicInfo/eqInfo/eqAdd'),
        meta: { title: '仪器新建', activeMenu: '/equipmentBasicInfo/eqBasicInfo/eqInfo' },
        hidden: true
      },
      {
        path: 'eqEdit/:uuid',
        name: 'eqEdit',
        component: () => import('@equipmentBasicInfo/eqInfo/eqAdd'),
        meta: { title: '仪器编辑', activeMenu: '/equipmentBasicInfo/eqBasicInfo/eqInfo' },
        hidden: true
      },
      {
        path: 'eqDetail/:uuid',
        name: 'eqDetail',
        component: () => import('@equipmentBasicInfo/eqInfo/eqDetail'),
        meta: { title: '仪器详情', activeMenu: '/equipmentBasicInfo/eqBasicInfo/eqInfo' },
        hidden: true
      },
      {
        path: 'eqType',
        name: 'eqType',
        component: () => import('@equipmentBasicInfo/eqType/eqType'),
        meta: { title: '仪器类型' }
      },
      {
        path: 'skDataType',
        name: 'skDataType',
        component: () => import('@equipmentBasicInfo/skDataType/skDataType'),
        meta: { title: '技术资料类型' }
      },
    ]
  },
  {
    path: '/equipmentBasicInfo/eqTransfer',
    // name: 'eqTransfer',
    // component: () => import('@equipmentBasicInfo/eqTransfer/index'),
    component: Layout,
    system: 'equipmentBasicInfo',
    meta: { title: '仪器设备调拨' },
    children: [
      {
        path: 'transferIn',
        name: 'transferIn',
        component: () => import('@equipmentBasicInfo/eqTransfer/transferIn/transferIn'),
        meta: { title: '仪器调入' }
      },
      {
        path: 'transferOut',
        name: 'transferOut',
        component: () => import('@equipmentBasicInfo/eqTransfer/transferOut/transferOut'),
        meta: { title: '仪器调出' }
      }
    ]
  },
  {
    path: '/equipmentBasicInfo/eqMaint',
    // name: 'eqMaint',
    // component: () => import('@equipmentBasicInfo/eqMaint/index'),
    component: Layout,
    system: 'equipmentBasicInfo',
    meta: { title: '仪器设备维修' },
    children: [
      {
        path: 'maintBoard',
        name: 'maintBoard',
        component: () => import('@equipmentBasicInfo/eqMaint/maintBoard/maintBoard'),
        meta: { title: '维修看板' }
      },
      {
        path: 'maintOrder',
        name: 'maintOrder',
        component: () => import('@equipmentBasicInfo/eqMaint/maintOrder/maintOrder'),
        meta: { title: '维修工单' }
      },
      {
        path: 'maintRecord',
        name: 'maintRecord',
        component: () => import('@equipmentBasicInfo/eqMaint/maintRecord/maintRecord'),
        meta: { title: '维修记录' }
      },
      {
        path: 'maintProject',
        name: 'maintProject',
        component: () => import('@equipmentBasicInfo/eqMaint/maintProject/maintProject'),
        meta: { title: '维修项目' }
      }
    ]
  },
  {
    path: '/equipmentBasicInfo/eqScrap',
    // name: 'eqScrap',
    // component: () => import('@equipmentBasicInfo/eqScrap/index'),
    component: Layout,
    system: 'equipmentBasicInfo',
    meta: { title: '仪器设备报废' },
    children: [
      {
        path: 'scrapApply',
        name: 'scrapApply',
        component: () => import('@equipmentBasicInfo/eqScrap/scrapApply/scrapApply'),
        meta: { title: '报废申请' }
      },
      {
        path: 'scrapRecord',
        name: 'scrapRecord',
        component: () => import('@equipmentBasicInfo/eqScrap/scrapRecord/scrapRecord'),
        meta: { title: '报废记录' }
      }
    ]
  }
]