import Layout from '@laboratory/layout'

export const gradingClassRouter = [
  // 实验中心管理
  {
    path: '/gradingClass/centerMgmt',
    component: Layout,
    system: 'gradingClass',
    redirect: '/gradingClass/centerMgmt/centerMgmt',
    children: [
      {
        path: 'centerMgmt',
        name: 'centerMgmt',
        component: () => import('@gradingClass/centerMgmt/centerMgmt'),
        meta: { title: '实验中心管理' }
      }
    ]
  },
  // 实验室管理
  {
    path: '/gradingClass/labMgmt',
    component: Layout,
    system: 'gradingClass',
    redirect: '/gradingClass/labMgmt/labMgmt',
    children: [
      // 实验室管理员权限
      {
        path: 'labMgmt',
        name: 'labMgmt',
        component: () => import('@gradingClass/labMgmt/labMgmt'),
        meta: { title: '实验室管理' },
      },
      {
        path: 'mgmtLabDetail/:uuid',
        name: 'mgmtLabDetail',
        component: () => import('@gradingClass/labMgmt/mgmtLabDetail'),
        meta: { title: '实验室详情', activeMenu: '/gradingClass/labMgmt/labMgmt' },
        hidden: true
      },
      {
        path: 'mgmtLabEdit/:uuid',
        name: 'mgmtLabEdit',
        component: () => import('@gradingClass/labMgmt/mgmtLabEdit'),
        meta: { title: '实验室编辑', activeMenu: '/gradingClass/labMgmt/labMgmt' },
        hidden: true
      },
      // 实验中心管理员权限下的实验室管理详情和编辑
      {
        path: 'centerLabMgmt/:centerUuid',
        name: 'centerLabMgmt',
        component: () => import('@gradingClass/labMgmt/labMgmt'),
        meta: { title: '实验室管理', activeMenu: '/gradingClass/centerMgmt/centerMgmt' },
        hidden: true
      },
      {
        path: 'centerMgmtLabDetail/:centerUuid/:uuid',
        name: 'centerMgmtLabDetail',
        component: () => import('@gradingClass/labMgmt/mgmtLabDetail'),
        meta: { title: '实验室详情', activeMenu: '/gradingClass/centerMgmt/centerMgmt' },
        hidden: true
      },
      {
        path: 'centerMgmtLabEdit/:centerUuid/:uuid',
        name: 'centerMgmtLabEdit',
        component: () => import('@gradingClass/labMgmt/mgmtLabEdit'),
        meta: { title: '实验室编辑', activeMenu: '/gradingClass/centerMgmt/centerMgmt' },
        hidden: true
      },
      // 实验中心管理员权限
    ]
  },
  // 分级管理
  {
    path: '/gradingClass/grading',
    component: Layout,
    system: 'gradingClass',
    meta: { title: '分级管理' },
    children: [
      {
        path: 'labGrading',
        name: 'labGrading',
        component: () => import('@gradingClass/grading/labGrading/labGrading'),
        meta: { title: '实验室分级管理' }
      },
      {
        path: 'riskSource',
        name: 'riskSource',
        component: () => import('@gradingClass/grading/riskSource/riskSource'),
        meta: { title: '实验室风险源管理' }
      }
    ]
  },
  // 分类管理
  {
    path: '/gradingClass/category',
    component: Layout,
    system: 'gradingClass',
    redirect: '/gradingClass/category/category',
    children: [
      {
        path: 'category',
        name: 'categoryMgmt',
        component: () => import('@gradingClass/category/category'),
        meta: { title: '分类管理' }
      }
    ]
  },
  // 防护要点
  {
    path: '/gradingClass/protection',
    component: Layout,
    system: 'gradingClass',
    redirect: '/gradingClass/protection/protection',
    children: [
      {
        path: 'protection',
        name: 'protectionPoints',
        component: () => import('@gradingClass/protection/protection'),
        meta: { title: '防护要点' }
      }
    ]
  },
  // 安全标识库
  {
    path: '/gradingClass/safetySign',
    component: Layout,
    system: 'gradingClass',
    redirect: '/gradingClass/safetySign/safetySign',
    children: [
      {
        path: 'safetySign',
        name: 'safetySignLib',
        component: () => import('@gradingClass/safetySign/safetySign'),
        meta: { title: '安全标识库' }
      }
    ]
  }
]