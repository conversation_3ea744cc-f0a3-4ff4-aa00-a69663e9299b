import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)
/* Layout */
import Layout from '@laboratory/layout'
import { basicInfoMangeRouter } from './basicInfoMange'
import { gradingClassRouter } from './gradingClass'
import { equipmentPurchaseRouter } from './equipmentPurchase'
import { equipmentBasicInfoRouter } from './equipmentBasicInfo'
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@laboratory/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@laboratory/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/basicInfoMange/platformServices/centerManage',
    // children: [
    //   {
    //     path: 'home',
    //     name: 'home',
    //     component: () => import('@laboratory/views/home/<USER>'),
    //     meta: {
    //       title: '主页',
    //     }
    //   }
    // ]
  }
]

// export const router = new Router({
//     routes: constantRoutes.concat(errorRoutes)
// })

// 异步挂载的路由
// 动态需要根据权限加载的路由表
// 系统配置应用：Systemconfiguration  //系统应用：Systemapplication //综合统计：Comprehensivestatistics
export const asyncRoutes = [
  ...basicInfoMangeRouter,
  ...gradingClassRouter,
  ...equipmentPurchaseRouter,
  ...equipmentBasicInfoRouter
]

export const errorRoutes = [
  // 404 页面必须放置在最后一个页面
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

// 创建路由
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    // base: '/Lab/',
    // mode: 'history',

    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes.concat(asyncRoutes).concat(errorRoutes)
    // routes: constantRoutes.concat(errorRoutes)
  })

export const router = createRouter()

// 重置路由
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
