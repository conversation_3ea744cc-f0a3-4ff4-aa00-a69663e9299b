import { asyncRoutes, constantRoutes, errorRoutes } from "@laboratory/router";
import { getlistby<PERSON>e, getAppList, changeApp } from "@laboratory/api/user";
import Layout from "@laboratory/layout";
import router from "@laboratory/router/index";
import store from "../../store/index";
import { RSAKey } from "jsencrypt/lib/lib/jsbn/rsa";

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission (roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some((role) => route.meta.roles.includes(role));
  } else {
    return true;
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes (routes, roles) {
  const res = [];

  routes.forEach((route) => {
    const tmp = { ...route };
    if (hasPermission(roles, tmp)) {
      if (tmp.children.length != 0 && tmp.funType == 4) {
        tmp.children = filterAsyncRoutes(tmp.children, roles);
      }
      res.push(tmp);
    }
  });

  return res;
}
let realdata = [];
export function generaMenu (asyncRoutes, data) {
  let menu;
  data.forEach((item) => {
    menu = {
      path: item.uiUrl,
      component: (resolve) =>
        require([`@laboratory/views/${item.componentUrl}/index`], resolve),
      // component: () => import(`@laboratory/views/${item.componentUrl}/index`) ,
      children: [],
      redirect: `${item.uiUrl}`,
      meta: { title: item.funName, icon: item.icon, options: item.options },
    };
    if (item.funParenId == 0) {
      menu.component = Layout;
    }
    if (item.children.length != 0 && item.funType == 4) {
      // asyncRoutes.map((c, d) => {
      //   if (item.uiUrl == c.path) {
      //     menu.children = c.children;
      //     menu.children.map((e, f) => {
      //       console.log(e);
      //       c.children.map((g, h) => {
      //         console.log(g);
      //         // e.children = g.children;
      //       });
      //     });
      //   }
      // });
      // menu.options = item.options;
      // generaMenu(menu.children, item.children);
    }
    if (item.children.length != 0 && item.funType == 1) {
      menu.options = item.options;
      // asyncRoutes.map((c, d) => {
      //   if (item.uiUrl == c.path) {
      //     c.children.map((a,b)=>{
      //       if(a.path == )
      //     })
      //   }
      // });
      // menu.children = item.children;
      // generaMenu(menu.children, item.children);
    }
    realdata.push(menu);
    asyncRoutes.push(menu);
  });
}

const state = {
  routes: [],
  addRoutes: [],
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes;
    state.routes = constantRoutes.concat(routes).concat(errorRoutes);
    sessionStorage.setItem("routers", JSON.stringify(routes));
  },
  SET_ROUTES_BY_SYSTEM: (state, { routes, system }) => {
    // 根据system过滤路由
    const filteredRoutes = routes.filter(route => route.system === system);
    state.addRoutes = filteredRoutes;
    state.routes = constantRoutes.concat(filteredRoutes).concat(errorRoutes);
    sessionStorage.setItem("routers", JSON.stringify(filteredRoutes));
    sessionStorage.setItem("currentSystem", system);
  },
};
function routerMatch (userInfo, fullRouter) {
  return new Promise((resolve) => {
    if (!userInfo) {
      resolve([]);
    }
    var result = [];
    // console.log(userInfo,fullRouter)
    userInfo.forEach((item) => {
      let arr1duiyin = fullRouter.find((arr1Item) => {
        return arr1Item.path == item.uiUrl;
      });
      // 判断有没有子项
      if (item.children.length != 0 && item.funType == 4) {
        // 如果用户的有的话
        let obj = Object.assign({}, arr1duiyin);
        obj.children = [];
        item.children.forEach((childItem) => {
          if (arr1duiyin) {
            let res = arr1duiyin.children.find((arr1child) => {
              return arr1child.path === childItem.uiUrl;
            });
            if (res) {
              res.meta.options = childItem.options;
              res.meta.title = childItem.funName;
              res.meta.icon = childItem.icon;
              obj.children.push(Object.assign({}, res));
            }
          }
        });
        result.push(obj);
      }
      // else if(item.children.length != 0 && item.funType == 1){
      //   // if (arr1duiyin.children.length != 0) {
      //   //   arr1duiyin.children.map((index,item)=>{
      //   //     item.children.map((v,k)=>{

      //   //     })
      //   //   })
      //   // }
      //   // console.log(arr1duiyin);
      //   // arr1duiyin.children = item.children;
      //   result.push(Object.assign({}, arr1duiyin))
      // }
      else {
        // 没有子项 那么 路由里的也没有直接复制就行
        // result.push(Object.assign({}, item))
        if (arr1duiyin.children.length != 0) {
          if (arr1duiyin.children[0].meta) {
            arr1duiyin.children[0].meta.options = item.options;
          }
        }
        result.push(Object.assign({}, arr1duiyin));
      }
    });
    // console.log([result])
    resolve([result]);
  });
}

const actions = {
  // 临时注释掉动态路由生成逻辑，改为直接使用静态路由
  generateRoutes ({ commit }, applicationSn, roleId, next) {
    return new Promise((resolve) => {
      // 直接使用静态路由
      // commit('SET_ROUTES', asyncRoutes);
      // resolve(asyncRoutes);
      // 首次登录时根据当前system或默认system过滤路由
      const currentSystem = sessionStorage.getItem("currentSystem") || "basicInfoMange";
      commit('SET_ROUTES_BY_SYSTEM', { routes: asyncRoutes, system: currentSystem });
      resolve(asyncRoutes.filter(route => route.system === currentSystem));

      /* 原动态路由代码，临时注释
      // 通过传进来的applicationSn查询到路由
      let json = {
        applicationSn: applicationSn,
      };
      changeApp(json).then((res) => {
        realdata = [];
        if (res.code == 0) {
          let data = res.data;
          generaMenu(asyncRoutes, data);
          let accessedRoutes;
          routerMatch(data, asyncRoutes).then((res) => {
            accessedRoutes = filterAsyncRoutes(res[0], roleId);
            commit("SET_ROUTES", accessedRoutes);
            resolve(accessedRoutes);
          });
          // if (realdata.length == 0) {
          //   routerMatch(data, asyncRoutes).then((res) => {
          //     accessedRoutes = filterAsyncRoutes(res[0], roleId);
          //     commit("SET_ROUTES", accessedRoutes);
          //     resolve(accessedRoutes);
          //   });
          // } else {
          //   routerMatch(data, realdata).then((res) => {
          //     accessedRoutes = filterAsyncRoutes(res[0], roleId);
          //     commit("SET_ROUTES", accessedRoutes);
          //     resolve(accessedRoutes);
          //   });
          // }
        } else {
          this.$message({
            message: "菜单数据加载异常",
            type: 0,
          });
        }
      });
      */
    });
  },

  // 根据system切换路由
  switchRoutesBySystem ({ commit }, system) {
    return new Promise((resolve) => {
      commit('SET_ROUTES_BY_SYSTEM', { routes: asyncRoutes, system });
      resolve(asyncRoutes.filter(route => route.system === system));
    });
  },

  //旧的获取路由方式-用角色id获取
  // generateRoutes({ commit }, roleId, next) {
  //   return new Promise(resolve => {
  //     const loadMenuData = [];
  //     if (roleId) {
  //       // 先查询后台并返回左侧菜单数据并把数据添加到路由
  //       getlistbyrole(roleId).then(response => {
  //         if (response) {
  //           if (response.code !== 0) {
  //             this.$message({
  //               message: '菜单数据加载异常',
  //               type: 0
  //             })
  //           } else {
  //             let data = response.data;
  //             generaMenu(asyncRoutes,data);
  //             let accessedRoutes;
  //             routerMatch(data, asyncRoutes).then(res => {
  //               // accessedRoutes = filterAsyncRoutes(res[0], roleId);
  //               accessedRoutes = filterAsyncRoutes(asyncRoutes, roleId);
  //               let Tabroutes = constantRoutes.concat(accessedRoutes);
  //               console.log(Tabroutes);
  //               store.dispatch("app/setTabRoutes", Tabroutes);
  //               commit('SET_ROUTES', accessedRoutes)
  //               resolve(accessedRoutes)
  //             })

  //           }
  //         } else {
  //           router.push('/login')

  //         }

  //         // generaMenu(asyncRoutes, data)
  //       }).catch(error => {
  //         console.log(error)
  //       })
  //     } else {
  //       commit('SET_ROUTES', "")
  //     }

  //   })
  // }

  // return new Promise(resolve => {
  //   const { roles } = data;
  //   const accessedRouters = asyncRouterMap.filter(v => {
  //     // if (roles.indexOf('admin') >= 0) {
  //     //     return true;
  //     // };
  //     if ((roles, v)) {
  //       if (v.children && v.children.length > 0) {
  //         v.children = v.children.filter(child => {
  //           if (hasPermission(roles, child)) {
  //             return child
  //           }
  //           return false;
  //         });
  //         return v
  //       } else {
  //         return v
  //       }
  //     }
  //     return false;
  //   });
  //   console.log(accessedRouters)
  //   commit('SET_ROUTES', accessedRouters);
  //   resolve();
  // })

  // return new Promise(resolve => {
  //   let accessedRoutes
  //   if (roles.includes('admin')) {
  //     accessedRoutes = asyncRoutes || []
  //   } else {
  //     accessedRoutes = filterAsyncRoutes(asyncRouterMap, roles)
  //   }
  //   console.log(accessedRoutes)
  //   commit('SET_ROUTES', accessedRoutes)
  //   resolve(accessedRoutes)
  //   // getFunction().then(response => {
  //   //   const { detail } = response
  //   //   console.log('in getMenus')
  //   //   console.log(detail)
  //   //   const asyncRouter = filterAsyncRouter(detail)
  //   //   commit('SET_ROUTES', asyncRouter)
  //   //   resolve(asyncRouter)
  //   // }).catch(error => {
  //   //   reject(error)
  //   // })

  // })
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
