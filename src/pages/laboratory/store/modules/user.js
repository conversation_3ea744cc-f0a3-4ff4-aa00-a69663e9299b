import { login, logout, getlistbyrole } from "@laboratory/api/user";
import { getAuthAddress } from "@laboratory/api/login";
import { getalluserinfo } from "@laboratory/api/getuserinfo";
import { getToken, setToken, removeToken } from "@laboratory/utils/auth";
import router, { resetRouter } from "@laboratory/router";

const state = {
  name: sessionStorage.getItem("name"),
  logonName: sessionStorage.getItem("logonName"),
  userInfo: sessionStorage.getItem("userInfo"),
  //角色Id
  roles: sessionStorage.getItem("roles"),
  // 是否登录
  isLogin: sessionStorage.getItem("isLogin"),
  permission_routes: sessionStorage.getItem("permission_routes"),
  // 记录用户创建的预约信息
  newResearch: sessionStorage.getItem("newResearch"),
  createdStatus: sessionStorage.getItem("createdStatus"),
};

const mutations = {
  SET_ISLOGIN: (state, isLogin) => {
    state.isLogin = isLogin;
    sessionStorage.setItem("isLogin", isLogin);
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction;
  },
  SET_NAME: (state, name) => {
    state.name = name;
    sessionStorage.setItem("name", name);
  },
  SET_LOGONAME: (state, logonName) => {
    state.logonName = logonName;
    sessionStorage.setItem("logonName", logonName);
  },
  SET_USERINFO: (state, userInfo) => {
    state.userInfo = userInfo;
    sessionStorage.setItem("userInfo", userInfo);
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
    sessionStorage.setItem("roles", roles);
  },
  SET_ROUTES: (state, routers) => {
    state.routers = routers;
    sessionStorage.setItem("routers", routers);
  },
  SET_ROLELEVEL: (state, roleLevel) => {
    state.roleLevel = roleLevel;
    sessionStorage.setItem("roleLevel", roleLevel);
  },
  SET_NEWRESEARCH: (state, newResearch) => {
    state.newResearch = newResearch;
    sessionStorage.setItem("newResearch", newResearch);
  },
  SET_CREATEDSTATUS: (state, createdStatus) => {
    state.createdStatus = createdStatus;
    sessionStorage.setItem("createdStatus", createdStatus);
  },
};

const actions = {
  addnewsearch ({ commit }, newSearch) {
    return new Promise((resolve, reject) => {
      commit("SET_NEWRESEARCH", newSearch);
      resolve();
    });
  },
  addcreatedstatus ({ commit }, createdStatus) {
    return new Promise((resolve, reject) => {
      commit("SET_CREATEDSTATUS", createdStatus);
      resolve();
    });
  },
  //用户登录
  login ({ commit }, userInfo) {
    const { username, password, captcha } = userInfo;
    commit("SET_USERINFO", userInfo); //存下
    return new Promise((resolve, reject) => {
      login({
        logonName: username.trim(),
        password: password,
        captcha: captcha,
        consoleType: 16,
      })
        .then((response) => {
          if (response.code == 100) {
            this.$message({
              type: "error",
              message: response.message,
              duration: "3000",
            });
            reject(response.message)
            return;
          }
          setToken(response.data.token);
          commit("SET_ISLOGIN", true); //存下已登录的状态
          commit("SET_ROLES", response.data.roleIds);
          commit("SET_NAME", response.data.trueName);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  //判断用户是否登录
  judgelogin ({ commit }) {
    return new Promise((resolve, reject) => {
      getalluserinfo()
        .then((response) => {
          if (response.code == 0) {
            if (response.data) {
              commit("SET_USERINFO", response.data.accNo); //存下
              commit("SET_ISLOGIN", true); //存下已登录的状态
              commit("SET_ROLES", response.data.roleIds);
              commit("SET_NAME", response.data.trueName);
              commit("SET_LOGONAME", response.data.logonName);
              commit("SET_ROLELEVEL", response.data.roleLevel);
              resolve();
            }
          } else {
            sessionStorage.removeItem("isLogin");
            sessionStorage.removeItem("roles");
            sessionStorage.removeItem("name");
            sessionStorage.removeItem("userinfo");
            sessionStorage.removeItem("activeName");
            commit("SET_ISLOGIN", false);
            commit("SET_ROLES", "");
            commit("SET_NAME", "");
            commit("SET_USERINFO", ""); //存下
            // commit('SET_ROUTES', '');
            commit("SET_ROLELEVEL", "");
            resetRouter();
            resolve();
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  //统一身份认证
  thirdlogin ({ commit }) {
    return new Promise((resolve, reject) => {
      getalluserinfo().then((res) => {
        if (res.code == 0) {
          if (res.data) {
            commit("SET_ISLOGIN", true); //存下已登录的状态
            commit("SET_ROLES", res.data.roleIds);
            commit("SET_NAME", res.data.trueName);
            commit("SET_USERINFO", res.data.accNo); //存下
            resolve();
          } else {
            let href =
              window.location.protocol +
              "//" +
              window.location.host +
              window.g.thirdurl +
              "/thirdlogin.html#/adminindex";
            let json = {
              queryParam: "",
              typeCode: "",
              redirectUrl: href,
              targetPageUrl: "",
            };
            getAuthAddress(json).then((res) => {
              if (res.code == 0) {
                window.location.href = res.data;
              }
            });
          }
        } else {
          let href =
            window.location.protocol +
            "//" +
            window.location.host +
            window.g.thirdurl +
            "/thirdlogin.html#/adminindex";
          let json = {
            queryParam: "",
            typeCode: "",
            redirectUrl: href,
            targetPageUrl: "",
          };
          getAuthAddress(json).then((res) => {
            if (res.code == 0) {
              window.location.href = res.data;
            }
          });
        }
      });
    });
  },

  //根据用户roleId获取该用户的权限列表
  getlistbyrole ({ commit }, roleId) {
    return new Promise((resolve, reject) => {
      getlistbyrole(roleId[0])
        .then((response) => {
          const { data } = response;
          if (!data) {
            reject("Verification failed, please Login again.");
          }
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  //用户退出系统登录
  logout ({ commit, userInfo }) {
    return new Promise((resolve, reject) => {
      sessionStorage.removeItem("isLogin");
      sessionStorage.removeItem("roles");
      sessionStorage.removeItem("name");
      sessionStorage.removeItem("userinfo");
      commit("SET_ISLOGIN", false);
      commit("SET_ROLES", "");
      commit("SET_NAME", "");
      commit("SET_USERINFO", ""); //存下
      resetRouter();
      resolve();

      // logout().then(() => {
      //   sessionStorage.removeItem('isLogin');
      //   sessionStorage.removeItem('roles');
      //   sessionStorage.removeItem('name');
      //   sessionStorage.removeItem('userinfo');
      //   commit('SET_ISLOGIN', false);
      //   commit('SET_ROLES', '');
      //   commit('SET_NAME', '');
      //   commit('SET_USERINFO', '')//存下
      //   // router.addRoutes("")
      //   resetRouter()
      //   // store.getters.permission_routes = [];
      //   resolve()
      // }).catch(error => {
      //   reject(error)
      // })
    });
  },

  // remove token
  // resetToken({ commit }) {
  //   return new Promise(resolve => {
  //     commit('SET_TOKEN', '')
  //     commit('SET_ROLES', [])
  //     removeToken()
  //     resolve()
  //   })
  // },

  // remove token
  resetToken ({ commit }) {
    return new Promise((resolve) => {
      commit("SET_ISLOGIN", false);
      commit("SET_ROLES", "");
      commit("SET_NAME", "");
      commit("SET_USERINFO", ""); //存下
      sessionStorage.removeItem("isLogin");
      sessionStorage.removeItem("roles");
      sessionStorage.removeItem("name");
      sessionStorage.removeItem("userinfo");
      router.push("/login");
      resetRouter();
      resolve();
    });
  },

  // dynamically modify permissions
  async changeRoles ({ commit, dispatch }, role) {
    const token = role + "-token";

    commit("SET_TOKEN", token);
    setToken(token);

    const { roles } = await dispatch("getInfo");

    resetRouter();

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch("permission/generateRoutes", roles, {
      root: true,
    });
    // dynamically add accessible routes
    router.addRoutes(accessRoutes);

    // reset visited views and cached views
    dispatch("tagsView/delAllViews", null, { root: true });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
