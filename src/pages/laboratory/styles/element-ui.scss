// cover some element-ui styles
/* 改变主题色变量 */
$--color-primary: #2050D1;
$--color-warning: #DE8142;
$--color-danger: #D2585D;
$--color-success: #00A870;
/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
// 分页样式
// .el-pager .active {
//   color: #FFFFFF!important;
//   background-color: #2050D1 !important;
// }
// .el-button--primary {
//   background-color: #2050D1 !important;
//   border: #2050D1!important;
//   font-size: 14px;
//   border-radius: 3px;
// }
// .el-button--primary.is-plain {
//   border: 1px solid #2050D1!important;
//   background-color: rgba(32, 80, 209, 0.24) !important;
//   color: #0052D9 !important;
// }
// .el-button--primary.is-plain:hover {
//   background-color: #2050D1 !important;
//   color: #ffffff!important;
// }
// .el-button--warning {
//   background-color: #DE8142 !important;
//   border: #DE8142!important;
//   font-size: 14px;
//   border-radius: 3px;
// }
// .el-button--warning.is-plain {
//   border: 1px solid #DE8142!important;
//   background-color: #dbcec5 !important;
//   color: #DE8142 !important;
// }

// .el-button--warning.is-plain:hover {
//   background-color: #DE8142 !important;
//   color: #ffffff !important;
// }
// .el-button--danger {
//   background-color: #D2585D !important;
//   border: #D2585D!important;
//   font-size: 14px;
//   border-radius: 3px;
// }
// .el-button--danger.is-plain {
//   border: 1px solid #D2585D!important;
//   background-color: rgba(253, 236, 238, 0.24)!important;
//   color: #D2585D !important;
// }

// .el-button--danger.is-plain:hover {
//   background-color: #D2585D !important;
//   color: #ffffff !important;
// }
// .el-button--success {
//   background-color: #00A870 !important;
//   border: #00A870!important;
//   font-size: 14px;
//   border-radius: 3px;
// }
// .el-button--success.is-plain {
//   border: 1px solid #00A870!important;
//   background-color: #8ceacb !important;
//   color: #00A870 !important;
// }

// .el-button--success.is-plain:hover {
//   background-color: #00A870 !important;
//   color: #ffffff !important;
// }

.el-button--delete.is-plain {
  background-color: #FFFFFF ;
  color: #999999;
  border: 1px solid #B8B8B8;
}
.el-button--delete.is-plain:hover {
  background-color: #FDECEE ;
  color: #D2585D;
  border: 1px solid #D2585D;
}