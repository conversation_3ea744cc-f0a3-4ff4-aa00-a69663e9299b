@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.app-container {
  padding: 20px;
}






//全局设置弹窗顶部的样式
.el-dialog {
  border-radius: 21px !important;
  // margin: auto !important;
  // top: 50% !important;
  // transform: translateY(-50%) !important;
}

.el-dialog__header {
  border-radius: 21px 21px 0 0 !important;
  background: #F3F3F3 !important;
  padding: 10px 20px !important;
  position: relative;
  text-align: center;
}

.el-dialog__title {
  color: #333333;
  font-size: 16px;
  display: block;
  width: 95%;
}

.el-dialog__headerbtn {
  position: absolute;
  top: 50% !important;
  transform: translateY(-50%) !important;
  right: 20px;

}

.el-dialog__headerbtn .el-dialog__close {
  font-size: 16px;
  color: #333333 !important;
}

.el-dialog__body {
  padding: 20px !important;
  min-height: 150px;
}


.dialog-footer {
  text-align: right;

  button:nth-of-type(1) {
    background: rgba(250, 252, 254, 1);
  }

  button:nth-of-type(2) {
    // background: rgba(78, 152, 217, 1);
    margin-left: 10px;
  }
}

.el-dialog__footer {
  text-align: center !important;
  padding: 0px 20px 20px !important;

  button:nth-of-type(1) {
    background: rgba(250, 252, 254, 1) !important;
  }

  button:nth-of-type(2) {
    // background: rgba(78, 152, 217, 1);
    margin-left: 10px !important;
  }
}


.marginTop {
  margin-top: 1%;
}

.center {
  text-align: center;
  margin-top: 1%;
}

.paging {
  width: 100%;
  height: 50px;
  // line-height: 50px;
}


//全局设置外盒的样式
.bigbox {
  width: 96%;
  height: 96%;
  margin: 15px;
  background: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  padding: 1% 2% 2% 2%;
}

.box {
  padding-left: 5px;
  padding-right: 5px;
  // padding-top: 20px;
  padding-bottom: 5%;
  box-sizing: border-box;
  margin: 15px;
  background: white;
}

//全局设置搜索按钮样式
.search {
  width: 100%;
  padding: 15px 15px 0px 15px;
  border: 1px solid rgba(230, 230, 230, 1);
}

.smallbtn {
  margin-top: 2px !important;
  background: #3D7BA9 !important;
  border-color: white !important;
}

.addbtn {
  background: #40abd2 !important;
  border-color: white !important;
}

.boxcontent {
  border: 1px solid rgba(219, 222, 226, 1);
  padding: 1%;
}

.boxmore {
  width: 100%;
  height: 100%;
  padding: 1%;
}

.borderbox {
  width: 100%;
  height: 100%;
  padding: 1%;
  border: 1px solid rgba(219, 222, 226, 1);
  margin-bottom: 100px;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  clear: both;
  display: table;
  content: "";
}


.marginitem {
  margin-top: 2%;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  display: table;
  content: "";
}

ul,
li {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

button:focus {
  border: none;
}

p {
  margin-bottom: 0px !important;
}


//设置统计报表的弹窗样式
.pstyle {
  font-weight: bold;
  font-size: 20px;
  color: #007bfc;
}

.smallpstyle {
  font-weight: bold;
  font-size: 18px;
  margin-left: 20px;
  color: #dda644;
}

.smallpstyle2 {
  font-weight: bold;
  font-size: 16px;
  margin-left: 40px;
  color: #ddad8c;
}



.el-pagination {
  margin-top: 1% !important;
}

.el-table td {
  padding: 4px 0 !important;
}

.el-table th {
  padding: 4px 0 !important;
  background: #dadce1 !important;
  color: #1a1a1a !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #f6f6f6 !important;
}

.el-table td.el-table__cell {
  border-bottom: 1px solid #cecece !important;
  border-right: 1px solid #cecece;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid #cecece !important;
}

.el-table--border .el-table__cell {
  border-right: 1px solid #cecece !important;
}

.el-form-item {
  margin-right: 35px !important;
  margin-bottom: 14px !important;
}

// 下拉搜索框
.el-input--suffix .el-input__inner {
  padding-right: 15px !important;
}

//管理端-查看按钮直接改到表格文字内容
.Lookunderline {
  color: #007bfc;
  font-size: 14px;
  text-decoration: underline;
  cursor: pointer;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap; // 默认不换行；
}


//导入下拉菜单的样式
.import {
  display: inline-block;
  white-space: nowrap;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
  margin-left: 20px;
  background: #40abd2;
  color: white !important;
}

.import .icon {
  font-size: 16px;
  margin-right: 5px;
}

//管理端-导入下拉框中的模版下载样式
.downtemplateCss {
  color: rgb(96, 98, 102);
  font-size: 14px;
  font-weight: 500;
  margin-left: 2px;
  line-height: 40px;
}

//管理端-综合信息-统筹-左侧学院筛选的样式
.leftlist {
  width: 100%;
  height: 100%;
  border: 1px solid #e6e6e6;
  padding: 10px;
  box-sizing: border-box;
  margin-top: 10px;

  .pcontent {
    width: 100%;
    height: 100%;

    p {
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      padding-left: 10px;
      box-sizing: border-box;
      cursor: pointer;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行
    }

    p:hover {
      background: #6abb6d;
      color: white;
    }
  }
}

.icon-success {
  color: #48C79C !important;
}

.icon-warning {
  color: #DE8142 !important;
}

.icon-danger {
  color: #D2585D !important;
}

// 最新ele-table样式
.el-table {
  width: 100%;

  .el-table__body-wrapper {
    // overflow-x: auto;
  }

  .el-table__header-wrapper {
    overflow-x: hidden;
  }

  th {
    background: #ffffff !important;
    color: #999999 !important;
    padding: 8px 0;
    text-align: center;
  }

  td {
    padding: 8px 0;
  }
}

// 头部搜索统一样式
.filter-style {
  margin-bottom: 20px;

  .el-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .el-form-item {
    margin-bottom: 10px;
    margin-right: 20px;
  }

  .el-input {
    width: 220px;
  }

  .el-select {
    width: 220px;
  }

  .el-button {
    margin-left: 0;
    margin-right: 10px;
  }

  .el-button+.el-button {
    margin-left: 10px;
  }
}

.ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 搜索栏公共样式
.search-section {
  min-height: 75px;
  background-color: #fff;
  padding: 20px 10px;
  border-radius: 3px;
  border-bottom: 1px solid #dcdcdc;
}

.photo-uploader {
  .el-upload {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}

.steps-nav {
  width: 180px;
  border-right: 1px solid #ebeef5;
  padding: 20px 0;

  /* 调整 el-steps 样式 */
  .el-steps {
    padding: 0 20px;
  }

  .el-step__title {
    font-size: 16px;
    cursor: pointer;

    &:hover {
      color: #2050d1;
    }
  }

  .el-step__head {
    padding-left: 0;
    cursor: pointer;
  }

   .el-step.is-vertical .el-step__line {
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    height: 100%;
  }

  .el-step.is-vertical {
    cursor: pointer;
  }

  .step-title {
    width: 100px;
    position: relative;
    // color: #333333;
    font-size: 16px;
    font-weight: 400;

    span {
      font-size: 26px;
      color: #d2585d;
      position: absolute;
      left: -8px;
      top: 70%;
      transform: translateY(-50%);
    }
  }
}