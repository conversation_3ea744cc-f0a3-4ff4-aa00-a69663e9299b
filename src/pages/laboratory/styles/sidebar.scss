#app {

  .sidebar-container {
    // padding: 0 12px;
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: auto;
    // overflow: hidden;

    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }
    // 菜单高度
    .el-scrollbar {
      width: $scrollbarWidth !important;
      margin: 10px;
      height: 100%;
    }
    .el-submenu__title,
    .el-menu-item {
      height: 36px;
      text-align: left;
      line-height: 36px;
      padding: 0 !important;
      padding-left: 15px !important;
      background-color: none !important;
    }
    .el-submenu {
      .el-menu-item {
        padding-left: 30px !important;
      }
    }
    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHover !important;
      }
    }
    .is-active>.el-submenu__title {
      color: $subMenuActiveText !important;
    }
    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $scrollbarWidth !important;
      // background-color: $subMenuBg !important;

      &:hover {
      // border-radius: 5px;
        background-color: $subMenuHover !important;
      }
    }

    // .router-link-active {
    .is-active {
      // border-radius: 5px;
      background-color: $menuHover !important;
    }
  }


  .el-menu--collapse .el-menu .el-submenu {
    min-width: $scrollbarWidth !important;
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
.sidebar-container {
  .el-icon-arrow-down {
    right: -15px!important;
    position: relative;
    top: 6% !important;
  }
  .el-icon-arrow-down::before {
    content: '' !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: transparent;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    width: 12px;
    height: 6px;
    background-color: #000000;
  }
}
