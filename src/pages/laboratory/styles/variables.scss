// sidebar
$menuText:#333333;
$menuActiveText:#0052D9;
$subMenuActiveText:#333333; //https://github.com/ElemeFE/element/issues/12951
$menuBg:#FFFFFF;
$menuHover:#D7E2FA;

$subMenuBg:#FFFFFF;
$subMenuHover:#D7E2FA;

$sideBarWidth: 184px;
$scrollbarWidth: 164px;
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  scrollbarWidth: $scrollbarWidth;
}
