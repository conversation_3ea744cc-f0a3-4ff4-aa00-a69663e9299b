import * as XLSX from 'xlsx';

/**
 * 设备导入导出工具类
 * 提供Excel文件的导入、导出、模板生成功能
 */
class DeviceImportExport {

  /**
   * 解析Excel文件，提取指定字段的数据列表
   * @param {File} file - Excel文件
   * @param {string} fieldName - 要提取的字段名，默认为"资产编号"
   * @returns {Promise<string[]>} 字段数据数组
   */
  static parseExcelFile (file, fieldName = '资产编号') {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });

          // 获取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];

          // 将工作表转换为JSON数组
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          if (jsonData.length < 2) {
            reject(new Error('Excel文件格式错误：至少需要包含表头和一行数据'));
            return;
          }

          // 获取表头，查找指定字段列的索引
          const headers = jsonData[0];
          const fieldIndex = headers.findIndex(header =>
            header && header.toString().includes(fieldName)
          );

          if (fieldIndex === -1) {
            reject(new Error(`Excel文件格式错误：未找到"${fieldName}"列`));
            return;
          }

          // 提取指定字段列的数据（跳过表头）
          const fieldValues = [];
          for (let i = 1; i < jsonData.length; i++) {
            const row = jsonData[i];
            const fieldValue = row[fieldIndex];
            if (fieldValue && fieldValue.toString().trim()) {
              fieldValues.push(fieldValue.toString().trim());
            }
          }

          if (fieldValues.length === 0) {
            reject(new Error(`Excel文件中未找到有效的${fieldName}数据`));
            return;
          }

          // 去重处理
          const uniqueValues = [...new Set(fieldValues)];

          resolve(uniqueValues);
        } catch (error) {
          reject(new Error(`解析Excel文件失败：${error.message}`));
        }
      };

      reader.onerror = () => {
        reject(new Error('读取文件失败'));
      };

      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 生成导入模板Excel文件
   * @param {string} filename - 文件名（不含扩展名）
   * @param {string} fieldName - 字段名，默认为"资产编号"
   * @param {string[]} exampleData - 示例数据数组，默认为资产编号示例
   */
  static generateTemplate (filename = '设备导入模板', fieldName = '资产编号', exampleData = ['ASSET001', 'ASSET002']) {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 创建示例数据
    const templateData = [
      [fieldName], // 表头
      ...exampleData.map(data => [data]) // 示例数据
    ];

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(templateData);

    // 设置列宽
    worksheet['!cols'] = [
      { width: 20 } // 字段列宽度
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '数据列表');

    // 生成文件并下载
    const fileName = `${filename}_${this.formatDate()}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  }

  /**
   * 处理文件下载
   * @param {Blob|string} data - 文件数据或下载URL
   * @param {string} filename - 文件名
   */
  static downloadFile (data, filename) {
    try {
      let downloadUrl;

      if (typeof data === 'string') {
        // 如果是URL字符串，直接使用
        downloadUrl = data;
      } else if (data instanceof Blob) {
        // 如果是Blob对象，创建URL
        downloadUrl = URL.createObjectURL(data);
      } else {
        throw new Error('不支持的文件数据类型');
      }

      // 创建下载链接
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      link.style.display = 'none';

      // 添加到DOM并触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      if (data instanceof Blob) {
        URL.revokeObjectURL(downloadUrl);
      }
    } catch (error) {
      throw new Error(`文件下载失败：${error.message}`);
    }
  }

  /**
   * 验证文件类型
   * @param {File} file - 文件对象
   * @returns {boolean} 是否为有效的Excel文件
   */
  static validateFileType (file) {
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    return validTypes.includes(file.type) || file.name.match(/\.(xlsx|xls)$/i);
  }

  /**
   * 格式化当前日期为字符串
   * @returns {string} 格式化的日期字符串 (YYYYMMDD_HHMMSS)
   */
  static formatDate () {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 文件大小（字节）
   * @returns {string} 格式化的文件大小
   */
  static formatFileSize (bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export default DeviceImportExport;