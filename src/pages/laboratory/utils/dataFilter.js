/**
 * 数据筛选工具方法
 */

/**
 * 根据指定字段筛选数组，保留在参考数组中存在的项
 * @param {Array} targetArray - 需要筛选的目标数组
 * @param {Array} referenceArray - 参考数组
 * @param {string} compareField - 用于比较的字段名
 * @returns {Array} 筛选后的数组
 */
export function filterArrayByField(targetArray, referenceArray, compareField) {
  if (!Array.isArray(targetArray) || !Array.isArray(referenceArray) || !compareField) {
    return targetArray || [];
  }

  // 创建参考数组中指定字段值的Set，用于快速查找
  const referenceFieldValues = new Set(
    referenceArray.map(item => item[compareField]).filter(value => value !== undefined && value !== null)
  );

  // 筛选目标数组，保留在参考数组中存在的项
  return targetArray.filter(item => {
    const fieldValue = item[compareField];
    return fieldValue !== undefined && fieldValue !== null && referenceFieldValues.has(fieldValue);
  });
}