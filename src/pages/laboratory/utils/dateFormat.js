Date.prototype.getWeekday = function(weekdayNum) {
  var weekdayName = null
  switch (weekdayNum) {
    case 0:
      weekdayName = 'Sunday'
      break
    case 1:
      weekdayName = 'Monday'
      break
    case 2:
      weekdayName = 'Tuesday'
      break
    case 3:
      weekdayName = 'Wednesday'
      break
    case 4:
      weekdayName = 'Thursday'
      break
    case 5:
      weekdayName = 'Friday'
      break
    case 6:
      weekdayName = 'Saturday'
      break
    default:
      weekdayName = 'weekday change error!'
  }
  return weekdayName
}
Date.prototype.Format = function(format) {
  // 替换 year yyyy
  format = format.replace('yyyy', this.getFullYear().toString())

  // 替换 month MM  this.getMonth()返回0-11
  format = format.replace(
    'MM',
    (this.getMonth() > 8 ? '' : '0') + (this.getMonth() + 1).toString()
  )

  // 替换 day dd
  format = format.replace(
    'dd',
    (this.getDate() > 9 ? '' : '0') + this.getDate().toString()
  )

  // 替换 hour HH
  format = format.replace('HH', this.getHours().toString())

  // 替换 minutes mm
  format = format.replace('mm', this.getMinutes().toString())

  // 替换 seconds ss
  format = format.replace('ss', this.getSeconds().toString())

  // 替换 weekdays ww  this.getDay()返回的是0-6
  format = format.replace('ww', this.getWeekday(this.getDay()))

  return format
}

// 使用   new Date().Format("yyyy-MM")  得2022-01
