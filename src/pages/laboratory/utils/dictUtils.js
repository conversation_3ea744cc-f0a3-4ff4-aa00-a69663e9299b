/**
 * 处理树形数据，移除空的children字段
 * 优化版：使用更简洁的方式处理树形数据
 * @param {Array} data 树形数据
 * @returns {Array} 处理后的树形数据
 */
export function processTreeData (data) {
  if (!Array.isArray(data)) return data;

  return data.map(item => {
    // 创建一个新对象，避免修改原始数据
    const newItem = { ...item };

    // 如果有children字段且不为空数组，则递归处理
    if (newItem.children && Array.isArray(newItem.children) && newItem.children.length > 0) {
      newItem.children = processTreeData(newItem.children);
    } else if (newItem.children) {
      // 如果children为空数组或undefined，则删除该字段
      delete newItem.children;
    }

    return newItem;
  });
}

/**
 * 合并级联选择器配置
 * @param {Object} defaultProps 默认配置
 * @param {Object} customProps 自定义配置
 * @returns {Object} 合并后的配置
 */
export function mergeCascaderProps (defaultProps, customProps) {
  return {
    ...defaultProps,
    ...customProps
  };
}

/**
 * 默认级联选择器配置
 */
export const defaultCascaderProps = {
  value: 'codeValue',
  label: 'name',
  children: 'children',
  checkStrictly: false, // 默认只能选择最后一级
  emitPath: false // 只返回选中的值，不返回路径
};