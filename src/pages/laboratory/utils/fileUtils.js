/**
 * 文件类型判断和处理工具函数
 */

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 文件扩展名（小写）
 */
export const getFileExtension = (fileName) => {
  if (!fileName) return '';
  return fileName.split('.').pop().toLowerCase();
};

/**
 * 获取文件类型
 * @param {string} fileName - 文件名
 * @returns {string} 文件类型：'image', 'pdf', 'document', 'other'
 */
export const getFileType = (fileName) => {
  const ext = getFileExtension(fileName);
  
  // 图片格式
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  if (imageExts.includes(ext)) return 'image';
  
  // PDF格式
  if (ext === 'pdf') return 'pdf';
  
  // 文档格式
  const documentExts = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
  if (documentExts.includes(ext)) return 'document';
  
  return 'other';
};

/**
 * 判断文件是否可以预览
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否可以预览
 */
export const canPreview = (fileName) => {
  const type = getFileType(fileName);
  return ['image', 'pdf'].includes(type);
};

/**
 * 判断是否为图片文件
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否为图片
 */
export const isImageFile = (fileName) => {
  return getFileType(fileName) === 'image';
};

/**
 * 判断是否为PDF文件
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否为PDF
 */
export const isPdfFile = (fileName) => {
  return getFileType(fileName) === 'pdf';
};

/**
 * 获取文件类型对应的图标
 * @param {string} fileName - 文件名
 * @returns {string} Element UI 图标类名
 */
export const getFileIcon = (fileName) => {
  const type = getFileType(fileName);
  const ext = getFileExtension(fileName);
  
  const iconMap = {
    // 图片
    image: 'el-icon-picture',
    // PDF
    pdf: 'el-icon-document',
    // 文档
    doc: 'el-icon-document',
    docx: 'el-icon-document',
    xls: 'el-icon-s-grid',
    xlsx: 'el-icon-s-grid',
    ppt: 'el-icon-present',
    pptx: 'el-icon-present',
    txt: 'el-icon-document-copy',
    // 压缩包
    zip: 'el-icon-folder-opened',
    rar: 'el-icon-folder-opened',
    '7z': 'el-icon-folder-opened',
    // 默认
    default: 'el-icon-document'
  };
  
  return iconMap[ext] || iconMap[type] || iconMap.default;
};

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
};