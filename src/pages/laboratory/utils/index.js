/**
 * Created by PanJiaChen on 16/11/18.
 */
import { getWorkSchedule } from '@laboratory/api/Settings/Semester/WorkSchedule'
import Moment from 'moment'
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */

// 获取当前节次
export function getNowfestival () {
  getWorkSchedule(1, 100).then((res) => {
    if (res.code == 0) {
      const nowtime = Moment(new Date()).format('HH:mm')
      res.data.map((item, index) => {
        const beginTime = JSON.stringify(item.beginTime)
        let changebeginTime
        if (beginTime.length == 3) {
          changebeginTime = beginTime.slice(0, 1) + ':' + beginTime.slice(1, 3)
        } else {
          changebeginTime = beginTime.slice(0, 2) + ':' + beginTime.slice(2, 4)
        }
        const endTime = JSON.stringify(item.endTime)
        let changeendTime
        if (endTime.length == 3) {
          changeendTime = endTime.slice(0, 1) + ':' + endTime.slice(1, 3)
        } else {
          changeendTime = endTime.slice(0, 2) + ':' + endTime.slice(2, 4)
        }
        if (
          CompareDate(changebeginTime, nowtime) &&
          CompareDate(nowtime, changeendTime)
        ) {
          sessionStorage.setItem('currentFestival', item.classTimeSn)
          return item.classTimeSn
        }
      })
    }
  })
}

// 比较两个时间的大小
export function CompareDate (t1, t2) {
  var date = new Date()
  var a = t1.split(':')
  var b = t2.split(':')
  return date.setHours(a[0], a[1]) < date.setHours(b[0], b[1])
}

// 计算两个日期之间相差的天数
export function DateDiff (sDate1, sDate2) {
  // sDate1和sDate2是2002-12-18格式
  var aDate, oDate1, oDate2, iDays
  aDate = sDate1.split('-')
  oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]) // 转换为12-18-2002格式
  aDate = sDate2.split('-')
  oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])
  iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24) // 把相差的毫秒数转换为天数
  return iDays
}

// 根据开始周次显示结束周次
export function CutWeek (beginweek, weekOptions) {
  const arr = []
  weekOptions.map((item, index) => {
    if (item.value > beginweek || item.value == beginweek) {
      arr.push({
        label: item.label,
        value: item.value
      })
    }
  })
  return arr
}


// 预约状态换
// 状态转换
export function resvStatusShow (row) {
  const arr = []
  if ((row & 2) > 0) {
    arr.push('未开始')
  }
  if ((row & 4) > 0) {
    arr.push('生效中')
  }
  if ((row & 8) > 0) {
    arr.push('未缴费')
  }
  if ((row & 16) > 0) {
    arr.push('超时违约取消')
  }
  if ((row & 32) > 0) {
    arr.push('已缴费')
  }
  if ((row & 64) > 0) {
    arr.push('已签到')
  }
  if ((row & 128) > 0) {
    arr.push('已结束')
  }
  if ((row & 256) > 0) {
    arr.push('待审核')
  }
  if ((row & 512) > 0) {
    arr.push('审核未通过')
  }
  if ((row & 1024) > 0) {
    arr.push('审核通过')
  }
  if ((row & 2048) > 0) {
    arr.push('暂离')
  }
  return arr.join('；')
}
// 属性转换
export function resvPropertyFormatter (row) {
  if ((row & 1024) > 0) {
    return '延迟下课'
  }
}

// 星期、节次、由汉字换算成数字
export function ENnum (num) {
  if (num == '周一') {
    num = '1'
  } else if (num == '周二') {
    num = '2'
  } else if (num == '周三') {
    num = '3'
  } else if (num == '周四') {
    num = '4'
  } else if (num == '周五') {
    num = '5'
  } else if (num == '周六') {
    num = '6'
  } else if (num == '周七') {
    num = '7'
  } else if (num == '周日') {
    num = '7'
  }
  return num
}

// 星期、节次、由汉字换算成数字
export function CHnum (num) {
  if (num == '1') {
    num = '周一'
  } else if (num == '2') {
    num = '周二'
  } else if (num == '3') {
    num = '周三'
  } else if (num == '4') {
    num = '周四'
  } else if (num == '5') {
    num = '周五'
  } else if (num == '6') {
    num = '周六'
  } else if (num == '7') {
    num = '周日'
  }
  return num
}

// 处理周次排课表中相同节次合并数组
export function mergeArray (array) {
  const obj = {}
  array.forEach((item) => {
    if (obj[item.classTime]) {
      if (Array.isArray(obj[item.classTime])) {
        obj[item.classTime].push(item)
      } else {
        const tmp = obj[item.classTime]
        obj[item.classTime] = []
        obj[item.classTime].push(tmp, item)
      }
    } else {
      obj[item.classTime] = item
    }
  })
  const result = []
  for (const key of Object.keys(obj)) {
    if (Array.isArray(obj[key])) {
      result.push({
        classTime: key,
        array: obj[key]
      })
    } else {
      result.push(obj[key])
    }
  }
  // console.log(result)
  return result
}
// 根据对象的字段合并相同字段值的对象
export function mergeArrayNomral (array, string) {
  const obj = {}
  array.forEach((item) => {
    if (obj[item[string]]) {
      if (Array.isArray(obj[item[string]])) {
        obj[item[string]].push(item)
      } else {
        const tmp = obj[item[string]]
        obj[item[string]] = []
        obj[item[string]].push(tmp, item)
      }
    } else {
      obj[item[string]] = item
    }
  })
  const result = []
  for (const key of Object.keys(obj)) {
    if (Array.isArray(obj[key])) {
      result.push({
        string: key,
        array: obj[key]
      })
    } else {
      result.push(obj[key])
    }
  }
  return result
}

// 转化时间
export function parseTime (time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime (time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj (url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

// 上传一个分钟数 返回x天 x 时 x 分
export function getDHS (num) {
  var d = Math.floor(num / 1440)
  var h = Math.floor((num % 1440) / 60)
  var s = Math.floor((num % 1440) % 60)
  return `${d}天${h}时${s}分`
}

// 判断一个数字中的多选
export function getCheckArr (num, arr) {
  var newArr = []
  if (!num) {
    return []
  }
  arr.forEach((item) => {
    if ((item.value & num) > 0) {
      newArr.push(item.value)
    }
  })
  return newArr
}

// 数组去重
export function unique (arr) {
  // 根据唯一标识orderId来对数组进行过滤
  const res = new Map() // 定义常量 res,值为一个Map对象实例
  // 返回arr数组过滤后的结果，结果为一个数组   过滤条件是，如果res中没有某个键，就设置这个键的值为1
  return arr.filter((arr) => !res.has(arr.OrderId) && res.set(arr.OrderId, 1))
}

export function uniquearr (data) {
  var arr = [] // 定义一个临时数组
  for (var i = 0; i < data.length; i++) {
    // 循环遍历当前数组
    // 判断当前数组下标为i的元素是否已经保存到临时数组
    // 如果已保存，则跳过，否则将此元素保存到临时数组中
    if (arr.indexOf(data[i]) == -1) {
      arr.push(data[i])
    }
  }
  return arr
}

// 删除数组中指定元素
export function remove (arr, val) {
  var index = arr.indexOf(val)
  if (index > -1) {
    arr.splice(index, 1)
  }
  return arr
}

// 判断一个数字中的多选
// export function getCheckArr(num, arr){
//     var newArr = []
//     if (!num) {
//         return []
//     }
//     arr.forEach(item => {
//         if ((item.value & num) > 0) {
//             newArr.push(item.value)
//         }
//     })
//     return newArr
// };

// 获取地址栏参数
export function getUrlKey (paraName) {
  var url = document.location.toString()
  var arrObj = url.split('?')

  if (arrObj.length > 1) {
    var arrPara = arrObj[1].split('&')
    var arr

    for (var i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split('=')

      if (arr != null && arr[0] == paraName) {
        return arr[1]
      }
    }
    return ''
  } else {
    return ''
  }
}

// 比较两个数组不同的元素
export function getArrDifference (arr1, arr2) {
  return arr1.concat(arr2).filter(function(v, i, arr) {
    return arr.indexOf(v) === arr.lastIndexOf(v)
  })
}

export function sortBy (attr, rev) {
  // 第二个参数没有传递 默认升序排列
  if (rev == undefined) {
    rev = 1
  } else {
    rev = rev ? 1 : -1
  }

  return function(a, b) {
    a = a[attr]
    b = b[attr]
    if (a < b) {
      return rev * -1
    }
    if (a > b) {
      return rev * 1
    }
    return 0
  }
}

// 判断一串数字是否是连续的
export function isContinuityNum (num) {
  let array = []
  if (num instanceof Array) {
    array = [...num]
  } else {
    array = Array.from(num.toString()) // 转换为数组
  }

  var i = array[0]
  var isContinuation = true
  for (var e in array) {
    if (array[e] != i) {
      isContinuation = false
      break
    }
    i++
  }
  return isContinuation
}

// 乘法-解决乘法浮点数的问题
export function accMul (arg1, arg2) {
  var m = 0
  var s1 = arg1.toString()
  var s2 = arg2.toString()
  try {
    m += s1.split('.')[1].length
  } catch (e) { }
  try {
    m += s2.split('.')[1].length
  } catch (e) { }
  return (
    (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
    Math.pow(10, m)
  )
}

// 测试图片转二进制
export function convertImageToBlob (imageUrl) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', imageUrl, true)
    xhr.responseType = 'blob'
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      } else {
        reject(new Error(`Error fetching image: ${xhr.statusText}`))
      }
    }
    xhr.onerror = () => {
      reject(new Error('Network error'))
    }
    xhr.send()
  })
}

export function deepClone (source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}
/**
 * 下载文件
 * @param {*} data 文件数据
 */
export function downLoadFile (url, filName) {
  const ApiUrl = window.g.ApiUrl
  const thirdurl = window.g.thirdurl
  const hrefurl = ApiUrl + thirdurl + url
  console.log(hrefurl)
  var blob = new Blob([hrefurl], {
    type: 'application/vnd.openxmlformats-  officedocument.spreadsheetml.sheet;charset=utf-8'
  }) // type这里表示xlsx类型
  const downloadElement = document.createElement('a')
  const href = window.URL.createObjectURL(blob) // 创建下载的链接
  downloadElement.href = hrefurl
  downloadElement.download = filName || '下载'// 下载后文件名
  document.body.appendChild(downloadElement)
  downloadElement.click() // 点击下载
  document.body.removeChild(downloadElement) // 下载完成移除元素
  window.URL.revokeObjectURL(href) // 释放掉blob对象
}
