import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@laboratory/store'
import router from '@laboratory/router/index'
import { setToken } from '@laboratory/utils/auth'

// create an axios instance
const service = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  baseURL: window.g.ApiUrl,
  timeout: 200000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (localStorage.getItem('token')) {
      config.headers['Uni-Access-Token'] = localStorage.getItem('token')
    }
    if (config.XAPIVersion) {
      config.headers['X-API-Version'] = config.XAPIVersion
    } else {
      config.headers['X-API-Version'] = '1.0.0'
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    const token = response.headers['refresh-token']
    if (token) {
      setToken(token)
    }
    const res = response.data
    if (res.code !== 0) {
      Message.closeAll()
      if (res.code == 2) {
        return res
      }
      if (res.code == 1) {
        return res
      }
      if (res.code == 100) {
        Message({
          message: res.message || 'Error',
          type: 'error',
          duration: 1000
        })

        return res
      }
      // 判断未登录
      if (res.code == 300) {
        let num = Number(localStorage.getItem('frequency'))
        localStorage.setItem('frequency', num + 1)
        // 剔除未登录后重复报错
        if ( num < 1) {
          Message({
            message: res.message || 'Error',
            type: 'error',
            duration: 1000
          })
          sessionStorage.removeItem('isLogin')
          sessionStorage.removeItem('roles')
          sessionStorage.removeItem('name')
          sessionStorage.removeItem('userinfo')
          sessionStorage.removeItem('activeName')
          store.state.user.isLogin = 'false'
          router.replace({
            path: '/login'
          })
          return res
        }
      } else if (res.code == 302) {
        store.state.user.isLogin = 'false'
        router.replace({
          path: '/login'
        })
      }
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 1000
      })
    } else {
      return res
    }
  },
  (error) => {
    Message({
      message: error.message,
      type: 'error',
      duration: 1000
    })
    return Promise.reject(error)
  }
)

export default service
