import axios from "axios";
import { MessageBox, Message } from "element-ui";
import store from "@laboratory/store";
import router from "@laboratory/router/index";
import { setToken } from "@laboratory/utils/auth";

// create an axios instance
const service = axios.create({
  //baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  baseURL: window.g.ApiUrl,
  timeout: 200000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (localStorage.getItem("token")) {
      config.headers["Uni-Access-Token"] = localStorage.getItem("token");
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    const token = response.headers["refresh-token"];
    if (token) {
      setToken(token);
    }
    if (res.code !== 0) {
      if (res.code == 2) {
        return res;
      }
      if (res.code == 1) {
        return res;
      }
      if (res.code == 100) {
        return res;
      }
      //判断未登录
      if (res.code == 300) {
        return res;
      } else if (res.code == 302) {
        store.state.user.isLogin = "false";
        router.replace({
          path: "/login",
        });
      }
      Message({
        message: res.message || "Error",
        type: "error",
        duration: 1000,
      });
    } else {
      return res;
    }
  },
  (error) => {
    console.log("err" + error); // for debug
    Message({
      message: error.message,
      type: "error",
      duration: 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
