import axios from "axios";
import { MessageBox, Message } from "element-ui";
import store from "@laboratory/store";
import router from "@laboratory/router/index";
import { getToken } from "@laboratory/utils/auth";
import {
  setToken
} from '@laboratory/utils/auth'

// create an axios instance
const service = axios.create({
  //baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  baseURL: window.g.ApiUrl,
  timeout: 200000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (localStorage.getItem("token")) {
      config.headers["Uni-Access-Token"] = localStorage.getItem("token");
    }
    if (config.XAPIVersion) {
      config.headers['X-API-Version'] = config.XAPIVersion
    } else {
      config.headers['X-API-Version'] = '1.0.0'
    }
    return config;
  },
  (error) => {
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    const token = response.headers["refresh-token"];
    if (token) {
      setToken(token);
    }
    const res = response;
    return res;
  },
  (error) => {
    Message({
      message: error.message,
      type: "error",
      duration: 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
