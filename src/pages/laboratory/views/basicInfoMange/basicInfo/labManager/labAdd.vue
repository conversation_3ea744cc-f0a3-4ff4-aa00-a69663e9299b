<template>
  <layoutContent>
    <!-- 顶部导航栏 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ isEditMode ? "实验室编辑" : "实验室新建" }}</span>
      </div>
      <el-button :disabled="!labUuid" type="primary" @click="handleComplete">完成</el-button>
    </div>

    <!-- 主体内容区 -->
    <div class="lab-add-container">
      <!-- 左侧步骤导航 -->
      <div class="steps-nav">
        <el-steps direction="vertical" :active="currentStep - 1" :space="80">
          <el-step v-for="(step, index) in steps" :key="index" :title="step" :class="{ clickable: true }">
            <div slot="title" @click="handleStepClick(index)" class="step-title">
              <span v-if="index === 0">*</span>
              {{ step }}
            </div>
          </el-step>
        </el-steps>
      </div>

      <!-- 右侧表单内容 -->
      <div class="form-content">
        <!-- 基本信息表单 -->
        <div v-show="currentStep === 1" class="step-form">
          <div class="form-title">基本信息</div>
          <lab-basic-info
            ref="labBasicInfo"
            :labUuid="labUuid"
            :isEditMode="isEditMode"
            :initialData="basicForm"
            @save-success="handleBasicInfoSaveSuccess"
          />
        </div>

        <!-- 实验室介绍表单 -->
        <div v-show="currentStep === 2" class="step-form">
          <div class="form-title">实验室介绍</div>
          <div class="form-actions">
            <el-button class="save-btn" type="primary" @click="handleSaveIntro" :loading="introSaveLoading">保存</el-button>
          </div>
          <div class="intro-form">
            <rich-text-editor
              v-model="introForm.roomLabDesc"
              :height="400"
              placeholder="请输入实验室介绍内容..."
              @upload-success="handleUploadSuccess"
            ></rich-text-editor>
          </div>
        </div>

        <!-- 人员信息表单 -->
        <div v-show="currentStep === 3" class="step-form">
          <div class="form-title">人员信息</div>
          <personnel-info
            v-if="labUuid"
            :labUuid="labUuid"
            :resourceType="2"
            :screenWidth="screenWidth"
            @next-step="handlePersonnelNextStep"
          ></personnel-info>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取实验室ID后才能管理人员信息
          </div>
        </div>

        <!-- 房间信息表单 -->
        <div v-show="currentStep === 4" class="step-form">
          <div class="form-title">房间信息</div>
          <lab-rooms-info v-if="labUuid" :labUuid="labUuid" :screenWidth="screenWidth" @next-step="handleRoomsNextStep"></lab-rooms-info>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取实验室ID后才能管理房间信息
          </div>
        </div>

        <!-- 仪器设备表单 -->
        <div v-show="currentStep === 5" class="step-form">
          <div class="form-title">仪器设备</div>
          <center-device-info
            v-if="labUuid"
            :labUuid="labUuid"
            :resourceType="2"
            :activeTab="currentStep === 5 ? 'device' : ''"
            :screenWidth="screenWidth"
            @next-step="handleDeviceNextStep"
          ></center-device-info>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取实验室ID后才能管理仪器设备
          </div>
        </div>

        <!-- 技术资料表单 -->
        <div v-show="currentStep === 6" class="step-form">
          <div class="form-title">技术资料</div>
          <center-regulations v-if="labUuid" :labUuid="labUuid" :resourceType="2" :screenWidth="screenWidth"></center-regulations>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取实验室ID后才能管理技术资料
          </div>
        </div>
      </div>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import { labInfoSave, labInfoUpdate, labInfoDetail, labInfoSummaryUpdate } from "@laboratory/api/laboratory/laboratory";
import { getDeptList, getDictAll } from "@laboratory/api/laboratory/basicMessage";
import RichTextEditor from "@laboratory/components/RichTextEditor/index.vue";
import PersonnelInfo from "@centerManage/centerAddCom/PersonnelInfo.vue";
import LabBasicInfo from "./labAddCom/LabBasicInfo.vue";
import LabRoomsInfo from "./labAddCom/LabRoomsInfo.vue";
import CenterDeviceInfo from "@centerManage/centerAddCom/CenterDeviceInfo.vue";
import CenterRegulations from "@centerManage/centerAddCom/CenterRegulations.vue";

export default {
  name: "labAdd",
  components: {
    layoutContent,
    RichTextEditor,
    PersonnelInfo,
    LabBasicInfo,
    LabRoomsInfo,
    CenterDeviceInfo,
    CenterRegulations,
  },
  data() {
    return {
      // 步骤配置
      steps: ["基础信息", "实验室介绍", "人员信息", "房间信息", "仪器设备", "技术资料"],
      currentStep: 1,

      // 屏幕宽度
      screenWidth: 0,

      // 模式标识
      isEditMode: false,
      loading: false,

      // 基本信息表单
      basicForm: {
        roomLabName: "",
        roomLabSn: "",
        establishmentDate: "",
        labAcademic: "",
        usageStatus: 1,
        deptId: "",
        roomLabCategory: "",
        contactInfo: "",
        labAddress: "",
        roomLabImage: "",
      },

      // 表单验证规则
      basicRules: {
        roomLabName: [{ required: true, message: "请输入实验室名称", trigger: "blur" }],
        roomLabSn: [{ required: true, message: "请输入实验室编号", trigger: "blur" }],
        establishmentDate: [{ required: true, message: "请选择建设年份", trigger: "change" }],
        deptId: [{ required: true, message: "请选择所属部门", trigger: "change" }],
        usageStatus: [{ required: true, message: "请选择使用状态", trigger: "change" }],
      },

      // 下拉选项
      deptOptions: [], // 部门选项
      labCategoryOptions: [], // 实验室类别选项
      labTypeOptions: [], // 实验室类型选项
      labAcademicOptions: [], // 所属学科选项

      // 保存状态
      saveLoading: false,
      labUuid: "", // 创建成功后的实验室UUID
      labData: null, // 保存返回的完整实验室数据对象

      // 实验室介绍表单
      introForm: {
        roomLabDesc: "",
      },
      introSaveLoading: false,
    };
  },
  created() {
    // 判断当前是否为编辑模式
    this.isEditMode = this.$route.name === "labEdit";

    // 如果是编辑模式，则获取 UUID 并调用详情接口
    if (this.isEditMode) {
      const uuid = this.$route.params.uuid;
      if (uuid) {
        this.labUuid = uuid;
        this.fetchLabDetail();
      } else {
        this.$message.warning("缺少必要的参数");
        this.goBack();
      }
    }

    // 获取下拉选项数据
    this.fetchOptions();

    // 初始化屏幕宽度
    this.handleResize();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      if (window.innerWidth > 1640) {
        this.screenWidth = window.innerWidth - 620;
      } else {
        this.screenWidth = window.innerWidth - 580;
      }
    },

    // 获取下拉选项数据
    fetchOptions() {
      // 获取部门选项
      getDeptList().then((res) => {
        if (res.code === 0) {
          this.deptOptions = (res.data || []).map((item) => ({
            value: item.deptId,
            label: item.deptName,
          }));
        }
      });

      // 获取实验室类别字典
      getDictAll({ codeType: "1014" }).then((res) => {
        if (res.code === 0) {
          this.labCategoryOptions = (res.data || []).map((item) => ({
            value: item.codeValue,
            label: item.name,
          }));
        }
      });

      // 获取实验室类型字典
      getDictAll({ codeType: "1015" }).then((res) => {
        if (res.code === 0) {
          this.labTypeOptions = (res.data || []).map((item) => ({
            value: item.codeValue,
            label: item.name,
          }));
        }
      });

      // 获取所属学科字典
      getDictAll({ codeType: "1003" }).then((res) => {
        if (res.code === 0) {
          this.labAcademicOptions = (res.data || []).map((item) => ({
            value: item.codeValue,
            label: item.name,
          }));
        }
      });
    },

    // 获取实验室详情
    fetchLabDetail() {
      if (!this.labUuid) return;

      this.loading = true;
      labInfoDetail({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 回填基本信息
            this.basicForm = {
              roomLabName: res.data.roomLabName || "",
              roomLabSn: res.data.roomLabSn || "",
              establishmentDate: res.data.establishmentDate || "",
              labAcademic: res.data.labAcademic || "",
              usageStatus: res.data.usageStatus || 1,
              deptId: res.data.deptId || "",
              roomLabCategory: res.data.roomLabCategory || "",
              contactInfo: res.data.contactInfo || "",
              labAddress: res.data.labAddress || "",
              roomLabImage: res.data.roomLabImage || "",
              responsiblePerson: res.data.responsiblePerson || "",
              safetyOfficer: res.data.safetyOfficer || "",
              roomLabType: res.data.roomLabType || "",
            };

            // 回填实验室介绍
            this.introForm.roomLabDesc = res.data.roomLabDesc || "";

            // 保存完整的实验室数据
            this.labData = res.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理步骤点击事件
    handleStepClick(index) {
      // 在编辑模式下，允许自由切换步骤
      if (this.isEditMode) {
        this.switchStep(index + 1);
        return;
      }

      // 在新增模式下，检查是否已有labUuid
      if (!this.labUuid) {
        this.$message.warning("请先完成基本信息的保存");
        this.switchStep(1);
        return;
      }
      // 直接使用 index + 1 作为步骤值，因为 currentStep 是从 1 开始的
      this.switchStep(index + 1);
    },

    // 切换步骤 - 简化逻辑，允许自由切换
    switchStep(step) {
      this.currentStep = step;

      // 如果切换到人员信息步骤，且已有labUuid，则确保人员列表已加载
      if (step === 3 && this.labUuid) {
        console.log("进入人员信息步骤，实验室UUID:", this.labUuid);
      }
    },

    // 返回列表页或详情页
    goBack() {
      if (this.isEditMode && this.$route.query.from === "detail") {
        // 如果是从详情页进入的编辑页，则返回详情页
        this.$router.push({
          name: "labDetail",
          params: { uuid: this.labUuid },
        });
      } else {
        // 否则返回列表页
        this.$router.push({ name: "labManager" });
      }
    },

    // 处理基本信息保存成功
    handleBasicInfoSaveSuccess(data) {
      // 更新UUID
      this.labUuid = data.uuid;
      // 更新表单数据
      this.basicForm = { ...this.basicForm, ...data };
      // 更新实验室数据
      this.labData = { ...this.labData, ...data };
      // 跳转到下一步
      this.switchStep(this.currentStep + 1);
    },

    // 保存实验室介绍
    handleSaveIntro() {
      // 检查是否已有labUuid
      if (!this.labUuid) {
        this.$message.warning("请先完成基本信息的保存");
        this.switchStep(1);
        return;
      }

      if (!this.introForm.roomLabDesc || this.introForm.roomLabDesc.trim() === "") {
        this.$message.warning("请输入实验室介绍内容");
        return;
      }

      this.introSaveLoading = true;

      // 构造更新参数，只包含uuid和roomLabDesc
      const params = {
        uuid: this.labUuid,
        roomLabDesc: this.introForm.roomLabDesc,
      };

      // 调用专门的实验室介绍更新API
      labInfoSummaryUpdate(params)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("实验室介绍保存成功");
            // 更新本地保存的实验室数据的介绍字段
            if (this.labData) {
              this.labData.roomLabDesc = this.introForm.roomLabDesc;
            }
            // 自动进入下一步
            this.switchStep(this.currentStep + 1);
          }
        })
        .finally(() => {
          this.introSaveLoading = false;
        });
    },

    // 完成按钮点击事件
    handleComplete() {
      // 最终完成操作，可以进行最后的保存或跳转
      const confirmMessage = this.isEditMode ? "确认完成实验室编辑?" : "确认完成实验室创建?";

      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.goBack(); // 返回列表页
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 处理上传成功后的逻辑
    handleUploadSuccess(event) {
      console.log("上传成功:", event);
    },

    // 处理人员信息下一步事件
    handlePersonnelNextStep() {
      // 切换到下一个步骤（房间信息）
      this.switchStep(4);
    },

    // 处理房间信息下一步事件
    handleRoomsNextStep() {
      // 切换到下一个步骤（仪器设备）
      this.switchStep(5);
    },

    // 处理仪器设备下一步事件
    handleDeviceNextStep() {
      // 切换到下一个步骤（技术资料）
      this.switchStep(6);
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.lab-add-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 600px;

  .form-content {
    flex: 1;
    padding: 20px;

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: block;
        width: 4px;
        height: 16px;
        background-color: #2468f2;
        margin-right: 8px;
      }
    }

    .step-form {
      max-width: 100%;
    }

    .intro-form {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 10px;
    }

    .form-actions {
      display: flex;
      margin: 20px 0;

      .save-btn {
        margin-left: auto;
      }
    }

    .placeholder {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }

    .empty-tip {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}
.full-width-item {
  width: 500px;
}
.photo-uploader {
  ::v-deep .el-upload {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}
</style>
