<template>
  <div class="lab-basic-info">
    <div class="form-actions">
      <el-button type="primary" @click="saveBasicInfo" :loading="saveLoading">保存</el-button>
    </div>
    <el-form :model="basicForm" :rules="basicRules" ref="basicForm" :inline="true" label-width="110px">
      <el-form-item label="实验室名称：" prop="roomLabName">
        <el-input v-model="basicForm.roomLabName" placeholder="请输入实验室名称"></el-input>
      </el-form-item>
      <el-form-item label="实验室编号：" prop="roomLabSn">
        <el-input v-model="basicForm.roomLabSn" placeholder="请输入实验室编号"></el-input>
      </el-form-item>
      <el-form-item label="建设年份：" prop="establishmentDate">
        <el-date-picker style="width: 177px" v-model="basicForm.establishmentDate" type="date" placeholder="选择建设年份"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="所属学科：" prop="labAcademic">
        <el-select  v-model="basicForm.labAcademic" placeholder="请选择所属学科">
          <el-option v-for="item in labAcademicOptions" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="使用状态：" prop="usageStatus">
        <el-select v-model="basicForm.usageStatus" placeholder="请选择使用状态">
          <el-option label="在用" :value="1"></el-option>
          <el-option label="停用" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属部门：" prop="deptId">
        <el-select v-model="basicForm.deptId" filterable placeholder="请选择所属部门">
          <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实验室类别：" prop="roomLabCategory">
        <el-select v-model="basicForm.roomLabCategory" placeholder="请选择实验室类别">
          <el-option v-for="item in labCategoryOptions" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实验室类型：" prop="roomLabType">
        <el-select v-model="basicForm.roomLabType" placeholder="请选择实验室类型">
          <el-option v-for="item in labTypeOptions" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="联系方式：" prop="contactInfo">
        <el-input v-model="basicForm.contactInfo" placeholder="请输入联系方式"></el-input>
      </el-form-item>
      <el-form-item label="管理员：" prop="responsiblePerson">
        <el-input v-model="basicForm.responsiblePerson" placeholder="请输入管理员"></el-input>
      </el-form-item>
      <el-form-item label="安全管理员：" prop="safetyOfficer">
        <el-input v-model="basicForm.safetyOfficer" placeholder="请输入安全管理员"></el-input>
      </el-form-item>
      <el-form-item label="地址：" prop="labAddress" style="display: block;" >
        <el-input type="textarea" :rows="2" class="full-width-item" v-model="basicForm.labAddress"
          placeholder="请输入地址"></el-input>
      </el-form-item>
      <el-row>
        <el-col>
          <el-form-item label="上传照片：">
            <file-uploader v-model="basicForm.roomLabImage" type="image" :maxSize="2" tip="支持JPG、PNG格式，不超过2MB"
              class="photo-uploader" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <div class="form-actions">
      <el-button type="primary" @click="saveBasicInfo" :loading="saveLoading">保存</el-button>
    </div> -->
  </div>
</template>

<script>
import FileUploader from "@laboratory/components/FileUploader/index.vue";
import { labInfoSave, labInfoUpdate } from "@laboratory/api/laboratory/laboratory";
import { getDeptList, getDictAll } from "@laboratory/api/laboratory/basicMessage";

export default {
  name: "LabBasicInfo",
  components: {
    FileUploader,
  },
  props: {
    // 实验室UUID，编辑模式时传入
    labUuid: {
      type: String,
      default: "",
    },
    // 是否为编辑模式
    isEditMode: {
      type: Boolean,
      default: false,
    },
    // 初始表单数据
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 基本信息表单
      basicForm: {
        roomLabName: "",
        roomLabSn: "",
        establishmentDate: "",
        labAcademic: "",
        usageStatus: 1,
        deptId: "",
        roomLabCategory: "",
        roomLabType: "",
        contactInfo: "",
        responsiblePerson: "",
        safetyOfficer: "",
        labAddress: "",
        roomLabImage: "",
      },

      // 表单验证规则
      basicRules: {
        roomLabName: [{ required: true, message: "请输入实验室名称", trigger: "blur" }],
        roomLabSn: [{ required: true, message: "请输入实验室编号", trigger: "blur" }],
        establishmentDate: [{ required: true, message: "请选择建设年份", trigger: "change" }],
        deptId: [{ required: true, message: "请选择所属部门", trigger: "change" }],
        usageStatus: [{ required: true, message: "请选择使用状态", trigger: "change" }],
      },

      // 下拉选项
      deptOptions: [], // 部门选项
      labCategoryOptions: [], // 实验室类别选项
      labTypeOptions: [], // 实验室类型选项
      labAcademicOptions: [], // 所属学科选项

      // 保存状态
      saveLoading: false,
    };
  },
  watch: {
    // 监听初始数据变化
    initialData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.basicForm = { ...this.basicForm, ...newVal };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    // 获取下拉选项数据
    this.fetchOptions();
  },
  methods: {
    // 获取下拉选项数据
    fetchOptions() {
      // 获取部门选项
      getDeptList().then((res) => {
        if (res.code === 0) {
          this.deptOptions = (res.data || []).map((item) => ({
            value: item.deptId,
            label: item.deptName,
          }));
        }
      });

      // 获取实验室类别字典
      getDictAll({ codeType: "1014" }).then((res) => {
        if (res.code === 0) {
          this.labCategoryOptions = (res.data || []).map((item) => ({
            value: item.codeValue,
            label: item.name,
          }));
        }
      });

      // 获取实验室类型字典
      getDictAll({ codeType: "1015" }).then((res) => {
        if (res.code === 0) {
          this.labTypeOptions = (res.data || []).map((item) => ({
            value: item.codeValue,
            label: item.name,
          }));
        }
      });

      // 获取所属学科字典
      getDictAll({ codeType: "1003" }).then((res) => {
        if (res.code === 0) {
          this.labAcademicOptions = (res.data || []).map((item) => ({
            value: item.codeValue,
            label: item.name,
          }));
        }
      });
    },

    // 保存基本信息
    saveBasicInfo() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          // 构造保存参数
          let params = {
            ...this.basicForm,
          };

          // 判断是新增还是更新
          if (this.labUuid) {
            // 已有UUID，调用更新接口
            params.uuid = this.labUuid;
            labInfoUpdate(params)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success("更新成功");
                  // 触发保存成功事件
                  this.$emit("save-success", params);
                } else {
                  this.$message.error(res.message || "更新失败");
                }
              })
              .finally(() => {
                this.saveLoading = false;
              });
          } else {
            // 新增接口
            labInfoSave(params)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success("保存成功");
                  // 触发保存成功事件
                  this.$emit("save-success", res.data);
                } else {
                  this.$message.error(res.message || "保存失败");
                }
              })
              .finally(() => {
                this.saveLoading = false;
              });
          }
        }
      });
    },

    // 验证表单
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.basicForm.validate((valid) => {
          resolve(valid);
        });
      });
    },

    // 获取表单数据
    getFormData() {
      return { ...this.basicForm };
    },

    // 重置表单
    resetForm() {
      this.$refs.basicForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-basic-info {
  .form-actions {
    display: flex;
    margin-top: 20px;

    .el-button {
      margin-left: auto;
    }
  }

  .full-width-item {
    width: 500px;
  }
}
::v-deep.el-form-item--small .el-form-item__content,.el-select--small {
  width: 177px !important;
}
</style>
