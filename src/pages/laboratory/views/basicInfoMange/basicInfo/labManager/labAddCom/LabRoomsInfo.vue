<template>
  <div class="lab-rooms-info">
    <div class="header-actions">
      <div class="left-actions">
        <el-button plain type="primary" icon="el-icon-plus" @click="openBindDialog">绑定房间</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="batchUnbind" :disabled="selectedRooms.length === 0">
          批量解绑
        </el-button>
        <!-- 导入导出功能 -->
        <import-export-buttons
          :uuid="labUuid"
          uuidName="labUuid"
          fieldName="房间号"
          templateName="房间导入模板"
          :exampleData="['101', '102', '103']"
          exportFilePrefix="房间列表"
          importParamField="roomSnList"
          :exportApi="handleExportApi"
          :importApi="handleImportApi"
          @import-success="handleImportSuccess"
        />
        <el-button class="nextStep" type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>

    <div class="table-container">
      <table-pagetion
        ref="roomTable"
        :loading="loading"
        :table-data="roomList"
        :columns="columns"
        :screenWidth="screenWidth"
        :pagination="paginationConfig"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 面积列 -->
        <template #roomArea="{ row }">
          {{ row.roomArea ? row.roomArea / 100 + "㎡" : "0" }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" class="delete-btn" @click="handleUnbind(row)">解绑</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 绑定房间弹窗 -->
    <rooms-dialog :visible.sync="bindDialogVisible" :labUuid="labUuid" :boundRoomIds="boundRoomIds" @success="handleBindSuccess" />

    <!-- 下一步按钮 -->
    <!-- <div class="bottom-actions">
      <el-button type="primary" @click="nextStep">下一步</el-button>
    </div> -->
  </div>
</template>

<script>
import {
  labRoomBindPage,
  labRoomBindDelete,
  labRoomBindDevIds,
  labRoomExport,
  labRoomImportant,
} from "@laboratory/api/laboratory/laboratory";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";
import ImportExportButtons from "@laboratory/components/ImportExportButtons/index.vue";
import RoomsDialog from "./RoomsDialog.vue";

export default {
  name: "LabRoomsInfo",
  components: {
    TablePagetion,
    ImportExportButtons,
    RoomsDialog,
  },
  props: {
    labUuid: {
      type: String,
      required: true,
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    paginationConfig() {
      return {
        currentPage: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        pageSizes: [10, 20, 30, 50],
        total: this.total,
      };
    },
  },
  data() {
    return {
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      loading: false,
      roomList: [],
      selectedRooms: [],
      total: 0,
      boundRoomIds: [], // 已绑定的房间ID列表
      bindDialogVisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "index", label: "序号", type: "index", width: "80" },
        { prop: "campusName", label: "所属校区", minWidth: "120" },
        { prop: "buildingName", label: "所属楼宇", minWidth: "120" },
        { prop: "floorName", label: "所属楼层", minWidth: "100" },
        { prop: "roomName", label: "房间名称", minWidth: "150" },
        { prop: "roomSn", label: "房间号", minWidth: "120" },
        { prop: "roomArea", label: "房屋使用面积", slotName: "roomArea", minWidth: "130" },
        { prop: "operation", label: "操作", fixed: "right", width: "100", slotName: "operation" },
      ],
    };
  },
  created() {
    if (this.labUuid) {
      this.fetchRoomList();
      this.fetchBoundRoomIds();
    }
  },
  methods: {
    fetchRoomList() {
      if (!this.labUuid) return;

      this.loading = true;
      const params = {
        ...this.queryParams,
        labUuid: this.labUuid,
      };

      labRoomBindPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.roomList = res.data.map((item) => {
              item = {
                ...item,
                ...item.roomInfo,
                uuid: item.uuid,
              };
              return item;
            });
            this.total = res.count || 0;
          } else {
            this.$message.error(res.message || "获取房间列表失败");
            this.roomList = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          console.error("获取房间列表失败:", error);
          this.$message.error("获取房间列表失败");
          this.roomList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    handleSelectionChange(selection) {
      this.selectedRooms = selection;
    },

    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.fetchRoomList();
    },

    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.fetchRoomList();
    },

    openBindDialog() {
      this.bindDialogVisible = true;
      // 获取最新的已绑定房间ID列表
      this.fetchBoundRoomIds();
    },

    handleBindSuccess() {
      this.$message.success("绑定成功");
      this.fetchRoomList();
      this.fetchBoundRoomIds();
    },

    handleView(row) {
      // TODO: 实现查看房间详情
      console.log("查看房间详情", row);
    },

    handleUnbind(row) {
      this.$confirm("确认解绑该房间?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.unbindRooms([row.uuid]);
        })
        .catch(() => {});
    },

    batchUnbind() {
      if (this.selectedRooms.length === 0) {
        this.$message.warning("请选择要解绑的房间");
        return;
      }

      this.$confirm(`确认解绑选中的 ${this.selectedRooms.length} 个房间?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const uuids = this.selectedRooms.map((item) => item.uuid);
          this.unbindRooms(uuids);
        })
        .catch(() => {});
    },

    unbindRooms(uuidSet) {
      this.loading = true;
      labRoomBindDelete({ uuidSet })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("解绑成功");
            this.fetchRoomList();
            this.fetchBoundRoomIds();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 导入成功回调
    handleImportSuccess(importResult) {
      console.log("导入完成:", importResult);
      // 刷新房间列表
      this.fetchRoomList();
      this.fetchBoundRoomIds();

      // 根据导入结果显示不同的消息
      // if (importResult.failed > 0) {
      //   this.$message.warning(`导入完成！成功：${importResult.success}条，失败：${importResult.failed}条`);
      // } else {
      //   this.$message.success(`导入成功！共导入${importResult.success}条房间数据`);
      // }
    },
    // 导出接口处理
    async handleExportApi(params) {
      return labRoomExport(params);
    },

    // 导入接口处理
    async handleImportApi(params) {
      return labRoomImportant(params);
    },

    // 获取已绑定房间ID列表
    fetchBoundRoomIds() {
      if (!this.labUuid) return;

      const params = { uuid: this.labUuid };
      labRoomBindDevIds(params)
        .then((res) => {
          if (res.code === 0) {
            this.boundRoomIds = res.data || [];
          }
        })
        .catch((error) => {
          console.error("获取已绑定房间ID列表失败:", error);
        });
    },

    nextStep() {
      this.$emit("next-step");
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-rooms-info {
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .left-actions {
      width: 100%;
      display: flex;
      .nextStep {
        margin-left: auto;
      }
    }
  }

  .search-container {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;
      }
    }
  }

  .table-container {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .next-step-container {
    justify-content: center;
    padding: 20px 0;
  }

  .delete-btn {
    color: #f56c6c;
  }
  .bottom-actions {
    margin-top: 10px;
    margin-left: 50px;
  }
}
</style>
