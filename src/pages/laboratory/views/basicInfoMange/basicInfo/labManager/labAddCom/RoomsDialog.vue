<template>
  <el-dialog title="绑定房间" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
    <!-- 搜索区域 -->
    <div class="filter-style">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="存放地点">
          <el-cascader
            v-model="storageLocationCascader"
            :options="roomTreeOptions"
            :props="cascaderProps"
            placeholder="请选择存放地点"
            clearable
            filterable
            @change="handleStorageLocationChange"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="房间号">
          <el-input v-model="searchForm.roomSn" placeholder="请输入房间号"></el-input>
        </el-form-item>
        <el-form-item label="房间名称">
          <el-input v-model="searchForm.roomName" placeholder="请输入房间名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search">搜索</el-button>
          <el-button @click="resetSearch" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 房间列表 -->
    <div class="room-list">
      <table-pagetion
        ref="roomTable"
        :loading="loading"
        :table-data="roomList"
        :columns="columns"
        :pagination="paginationConfig"
        :selectable="checkRowSelectable"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 面积列 -->
        <template #roomArea="{ row }">
          {{ row.roomArea ? row.roomArea / 100 + "㎡" : "0" }}
        </template>
        <!-- 操作列 -->
        <template #bindingStatus="{ row }">
          <span style="color: #00A870" v-if="row.bindingStatus === 0">未绑定</span>
          <span style="color: #333333" v-else-if="row.bindingStatus === 1">已绑定</span>
          <span style="color: #333333" v-else>未知状态</span>
        </template>
      </table-pagetion>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" :loading="bindLoading">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="bindLoading" :disabled="localBoundRoomIds.length === 0">
        确认绑定 ({{ localBoundRoomIds.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { labRoomBindSave, roomTree } from "@laboratory/api/laboratory/laboratory";
import { roomPage } from "@laboratory/api/laboratory/laboratory";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";

export default {
  name: "RoomsDialog",
  components: {
    TablePagetion,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    labUuid: {
      type: String,
      required: true,
    },
    boundRoomIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      loading: false,
      bindLoading: false,
      roomList: [],
      selectedRooms: [],
      total: 0,
      searchForm: {
        roomSn: "",
        roomName: "",
        campusName: "",
        buildingName: "",
        floorName: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 本地存储的当前绑定状态的房间ID列表（动态维护）
      localBoundRoomIds: [],
      // 标志：是否正在恢复选中状态（避免触发选择变化事件）
      isRestoringSelection: false,
      // 级联选择器相关
      storageLocationCascader: [],
      roomTreeOptions: [],
      cascaderProps: {
        value: "id",
        label: "name",
        children: "children",
        emitPath: true, // 返回完整路径
        checkStrictly: true, // 可以选择任意一级
        leaf: (node, resolve) => {
          // 当节点层级达到3（楼层）时，设置为叶子节点，不再展开
          return node.level >= 3;
        },
      },
      columns: [
        { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "index", label: "序号", type: "index", width: "80" },
        { prop: "campusName", label: "所属校区", minWidth: "120" },
        { prop: "buildingName", label: "所属楼宇", minWidth: "120" },
        { prop: "floorName", label: "所属楼层", minWidth: "100" },
        { prop: "roomName", label: "房间名称", minWidth: "150" },
        { prop: "roomSn", label: "房间号", minWidth: "120" },
        { prop: "roomArea", label: "房屋使用面积", slotName: "roomArea", minWidth: "130" },
        { prop: "bindingStatus", slotName: "bindingStatus", label: "绑定状态", width: "100", align: "center", fixed: "right" },
      ],
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    paginationConfig() {
      return {
        currentPage: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        pageSizes: [10, 20, 30, 50],
        total: this.total,
      };
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initDialog();
        this.fetchRoomTree();
      } else {
        this.resetData();
      }
    },
    boundRoomIds: {
      handler(newVal) {
        // 初始化时将传入的已绑定ID列表赋值给本地存储
        this.localBoundRoomIds = newVal && newVal.length ? [...newVal] : [];
      },
      immediate: true,
    },
  },
  methods: {
    // 检查行是否可选择
    checkRowSelectable(row, index) {
      // 如果房间已绑定且不在传入的boundRoomIds中，则禁用勾选
      if (row.bindingStatus === 1 && !this.boundRoomIds.includes(row.roomId)) {
        return false;
      }
      return true;
    },

    // 初始化弹窗
    initDialog() {
      this.queryParams.pageNum = 1;
      this.selectedRooms = [];
      this.storageLocationCascader = [];

      // 如果表格组件已经渲染，则清除选择
      if (this.$refs.roomTable) {
        this.$refs.roomTable.clearSelection();
      }

      this.fetchRoomList();
    },

    // 获取房间树数据
    fetchRoomTree() {
      roomTree()
        .then((res) => {
          if (res.code === 0) {
            // 过滤房间树数据，只保留到第三层（楼层）
            this.roomTreeOptions = this.filterRoomTreeToThreeLevels(res.data || []);
          }
        })
        .catch((error) => {
          console.error("获取房间树数据失败:", error);
        });
    },

    // 过滤房间树数据，只保留到第三层
    filterRoomTreeToThreeLevels(treeData) {
      return treeData.map((node) => {
        const newNode = { ...node };

        if (node.level === 3) {
          // 第三层（楼层）不再有子节点
          delete newNode.children;
        } else if (node.children && node.children.length > 0) {
          // 递归处理子节点
          newNode.children = this.filterRoomTreeToThreeLevels(node.children);
        }

        return newNode;
      });
    },

    // 处理存放地点级联选择器变化
    handleStorageLocationChange(value) {
      // 清空之前的选择
      this.searchForm.campusName = "";
      this.searchForm.buildingName = "";
      this.searchForm.floorName = "";

      if (value && value.length > 0) {
        // 根据选择的层级设置对应的查询参数（只需要三层：校区、楼宇、楼层）
        this.setQueryParamsByLevel(value);
      }
    },

    // 根据选择的层级设置查询参数（只处理三层）
    setQueryParamsByLevel(selectedValues) {
      if (!selectedValues || selectedValues.length === 0) return;

      let currentOptions = this.roomTreeOptions;

      for (let i = 0; i < selectedValues.length; i++) {
        const value = selectedValues[i];
        const option = currentOptions.find((item) => item.id === value);
        if (option) {
          // 根据层级设置对应的查询参数
          // level: 1:校区 2:楼宇 3:楼层 (房间层级我们不需要)
          switch (option.level) {
            case 1:
              this.searchForm.campusName = option.name;
              break;
            case 2:
              this.searchForm.buildingName = option.name;
              break;
            case 3:
              this.searchForm.floorName = option.name;
              break;
          }
          currentOptions = option.children || [];
        }
      }
    },

    fetchRoomList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        roomSn: this.searchForm.roomSn || undefined,
        roomName: this.searchForm.roomName || undefined,
        campusName: this.searchForm.campusName || undefined,
        buildingName: this.searchForm.buildingName || undefined,
        floorName: this.searchForm.floorName || undefined,
      };

      roomPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.roomList = res.data.map((item) => {
              item = {
                ...item,
                ...item.roomInfo,
              };
              return item;
            });
            this.total = res.count || 0;

            // 根据 localBoundRoomIds 恢复当前页面的选中状态
            this.$nextTick(() => {
              this.restoreCurrentPageSelection();
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 根据 localBoundRoomIds 恢复当前页面的选中状态
    restoreCurrentPageSelection() {
      if (!this.$refs.roomTable) return;

      // 找出当前页面中在 localBoundRoomIds 中的房间
      const roomsToSelect = this.roomList.filter((room) => this.localBoundRoomIds.includes(room.roomId));
      // 设置标志，表示正在恢复选中状态
      this.isRestoringSelection = true;

      // 先清除当前页面的选择
      this.$refs.roomTable.clearSelection();

      // 如果有需要选中的房间，则选中它们
      if (roomsToSelect.length) {
        this.$nextTick(() => {
          roomsToSelect.forEach((row) => {
            this.$refs.roomTable.toggleRowSelection(row, true);
          });
          // 延迟更长时间确保选择操作完全完成
          setTimeout(() => {
            this.isRestoringSelection = false;
          }, 500);
        });
      } else {
        // 如果没有需要选中的房间，直接恢复事件
        setTimeout(() => {
          this.isRestoringSelection = false;
        }, 200);
      }
    },

    handleSelectionChange(selection) {
      // 如果正在恢复选中状态，则跳过处理，避免干扰绑定状态
      if (this.isRestoringSelection) {
        return;
      }
      // 更新当前页面选中的房间
      this.selectedRooms = selection;

      // 更新选中状态
      this.updateSelectedRoomsState(selection);
    },

    // 更新选中状态的方法（抽取公共逻辑）
    updateSelectedRoomsState(selection) {
      // 1. 获取当前页面所有房间的ID
      const currentPageRoomIds = this.roomList.map((room) => room.roomId);

      // 2. 从 localBoundRoomIds 中移除当前页面的所有房间ID（无论是否选中）
      const otherPagesRoomIds = this.localBoundRoomIds.filter((roomId) => !currentPageRoomIds.includes(roomId));

      // 3. 将当前页面选中的房间ID添加到其他页面的选中ID中
      const selectedRoomIds = selection.map((room) => room.roomId);
      this.localBoundRoomIds = [...otherPagesRoomIds, ...selectedRoomIds];
    },

    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.fetchRoomList();
    },

    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.fetchRoomList();
    },

    handleSearch() {
      this.queryParams.pageNum = 1;
      this.fetchRoomList();
    },

    resetSearch() {
      this.searchForm = {
        roomSn: "",
        roomName: "",
        campusName: "",
        buildingName: "",
        floorName: "",
      };
      this.storageLocationCascader = [];
      this.queryParams.pageNum = 1;
      this.fetchRoomList();
    },

    handleConfirm() {
      if (this.localBoundRoomIds.length === 0) {
        this.$message.warning("请选择要绑定的房间");
        return;
      }

      const roomIds = this.localBoundRoomIds;
      this.bindLoading = true;
      labRoomBindSave({
        labUuid: this.labUuid,
        roomIds: roomIds,
      }).then((res) => {
        if (res.code === 0) {
          this.$message.success("绑定成功");
          this.$emit("success");
          this.handleClose();
        }
        this.bindLoading = false;
      });
    },

    handleClose() {
      this.dialogVisible = false;
    },

    resetData() {
      this.selectedRooms = [];
      this.roomList = [];
      this.localBoundRoomIds = [];
      this.isRestoringSelection = false;
      this.storageLocationCascader = [];
      this.searchForm = {
        roomSn: "",
        roomName: "",
        campusName: "",
        buildingName: "",
        floorName: "",
      };
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.room-list {
  margin-bottom: 20px;
}
</style>
