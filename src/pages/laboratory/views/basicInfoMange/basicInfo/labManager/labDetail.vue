<template>
  <layoutContent>
    <!-- 返回按钮和编辑按钮放在search插槽中 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ labDetail.roomLabName || "加载中..." }}</span>
      </div>
      <el-button type="primary" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
    </div>

    <!-- 使用封装的顶部信息卡片组件 -->
    <info-detail-header
      :centerDetail="labDetail"
      :title="labDetail.roomLabName || '加载中...'"
      :image-path="labDetail.roomLabImage"
      :image-prefix="imageSrc"
      :info-items="headerInfoItems"
      :loading="loading"
    >
      <div slot="dept">
        <div class="center-dept on-line" v-if="labDetail.usageStatus === 1">
          在用
        </div>
        <div class="center-dept un-line" v-else-if="labDetail.usageStatus === 2">停用</div>
        <div class="center-dept un-konw" v-else>未知</div>
      </div>
    </info-detail-header>

    <!-- 标签页导航 -->
    <div class="lab-detail-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="实验室介绍" name="intro" lazy>
          <div class="tab-content intro-content" v-loading="loading" v-if="activeTab === 'intro'">
            <div v-if="labDetail.roomLabDesc">
              <Editor style="overflow-y: hidden;" :value="labDetail.roomLabDesc" :defaultConfig="editorConfig" mode="simple" />
            </div>
            <el-empty v-else description="暂无实验室介绍信息"></el-empty>
          </div>
        </el-tab-pane>
        <el-tab-pane name="staff" lazy>
          <span slot="label">
            人员信息
            <el-badge v-if="summaryData.personnelCount > 0" :value="summaryData.personnelCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'staff'">
            <CenterStaffInfo :labUuid="labUuid" :mode="'lab'" :loading="tabLoading.staff" @fetchSummaryData="fetchSummaryData"></CenterStaffInfo>
          </div>
        </el-tab-pane>
        <el-tab-pane name="rooms" lazy>
          <span slot="label">
            房间信息
            <el-badge v-if="summaryData.roomCount > 0" :value="summaryData.roomCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'rooms'">
            <lab-rooms-info :labUuid="labUuid" :loading="tabLoading.rooms"></lab-rooms-info>
          </div>
        </el-tab-pane>
        <el-tab-pane name="equipment" lazy>
          <span slot="label">
            仪器设备
            <el-badge v-if="summaryData.deviceCount > 0" :value="summaryData.deviceCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'equipment'">
            <center-equipment-list :labUuid="labUuid" :mode="'lab'" :loading="tabLoading.equipment"></center-equipment-list>
          </div>
        </el-tab-pane>
        <el-tab-pane name="materials" lazy>
          <span slot="label">
            技术资料
            <el-badge v-if="summaryData.regulationCount > 0" :value="summaryData.regulationCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'materials'">
            <center-rules-list :labUuid="labUuid" :mode="'lab'" :loading="tabLoading.materials"></center-rules-list>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import InfoDetailHeader from "@laboratory/components/InfoDetailHeader.vue";
import { labInfoDetail, labSummary } from "@laboratory/api/laboratory/laboratory";
import { Editor } from "@wangeditor/editor-for-vue";
import CenterStaffInfo from "@centerManage/centerDetailCom/CenterStaffInfo.vue";
import LabRoomsInfo from "@labManager/labDetailCom/LabRoomsInfo.vue";
import CenterEquipmentList from "@centerManage/centerDetailCom/CenterEquipmentList.vue";
import CenterRulesList from "@centerManage/centerDetailCom/CenterRulesList.vue";
export default {
  name: "labDetail",
  components: {
    layoutContent,
    InfoDetailHeader,
    Editor,
    CenterStaffInfo,
    LabRoomsInfo,
    CenterEquipmentList,
    CenterRulesList,
  },
  data() {
    return {
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      labUuid: "",
      labDetail: {},
      loading: false,
      activeTab: "intro",
      editorConfig: {
        readOnly: true,
        // 可根据需要添加其他编辑器配置
      },
      // 各标签页的加载状态
      tabLoading: {
        staff: false,
        rooms: false,
        equipment: false,
        materials: false,
      },
      // 概览数据
      summaryData: {
        personnelCount: 0,
        roomCount: 0,
        deviceCount: 0,
        regulationCount: 0,
      },
    };
  },
  computed: {
    // 计算顶部信息卡片的数据
    headerInfoItems() {
      const {
        roomLabSn,
        establishmentDate,
        contactInfo,
        roomArea,
        roomLabTypeName,
        roomLabCategoryName,
        labAcademicName,
        deptName,
        responsiblePerson,
        safetyOfficer,
        labAddress,
      } = this.labDetail;

      return [
        { label: "实验室编号", value: roomLabSn || "-" },
        { label: "建设年份", value: establishmentDate || "-" },
        { label: "实验室联系方式", value: contactInfo || "-" },
        { label: "房屋使用面积", value: roomArea / 100 + '㎡' || "-" },
        { label: "实验室类别", value: roomLabCategoryName || "-" },
        { label: "实验室类型", value: roomLabTypeName || "-" },
        { label: "所属学科", value: labAcademicName || "-" },
        { label: "所属部门", value: deptName || "-" },
        { label: "管理员", value: responsiblePerson || "-" },
        { label: "安全管理员", value: safetyOfficer || "-" },
        { label: "地址", value: labAddress || "-" },
      ];
    },
  },
  created() {
    // 从路由参数中获取实验室UUID
    this.labUuid = this.$route.params.uuid;
    if (!this.labUuid) {
      this.$message.error("缺少必要的实验室标识");
      this.goBack();
      return;
    }

    // 获取实验室详情
    this.fetchLabDetail();
    // 获取概览信息
    this.fetchSummaryData();
  },
  methods: {
    // 获取实验室详情
    fetchLabDetail() {
      if (!this.labUuid) return;

      this.loading = true;
      labInfoDetail({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0) {
            this.labDetail = res.data || {};
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取概览数据
    fetchSummaryData() {
      if (!this.labUuid) return;

      labSummary({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            this.summaryData = {
              personnelCount: res.data.personnelCount || 0,
              roomCount: res.data.roomCount || 0,
              deviceCount: res.data.deviceCount || 0,
              regulationCount: res.data.regulationCount || 0,
            };
          }
        })
        .catch((error) => {
          console.error("获取概览数据失败:", error);
        });
    },
    // 返回列表页
    goBack() {
      this.$router.push({ name: "labManager" });
    },

    // 实验中心编辑
    handleEdit() {
      if (this.labUuid) {
        this.$router.push({
          name: "labEdit",
          params: { uuid: this.labUuid },
          query: { from: "detail" }, // 标记来源为详情页
        });
      } else {
        this.$message.error("编辑失败：无效的实验室信息");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-button + .el-button {
    margin-left: 10px;
  }

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.lab-detail-tabs {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 0;

  .tab-content {
    min-height: 300px;
    padding: 20px;

    &.intro-content {
      line-height: 1.6;

      /* 富文本样式 */
      :deep(.w-e-text-container) {
        height: auto !important;
      }

      :deep(.w-e-text) {
        padding: 0;
        overflow: visible;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        margin: 10px auto;
        display: block;
      }

      :deep(p) {
        margin-bottom: 15px;
      }

      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;

        th,
        td {
          border: 1px solid #ddd;
          padding: 8px 12px;
        }

        th {
          background-color: #f6f6f6;
        }
      }
    }
  }
}

::v-deep .el-tabs__header {
  padding: 0 20px !important;
}
.on-line {
  background-color: #cff2e6 !important;
  color: #09ab75 !important;
}
.un-line {
  background-color: #fae5e5 !important;
  color: #d2585d !important;
}
.un-konw {
  background-color: #cdcaca !important;
  color: #625e5e !important;
}
</style>
