<template>
  <div class="lab-rooms-info">
    <!-- 搜索过滤区域 -->
    <div class="filter-style">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="房间名称:">
          <el-input v-model="searchForm.roomName" placeholder="请输入房间名称" clearable />
        </el-form-item>
        <el-form-item label="房间号:">
          <el-input v-model="searchForm.roomSn" placeholder="请输入房间号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search" :loading="isLoading">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <!-- 房间信息表格展示 -->
    <div v-else-if="roomList && roomList.length > 0" class="rooms-container">
      <table-pagetion
        ref="roomTable"
        :loading="false"
        :table-data="roomList"
        :columns="columns"
        :pagination="paginationConfig"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
      >
        <!-- 房间面积 -->
        <template #roomArea="{ row }">
          {{ row.roomArea ? row.roomArea / 100 + "㎡" : "0" }}
        </template>
      </table-pagetion>
    </div>
    <el-empty v-else description="暂无房间信息" />
  </div>
</template>

<script>
import { labRoomBindPage } from "@laboratory/api/laboratory/laboratory";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";

export default {
  name: "LabRoomsInfo",
  components: {
    TablePagetion,
  },
  props: {
    labUuid: {
      type: String,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      innerLoading: false,
      roomList: [],
      total: 0,
      searchForm: {
        roomName: "",
        roomSn: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      columns: [
        { type: "index", prop: "index", label: "序号", width: "60", align: "center" },
        { prop: "roomName", label: "房间名称", minWidth: "150", align: "center" },
        { prop: "roomSn", label: "房间号", minWidth: "120", align: "center" },
        { prop: "campusName", label: "所属校区", minWidth: "120", align: "center" },
        { prop: "deptName", label: "所属院系", minWidth: "120", align: "center" },
        { prop: "buildingName", label: "所属楼宇", minWidth: "120", align: "center" },
        { prop: "floorName", label: "所属楼层", minWidth: "100", align: "center" },
        { prop: "roomArea", label: "房屋使用面积", width: "120", align: "center", slotName: "roomArea" },
      ],
    };
  },
  computed: {
    isLoading() {
      return this.loading || this.innerLoading;
    },
    paginationConfig() {
      return {
        currentPage: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        pageSizes: [10, 20, 30, 50],
        total: this.total,
      };
    },
  },
  created() {
    // if (this.labUuid) {
    //   this.fetchRoomsData();
    // }
  },
  watch: {
    labUuid: {
      handler(newVal) {
        if (newVal) {
          this.fetchRoomsData();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取房间数据
    fetchRoomsData() {
      if (!this.labUuid) return;

      this.innerLoading = true;

      const params = {
        ...this.queryParams,
        labUuid: this.labUuid,
        roomName: this.searchForm.roomName || undefined,
        roomSn: this.searchForm.roomSn || undefined,
      };

      labRoomBindPage(params)
        .then((res) => {
          if (res.code === 0) {
            // 将roomInfo字段拓展到外层，参考labAddCom的处理方式
            this.roomList = res.data.map((item) => {
              return {
                ...item,
                ...item.roomInfo,
              };
            });
            this.total = res.count || 0;
          } else {
            this.$message.error(res.message || "获取房间列表失败");
            this.roomList = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          console.error("获取房间列表失败:", error);
          this.$message.error("获取房间列表失败");
          this.roomList = [];
          this.total = 0;
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },

    // 搜索房间
    handleSearch() {
      this.queryParams.pageNum = 1;
      this.fetchRoomsData();
    },

    // 重置搜索
    handleResetSearch() {
      this.searchForm = {
        roomName: "",
        roomSn: "",
      };
      this.queryParams.pageNum = 1;
      this.fetchRoomsData();
    },

    // 分页大小变化事件
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.fetchRoomsData();
    },

    // 分页页码变化事件
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.fetchRoomsData();
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-rooms-info {
  min-height: 200px;

  .filter-style {
    margin-bottom: 20px;
  }

  .loading-container {
    padding: 20px;
  }

  .rooms-container {
    background-color: #fff;
    border-radius: 4px;
  }

  .default-image {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #c0c4cc;
    font-size: 24px;
  }

  .el-empty {
    padding: 60px 0;
  }
}
</style>
