<template>
  <div class="labManager">
    <div class="tree">
      <dept-tree ref="deptTree" @node-click="handleTreeNodeClick" />
    </div>
    <labs-list
      class="labs-list"
      ref="labsList"
      mode="manager"
      :searchParams="searchParams"
      @reset="handleReset"
      @addLab="handleAddLab"
      @editLab="handleEditLab"
      @viewLab="handleViewLab"
    ></labs-list>
  </div>
</template>

<script>
import LabsList from "../labManagerCom/LabsList.vue";
import DeptTree from "../labManagerCom/DeptTree.vue";

export default {
  name: "labManager",
  components: {
    LabsList,
    DeptTree,
  },
  data() {
    return {
      searchParams: {}, // 传递给LabsList的搜索参数
    };
  },
  methods: {
    // 处理来自树组件的节点点击事件
    handleTreeNodeClick(params) {
      // 更新搜索参数并触发LabsList搜索
      this.searchParams = { ...params };

      // 通知LabsList组件进行搜索
      if (this.$refs.labsList && this.$refs.labsList.handleTreeSearch) {
        this.$refs.labsList.handleTreeSearch(this.searchParams);
      }
    },
    handleReset () {
      this.$refs.deptTree.$refs.deptTree.setCurrentKey(null)
    },
    // 处理新增实验室事件，跳转到新增路由
    handleAddLab(centerUuid) {
      this.$router.push({ name: "labAdd" });
    },

    // 处理实验室编辑事件
    handleEditLab(item) {
      this.$router.push({
        name: "labEdit",
        params: { uuid: item.uuid },
        query: { from: "list" },
      });
    },

    // 查看实验室详情
    handleViewLab(item) {
      this.$router.push({
        name: "labDetail",
        params: { uuid: item.uuid },
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.labManager {
  width: 100%;
  display: flex;
  background-color: #f8f8f8;
  height: calc(100vh - 265px);
  overflow-y: hidden;
}
// .tree {
//   width: 230px;
// }
.labs-list {
  overflow-y: auto;
}
</style>
