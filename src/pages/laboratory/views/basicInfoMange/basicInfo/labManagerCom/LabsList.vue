<template>
  <layoutContent :class="mode === 'center' ? 'layout-center' : ''">
    <div class="search-form" slot="search">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="实验室名称：">
          <el-input v-model="searchForm.roomLabName" placeholder="请输入实验室名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="实验室类型：">
          <dict-cascader v-model="searchForm.roomLabType" :code-type="1015" placeholder="全部"></dict-cascader>
        </el-form-item>
        <el-form-item label="实验室管理员：">
          <el-input v-model="searchForm.responsiblePerson" placeholder="请输入管理员姓名" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button plain type="primary" icon="el-icon-plus" @click="handleAddLab">
            {{ mode === "manager" ? "新增实验室" : "绑定实验室" }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 卡片列表区域 -->
    <div class="labs-list">
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <el-empty v-else-if="labsList.length === 0" description="暂无数据"></el-empty>
      <div class="card-container" v-else>
        <lab-center-card v-for="(item, index) in labsList" :key="item.uuid || index" :item="adaptLabDataForCard(item)"
          :image-src="imageSrc" :info-items="generateInfoItems(item)" :use-custom-info-items="true"
          :viewDeptName="false" :actions="mode === 'manager'" :showIcon="false" :showIcon2="true">
          <!-- 使用具名插槽自定义状态文本 -->
          <template #showIcon2>
            <div class="center-dept" style="margin-right: 11px;">{{ item.usageStatus === 1 ? "在用" : "停用" }}</div>
          </template>
          <!-- <template #status-text>
            <div class="card-icon">
              <i class="iconfont icon-bianyuan" :class="item.usageStatus == 1 ? 'icon-success' : 'icon-danger'" />
              <div class="text">
                <span>{{ item.usageStatus === 1 ? "在用" : "停用" }}</span>
              </div>
            </div>
          </template> -->
          <!-- 使用具名插槽自定义按钮，只在manager模式下显示 -->
          <template #actions v-if="mode === 'manager'">
            <!-- 实验室目前不提供删除 -->
            <!-- <el-button plain type="danger" icon="el-icon-delete" @click="handleDeleteLab(item)">删除</el-button> -->
            <el-button plain type="primary" icon="el-icon-edit" @click="handleEditLab(item)">编辑</el-button>
            <el-button plain type="primary" @click="handleViewLab(item)">查看详情</el-button>
          </template>
        </lab-center-card>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog title="删除确认" :visible.sync="deleteDialogVisible" width="30%" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="delete-confirm">
        <i class="el-icon-warning-outline warning-icon"></i>
        <p>
          确定要删除实验室
          <span class="highlight">{{ currentLab.roomLabName }}</span>
          吗？
        </p>
        <p class="warning-text">删除后将无法恢复，请谨慎操作！</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" :loading="deleteLoading" @click="confirmDelete">确认删除</el-button>
      </div>
    </el-dialog>
  </layoutContent>
</template>

<script>
import LabCenterCard from "@laboratory/components/LabCenterCard.vue";
import { labInfoPage, labInfoDelete } from "@laboratory/api/laboratory/laboratory";
import layoutContent from "@laboratory/components/layoutContent";
import { getDictAll } from "@laboratory/api/laboratory/basicMessage";
import DictCascader from "@laboratory/components/DictCascader.vue";

export default {
  name: "LabsList",
  components: {
    LabCenterCard,
    layoutContent,
    DictCascader,
  },
  props: {
    mode: {
      type: String,
      default: "manager", // 'manager' 或 'center'
      validator: (value) => ["manager", "center"].includes(value),
    },
    centerUuid: {
      type: String,
      required: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      labsList: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      searchForm: {
        roomLabName: "",
        roomLabType: "",
        responsiblePerson: "",
      },
      // 来自树组件的搜索参数
      treeSearchParams: {},
      innerLoading: false,
      // 删除相关
      deleteDialogVisible: false,
      deleteLoading: false,
      currentLab: {},
      // 图片路径前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    isLoading() {
      return this.loading || this.innerLoading;
    },
  },
  created() {
    // 组件创建时初始化数据
    this.fetchLabsData();
  },
  methods: {
    // 将实验室数据适配为卡片组件所需的格式
    adaptLabDataForCard(item) {
      // 创建一个新对象，避免修改原对象
      const adaptedItem = { ...item };

      // 添加组件显示需要的其他属性
      adaptedItem.roomKindName = item.roomLabName;
      adaptedItem.roomKindImage = item.roomLabImage || "";

      return adaptedItem;
    },

    // 为每个实验室生成信息项数组
    generateInfoItems(item) {
      return [
        {
          label: "实验室管理员",
          value: item.responsiblePerson || "-",
          icon: "iconfont icon-gerenzhongxin_0",
        },
        {
          label: "实验室类型",
          value: item.roomLabTypeName || "-",
          icon: "iconfont icon-xingzhuangjiehe",
        },
        {
          label: "地址",
          value: item.labAddress || "-",
          icon: "iconfont icon-a-bianzu5",
        },
      ];
    },

    // 获取实验中心下的实验室数据
    fetchLabsData() {
      this.innerLoading = true;

      // 使用实际API
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        orderItems: "gmtCreate",
        orderRule: "desc",
      };

      // 添加中心UUID（如果存在）
      if (this.centerUuid) {
        params.labCenterUuid = this.centerUuid;
      }

      // 添加来自树组件的搜索参数
      if (this.treeSearchParams.deptUuid) {
        params.deptUuid = this.treeSearchParams.deptUuid;
      }
      if (this.treeSearchParams.labCenterUuid) {
        params.labCenterUuid = this.treeSearchParams.labCenterUuid;
      }

      // 添加表单搜索条件
      if (this.searchForm.roomLabName) {
        params.roomLabName = this.searchForm.roomLabName;
      }
      if (this.searchForm.roomLabType) {
        params.roomLabType = this.searchForm.roomLabType;
      }
      if (this.searchForm.responsiblePerson) {
        params.responsiblePerson = this.searchForm.responsiblePerson;
      }

      labInfoPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.labsList = res.data || [];
            this.total = res.count || 0;
          }
        })
        .catch((error) => {
          console.error("获取实验室列表失败:", error);
          this.$message.error("获取实验室列表失败");
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },

    // 获取图片完整URL
    getImageUrl(path) {
      if (!path) return "";
      if (path.startsWith("http")) return path;
      return this.imageSrc + path;
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchLabsData();
    },

    // 重置搜索条件
    handleReset() {
      this.treeSearchParams = {};
      this.searchForm = {
        roomLabName: "",
        roomLabType: "",
        responsiblePerson: "",
      };
      this.currentPage = 1;
      this.fetchLabsData();
      this.$emit('reset')
    },

    // 分页大小变化事件
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchLabsData();
    },

    // 分页页码变化事件
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchLabsData();
    },

    // 新增/绑定实验室
    handleAddLab() {
      if (this.mode === "manager") {
        // 实验室管理模式 - 发出addLab事件，用于路由跳转
        this.$emit("addLab", this.centerUuid);
      } else {
        // 实验中心模式 - 发出bindLab事件，用于打开弹窗
        this.$emit("bindLab", this.centerUuid);
      }
    },

    // 实验室编辑
    handleEditLab(item) {
      this.$emit("editLab", item);
    },

    // 查看实验室详情
    handleViewLab(item) {
      if (this.mode === "manager") {
        // 实验室管理模式 - 路由跳转到详情页
        this.$router.push({
          name: "labDetail",
          params: { uuid: item.uuid },
        });
      } else {
        // 实验中心模式 - 发出事件
        this.$emit("viewLab", item);
      }
    },

    // 删除实验室
    handleDeleteLab(item) {
      this.currentLab = item;
      this.deleteDialogVisible = true;
    },

    // 处理来自树组件的搜索
    handleTreeSearch(params) {
      this.treeSearchParams = { ...params };
      this.currentPage = 1; // 重置到第一页
      this.fetchLabsData();
    },

    // 确认删除实验室
    confirmDelete() {
      if (!this.currentLab || !this.currentLab.uuid) {
        this.$message.error("删除失败：无效的实验室信息");
        return;
      }

      this.deleteLoading = true;

      // 实际API删除
      const data = {
        uuid: this.currentLab.uuid,
      };

      labInfoDelete(data)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("删除成功");
            this.deleteDialogVisible = false;

            // 如果当前页只有一条数据且不是第一页，则删除后跳转到上一页
            if (this.labsList.length === 1 && this.currentPage > 1) {
              this.currentPage -= 1;
            }
            // 重新加载数据
            this.fetchLabsData();
          }
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.layout-center {
  ::v-deep .search-section {
    padding: 20px 20px 0;
    // border-bottom: 1px solid #ebeef5;
    border-bottom: none;
    background-color: #fff;
  }
  ::v-deep .center-list {
    padding: 20px;
    // background-color: #f5f7fa;
    min-height: 400px;
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.labs-list {
  position: relative;
  min-height: 400px;

  .loading-container {
    padding: 20px;
  }

  .card-container {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
  }
}

.pagination-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
}

/* center模式下的分页样式调整 */
.layout-center {
  .pagination-section {
    background-color: #fff;
    margin: 0;
    border-top: 1px solid #ebeef5;
    border-radius: 0;
  }
}

/* 删除确认对话框样式 */
.delete-confirm {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;

  .warning-icon {
    font-size: 48px;
    color: #e6a23c;
    margin-bottom: 20px;
  }

  p {
    margin: 10px 0;
    font-size: 16px;
    text-align: center;

    .highlight {
      font-weight: bold;
      color: #f56c6c;
    }
  }

  .warning-text {
    color: #f56c6c;
    font-size: 14px;
  }
}
</style>
