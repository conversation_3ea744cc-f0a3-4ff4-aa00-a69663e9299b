<template>
  <layoutContent>
    <!-- 顶部导航栏 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ isEditMode ? "实验中心编辑" : "实验中心新建" }}</span>
      </div>
      <el-button :disabled="!centerUuid" type="primary" @click="handleComplete">完成</el-button>
    </div>

    <!-- 主体内容区 -->
    <div class="center-add-container">
      <!-- 左侧步骤导航 - 使用 Element UI 的 el-steps 组件 -->
      <div class="steps-nav">
        <el-steps direction="vertical" :active="currentStep - 1" :space="80">
          <el-step v-for="(step, index) in steps" :key="index" :title="step" :class="{ clickable: true }">
            <div slot="title" @click="handleStepClick(index)" class="step-title">
              <span v-if="index === 0">*</span>
              {{ step }}
            </div>
          </el-step>
        </el-steps>
      </div>

      <!-- 右侧表单内容 -->
      <div class="form-content">
        <!-- 基本信息表单 -->
        <div v-show="currentStep === 1" class="step-form">
          <div class="form-title">基本信息</div>
          <center-basic-info ref="centerBasicInfo" :centerUuid="centerUuid" :isEditMode="isEditMode"
            :initialData="basicForm" @save-success="handleBasicInfoSaveSuccess" />
        </div>

        <!-- 中心介绍表单 -->
        <div v-show="currentStep === 2" class="step-form">
          <div class="form-title">中心介绍</div>
          <div class="form-actions">
            <el-button type="primary" @click="handleSaveIntro" :loading="introSaveLoading">保存</el-button>
          </div>
          <div class="intro-form">
            <rich-text-editor v-model="introForm.roomKindDesc" :height="400" placeholder="请输入中心介绍内容..."
              @upload-success="handleUploadSuccess"></rich-text-editor>
          </div>
        </div>

        <!-- 人员信息表单 -->
        <div v-show="currentStep === 3" class="step-form">
          <div class="form-title">人员信息</div>
          <personnel-info v-if="centerUuid" :centerUuid="centerUuid" :screenWidth="screenWidth"
            @next-step="handlePersonnelNextStep"></personnel-info>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取中心ID后才能管理人员信息
          </div>
        </div>

        <!-- 实验室表单 -->
        <div v-show="currentStep === 4" class="step-form">
          <div class="form-title">实验室</div>
          <center-labs-info v-if="centerUuid" :centerUuid="centerUuid" :screenWidth="screenWidth"
            @next-step="handleLabsNextStep"></center-labs-info>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取中心ID后才能管理实验室信息
          </div>
        </div>

        <div v-show="currentStep === 5" class="step-form">
          <div class="form-title">仪器设备</div>
          <center-device-info v-if="centerUuid" :centerUuid="centerUuid" :activeTab="currentStep === 5 ? 'device' : ''"
            :screenWidth="screenWidth" @next-step="handleDeviceNextStep"></center-device-info>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取中心ID后才能管理仪器设备
          </div>
        </div>
        <div v-show="currentStep === 6" class="step-form">
          <div class="form-title">规章制度</div>
          <center-regulations v-if="centerUuid" :centerUuid="centerUuid"
            :screenWidth="screenWidth"></center-regulations>
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取中心ID后才能管理规章制度
          </div>
        </div>
      </div>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import { labCenterDetail, getDeptList, updateCenterDesc } from "@laboratory/api/laboratory/basicMessage";
import RichTextEditor from "@laboratory/components/RichTextEditor/index.vue";
import CenterBasicInfo from "./centerAddCom/CenterBasicInfo.vue";
import PersonnelInfo from "@centerManage/centerAddCom/PersonnelInfo.vue";
import CenterLabsInfo from "@centerManage/centerAddCom/CenterLabsInfo.vue";
import CenterRegulations from "@centerManage/centerAddCom/CenterRegulations.vue";
import CenterDeviceInfo from "@centerManage/centerAddCom/CenterDeviceInfo.vue";
import { CENTER_TYPE_OPTIONS, CENTER_LEVEL_OPTIONS } from "@laboratory/constants/options";

export default {
  name: "centerAdd",
  components: {
    layoutContent,
    RichTextEditor,
    CenterBasicInfo,
    PersonnelInfo,
    CenterLabsInfo,
    CenterRegulations,
    CenterDeviceInfo,
  },
  data() {
    return {
      CENTER_TYPE_OPTIONS,
      CENTER_LEVEL_OPTIONS,
      // 步骤配置
      steps: ["基础信息", "中心介绍", "人员信息", "实验室", "仪器设备", "规章制度"],
      currentStep: 1,

      // 屏幕宽度
      screenWidth: 0,

      // 模式标识
      isEditMode: false,
      loading: false,

      // 基本信息表单
      basicForm: {
        roomKindName: "",
        roomKindKind: "",
        deptId: "",
        deptName: "",
        roomKindLevel: "",
        roomKindAddress: "",
        contactInfo: "",
        responsiblePerson: "",
        safetyOfficer: "",
        roomKindImage: "",
      },

      // 表单验证规则
      basicRules: {
        roomKindName: [{ required: true, message: "请输入中心名称", trigger: "blur" }],
        roomKindKind: [{ required: true, message: "请选择中心类型", trigger: "change" }],
        deptId: [{ required: true, message: "请选择所属部门", trigger: "change" }],
        roomKindLevel: [{ required: true, message: "请选择中心级别", trigger: "change" }],
        roomKindAddress: [{ required: true, message: "请输入地址", trigger: "blur" }],
        responsiblePerson: [{ required: true, message: "请输入负责人", trigger: "blur" }],
        safetyOfficer: [{ required: true, message: "请输入安全负责人", trigger: "blur" }],
      },

      // 下拉选项
      deptOptions: [], // 部门选项

      // 保存状态
      saveLoading: false,
      centerUuid: "", // 创建成功后的中心UUID
      centerData: null, // 保存返回的完整中心数据对象

      // 中心介绍表单
      introForm: {
        roomKindDesc: "",
      },
      introSaveLoading: false,
    };
  },
  created() {
    // 判断当前是否为编辑模式
    this.isEditMode = this.$route.name === "centerEdit";

    // 如果是编辑模式，则获取 UUID 并调用详情接口
    if (this.isEditMode) {
      const uuid = this.$route.params.uuid;
      if (uuid) {
        this.centerUuid = uuid;
        this.switchStep(Number(this.$route.query.currentStep) || 1);
        this.fetchCenterDetail();
      } else {
        this.$message.warning("缺少必要的参数");
        this.goBack();
      }
    }

    // 获取部门列表
    this.fetchDeptOptions();

    // 初始化屏幕宽度
    this.handleResize();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      if (window.innerWidth > 1640) {
        this.screenWidth = window.innerWidth - 620;
      } else {
        this.screenWidth = window.innerWidth - 580;
      }
    },

    // 获取部门选项
    fetchDeptOptions() {
      getDeptList().then((res) => {
        if (res.code === 0) {
          this.deptOptions = (res.data || []).map((item) => ({
            value: item.deptId,
            label: item.deptName,
          }));
        }
      });
    },

    // 获取中心详情
    fetchCenterDetail() {
      if (!this.centerUuid) return;

      this.loading = true;
      labCenterDetail({ uuid: this.centerUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 回填基本信息
            this.basicForm = {
              roomKindName: res.data.roomKindName || "",
              roomKindKind: res.data.roomKindKind ? res.data.roomKindKind : "",
              deptId: res.data.deptId || "",
              deptName: res.data.deptName || "",
              roomKindLevel: res.data.roomKindLevel ? res.data.roomKindLevel : "",
              roomKindAddress: res.data.roomKindAddress || "",
              contactInfo: res.data.contactInfo || "",
              responsiblePerson: res.data.responsiblePerson || "",
              safetyOfficer: res.data.safetyOfficer || "",
              roomKindImage: res.data.roomKindImage || "",
            };

            // 回填中心介绍
            this.introForm.roomKindDesc = res.data.roomKindDesc || "";

            // 保存完整的中心数据
            this.centerData = res.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理步骤点击事件
    handleStepClick(index) {
      // 在编辑模式下，允许自由切换步骤
      if (this.isEditMode) {
        this.switchStep(index + 1);
        return;
      }

      // 在新增模式下，检查是否已有centerUuid
      if (!this.centerUuid) {
        this.$message.warning("请先完成基本信息的保存");
        this.switchStep(1);
        return;
      }
      // 直接使用 index + 1 作为步骤值，因为 currentStep 是从 1 开始的
      this.switchStep(index + 1);
    },

    // 切换步骤 - 简化逻辑，允许自由切换
    switchStep(step) {
      this.currentStep = step;

      // 如果切换到人员信息步骤，且已有centerUuid，则确保人员列表已加载
      if (step === 3 && this.centerUuid) {
        // PersonnelInfo组件会自动加载数据，这里不需要额外操作
        console.log("进入人员信息步骤，中心UUID:", this.centerUuid);
      }
    },

    // 返回列表页或详情页
    goBack() {
      if (this.isEditMode && this.$route.query.from === "detail") {
        // 如果是从详情页进入的编辑页，则返回详情页
        this.$router.push({
          name: "centerDetail",
          params: { uuid: this.centerUuid },
        });
      } else {
        // 否则返回列表页
        this.$router.push({ name: "centerManage" });
      }
    },

    // 处理基本信息保存成功
    handleBasicInfoSaveSuccess(data) {
      // 更新UUID
      this.centerUuid = data.uuid;
      // 更新表单数据
      this.basicForm = { ...this.basicForm, ...data };
      // 更新中心数据
      this.centerData = { ...this.centerData, ...data };
      // 跳转到下一步
      this.switchStep(this.currentStep + 1);
    },

    // 保存中心介绍
    handleSaveIntro() {
      // 检查是否已有centerUuid
      if (!this.centerUuid) {
        this.$message.warning("请先完成基本信息的保存");
        this.switchStep(1);
        return;
      }

      if (!this.introForm.roomKindDesc || this.introForm.roomKindDesc.trim() === "") {
        this.$message.warning("请输入中心介绍内容");
        return;
      }

      this.introSaveLoading = true;

      // 构造更新参数，只包含uuid和roomKindDesc
      const params = {
        uuid: this.centerUuid,
        roomKindDesc: this.introForm.roomKindDesc,
      };

      // 调用专门的中心介绍更新API
      updateCenterDesc(params)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("中心介绍保存成功");
            // 更新本地保存的中心数据的介绍字段
            if (this.centerData) {
              this.centerData.roomKindDesc = this.introForm.roomKindDesc;
            }
            // 自动进入下一步
            this.switchStep(this.currentStep + 1);
          }
        })
        .finally(() => {
          this.introSaveLoading = false;
        });
    },

    // 以下是其他步骤的保存方法（暂时只是模拟）
    handleSaveLabs() {
      // 模拟保存实验室
      setTimeout(() => {
        this.$message.success("实验室信息保存成功");
      }, 500);
    },

    // 完成按钮点击事件
    handleComplete() {
      // 最终完成操作，可以进行最后的保存或跳转
      const confirmMessage = this.isEditMode ? "确认完成实验中心编辑?" : "确认完成实验中心创建?";

      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.goBack(); // 返回列表页
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 处理上传成功后的逻辑
    handleUploadSuccess(event) {
      // 这里可以处理上传成功后的逻辑，比如显示上传成功的提示
      console.log("上传成功:", event);
    },

    // 处理人员信息下一步事件
    handlePersonnelNextStep() {
      // 切换到下一个步骤（实验室）
      this.switchStep(4);
    },

    // 处理实验室下一步事件
    handleLabsNextStep() {
      // 切换到下一个步骤（仪器设备）
      this.switchStep(5);
    },

    // 处理仪器设备下一步事件
    handleDeviceNextStep() {
      // 切换到下一个步骤（规章制度）
      this.switchStep(6);
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.center-add-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 600px;

  .form-content {
    flex: 1;
    padding: 20px;

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: block;
        width: 4px;
        height: 16px;
        background-color: #2468f2;
        margin-right: 8px;
      }
    }

    .step-form {
      max-width: 100%;
    }

    .intro-form {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 10px;
    }

    .form-actions {
      display: flex;
      margin: 20px 0;

      .el-button {
        margin-left: auto;
      }
    }

    .placeholder {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }

    .empty-tip {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}
.full-width-item {
  width: 500px;
}
.photo-uploader {
  ::v-deep .el-upload {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}
</style>
