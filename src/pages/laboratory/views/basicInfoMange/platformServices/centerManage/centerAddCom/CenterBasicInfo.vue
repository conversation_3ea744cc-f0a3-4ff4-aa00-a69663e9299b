<template>
  <div class="center-basic-info">
        <div class="form-actions">
      <el-button type="primary" @click="saveBasicInfo" :loading="saveLoading">保存</el-button>
    </div>
    <el-form :model="basicForm" :rules="basicRules" ref="basicForm" :inline="true" label-width="110px">
      <el-form-item label="中心名称：" prop="roomKindName">
        <el-input v-model="basicForm.roomKindName" placeholder="请输入中心名称"></el-input>
      </el-form-item>
      <el-form-item label="中心类型：" prop="roomKindKind">
        <el-select v-model="basicForm.roomKindKind" placeholder="请选择中心类型">
          <el-option v-for="item in CENTER_TYPE_OPTIONS" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属部门：" prop="deptId">
        <el-select v-model="basicForm.deptId" filterable placeholder="请选择所属部门">
          <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="中心级别：" prop="roomKindLevel">
        <el-select v-model="basicForm.roomKindLevel" placeholder="请选择级别">
          <el-option v-for="item in CENTER_LEVEL_OPTIONS" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地址：" prop="roomKindAddress">
        <el-input type="textarea" :rows="2" class="full-width-item" v-model="basicForm.roomKindAddress" placeholder="请输入地址"></el-input>
      </el-form-item>
      <el-form-item label="联系方式：" prop="contactInfo">
        <el-input v-model="basicForm.contactInfo" placeholder="请输入联系方式"></el-input>
      </el-form-item>
      <el-form-item label="负责人：" prop="responsiblePerson">
        <el-input v-model="basicForm.responsiblePerson" placeholder="请输入负责人"></el-input>
      </el-form-item>
      <el-form-item label="安全负责人：" prop="safetyOfficer">
        <el-input v-model="basicForm.safetyOfficer" placeholder="请输入安全负责人"></el-input>
      </el-form-item>
      <el-row>
        <el-col>
          <el-form-item label="上传照片：">
            <file-uploader
              v-model="basicForm.roomKindImage"
              type="image"
              :maxSize="2"
              tip="支持JPG、PNG格式，不超过2MB"
              class="photo-uploader"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- <div class="form-actions">
      <el-button type="primary" @click="saveBasicInfo" :loading="saveLoading">保存</el-button>
    </div> -->
  </div>
</template>

<script>
import FileUploader from "@laboratory/components/FileUploader/index.vue";
import { labCenterSave, labCenterUpdate, getDeptList } from "@laboratory/api/laboratory/basicMessage";
import { CENTER_TYPE_OPTIONS, CENTER_LEVEL_OPTIONS } from "@laboratory/constants/options";

export default {
  name: "CenterBasicInfo",
  components: {
    FileUploader,
  },
  props: {
    // 中心UUID，编辑模式时传入
    centerUuid: {
      type: String,
      default: "",
    },
    // 是否为编辑模式
    isEditMode: {
      type: Boolean,
      default: false,
    },
    // 初始表单数据
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      CENTER_TYPE_OPTIONS,
      CENTER_LEVEL_OPTIONS,

      // 基本信息表单
      basicForm: {
        roomKindName: "",
        roomKindKind: "",
        deptId: "",
        deptName: "",
        roomKindLevel: "",
        roomKindAddress: "",
        contactInfo: "",
        responsiblePerson: "",
        safetyOfficer: "",
        roomKindImage: "",
      },

      // 表单验证规则
      basicRules: {
        roomKindName: [{ required: true, message: "请输入中心名称", trigger: "blur" }],
        roomKindKind: [{ required: true, message: "请选择中心类型", trigger: "change" }],
        deptId: [{ required: true, message: "请选择所属部门", trigger: "change" }],
        roomKindLevel: [{ required: true, message: "请选择中心级别", trigger: "change" }],
        roomKindAddress: [{ required: true, message: "请输入地址", trigger: "blur" }],
        responsiblePerson: [{ required: true, message: "请输入负责人", trigger: "blur" }],
        safetyOfficer: [{ required: true, message: "请输入安全负责人", trigger: "blur" }],
      },

      // 下拉选项
      deptOptions: [], // 部门选项

      // 保存状态
      saveLoading: false,
    };
  },
  watch: {
    // 监听初始数据变化
    initialData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.basicForm = { ...this.basicForm, ...newVal };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    // 获取部门列表
    this.fetchDeptOptions();
  },
  methods: {
    // 获取部门选项
    fetchDeptOptions() {
      getDeptList().then((res) => {
        if (res.code === 0) {
          this.deptOptions = (res.data || []).map((item) => ({
            value: item.deptId,
            label: item.deptName,
          }));
        }
      });
    },

    // 保存基本信息
    saveBasicInfo() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          // 构造保存参数
          let params = {
            ...this.basicForm,
          };

          // 判断是新增还是更新
          if (this.centerUuid) {
            // 已有UUID，调用更新接口
            params.uuid = this.centerUuid;
            labCenterUpdate(params)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success("更新成功");
                  // 触发保存成功事件
                  this.$emit("save-success", params);
                } else {
                  this.$message.error(res.message || "更新失败");
                }
              })
              .finally(() => {
                this.saveLoading = false;
              });
          } else {
            // 新增接口
            labCenterSave(params)
              .then((res) => {
                if (res.code === 0) {
                  this.$message.success("保存成功");
                  // 触发保存成功事件
                  this.$emit("save-success", res.data);
                } else {
                  this.$message.error(res.message || "保存失败");
                }
              })
              .finally(() => {
                this.saveLoading = false;
              });
          }
        }
      });
    },

    // 验证表单
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.basicForm.validate((valid) => {
          resolve(valid);
        });
      });
    },

    // 获取表单数据
    getFormData() {
      return { ...this.basicForm };
    },

    // 重置表单
    resetForm() {
      this.$refs.basicForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.center-basic-info {
  .form-actions {
    margin: 20px 0;
    display: flex;
    .el-button {
      margin-left: auto;
    }
  }

  .full-width-item {
    width: 500px;
  }
}
</style>
