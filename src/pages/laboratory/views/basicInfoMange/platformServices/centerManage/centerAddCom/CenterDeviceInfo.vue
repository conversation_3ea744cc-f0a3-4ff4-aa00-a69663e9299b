<template>
  <div class="center-device-info">
    <div class="header-actions">
      <div class="left-actions">
        <el-button plain type="primary" icon="el-icon-plus" @click="openBindDialog">绑定仪器</el-button>
        <el-button type="danger" icon="el-icon-delete" @click="batchUnbind" :disabled="selectedDevices.length === 0">
          批量解绑
        </el-button>
        <!-- 导入导出功能 -->
        <!-- exportButtonText="导出设备"
          importButtonText="导入设备"
          templateButtonText="下载模板" -->
        <import-export-buttons :uuid="currentUuid" :uuidName="resourceType === 1 ? 'labCenterUuid' : 'labUuid'"
          :exportApi="handleExportApi" :importApi="handleImportApi" @import-success="handleImportSuccess"
          @export-success="handleExportSuccess" @import-error="handleImportError" />
        <el-button class="next-button" type="primary" @click="nextStep">下一步</el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <!-- <div class="search-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="仪器编号">
          <el-input v-model="queryParams.deviceCode" placeholder="请输入仪器编号"></el-input>
        </el-form-item>
        <el-form-item label="资产编号">
          <el-input v-model="queryParams.assetCode" placeholder="请输入资产编号"></el-input>
        </el-form-item>
        <el-form-item label="仪器名称">
          <el-input v-model="queryParams.deviceName" placeholder="请输入仪器名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div> -->

    <div class="table-container">
      <table-pagetion ref="deviceTable" :loading="loading" :table-data="deviceList" :columns="columns"
        :screenWidth="screenWidth" :pagination="paginationConfig" @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange" @handleSelectionChange="handleSelectionChange">
        <!-- 照片列 -->
        <template #imgUrl="{ row }">
          <el-image v-if="row.device.imgUrl" :src="imageSrc + row.device.imgUrl" style="width: 60px; height: 60px"
            :preview-src-list="[imageSrc + row.device.imgUrl]"></el-image>
          <span v-else>-</span>
        </template>
        <template #price="{ row }">
          {{ row.device.price ? row.device.price / 100 : "-" }}
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="viewDeviceDetail(row, 'bind')">详情</el-button>
          <el-button type="text" class="delete-btn" @click="unbindDevice(row)">解绑</el-button>
        </template>
      </table-pagetion>

      <!-- 将下一步按钮移动到列表底部 -->
      <!-- <div class="bottom-actions">
        <el-button type="primary" @click="nextStep">下一步</el-button>
      </div> -->
    </div>

    <!-- 设备绑定弹窗 -->
    <devices-dialog :visible.sync="bindDialogVisible" :center-uuid="centerUuid" :lab-uuid="labUuid"
      :resource-type="resourceType" :bound-device-ids="boundDeviceIds" @bind-success="fetchDeviceList"
      @viewDeviceDetail="(row) => viewDeviceDetail(row, 'detail')"></devices-dialog>

    <!-- 设备详情弹窗 -->
    <device-detail-dialog :visible.sync="detailDialogVisible" :device-uuid="currentDeviceUuid" :apiType="apiType"
      :mode="resourceType === 1 ? 'center' : 'lab'"></device-detail-dialog>
  </div>
</template>

<script>
import {
  labCenterDeviceBindPage,
  labCenterDeviceBindDelete,
  labCenterDeviceBindDevIds,
  labCenterDeviceBindImport,
  labCenterDeviceBindExport,
} from "@laboratory/api/laboratory/basicMessage";
import {
  labDeviceBindPage,
  labDeviceBindDelete,
  labDeviceBindDevIds,
  labDeviceImportant,
  labDeviceExport,
} from "@laboratory/api/laboratory/laboratory";
import DevicesDialog from "@centerManage/centerAddCom/CenterDeviceInfoCom/DevicesDialog.vue";
import DeviceDetailDialog from "@centerManage/centerAddCom/CenterDeviceInfoCom/DeviceDetailDialog.vue";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";
import ImportExportButtons from "@laboratory/components/ImportExportButtons/index.vue";

export default {
  name: "CenterDeviceInfo",
  components: {
    DevicesDialog,
    DeviceDetailDialog,
    TablePagetion,
    ImportExportButtons,
  },
  props: {
    centerUuid: {
      type: String,
      default: "",
    },
    labUuid: {
      type: String,
      default: "",
    },
    resourceType: {
      type: Number,
      default: 1, // 1: 实验中心, 2: 实验室
    },
    activeTab: {
      type: String,
      default: "",
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    // 当前资源的UUID
    currentUuid() {
      return this.resourceType === 2 ? this.labUuid : this.centerUuid;
    },
    // 动态API映射
    deviceApi() {
      return {
        1: {
          // 实验中心
          bindPage: labCenterDeviceBindPage,
          bindDelete: labCenterDeviceBindDelete,
          bindDevIds: labCenterDeviceBindDevIds,
          bindImport: labCenterDeviceBindImport,
          bindExport: labCenterDeviceBindExport,
          uuidField: "labCenterUuid",
        },
        2: {
          // 实验室
          bindPage: labDeviceBindPage,
          bindDelete: labDeviceBindDelete,
          bindDevIds: labDeviceBindDevIds,
          bindImport: labDeviceImportant,
          bindExport: labDeviceExport,
          uuidField: "labUuid",
        },
      }[this.resourceType];
    },
    // 分页配置
    paginationConfig() {
      return {
        currentPage: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        pageSizes: [10, 20, 50, 100],
        total: this.total,
      };
    },
  },
  data() {
    return {
      apiType: "bind",
      loading: false,
      deviceList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceCode: "",
        assetCode: "",
        deviceName: "",
      },
      selectedDevices: [],
      bindDialogVisible: false,
      detailDialogVisible: false,
      currentDeviceUuid: "",
      boundDeviceIds: [], // 已绑定的设备ID列表
      // 文件URL前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      columns: [
        { type: "selection", prop: "selection", width: "55", align: "center" },
        { type: "index", prop: "index", label: "序号", width: "60", align: "center" },
        { prop: "device.devSn", label: "仪器编号", minWidth: "120", align: "center" },
        { prop: "device.assetSn", label: "资产编号", minWidth: "120", align: "center" },
        { prop: "device.classNumCode", label: "分类号", minWidth: "120", align: "center" },
        { prop: "device.devName", label: "仪器名称", minWidth: "200", align: "center" },
        { prop: "device.devModel", label: "型号", minWidth: "120", align: "center" },
        { prop: "device.devSpecification", label: "规格", minWidth: "120", align: "center" },
        { prop: "device.devSourceName", label: "仪器来源", minWidth: "150", align: "center" },
        { prop: "device.countryCodeName", label: "国别码", minWidth: "150", align: "center" },
        // 单价单位分
        { prop: "device.price", slotName: "price", label: "单价", minWidth: "100", align: "center" },
        { prop: "device.purchaseDate", label: "购置日期", minWidth: "120", align: "center" },
        { prop: "device.presentSituationName", label: "现状码", minWidth: "120", align: "center" },
        { prop: "device.directionOfUseName", label: "使用方向", minWidth: "120", align: "center" },
        { prop: "device.deptName", label: "单位名称", minWidth: "120", align: "center" },
        { prop: "device.imgUrl", label: "图片", minWidth: "120", align: "center", slotName: "imgUrl" },
        { prop: "device.storageLocation", label: "存放地点", minWidth: "120", align: "center" },
        { prop: "device.devKindName", label: "仪器类型", minWidth: "150", align: "center" },
        { prop: "operation", slotName: "operation", label: "操作", width: "150", align: "center", fixed: "right" },
      ],
    };
  },
  watch: {},
  created() {
    this.fetchDeviceList();
    this.fetchBoundDeviceIds();
  },
  methods: {
    // 获取绑定设备列表
    fetchDeviceList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        [this.deviceApi.uuidField]: this.currentUuid,
      };

      this.deviceApi
        .bindPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.deviceList = res.data || [];
            this.total = res.count || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取已绑定设备ID列表
    fetchBoundDeviceIds() {
      const params = { uuid: this.currentUuid };
      this.deviceApi.bindDevIds(params).then((res) => {
        if (res.code === 0) {
          this.boundDeviceIds = res.data || [];
        }
      });
    },

    // 打开绑定弹窗
    openBindDialog() {
      this.bindDialogVisible = true;
      // 获取最新的已绑定设备ID列表
      this.fetchBoundDeviceIds();
    },

    // 查看设备详情
    viewDeviceDetail(row, apiType) {
      this.apiType = apiType;
      if (apiType === "detail") {
        this.currentDeviceUuid = row.device.uuid;
      } else {
        this.currentDeviceUuid = row.uuid;
      }
      console.log(this.currentDeviceUuid, "CenterDeviceInfo");
      this.detailDialogVisible = true;
    },

    // 解绑单个设备
    unbindDevice(row) {
      this.$confirm("确认解除该仪器设备的绑定关系?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.unbindDevices([row.uuid]);
        })
        .catch(() => {});
    },

    // 批量解绑设备
    batchUnbind() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning("请选择要解绑的仪器设备");
        return;
      }

      const deviceIds = this.selectedDevices.map((item) => item.uuid);

      this.$confirm(`确认解除选中的 ${deviceIds.length} 个仪器设备的绑定关系?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.unbindDevices(deviceIds);
        })
        .catch(() => {});
    },

    // 解绑设备（单个或批量）
    unbindDevices(deviceIds) {
      const params =
        this.resourceType === 2
          ? {
              // labUuid: this.currentUuid,
              uuidSet: deviceIds,
            }
          : {
              // labCenterUuid: this.currentUuid,
              uuidSet: deviceIds,
            };

      this.deviceApi.bindDelete(params).then((res) => {
        if (res.code === 0) {
          this.$message.success("解绑成功");
          this.fetchDeviceList();
          this.fetchBoundDeviceIds();
          this.selectedDevices = [];
        }
      });
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedDevices = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.fetchDeviceList();
    },

    // 页码变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.fetchDeviceList();
    },

    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1;
      this.fetchDeviceList();
    },

    // 重置搜索
    resetSearch() {
      this.queryParams = {
        pageNum: 1,
        pageSize: this.queryParams.pageSize,
        deviceCode: "",
        assetCode: "",
        deviceName: "",
      };
      this.fetchDeviceList();
    },

    // 导入成功回调
    handleImportSuccess(importResult) {
      // 刷新设备列表
      this.fetchDeviceList();
      this.fetchBoundDeviceIds();

      // 根据导入结果显示不同的消息
      // if (importResult.failed > 0) {
      //   this.$message.warning(`导入完成！成功：${importResult.success}条，失败：${importResult.failed}条`);
      // } else {
      //   this.$message.success(`导入成功！共导入${importResult.success}条设备数据`);
      // }
    },

    // 导出成功回调
    handleExportSuccess() {
      console.log("导出成功");
      // 可以添加统计或日志记录
    },

    // 导入失败回调
    handleImportError(error) {
      console.error("导入失败:", error);
      // 错误已在组件内部处理，这里可以添加额外的错误处理逻辑
    },

    // 导出失败回调
    handleExportError(error) {
      console.error("导出失败:", error);
      // 错误已在组件内部处理，这里可以添加额外的错误处理逻辑
    },

    // 模板下载回调
    handleTemplateDownload() {
      console.log("模板下载完成");
      // 可以添加使用统计
    },

    // 导出接口处理
    async handleExportApi(params) {
      return this.deviceApi.bindExport(params);
    },

    // 导入接口处理
    async handleImportApi(params) {
      return this.deviceApi.bindImport(params);
    },

    // 导入前检查
    beforeImportUpload(file) {
      const isExcel =
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || file.type === "application/vnd.ms-excel";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("只能上传Excel文件!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过10MB!");
        return false;
      }
      return true;
    },

    // 下一步
    nextStep() {
      this.$emit("next-step");
    },
  },
};
</script>

<style lang="scss" scoped>
.center-device-info {
  width: 100%;

  .header-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .left-actions {
      width: 100%;
      display: flex;
      gap: 10px;
      .next-button {
        margin-left: auto;
      }
    }
  }

  .search-container {
    margin-bottom: 20px;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
  }

  .table-container {
    margin-bottom: 20px;
  }

  .delete-btn {
    color: #f56c6c;
  }

  .bottom-actions {
    margin-top: 10px;
    margin-left: 50px;
  }

  .import-container {
    .template-download {
      margin-top: 15px;
      text-align: right;

      a {
        color: #2050d1;
        cursor: pointer;
        text-decoration: none;
      }
    }
  }
}
</style>
