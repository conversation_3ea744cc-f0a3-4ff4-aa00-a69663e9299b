<template>
  <el-dialog title="仪器设备详情" :visible.sync="dialogVisible" width="750px" :close-on-click-modal="false">
    <div v-loading="loading" class="device-detail-container">
      <div class="device-info-section">
        <div class="device-header">
          <div class="device-image">
            <img v-if="deviceDetail.imgUrl" :src="getImageUrl(deviceDetail.imgUrl)" alt="设备图片" @error="handleImageError" />
            <img v-else src="@laboratory/assets/image/labCover.png" alt="设备图片" />
            <!-- <div v-else class="no-image">
              <i class="el-icon-cpu" style="font-size: 48px; color: #c0c4cc;"></i>
              <div style="margin-top: 8px; font-size: 12px; color: #909399;">暂无图片</div>
            </div> -->
          </div>
          <div class="device-title">
            <h3>{{ deviceDetail.devName }}</h3>
            <div class="detail-grid">
              <div class="detail-column">
                <div class="detail-item">
                  <span class="label">仪器编号：</span>
                  <span>{{ deviceDetail.devSn }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">规格：</span>
                  <span>{{ deviceDetail.devSpecification || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">仪器来源：</span>
                  <span>{{ deviceDetail.devSourceName || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">单价：</span>
                  <span>{{ deviceDetail.price ? `¥${(deviceDetail.price / 100).toLocaleString()}` : "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">现状码：</span>
                  <span>{{ deviceDetail.presentSituationName || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">存放地点：</span>
                  <span>{{ deviceDetail.storageLocation || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">绑定状态：</span>
                  <span>{{ deviceDetail.bindingStatus === 1 ? "已绑定" : "未绑定" }}</span>
                </div>
              </div>
              <div class="detail-column">
                <div class="detail-item">
                  <span class="label">仪器类型：</span>
                  <span>{{ deviceDetail.devKindName }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">型号：</span>
                  <span>{{ deviceDetail.devModel }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">国别码：</span>
                  <span>{{ deviceDetail.countryCodeName || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">购置日期：</span>
                  <span>{{ deviceDetail.purchaseDate }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">使用方向：</span>
                  <span>{{ deviceDetail.directionOfUseName || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">单位名称：</span>
                  <span>{{ deviceDetail.deptName || "-" }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">所属对象：</span>
                  <span>{{ resourceName || "-" }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <el-divider></el-divider>

        <div class="files-section">
          <h4>附件</h4>
          <div v-if="deviceDetail.files && deviceDetail.files.length > 0" class="file-list">
            <div class="file-header">
              <span class="file-name-header">文件名</span>
              <span class="file-time-header">更新时间</span>
              <span class="file-action-header">操作</span>
            </div>
            <div v-for="(file, index) in deviceDetail.files" :key="index" class="file-item">
              <span class="file-name">{{ file.fileName }}</span>
              <span class="file-time">{{ file.updateTime }}</span>
              <el-button type="text" class="download-btn" @click="downloadFile(file)">下载</el-button>
            </div>
          </div>
          <div v-else class="no-files">暂无附件</div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { labCenterDeviceBindDetail, labCenterDeviceDetail } from "@laboratory/api/laboratory/basicMessage";
import { labDeviceBindDetail, labDeviceDetail } from "@laboratory/api/laboratory/laboratory";

export default {
  name: "DeviceDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    deviceData: {
      type: Object,
      default: () => ({}),
    },
    deviceUuid: {
      type: String,
      default: "",
    },
    // 新增prop：指定使用的API类型
    apiType: {
      type: String,
      default: "bind", // "bind" 使用绑定详情API, "detail" 使用设备详情API
      validator: (value) => ["bind", "detail"].includes(value),
    },
    mode: {
      type: String,
      default: "center", // "center" 使用中心API, "lab" 使用实验室API
      validator: (value) => ["center", "lab"].includes(value),
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      deviceDetail: {},
      resourceName: "",
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
    deviceUuid(val) {
      if (val && this.dialogVisible) {
        this.fetchDeviceDetail();
      }
    },
  },
  methods: {
    // 获取设备详情
    fetchDeviceDetail() {
      if (!this.deviceUuid) return;
      let apiCall;
      this.loading = true;
      if (this.mode === "center") {
        // 根据apiType选择不同的API
        // 这是实验中心的 这里的deviceUuid，详情列表拿数据里的device里的uuid，绑定的地方查看详情，用数据的uuid
        apiCall =
          this.apiType === "detail"
            ? labCenterDeviceDetail({ uuid: this.deviceUuid })
            : labCenterDeviceBindDetail({ uuid: this.deviceUuid });
      } else {
        // 这是实验室的
        // 这是实验室的 这里的deviceUuid，详情列表拿数据里的device里的uuid，绑定的地方查看详情，用数据的uuid
        apiCall = this.apiType === "detail" ? labDeviceDetail({ uuid: this.deviceUuid }) : labDeviceBindDetail({ uuid: this.deviceUuid });
      }
      apiCall
        .then((res) => {
          if (res.code === 0) {
            // 直接使用API返回的数据结构
            const data = res.data || {};
            this.deviceDetail = data.device || data;
            this.resourceName = data.resourceName || "";
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取图片URL的辅助方法
    getImageUrl(imgUrl) {
      if (!imgUrl) return "";
      return document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || "") + imgUrl;
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.style.display = "none";
      const parent = event.target.parentNode;
      const noImageDiv = parent.querySelector(".no-image");
      if (noImageDiv) {
        noImageDiv.style.display = "flex";
      } else {
        parent.innerHTML = `
          <div class="no-image" style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
            <i class="el-icon-cpu" style="font-size: 48px; color: #c0c4cc;"></i>
            <div style="margin-top: 8px; font-size: 12px; color: #909399;">图片加载失败</div>
          </div>
        `;
      }
    },

    // 下载文件
    downloadFile(file) {
      if (file && file.fileUrl) {
        window.open(file.fileUrl);
      } else {
        this.$message.warning("文件链接不可用");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.device-detail-container {
  padding: 10px;

  .device-header {
    display: flex;
    margin-bottom: 20px;

    .device-image {
      width: 150px;
      height: 150px;
      // border: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        max-width: 100%;
        max-height: 100%;
      }

      .no-image {
        color: #999;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }

    .device-title {
      flex: 1;

      h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 16px;
        color: #333;
        font-weight: bold;
      }

      .detail-grid {
        display: flex;
        flex-wrap: wrap;
        color: #333;

        .detail-column {
          flex: 1;
          min-width: 200px;

          .detail-item {
            margin-bottom: 15px;
            font-size: 14px;

            .label {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }

  .files-section {
    h4 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 16px;
      color: #333;
    }

    .file-list {
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .file-header {
        display: flex;
        background-color: #f5f7fa;
        padding: 10px;
        font-weight: bold;
        border-bottom: 1px solid #ebeef5;

        .file-name-header {
          flex: 1;
        }

        .file-time-header {
          width: 150px;
          text-align: center;
        }

        .file-action-header {
          width: 80px;
          text-align: center;
        }
      }

      .file-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .file-time {
          width: 150px;
          color: #999;
          font-size: 12px;
          text-align: center;
        }

        .download-btn {
          width: 80px;
          text-align: center;
        }
      }
    }

    .no-files {
      color: #999;
      font-size: 14px;
      text-align: center;
      padding: 20px 0;
      border: 1px solid #ebeef5;
      border-radius: 4px;
    }
  }
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
