<template>
  <el-dialog title="绑定仪器" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false" @closed="handleDialogClosed">
    <!-- 搜索栏 -->
    <div class="search-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="存放地点">
          <el-cascader
            v-model="storageLocationCascader"
            :options="roomTreeOptions"
            :props="cascaderProps"
            placeholder="请选择存放地点"
            clearable
            filterable
            style="width: 300px"
            @change="handleStorageLocationChange"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="仪器编号">
          <el-input v-model="queryParams.devSn" placeholder="请输入仪器编号"></el-input>
        </el-form-item>
        <el-form-item label="资产编号">
          <el-input v-model="queryParams.assetSn" placeholder="请输入资产编号"></el-input>
        </el-form-item>
        <el-form-item label="仪器名称">
          <el-input v-model="queryParams.devName" placeholder="请输入仪器名称"></el-input>
        </el-form-item>
        <el-form-item label="绑定状态">
          <el-select v-model="queryParams.bindingStatus" placeholder="请选择绑定状态" clearable>
            <el-option label="全部" :value="''"></el-option>
            <el-option label="已绑定" :value="1"></el-option>
            <el-option label="未绑定" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search">搜索</el-button>
          <el-button @click="resetSearch" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 设备列表 -->
    <div class="table-container">
      <table-pagetion
        ref="deviceTable"
        :loading="loading"
        :table-data="deviceList"
        :columns="columns"
        :pagination="paginationConfig"
        :selectable="checkRowSelectable"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- @tableRowClassName="tableRowClassName" -->
        <!-- 照片列 -->
        <template #imgUrl="{ row }">
          <el-image
            v-if="row.device.imgUrl"
            :src="imageSrc + row.device.imgUrl"
            style="width: 60px; height: 60px"
            :preview-src-list="[imageSrc + row.device.imgUrl]"
          ></el-image>
          <span v-else>-</span>
        </template>
        <!-- 操作列 -->
        <template #bindingStatus="{ row }">
          <span style="color: #00A870" v-if="row.bindingStatus === 0">未绑定</span>
          <span style="color: #333333" v-else-if="row.bindingStatus === 1">已绑定</span>
          <span style="color: #333333" v-else>未知状态</span>
        </template>
        <template #price="{ row }">
          {{ row.device.price ? row.device.price / 100 : "-" }}
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="viewDeviceDetail(row)">详情</el-button>
        </template>
      </table-pagetion>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" :loading="bindLoading">取消</el-button>
      <el-button type="primary" @click="confirmBind" :loading="bindLoading" :disabled="localBoundDeviceIds.length === 0">
        确认绑定 ({{ localBoundDeviceIds.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { devicePage, labCenterDeviceBindSave } from "@laboratory/api/laboratory/basicMessage";
import { labDeviceBindSave, roomTree } from "@laboratory/api/laboratory/laboratory";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";

export default {
  name: "DevicesDialog",
  components: {
    TablePagetion,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    centerUuid: {
      type: String,
      default: "",
    },
    labUuid: {
      type: String,
      default: "",
    },
    resourceType: {
      type: Number,
      default: 1, // 1: 实验中心, 2: 实验室
    },
    boundDeviceIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      // 绑定加载状态
      bindLoading: false,
      deviceList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        campusName: "",
        buildingName: "",
        floorName: "",
        roomName: "",
        devSn: "",
        assetSn: "",
        devName: "",
        bindingStatus: "", // 绑定状态：'' 全部, 0 未绑定, 1 已绑定
      },
      selectedDevices: [],
      // 本地存储的当前绑定状态的设备ID列表（动态维护）
      localBoundDeviceIds: [],
      // 标志：是否正在恢复选中状态（避免触发选择变化事件）
      isRestoringSelection: false,
      // 级联选择器相关
      storageLocationCascader: [],
      roomTreeOptions: [],
      cascaderProps: {
        value: "id",
        label: "name",
        children: "children",
        emitPath: true, // 返回完整路径
        checkStrictly: true, // 可以选择任意一级
      },
      columns: [
        { type: "selection", prop: "selection", width: "55", align: "center" },
        { type: "index", prop: "index", label: "序号", width: "60", align: "center" },
        { prop: "device.devSn", label: "仪器编号", minWidth: "120", align: "center" },
        { prop: "device.assetSn", label: "资产编号", minWidth: "120", align: "center" },
        { prop: "device.classNumCode", label: "分类号", minWidth: "120", align: "center" },
        { prop: "device.devName", label: "仪器名称", minWidth: "200", align: "center" },
        { prop: "device.devModel", label: "型号", minWidth: "120", align: "center" },
        { prop: "device.devSpecification", label: "规格", minWidth: "120", align: "center" },
        { prop: "device.devSourceName", label: "仪器来源", minWidth: "150", align: "center" },
        { prop: "device.countryCodeName", label: "国别码", minWidth: "150", align: "center" },
        { prop: "device.price", slotName: "price", label: "单价", minWidth: "100", align: "center" },
        { prop: "device.purchaseDate", label: "购置日期", minWidth: "120", align: "center" },
        { prop: "device.presentSituationName", label: "现状码", minWidth: "120", align: "center" },
        { prop: "device.directionOfUseName", label: "使用方向", minWidth: "120", align: "center" },
        { prop: "device.deptName", label: "单位名称", minWidth: "120", align: "center" },
        { prop: "device.imgUrl", label: "图片", minWidth: "120", align: "center", slotName: "imgUrl" },
        { prop: "device.storageLocation", label: "存放地点", minWidth: "120", align: "center" },
        { prop: "device.devKindName", label: "仪器类型", minWidth: "150", align: "center" },
        { prop: "bindingStatus", slotName: "bindingStatus", label: "绑定状态", width: "100", align: "center", fixed: "right" },
        { prop: "operation", slotName: "operation", label: "操作", width: "100", align: "center", fixed: "right" },
      ],
      // 文件URL前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    // 当前资源的UUID
    currentUuid() {
      return this.resourceType === 2 ? this.labUuid : this.centerUuid;
    },
    // 动态API映射
    deviceApi() {
      return {
        1: {
          // 实验中心
          bindSave: labCenterDeviceBindSave,
          uuidField: "labCenterUuid",
        },
        2: {
          // 实验室
          bindSave: labDeviceBindSave,
          uuidField: "labUuid",
        },
      }[this.resourceType];
    },
    // 分页配置
    paginationConfig() {
      return {
        currentPage: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        pageSizes: [10, 20, 50, 100],
        total: this.total,
      };
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initDialog();
        this.fetchRoomTree();
      }
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
    boundDeviceIds: {
      handler(newVal) {
        // 初始化时将传入的已绑定ID列表赋值给本地存储
        this.localBoundDeviceIds = newVal && newVal.length ? [...newVal] : [];
      },
      immediate: true,
    },
  },
  methods: {
    // 检查行是否可选择
    checkRowSelectable(row, index) {
      // 如果设备已绑定且不在传入的boundDeviceIds中，则禁用勾选
      if (row.bindingStatus === 1 && !this.boundDeviceIds.includes(row.devId)) {
        return false;
      }
      return true;
    },

    // 初始化弹窗
    initDialog() {
      this.queryParams.pageNum = 1;
      this.selectedDevices = [];
      this.storageLocationCascader = [];

      // 如果表格组件已经渲染，则清除选择
      if (this.$refs.deviceTable) {
        this.$refs.deviceTable.clearSelection();
      }

      this.fetchDeviceList();
    },

    // 获取房间树数据
    fetchRoomTree() {
      roomTree()
        .then((res) => {
          if (res.code === 0) {
            this.roomTreeOptions = res.data || [];
          }
        })
        .catch((error) => {
          console.error("获取房间树数据失败:", error);
        });
    },

    // 处理存放地点级联选择器变化
    handleStorageLocationChange(value) {
      // 清空之前的选择
      this.queryParams.campusName = "";
      this.queryParams.buildingName = "";
      this.queryParams.floorName = "";
      this.queryParams.roomName = "";

      if (value && value.length > 0) {
        // 根据选择的层级设置对应的查询参数
        this.setQueryParamsByLevel(value);
      }
    },

    // 根据选择的层级设置查询参数
    setQueryParamsByLevel(selectedValues) {
      if (!selectedValues || selectedValues.length === 0) return;

      let currentOptions = this.roomTreeOptions;

      for (let i = 0; i < selectedValues.length; i++) {
        const value = selectedValues[i];
        const option = currentOptions.find((item) => item.id === value);
        if (option) {
          // 根据层级设置对应的查询参数
          // level: 1:校区 2:楼宇 3:楼层 4:房间
          switch (option.level) {
            case 1:
              this.queryParams.campusName = option.name;
              break;
            case 2:
              this.queryParams.buildingName = option.name;
              break;
            case 3:
              this.queryParams.floorName = option.name;
              break;
            case 4:
              this.queryParams.roomName = option.name;
              break;
          }
          currentOptions = option.children || [];
        }
      }
    },

    // 获取设备列表
    fetchDeviceList() {
      this.loading = true;
      devicePage(this.queryParams)
        .then((res) => {
          if (res.code === 0) {
            this.deviceList = res.data || [];
            this.total = res.count || 0;

            // 根据 localBoundDeviceIds 恢复当前页面的选中状态
            this.$nextTick(() => {
              this.restoreCurrentPageSelection();
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 根据 localBoundDeviceIds 恢复当前页面的选中状态
    restoreCurrentPageSelection() {
      if (!this.$refs.deviceTable) return;

      // 找出当前页面中在 localBoundDeviceIds 中的设备
      const devicesToSelect = this.deviceList.filter((device) => this.localBoundDeviceIds.includes(device.devId));
      // 设置标志，表示正在恢复选中状态
      this.isRestoringSelection = true;

      // 先清除当前页面的选择
      this.$refs.deviceTable.clearSelection();

      // 如果有需要选中的设备，则选中它们
      if (devicesToSelect.length) {
        this.$nextTick(() => {
          devicesToSelect.forEach((row) => {
            this.$refs.deviceTable.toggleRowSelection(row, true);
          });
          // 延迟更长时间确保选择操作完全完成
          setTimeout(() => {
            this.isRestoringSelection = false;
          }, 500);
        });
      } else {
        // 如果没有需要选中的设备，直接恢复事件
        setTimeout(() => {
          this.isRestoringSelection = false;
        }, 200);
      }
    },

    // 查看设备详情
    viewDeviceDetail(row) {
      this.$emit("viewDeviceDetail", row);
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      // 如果正在恢复选中状态，则跳过处理，避免干扰绑定状态
      if (this.isRestoringSelection) {
        return;
      }
      // 更新当前页面选中的设备
      this.selectedDevices = selection;

      // 更新选中状态
      this.updateSelectedDevicesState(selection);
    },

    // 更新选中状态的方法（抽取公共逻辑）
    updateSelectedDevicesState(selection) {
      // 1. 获取当前页面所有设备的ID
      const currentPageDeviceIds = this.deviceList.map((device) => device.devId);

      // 2. 从 localBoundDeviceIds 中移除当前页面的所有设备ID（无论是否选中）
      const otherPagesDeviceIds = this.localBoundDeviceIds.filter((deviceId) => !currentPageDeviceIds.includes(deviceId));

      // 3. 将当前页面选中的设备ID添加到其他页面的选中ID中
      const selectedDeviceIds = selection.map((device) => device.devId);
      this.localBoundDeviceIds = [...otherPagesDeviceIds, ...selectedDeviceIds];
    },
    tableRowClassName({ row, rowIndex }, callback) {
      if (rowIndex === 0) {
        callback("gray-row");
      } else if (rowIndex === 1) {
        callback("gray-row");
      }
      callback("");
    },
    // 分页大小变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.fetchDeviceList();
    },

    // 页码变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.fetchDeviceList();
    },

    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1;
      this.fetchDeviceList();
    },

    // 重置搜索
    resetSearch() {
      this.queryParams = {
        pageNum: 1,
        pageSize: this.queryParams.pageSize,
        campusName: "",
        buildingName: "",
        floorName: "",
        roomName: "",
        devSn: "",
        assetSn: "",
        devName: "",
        bindingStatus: "",
      };
      this.storageLocationCascader = [];
      this.fetchDeviceList();
    },

    // 确认绑定
    confirmBind() {
      if (this.localBoundDeviceIds.length === 0) {
        this.$message.warning("请选择要绑定的仪器设备");
        return;
      }

      const deviceIds = this.localBoundDeviceIds;

      const params =
        this.resourceType === 2
          ? {
              // 实验室使用不同的参数结构
              labUuid: this.currentUuid,
              devIds: deviceIds,
            }
          : {
              // 实验中心
              labCenterUuid: this.currentUuid,
              devIds: deviceIds,
            };
      this.bindLoading = true;
      this.deviceApi.bindSave(params).then((res) => {
        this.bindLoading = false;
        if (res.code === 0) {
          this.$message.success("绑定成功");
          this.$emit("bind-success");
          this.dialogVisible = false;
        }
      });
    },

    // 处理弹窗关闭
    handleDialogClosed() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        campusName: "",
        buildingName: "",
        floorName: "",
        roomName: "",
        devSn: "",
        assetSn: "",
        devName: "",
        bindingStatus: "",
      };
      this.selectedDevices = [];
      this.localBoundDeviceIds = [];
      this.isRestoringSelection = false;
      this.storageLocationCascader = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 20px;

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.table-container {
  margin-bottom: 20px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
