<template>
  <div class="center-labs-info">
    <!-- 使用 LabsTableWithSearch 组件展示已绑定实验室列表 -->
    <labs-table-with-search
      ref="labsTable"
      mode="list"
      :loading="loading"
      :data="labsList"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      :screenWidth="screenWidth"
      @search="handleSearch"
      @nextStep="nextStep"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      @unbind="handleUnbind"
      @batch-unbind="handleBatchUnbind"
      @openBindDialog="openBindDialog"
    ></labs-table-with-search>
    <!-- 将下一步按钮移动到列表底部 -->
    <!-- <div class="bottom-actions">
      <el-button type="primary" @click="nextStep">下一步</el-button>
    </div> -->
    <!-- 使用 LabsDialog 组件展示可绑定实验室弹窗 -->
    <labs-dialog
      :visible.sync="bindDialogVisible"
      :center-uuid="centerUuid"
      :bound-lab-uuids="boundLabUuids"
      :bound-lab-ids="boundLabIds"
      :screenWidth="screenWidth"
      @fetch-data="fetchAvailableLabs"
      @confirm-bind="confirmBind"
    ></labs-dialog>

    <!-- 解绑确认对话框 -->
    <el-dialog
      title="解绑确认"
      :visible.sync="unbindDialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="unbind-confirm">
        <i class="el-icon-warning-outline warning-icon"></i>
        <p>
          确定要解除
          <span class="highlight">{{ unbindType === "single" ? currentLab.roomLabName : selectedLabs.length + "个" }}</span>
          实验室的绑定吗？
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="unbindDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmUnbind" :loading="unbindLoading">确认解绑</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { labInfoPage } from "@laboratory/api/laboratory/laboratory";
import {
  labCenterBindLabPage,
  labCenterBindLabSave,
  labCenterBindLabDelete,
  labCenterBindLabIds,
} from "@laboratory/api/laboratory/basicMessage";
import LabsTableWithSearch from "@centerManage/centerAddCom/CenterLabsInfoCom/LabsTableWithSearch.vue";
import LabsDialog from "@centerManage/centerAddCom/CenterLabsInfoCom/LabsDialog.vue";

export default {
  name: "CenterLabsInfo",
  components: {
    LabsTableWithSearch,
    LabsDialog,
  },
  props: {
    centerUuid: {
      type: String,
      required: true,
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      // 已绑定实验室列表相关
      labsList: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedLabs: [],

      // 绑定弹窗相关
      bindDialogVisible: false,
      boundLabIds: [], // 已绑定的实验室ID列表

      // 解绑确认相关
      unbindDialogVisible: false,
      unbindLoading: false,
      unbindType: "single", // 'single' 或 'batch'
      currentLab: {},
    };
  },
  computed: {
    // 已绑定实验室UUID列表
    boundLabUuids() {
      return this.labsList.map((lab) => lab.bindingUuid);
    },
  },
  created() {
    this.fetchLabsList();
    this.fetchBoundLabIds();
  },
  methods: {
    // 获取已绑定实验室列表
    fetchLabsList(params = {}) {
      this.loading = true;

      // 构造查询参数 - 获取已绑定的实验室列表
      const queryParams = {
        labCenterUuid: this.centerUuid,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        ...params,
      };

      // 调用接口获取数据
      labCenterBindLabPage(queryParams)
        .then((res) => {
          if (res.code === 0) {
            // 处理返回的数据，将 labInfo 属性展开到顶层
            this.labsList = (res.data || []).map((item) => {
              return {
                ...item,
                ...item.labInfo,
                bindingUuid: item.bindingUuid, // 保留绑定关系的UUID
                id: item.id, // 保留绑定关系的ID
              };
            });
            this.total = res.count || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取已绑定的实验室ID列表
    fetchBoundLabIds() {
      labCenterBindLabIds({ uuid: this.centerUuid }).then((res) => {
        if (res.code === 0) {
          this.boundLabIds = res.data || [];
        }
      });
    },

    // 处理搜索
    handleSearch(params) {
      this.currentPage = params.pageNum || 1;
      this.pageSize = params.pageSize || 10;
      delete params.pageNum;
      delete params.pageSize;
      this.fetchLabsList(params);
    },

    // 获取可绑定的实验室列表
    fetchAvailableLabs(params, callback) {
      // 调用接口获取所有实验室列表
      labInfoPage(params)
        .then((res) => {
          if (res.code === 0) {
            callback(res.data || [], res.count || 0);
          } else {
            callback([], 0);
          }
        })
        .catch((error) => {
          console.error("获取可绑定实验室列表失败", error);
          this.$message.error("获取可绑定实验室列表失败");
          callback([], 0);
        });
    },

    // 打开绑定弹窗
    openBindDialog() {
      // 刷新已绑定ID列表
      labCenterBindLabIds({ uuid: this.centerUuid })
        .then((res) => {
          if (res.code === 0) {
            this.boundLabIds = res.data || [];
            this.bindDialogVisible = true;
          } else {
            this.$message.error("获取已绑定实验室列表失败");
          }
        })
        .catch((error) => {
          console.error("获取已绑定实验室ID列表失败", error);
          this.$message.error("获取已绑定实验室列表失败");
        });
    },

    // 确认绑定实验室
    confirmBind(selectedLabs, callback) {
      if (selectedLabs.length === 0) {
        this.$message.warning("请选择要绑定的实验室");
        callback();
        return;
      }

      // 获取选中实验室的ID数组
      const labIds = selectedLabs;
      // const labIds = selectedLabs.map((lab) => lab.roomLabId);

      // 调用批量绑定接口
      labCenterBindLabSave({
        labCenterUuid: this.centerUuid,
        labIds: labIds,
      })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("绑定成功");
            this.fetchLabsList(); // 刷新列表
            this.fetchBoundLabIds(); // 刷新已绑定ID列表
          }
        })
        .finally(() => {
          callback();
        });
    },

    // 处理单个解绑
    handleUnbind(lab) {
      this.unbindType = "single";
      this.currentLab = lab;
      this.unbindDialogVisible = true;
    },

    // 处理批量解绑
    handleBatchUnbind() {
      if (this.selectedLabs.length === 0) {
        this.$message.warning("请选择要解绑的实验室");
        return;
      }

      this.unbindType = "batch";
      this.unbindDialogVisible = true;
    },

    // 确认解绑操作
    confirmUnbind() {
      this.unbindLoading = true;

      // 获取要解绑的实验室UUID数组
      let uuidSet = [];
      if (this.unbindType === "single") {
        uuidSet = [this.currentLab.bindingUuid];
      } else {
        uuidSet = this.selectedLabs.map((lab) => lab.bindingUuid);
      }

      // 调用批量解绑接口
      labCenterBindLabDelete({
        uuidSet: uuidSet,
      })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("解绑成功");
            this.unbindDialogVisible = false;
            this.fetchLabsList(); // 刷新列表
            this.fetchBoundLabIds(); // 刷新已绑定ID列表
          }
        })
        .finally(() => {
          this.unbindLoading = false;
        });
    },

    // 主列表分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchLabsList();
    },

    // 主列表页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchLabsList();
    },

    // 主列表选择变更
    handleSelectionChange(val) {
      this.selectedLabs = val;
    },

    // 跳转到下一步
    nextStep() {
      this.$emit("next-step");
    },
  },
};
</script>

<style lang="scss" scoped>
.center-labs-info {
  width: 100%;

  .labs-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-title {
      font-size: 16px;
      font-weight: 500;
    }

    .header-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .unbind-confirm {
    display: flex;
    flex-direction: column;
    align-items: center;

    .warning-icon {
      font-size: 48px;
      color: #e6a23c;
      margin-bottom: 20px;
    }

    p {
      font-size: 16px;
      text-align: center;

      .highlight {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
  .bottom-actions {
    margin-top: 10px;
    margin-left: 50px;
  }
}
</style>
