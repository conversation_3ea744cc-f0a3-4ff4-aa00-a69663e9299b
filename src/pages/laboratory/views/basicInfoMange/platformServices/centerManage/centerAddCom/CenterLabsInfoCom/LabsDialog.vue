<template>
  <el-dialog
    title="绑定实验室"
    :visible.sync="dialogVisible"
    :width="`${screenWidth}px`"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="handleDialogClosed"
  >
    <div class="labs-dialog-content">
      <!-- 使用 LabsTableWithSearch 组件 -->
      <labs-table-with-search
        ref="labsTableWithSearch"
        mode="dialog"
        :loading="loading"
        :data="availableLabsList"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :screenWidth="screenWidth"
        @search="handleSearch"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      ></labs-table-with-search>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" :loading="bindLoading">取 消</el-button>
      <el-button type="primary" @click="confirmBind" :loading="bindLoading" :disabled="localBoundLabUuids.length === 0">
        确认绑定 ({{ localBoundLabUuids.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import LabsTableWithSearch from "./LabsTableWithSearch.vue";

export default {
  name: "LabsDialog",
  components: {
    LabsTableWithSearch,
  },
  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false,
    },
    // 中心UUID
    centerUuid: {
      type: String,
      required: true,
    },
    // 已绑定的实验室UUID列表（保留兼容性）
    boundLabUuids: {
      type: Array,
      default: () => [],
    },
    // 已绑定的实验室ID列表，用于预选中
    boundLabIds: {
      type: Array,
      default: () => [],
    },
    // 屏幕宽度
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 绑定加载状态
      bindLoading: false,
      // 可用实验室列表
      availableLabsList: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 选中的实验室（当前页面）
      selectedLabs: [],
      searchParams: {},
      // 本地存储的当前绑定状态的实验室ID列表（动态维护）
      localBoundLabUuids: [],
      // 标志：是否正在恢复选中状态（避免触发选择变化事件）
      isRestoringSelection: false,
    };
  },
  computed: {
    // 控制弹窗显示
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    // 监听弹窗显示状态变化
    visible(val) {
      if (val) {
        this.initDialog();
      }
    },
    boundLabIds: {
      handler(newVal) {
        // 初始化时将传入的已绑定ID列表赋值给本地存储
        this.localBoundLabUuids = newVal && newVal.length ? [...newVal] : [];
      },
      immediate: true,
    },
  },
  methods: {
    // 初始化弹窗
    initDialog() {
      this.currentPage = 1;
      this.selectedLabs = [];
      this.searchParams = {};

      // 如果表格组件已经渲染，则清除选择
      if (this.$refs.labsTableWithSearch) {
        this.$refs.labsTableWithSearch.clearSelection();
      }

      this.fetchAvailableLabs();
    },

    // 获取可用实验室列表
    fetchAvailableLabs(params = {}) {
      this.loading = true;

      // 构造查询参数
      const queryParams = {
        ...params,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };

      // 触发获取数据事件
      this.$emit("fetch-data", queryParams, (data, total) => {
        // 不过滤任何实验室，显示所有实验室
        this.availableLabsList = data;
        this.total = total;
        this.loading = false;

        // 根据 localBoundLabUuids 恢复当前页面的选中状态
        this.$nextTick(() => {
          this.restoreCurrentPageSelection();
        });
      });
    },

    // 根据 localBoundLabUuids 恢复当前页面的选中状态
    restoreCurrentPageSelection() {
      if (!this.$refs.labsTableWithSearch) return;

      // 找出当前页面中在 localBoundLabUuids 中的实验室
      const labsToSelect = this.availableLabsList.filter((lab) => this.localBoundLabUuids.includes(lab.roomLabId));

      // 设置标志，表示正在恢复选中状态
      this.isRestoringSelection = true;

      // 先清除当前页面的选择
      this.$refs.labsTableWithSearch.clearSelection();

      // 如果有需要选中的实验室，则选中它们
      if (labsToSelect.length) {
        this.$nextTick(() => {
          labsToSelect.forEach((row) => {
            this.$refs.labsTableWithSearch.$refs.labsTable.toggleRowSelection(row, true);
          });
          // 延迟更长时间确保选择操作完全完成
          setTimeout(() => {
            this.isRestoringSelection = false;
          }, 500);
        });
      } else {
        // 如果没有需要选中的实验室，直接恢复事件
        setTimeout(() => {
          this.isRestoringSelection = false;
        }, 200);
      }
    },

    // 处理搜索
    handleSearch(params) {
      this.currentPage = 1;
      this.searchParams = params;
      this.fetchAvailableLabs(params);
    },

    // 处理分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchAvailableLabs();
    },

    // 处理页码变化
    handleCurrentChange(val) {
      // 更新页码并获取新页面数据
      this.currentPage = val;
      this.fetchAvailableLabs(this.searchParams);
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      // 如果正在恢复选中状态，则跳过处理，避免干扰绑定状态
      if (this.isRestoringSelection) {
        return;
      }
      // 更新当前页面选中的实验室
      this.selectedLabs = selection;

      // 更新选中状态
      this.updateSelectedLabsState(selection);
    },

    // 更新选中状态的方法（抽取公共逻辑）
    updateSelectedLabsState(selection) {
      // 1. 获取当前页面所有实验室的ID
      const currentPageLabIds = this.availableLabsList.map((lab) => lab.roomLabId);

      // 2. 从 localBoundLabUuids 中移除当前页面的所有实验室ID（无论是否选中）
      const otherPagesLabIds = this.localBoundLabUuids.filter((labId) => !currentPageLabIds.includes(labId));

      // 3. 将当前页面选中的实验室ID添加到其他页面的选中ID中
      const selectedLabIds = selection.map((lab) => lab.roomLabId);
      this.localBoundLabUuids = [...otherPagesLabIds, ...selectedLabIds];
    },

    // 确认绑定
    confirmBind() {
      if (this.localBoundLabUuids.length === 0) {
        this.$message.warning("请选择要绑定的实验室");
        return;
      }

      this.bindLoading = true;
      // 将选中的实验室ID转换为实验室对象（需要从所有页面的数据中查找）
      // 这里我们只传递ID列表，让父组件处理
      this.$emit("confirm-bind", this.localBoundLabUuids, () => {
        this.bindLoading = false;
        this.clearSelection();
        this.closeDialog();
      });
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false;
    },

    // 清除所有选择
    clearSelection() {
      this.selectedLabs = [];
      this.localBoundLabUuids = [];
      if (this.$refs.labsTableWithSearch) {
        this.$refs.labsTableWithSearch.clearSelection();
      }
    },

    // 处理弹窗关闭事件
    handleDialogClosed() {
      // 清空选择和重置状态
      this.clearSelection();
    },
  },
};
</script>

<style lang="scss" scoped>
.labs-dialog-content {
  min-height: 300px;
}
::v-deep .el-dialog__footer {
  padding: 10px 20px 20px;
}

::v-deep .el-button--small {
  padding: 8px 15px;
  font-size: 12px;
}
</style>
