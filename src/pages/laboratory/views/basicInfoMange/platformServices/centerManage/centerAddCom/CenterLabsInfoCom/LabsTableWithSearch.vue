<template>
  <div class="labs-table-with-search">
    <!-- 搜索区域 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" label-width="110px">
        <el-form-item label="实验室名称">
          <el-input v-model="searchForm.roomLabName" placeholder="请输入实验室名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="实验室编号">
          <el-input v-model="searchForm.roomLabSn" placeholder="请输入实验室编号" clearable></el-input>
        </el-form-item>
        <el-form-item label="所属学科">
          <dict-cascader v-model="searchForm.labAcademic" :code-type="1003" placeholder="请选择"></dict-cascader>
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select v-model="searchForm.usageStatus" placeholder="请选择" clearable>
            <el-option label="在用" :value="1"></el-option>
            <el-option label="停用" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实验室类别">
          <dict-cascader v-model="searchForm.roomLabCategory" :code-type="1014" placeholder="请选择"></dict-cascader>
        </el-form-item>
        <el-form-item label="实验室类型">
          <dict-cascader v-model="searchForm.roomLabType" :code-type="1015" placeholder="请选择"></dict-cascader>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格操作按钮区域 - 仅在列表模式下显示 -->
    <div class="table-actions" v-if="mode === 'list'">
      <el-button plain type="primary" icon="el-icon-link" @click="$emit('openBindDialog')">绑定实验室</el-button>
      <el-button type="danger" icon="el-icon-delete" :disabled="selectedRows.length === 0" @click="$emit('batch-unbind')">
        批量解绑
      </el-button>
      <el-button class="nextStep" type="primary" @click="$emit('nextStep')">下一步</el-button>
    </div>

    <!-- 使用 TablePagetion 组件替换原有表格和分页 -->
    <div class="labs-table" v-loading="loading">
      <table-pagetion
        ref="labsTable"
        :table-data="tableData"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        :screenWidth="screenWidth"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @sort-change="handleSortChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 使用状态列 -->
        <template #usageStatus="{ row }">
          <el-tag :type="row.usageStatus === 1 ? 'success' : 'danger'">
            {{ row.usageStatus === 1 ? "在用" : "停用" }}
          </el-tag>
        </template>

        <!-- 房屋使用面积列 -->
        <template #roomArea="{ row }">
          {{ row.roomArea ? row.roomArea / 100 + " m²" : "-" }}
        </template>

        <!-- 建设年份列 -->
        <template #establishmentDate="{ row }">
          {{ row.establishmentDate ? $moment(row.establishmentDate).format("YYYY") : "-" }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="$emit('unbind', row)">解绑</el-button>
        </template>
      </table-pagetion>
    </div>
  </div>
</template>

<script>
import DictCascader from "@laboratory/components/DictCascader.vue";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";

export default {
  name: "LabsTableWithSearch",
  components: {
    DictCascader,
    TablePagetion,
  },
  props: {
    // 模式：list - 列表页使用，dialog - 弹窗中使用
    mode: {
      type: String,
      default: "list",
      validator: (value) => ["list", "dialog"].includes(value),
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 表格数据
    data: {
      type: Array,
      default: () => [],
    },
    // 总数
    total: {
      type: Number,
      default: 0,
    },
    // 当前页
    currentPage: {
      type: Number,
      default: 1,
    },
    // 每页条数
    pageSize: {
      type: Number,
      default: 10,
    },
    // 屏幕宽度
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        roomLabName: "",
        roomLabSn: "",
        labAcademic: "",
        usageStatus: "",
        roomLabCategory: "",
        roomLabType: "",
      },
      // 选中的行
      selectedRows: [],
      // 表格列配置
      tableColumns: [
        { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "index", label: "序号", type: "index", width: "60" },
        { prop: "roomLabSn", label: "实验室编号", width: "120" },
        { prop: "roomLabName", label: "实验室名称", width: "120" },
        { prop: "establishmentDate", label: "建设年份", width: "100", slotName: "establishmentDate" },
        { prop: "labAcademicName", label: "所属学科", width: "120" },
        { prop: "usageStatus", label: "使用状态", width: "100", slotName: "usageStatus" },
        { prop: "roomLabCategoryName", label: "实验室类别", width: "120" },
        { prop: "roomLabTypeName", label: "实验室类型", width: "120" },
        { prop: "responsiblePerson", label: "实验室管理员", width: "120" },
        { prop: "contactInfo", label: "联系方式", width: "120" },
        { prop: "roomArea", label: "房屋使用面积", width: "120", slotName: "roomArea" },
        { prop: "labAddress", label: "地址", width: "" },
        { prop: "roomCount", label: "房间数量", width: "100" },
      ],
    };
  },
  created() {
    // 如果是列表模式，添加操作列
    if (this.mode === "list") {
      this.tableColumns.push({
        prop: "operation",
        label: "操作",
        fixed: "right",
        width: "120",
        slotName: "operation",
      });
    }
  },
  computed: {
    // 表格数据
    tableData() {
      return this.data || [];
    },
    // 分页配置
    paginationConfig() {
      return {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        pageSizes: [10, 20, 50, 100],
        total: this.total,
      };
    },
  },
  methods: {
    // 搜索
    handleSearch() {
      // 构造搜索参数
      const searchParams = {};

      // 添加非空搜索条件
      Object.keys(this.searchForm).forEach((key) => {
        if (this.searchForm[key] !== "" && this.searchForm[key] !== null && this.searchForm[key] !== undefined) {
          searchParams[key] = this.searchForm[key];
        }
      });

      // 触发搜索事件
      this.$emit("search", {
        ...searchParams,
        pageNum: 1, // 搜索时重置为第一页
        pageSize: this.pageSize,
      });
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        roomLabName: "",
        roomLabSn: "",
        labAcademic: "",
        usageStatus: "",
        roomLabCategory: "",
        roomLabType: "",
      };

      // 触发搜索事件
      this.$emit("search", {
        pageNum: 1,
        pageSize: this.pageSize,
      });
    },

    // 处理分页大小改变
    handleSizeChange(val) {
      this.$emit("size-change", val);
    },

    // 处理页码改变
    handleCurrentChange(val) {
      this.$emit("current-change", val);
    },

    // 处理排序变化
    handleSortChange(data) {
      this.$emit("sort-change", data);
    },

    // 处理选择变更
    handleSelectionChange(val) {
      this.selectedRows = val;
      this.$emit("selection-change", val);
    },

    // 清空选择
    clearSelection() {
      this.$refs.labsTable.clearSelection();
    },

    // 设置选中行
    setSelection(rows) {
      if (!this.$refs.labsTable) return;

      // 对于每一行数据，如果在需要选中的行列表中，则选中它
      rows.forEach((row) => {
        this.$refs.labsTable.toggleRowSelection(row, true);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.labs-table-with-search {
  width: 100%;

  .search-form {
    margin-bottom: 10px;
  }

  .table-actions {
    display: flex;
    margin-bottom: 15px;
    .nextStep {
      margin-left:auto;
    }
  }

  .labs-table {
    margin-bottom: 20px;
  }
}
</style>
