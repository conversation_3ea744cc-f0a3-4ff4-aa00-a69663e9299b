<template>
  <div class="center-regulations">
    <!-- 操作按钮区域 -->
    <div class="table-actions">
      <el-button type="primary" icon="el-icon-upload2" @click="openUploadDialog">上传文件</el-button>
      <el-button type="danger" icon="el-icon-delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete">
        批量删除
      </el-button>
    </div>

    <!-- 规章制度列表 -->
    <div class="regulations-table" v-loading="loading">
      <table-pagetion
        ref="regulationsTable"
        :table-data="regulationsList"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        :screenWidth="screenWidth"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 文件名称列 -->
        <template #fileName="{ row }">
          <div class="file-info">
            <i :class="getFileIcon(row.fileName)" class="file-icon"></i>
            <span class="file-name">{{ row.fileName || "-" }}</span>
          </div>
        </template>

        <!-- 规章类型列 -->
        <template #regulationType="{ row }">
          {{ getRegulationTypeName(row.regulationType) }}
        </template>

        <!-- 更新时间列 -->
        <template #gmtModified="{ row }">
          {{ row.gmtModified ? $moment(row.gmtModified).format("YYYY-MM-DD") : "-" }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handlePreview(row)" v-if="canPreview(row.fileName)">预览</el-button>
          <el-button type="text" @click="handleDownload(row)">下载</el-button>
          <el-button type="text" class="delete-btn" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 上传规章制度弹窗 -->
    <regulation-upload-dialog
      :visible.sync="uploadDialogVisible"
      :center-uuid="centerUuid"
      :lab-uuid="labUuid"
      :resource-type="resourceType"
      @upload-success="handleUploadSuccess"
    ></regulation-upload-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="删除确认"
      :visible.sync="deleteDialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="delete-confirm">
        <i class="el-icon-warning-outline warning-icon"></i>
        <p>
          确定要删除
          <span class="highlight">{{ deleteType === "single" ? currentRegulation.fileName : selectedRows.length + "个" }}</span>
          {{ resourceType === 1 ? "规章制度" : "技术资料" }}文件吗？
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">确认删除</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览弹窗 -->
    <file-preview-dialog :visible.sync="previewDialogVisible" :file-info="selectedFileInfo" />

    <!-- 图片预览组件 -->
    <image-preview-viewer ref="imagePreviewViewer" />
  </div>
</template>

<script>
import { labCenterRegulationPage, labCenterRegulationDelete } from "@laboratory/api/laboratory/basicMessage";
import { labRegulationPage, labRegulationDelete } from "@laboratory/api/laboratory/laboratory";
import { canPreview, getFileIcon, formatFileSize, isPdfFile } from "@laboratory/utils/fileUtils";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";
import RegulationUploadDialog from "@centerManage/centerAddCom/CenterRegulationsCom/RegulationUploadDialog.vue";
import FilePreviewDialog from "@laboratory/components/FilePreviewDialog/index.vue";
import { REGULATION_TYPE_OPTIONS } from "@laboratory/constants/options";
import ImagePreviewViewer from "@laboratory/components/ImagePreviewViewer/index.vue";

export default {
  name: "CenterRegulations",
  components: {
    TablePagetion,
    RegulationUploadDialog,
    FilePreviewDialog,
    ImagePreviewViewer,
  },
  props: {
    centerUuid: {
      type: String,
      default: "",
    },
    labUuid: {
      type: String,
      default: "",
    },
    resourceType: {
      type: Number,
      default: 1, // 1: 实验中心, 2: 实验室
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    // 当前资源的UUID
    currentUuid() {
      return this.resourceType === 2 ? this.labUuid : this.centerUuid;
    },
    // 动态API映射
    regulationApi() {
      return {
        1: {
          // 实验中心
          page: labCenterRegulationPage,
          delete: labCenterRegulationDelete,
          uuidField: "labCenterUuid",
        },
        2: {
          // 实验室
          page: labRegulationPage,
          delete: labRegulationDelete,
          uuidField: "labUuid",
        },
      }[this.resourceType];
    },
    // 表格列配置 - 根据resourceType动态生成
    tableColumns() {
      const baseColumns = [
        { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "index", label: "序号", type: "index", width: "80" },
        { prop: "fileName", label: "文件名称", slotName: "fileName", width: "" },
      ];

      // 只有实验中心才显示规章类型列
      if (this.resourceType === 1) {
        baseColumns.push({ prop: "regulationType", label: "规章类型", slotName: "regulationType", width: "120" });
      }

      baseColumns.push(
        { prop: "gmtModified", label: "更新时间", slotName: "gmtModified", width: "180" },
        {
          prop: "operation",
          label: "操作",
          width: "180",
          slotName: "operation",
        },
      );

      return baseColumns;
    },
    // 分页配置
    paginationConfig() {
      return {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        pageSizes: [10, 20, 50, 100],
        total: this.total,
      };
    },
  },
  data() {
    return {
      // 规章制度列表相关
      regulationsList: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedRows: [],

      // 上传弹窗相关
      uploadDialogVisible: false,

      // 删除确认相关
      deleteDialogVisible: false,
      deleteLoading: false,
      deleteType: "single", // 'single' 或 'batch'
      currentRegulation: {},

      // 预览相关
      previewDialogVisible: false,
      selectedFileInfo: {},

      // 文件URL前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),

      // 规章类型选项
      regulationTypeOptions: REGULATION_TYPE_OPTIONS,
    };
  },
  created() {
    this.fetchRegulationsList();
  },
  methods: {
    // 获取规章制度列表
    fetchRegulationsList(params = {}) {
      this.loading = true;

      // 构造查询参数
      const queryParams = {
        [this.regulationApi.uuidField]: this.currentUuid,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        ...params,
      };

      // 调用接口获取数据
      this.regulationApi
        .page(queryParams)
        .then((res) => {
          if (res.code === 0) {
            this.regulationsList = res.data || [];
            this.total = res.count || 0;
          } else {
            this.$message.error(res.message || "获取规章制度列表失败");
          }
        })
        .catch((error) => {
          console.error("获取规章制度列表失败:", error);
          this.$message.error("获取规章制度列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 打开上传弹窗
    openUploadDialog() {
      this.uploadDialogVisible = true;
    },

    // 处理上传成功
    handleUploadSuccess() {
      this.fetchRegulationsList();
    },

    // 处理预览文件
    handlePreview(row) {
      if (!row.attachmentUrl) {
        this.$message.warning("该文件暂无可预览的链接");
        return;
      }

      const fileUrl = this.imageSrc + row.attachmentUrl;
      const fileName = row.fileName || "";

      // 判断是否为图片类型
      if (this.$refs.imagePreviewViewer && this.$refs.imagePreviewViewer.isImageFile(fileName)) {
        // 图片类型使用el-image预览
        this.$refs.imagePreviewViewer.preview(fileUrl, fileName);
      } else if (this.isPdfFile(fileName)) {
        // PDF文件在默认浏览器中打开
        window.open(fileUrl, '_blank');
      } else {
        // 其他文件类型使用弹窗预览
        this.selectedFileInfo = {
          fileName: row.fileName,
          attachmentUrl: fileUrl,
          fileSize: row.fileSize,
          fileType: row.fileType,
        };
        this.previewDialogVisible = true;
      }
    },

    // 处理下载文件
    handleDownload(row) {
      if (!row.attachmentUrl) {
        this.$message.warning("该规章制度暂无可下载的文件");
        return;
      }

      try {
        // 创建一个隐藏的a标签进行下载
        const link = document.createElement("a");
        link.href = this.imageSrc + row.attachmentUrl;
        link.download = row.fileName || "规章制度文件";
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success("开始下载文件");
      } catch (error) {
        console.error("下载文件失败:", error);
        this.$message.error("下载文件失败");
      }
    },

    // 处理单个删除
    handleDelete(row) {
      this.deleteType = "single";
      this.currentRegulation = row;
      this.deleteDialogVisible = true;
    },

    // 处理批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的规章制度");
        return;
      }

      this.deleteType = "batch";
      this.deleteDialogVisible = true;
    },

    // 确认删除操作
    confirmDelete() {
      this.deleteLoading = true;

      // 获取要删除的规章制度UUID数组
      let uuidSet = [];
      if (this.deleteType === "single") {
        uuidSet = [this.currentRegulation.uuid];
      } else {
        uuidSet = this.selectedRows.map((row) => row.uuid);
      }

      // 调用批量删除接口
      this.regulationApi
        .delete({
          uuidSet: uuidSet,
        })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("删除成功");
            this.deleteDialogVisible = false;
            this.fetchRegulationsList();
          } else {
            this.$message.error(res.message || "删除失败");
          }
        })
        .catch((error) => {
          console.error("删除规章制度失败:", error);
          this.$message.error("删除规章制度失败");
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchRegulationsList();
    },

    // 页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchRegulationsList();
    },

    // 选择变更
    handleSelectionChange(val) {
      this.selectedRows = val;
    },

    // 获取规章类型名称
    getRegulationTypeName(type) {
      const option = this.regulationTypeOptions.find((item) => item.value === type);
      return option ? option.label : "-";
    },

    // 判断是否可以预览
    canPreview,

    // 获取文件类型图标
    getFileIcon,

    // 判断是否为PDF文件
    isPdfFile,

    // 格式化文件大小
    formatFileSize,
  },
};
</script>

<style lang="scss" scoped>
.center-regulations {
  width: 100%;

  .table-actions {
    display: flex;
    margin-bottom: 15px;
  }

  .regulations-table {
    margin-bottom: 20px;

    .file-info {
      display: flex;
      align-items: center;

      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2050d1;
      }

      .file-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .delete-btn {
    color: #f56c6c;
  }

  .delete-confirm {
    display: flex;
    flex-direction: column;
    align-items: center;

    .warning-icon {
      font-size: 48px;
      color: #e6a23c;
      margin-bottom: 20px;
    }

    p {
      font-size: 16px;
      text-align: center;

      .highlight {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
}
</style>
