<template>
  <!-- :show-close="false" -->
  <el-dialog
    title="上传文件"
    :visible.sync="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="handleDialogClosed"
  >
    <div class="upload-dialog-content">
      <!-- 文件类型选择 - 只有实验中心才显示 -->
      <div v-if="resourceType === 1" class="regulation-type-select">
        <span class="label">规章类型：</span>
        <el-select v-model="regulationType" placeholder="请选择规章类型">
          <el-option v-for="item in regulationTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>

      <!-- 文件上传组件 -->
      <div class="file-uploader-container">
        <multi-file-uploader
          ref="uploader"
          v-model="fileList"
          :action="uploadAction"
          :multiple="true"
          :limit="5"
          :file-size-limit="55"
          accept="*"
          button-text="选择文件"
          tip="支持批量上传文件，文件格式不限，最多只能上传 5 份文件，单个文件不超过50MB"
          @on-success="handleFileSuccess"
          @on-error="handleFileError"
        ></multi-file-uploader>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" :disabled="uploading">取消上传</el-button>
      <el-button type="primary" @click="startUpload" :loading="uploading">
        开始上传
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import MultiFileUploader from "@laboratory/components/FileUploader/MultiFileUploader.vue";
import { labCenterRegulationSave } from "@laboratory/api/laboratory/basicMessage";
import { labRegulationSave } from "@laboratory/api/laboratory/laboratory";
import { REGULATION_TYPE_OPTIONS } from "@laboratory/constants/options";

export default {
  name: "RegulationUploadDialog",
  components: {
    MultiFileUploader,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    centerUuid: {
      type: String,
      default: "",
    },
    labUuid: {
      type: String,
      default: "",
    },
    resourceType: {
      type: Number,
      default: 1, // 1: 实验中心, 2: 实验室
    },
  },
  data() {
    return {
      // 规章类型
      regulationType: "",
      // 文件列表
      fileList: [],
      // 上传状态
      uploading: false,
      // 上传成功计数
      successCount: 0,
      // 上传失败计数
      failCount: 0,
      // 规章类型选项
      regulationTypeOptions: REGULATION_TYPE_OPTIONS,
    };
  },
  computed: {
    // 当前资源的UUID
    currentUuid() {
      return this.resourceType === 2 ? this.labUuid : this.centerUuid;
    },
    // 动态API映射
    regulationApi() {
      return {
        1: {
          // 实验中心
          save: labCenterRegulationSave,
          uuidField: "labCenterUuid",
        },
        2: {
          // 实验室
          save: labRegulationSave,
          uuidField: "labUuid",
        },
      }[this.resourceType];
    },
    // 控制弹窗显示
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    // 上传接口地址
    uploadAction() {
      return process.env.VUE_APP_BASE_API + "/file/upload";
    },
  },
  methods: {
    // 初始化弹窗
    initDialog() {
      this.regulationType = "";
      this.fileList = [];
      this.uploading = false;
      this.successCount = 0;
      this.failCount = 0;
    },

    // 处理文件上传成功
    handleFileSuccess(response, file) {
      console.log(6, response, file);
      // 在 MultiFileUploader 组件中，文件状态已经被更新为 success
      // 文件 URL 已经在组件内部设置好了，我们只需要保存规章制度信息
      this.saveRegulation(file);
    },

    // 处理文件上传失败
    handleFileError(error, file) {
      // 在 MultiFileUploader 组件中，文件状态已经被更新为 error
      this.failCount++;
      console.log(222);
      this.checkUploadComplete();
      this.error = error;
    },

    // 开始上传
    startUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning("请至少选择一个文件");
        return;
      }

      // 只有实验中心才需要校验规章类型
      if (this.resourceType === 1 && !this.regulationType) {
        this.$message.warning("请选择规章制度类型");
        return;
      }

      this.successCount = 0;
      this.failCount = 0;
      this.uploading = true;
      // 使用 MultiFileUploader 的 submitUpload 方法上传所有文件
      this.$refs.uploader.submitUpload();
    },

    // 保存规章制度信息
    saveRegulation(file) {
      // 获取文件扩展名作为文件类型
      const fileType = file.name
        .split(".")
        .pop()
        .toUpperCase();

      // 构造保存参数
      const params = {
        [this.regulationApi.uuidField]: this.currentUuid,
        attachmentUrl: file.url, // MultiFileUploader 已经在 file.url 中设置了上传后的 URL
      };

      // 只有实验中心才传递规章类型
      if (this.resourceType === 1) {
        params.regulationType = this.regulationType;
      }

      // 调用保存接口
      this.regulationApi
        .save(params)
        .then((res) => {
          if (res.code === 0) {
            console.log("规章制度保存成功:", file.name);
            this.successCount++;
          } else {
            console.error("规章制度保存失败:", file.name, res.msg);
            this.failCount++;
          }
        })
        .finally(() => {
          // 检查是否所有文件都已处理完成
          console.log(111);
          this.checkUploadComplete();
        });
    },

    // 检查上传是否全部完成
    checkUploadComplete() {
      // 获取需要处理的文件总数
      // const totalFiles = this.fileList.filter((file) => file.status === "success").length;
      const totalFiles = this.fileList.length;
      if (this.successCount + this.failCount === totalFiles) {
        // 显示上传结果
        if (this.failCount === 0) {
          this.$message.success(`成功保存 ${this.successCount} 个规章制度`);
        } else {
          if (this.error) {
            this.$message.warning(
              `处理完成，成功 ${this.successCount} 个，失败 ${this.failCount} 个,失败原因：${this.error ? this.error.message : "未知错误"}`,
            );
          }
        }
        this.uploading = false;
        this.closeDialog();
        this.$emit("upload-success");
      }
    },

    // 处理移除文件
    handleRemoveFile(index) {
      this.fileList.splice(index, 1);
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false;
    },

    // 处理弹窗关闭事件
    handleDialogClosed() {
      this.initDialog();
    },

    // 状态类型和文本由 MultiFileUploader 组件内部处理，以下方法将被废弃
    // 获取状态类型（用于兼容）
    getStatusType(status) {
      switch (status) {
        case "success":
          return "success";
        case "error":
          return "danger";
        case "uploading":
          return "warning";
        default:
          return "info";
      }
    },

    // 获取状态文本（用于兼容）
    getStatusText(status) {
      switch (status) {
        case "success":
          return "上传成功";
        case "error":
          return "格式有误";
        case "uploading":
          return "上传中";
        default:
          return "待上传";
      }
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.initDialog();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-dialog-content {
  min-height: 300px;

  .regulation-type-select {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .label {
      margin-right: 10px;
      font-size: 14px;
    }

    .el-select {
      width: 200px;
    }
  }

  .file-uploader-container {
    margin-bottom: 20px;
  }

  .file-list-container {
    margin-top: 20px;
  }
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-dialog__footer {
  padding: 10px 20px 20px;
}

::v-deep .el-button--small {
  padding: 8px 15px;
  font-size: 12px;
}
</style>
