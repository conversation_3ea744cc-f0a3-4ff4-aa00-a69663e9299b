<template>
  <div class="personnel-info-container">
    <!-- 表格操作区 -->
    <div class="table-operations">
      <el-button plain type="primary" icon="el-icon-link" @click="handleBindPerson">绑定人员</el-button>
      <el-button type="danger" @click="handleBatchDelete" :disabled="!selectedPersons.length">批量解绑</el-button>
      <el-button class="nextStep" type="primary" @click="handleNextStep">下一步</el-button>
    </div>

    <!-- 表格主体 -->
    <table-pagetion
      ref="personTable"
      :table-data="personList"
      :columns="tableColumns"
      :pagination="paginationConfig"
      :show-pagination="true"
      :screenWidth="screenWidth"
      @page-size-change="handleSizeChange"
      @current-page-change="handleCurrentChange"
      @handleSelectionChange="handleSelectionChange"
      v-loading="loading"
    >
      <!-- 性别列 -->
      <template #sex="{ row }">
        {{ row.labPersonInfo && row.labPersonInfo.sex === 1 ? "男" : row.labPersonInfo && row.labPersonInfo.sex === 2 ? "女" : "保密" }}
      </template>

      <!-- 人员类型列 -->
      <template #personnelType="{ row }">
        {{ row.personnelType === 1 ? "专任" : "兼任" }}
      </template>

      <!-- 联系方式列 -->
      <template #phone="{ row }">
        {{ (row.labPersonInfo && row.labPersonInfo.phone) || "-" }}
      </template>

      <!-- 照片列 -->
      <template #personPhoto="{ row }">
        <el-image
          v-if="row.labPersonInfo && row.labPersonInfo.personPhoto"
          :src="imageSrc + row.labPersonInfo.personPhoto"
          style="width: 60px; height: 60px"
          :preview-src-list="[imageSrc + row.labPersonInfo.personPhoto]"
        ></el-image>
        <span v-else>-</span>
      </template>

      <!-- 操作列 -->
      <template #operation="{ row }">
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-button type="text" style="color: #D2585D" @click="handleDelete(row)">解绑</el-button>
      </template>
    </table-pagetion>

    <!-- 底部操作区 -->
    <!-- <div class="bottom-actions">
      <el-button type="primary" @click="handleNextStep">下一步</el-button>
    </div> -->

    <!-- 绑定人员弹窗组件 -->
    <staff-binding-dialog
      :visible.sync="dialogVisible"
      :centerUuid="centerUuid"
      :labUuid="labUuid"
      :currentUuid="currentUuid"
      :resourceType="resourceType"
      :isEdit="dialogType === 'edit'"
      :editData="dialogType === 'edit' ? currentEditData : null"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script>
import { labCenterPersonBindPage, labCenterPersonBindDelete } from "@laboratory/api/laboratory/basicMessage";
import { labPersonBindPage, labPersonBindDelete } from "@laboratory/api/laboratory/laboratory";
import StaffBindingDialog from "@laboratory/components/StaffBindingDialog.vue";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";

export default {
  name: "PersonnelInfo",
  components: {
    StaffBindingDialog,
    TablePagetion,
  },
  props: {
    centerUuid: {
      type: String,
      default: "",
    },
    labUuid: {
      type: String,
      default: "",
    },
    resourceType: {
      type: Number,
      default: 1, // 1: 实验中心, 2: 实验室
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    // 当前资源的UUID
    currentUuid() {
      return this.resourceType === 2 ? this.labUuid : this.centerUuid;
    },
    // 动态API映射
    personApi() {
      return {
        1: {
          // 实验中心
          bindPage: labCenterPersonBindPage,
          bindDelete: labCenterPersonBindDelete,
          uuidField: "labCenterUuid",
        },
        2: {
          // 实验室
          bindPage: labPersonBindPage,
          bindDelete: labPersonBindDelete,
          uuidField: "labUuid",
        },
      }[this.resourceType];
    },
    // 分页配置
    paginationConfig() {
      return {
        currentPage: this.pageNum,
        pageSize: this.pageSize,
        pageSizes: [10, 20, 50, 100],
        total: this.total,
      };
    },
  },
  data() {
    return {
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      loading: false,
      personList: [],
      selectedPersons: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogType: null,
      currentEditData: null,
      // 表格列配置
      tableColumns: [
        { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "index", label: "序号", type: "index", width: "60" },
        { prop: "labPersonInfo.trueName", label: "姓名", width: "110" },
        { prop: "labPersonInfo.sex", label: "性别", slotName: "sex" },
        { prop: "labPersonInfo.birthday", label: "出生年月日", width: "100" },
        { prop: "labPersonInfo.deptName", label: "单位" },
        { prop: "labPersonInfo.personnelAcademicName", label: "所属学科" },
        { prop: "personnelType", label: "人员类型", slotName: "personnelType" },
        { prop: "labPersonInfo.technicalPositionName", label: "专业技术职务", width: "110" },
        { prop: "position", label: "职称" },
        { prop: "labPersonInfo.educationalLevelName", label: "文化程度", width: "110" },
        { prop: "labPersonInfo.expertCategoryName", label: "专家类别" },
        { prop: "labPersonInfo.degreeName", label: "学位" },
        { prop: "labPersonInfo.email", label: "邮箱", width: "150" },
        { prop: "labPersonInfo.phone", label: "联系方式", slotName: "phone", width: "130" },
        { prop: "sysRoleName", label: "系统角色", width: "130" },
        { prop: "labPersonInfo.personPhoto", label: "照片", slotName: "personPhoto" },
        { prop: "operation", label: "操作", fixed: "right", width: "120", slotName: "operation" },
      ],
    };
  },
  created() {
    this.fetchPersonList();
  },
  methods: {
    fetchPersonList() {
      this.loading = true;
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        [this.personApi.uuidField]: this.currentUuid,
      };
      this.personApi
        .bindPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.personList = res.data || [];
            this.total = res.count || 0;
          }
        })

        .finally(() => {
          this.loading = false;
        });
    },

    handleSelectionChange(selection) {
      this.selectedPersons = selection;
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.pageNum = 1;
      this.fetchPersonList();
    },

    handleCurrentChange(page) {
      this.pageNum = page;
      this.fetchPersonList();
    },

    handleDelete(row) {
      this.$confirm("确认解绑该人员?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deletePersons([row.uuid]);
        })
        .catch(() => {});
    },

    handleBatchDelete() {
      if (this.selectedPersons.length === 0) {
        this.$message.warning("请选择要解绑的人员");
        return;
      }

      this.$confirm(`确认解绑选中的 ${this.selectedPersons.length} 名人员?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const uuids = this.selectedPersons.map((item) => item.uuid);
          this.deletePersons(uuids);
        })
        .catch(() => {});
    },

    deletePersons(uuidSet) {
      this.loading = true;
      this.personApi
        .bindDelete({ uuidSet })
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("解绑成功");
            this.fetchPersonList();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    handleBindPerson() {
      this.dialogType = null;
      this.dialogVisible = true;
    },

    handleDialogSuccess() {
      this.fetchPersonList();
    },

    handleNextStep() {
      this.$emit("next-step");
    },

    handleEdit(row) {
      this.currentEditData = row;
      // this.currentEditData = JSON.parse(JSON.stringify(row));
      console.log(row, "row");
      this.dialogType = "edit";
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.personnel-info-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;

  .table-operations {
    margin-bottom: 15px;
    display: flex;
    justify-content: flex-start;

    .nextStep {
      margin-left: auto;
    }
  }

  .bottom-actions {
    margin-top: 10px;
    margin-left: 50px;
  }
}
</style>
