<template>
  <layoutContent>
    <!-- 返回按钮和编辑按钮放在search插槽中 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ centerDetail.roomKindName || "加载中..." }}</span>
      </div>
      <el-button type="primary" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
    </div>

    <!-- 使用封装的顶部信息卡片组件 -->
    <info-detail-header
      :centerDetail="centerDetail"
      :title="centerDetail.roomKindName || '加载中...'"
      :image-path="centerDetail.roomKindImage"
      :image-prefix="imageSrc"
      :info-items="headerInfoItems"
      :loading="loading"
    ></info-detail-header>

    <!-- 标签页导航 -->
    <div class="center-detail-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="中心介绍" name="intro" lazy>
          <div class="tab-content intro-content" v-loading="loading" v-if="activeTab === 'intro'">
            <div v-if="centerDetail.roomKindDesc">
              <Editor style="overflow-y: hidden;" :value="centerDetail.roomKindDesc" :defaultConfig="editorConfig" mode="simple" />
            </div>
            <el-empty v-else description="暂无中心介绍信息"></el-empty>
          </div>
        </el-tab-pane>
        <el-tab-pane name="staff" lazy>
          <span slot="label">
            人员信息
            <el-badge v-if="summaryData.personnelCount > 0" :value="summaryData.personnelCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'staff'">
            <center-staff-info :centerUuid="centerUuid" :loading="tabLoading.staff" @fetchSummaryData="fetchSummaryData"></center-staff-info>
          </div>
        </el-tab-pane>
        <el-tab-pane name="labs" lazy>
          <span slot="label">
            实验室
            <el-badge v-if="summaryData.labCount > 0" :value="summaryData.labCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content labs-tab-content" v-if="activeTab === 'labs'">
            <labs-list mode="center" :centerUuid="centerUuid" :loading="tabLoading.labs" @bindLab="handleBindLab"></labs-list>
          </div>
        </el-tab-pane>
        <el-tab-pane name="equipment" lazy>
          <span slot="label">
            仪器设备
            <el-badge v-if="summaryData.deviceCount > 0" :value="summaryData.deviceCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'equipment'">
            <center-equipment-list :centerUuid="centerUuid" :loading="tabLoading.equipment"></center-equipment-list>
          </div>
        </el-tab-pane>
        <el-tab-pane name="rules" lazy>
          <span slot="label">
            规章制度
            <el-badge v-if="summaryData.regulationCount > 0" :value="summaryData.regulationCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'rules'">
            <center-rules-list :centerUuid="centerUuid" :loading="tabLoading.rules"></center-rules-list>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import InfoDetailHeader from "@laboratory/components/InfoDetailHeader.vue";
import { labCenterDetail, labCenterDelete, labCenterSummary } from "@laboratory/api/laboratory/basicMessage";
import { Editor } from "@wangeditor/editor-for-vue";
// 导入各个标签页的组件
import CenterStaffInfo from "@centerManage/centerDetailCom/CenterStaffInfo.vue";
import LabsList from "@basicInfoMange/basicInfo/labManagerCom/LabsList.vue";
import CenterEquipmentList from "@centerManage/centerDetailCom/CenterEquipmentList.vue";
import CenterRulesList from "@centerManage/centerDetailCom/CenterRulesList.vue";
import { CENTER_TYPE_OPTIONS, CENTER_LEVEL_OPTIONS, getLabelByValue } from "@laboratory/constants/options";

export default {
  name: "centerDetail",
  components: {
    layoutContent,
    InfoDetailHeader,
    Editor,
    CenterStaffInfo,
    LabsList,
    CenterEquipmentList,
    CenterRulesList,
  },
  data() {
    return {
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      centerUuid: "",
      centerDetail: {},
      loading: false,
      activeTab: "intro",
      editorConfig: {
        readOnly: true,
        // 可根据需要添加其他编辑器配置
      },
      // 各标签页的加载状态
      tabLoading: {
        staff: false,
        labs: false,
        equipment: false,
        rules: false,
      },
      // 概览数据
      summaryData: {
        personnelCount: 0,
        labCount: 0,
        deviceCount: 0,
        regulationCount: 0,
      },
    };
  },
  computed: {
    // 计算顶部信息卡片的数据
    headerInfoItems() {
      const { contactInfo, roomKindKind, roomKindLevel, deptName, responsiblePerson, safetyOfficer, roomKindAddress } = this.centerDetail;
      return [
        { label: "中心联系方式", value: contactInfo || "-" },
        { label: "中心类型", value: getLabelByValue(CENTER_TYPE_OPTIONS, roomKindKind) || "-" },
        { label: "中心级别", value: getLabelByValue(CENTER_LEVEL_OPTIONS, roomKindLevel) || "-" },
        { label: "所属部门", value: deptName || "-" },
        { label: "负责人", value: responsiblePerson || "-" },
        { label: "安全负责人", value: safetyOfficer || "-" },
        { label: "地址", value: roomKindAddress || "-" },
      ];
    },
  },
  created() {
    // 从路由参数中获取中心UUID
    this.centerUuid = this.$route.params.uuid;
    if (!this.centerUuid) {
      this.$message.error("缺少必要的实验中心标识");
      this.goBack();
      return;
    }

    // 获取实验中心详情
    this.fetchCenterDetail();
    // 获取概览信息
    this.fetchSummaryData();
  },
  methods: {
    // 获取实验中心详情
    fetchCenterDetail() {
      if (!this.centerUuid) return;

      this.loading = true;
      labCenterDetail({ uuid: this.centerUuid })
        .then((res) => {
          if (res.code === 0) {
            this.centerDetail = res.data || {};
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取概览数据
    fetchSummaryData() {
      if (!this.centerUuid) return;

      labCenterSummary({ uuid: this.centerUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            this.summaryData = {
              personnelCount: res.data.personnelCount || 0,
              labCount: res.data.labCount || 0,
              deviceCount: res.data.deviceCount || 0,
              regulationCount: res.data.regulationCount || 0,
            };
          }
        })
        .catch((error) => {
          console.error("获取概览数据失败:", error);
        });
    },
    // 返回列表页
    goBack() {
      this.$router.push({ name: "centerManage" });
    },

    // 实验中心编辑
    handleEdit(currentStep) {
      if (this.centerUuid) {
        this.$router.push({
          name: "centerEdit",
          params: { uuid: this.centerUuid },
          query: { from: "detail", currentStep }, // 标记来源为详情页
        });
      } else {
        this.$message.error("编辑失败：无效的实验中心信息");
      }
    },

    // 处理绑定实验室事件
    handleBindLab(centerUuid) {
      // 在这里实现打开绑定实验室弹窗的逻辑
      this.handleEdit(4);
      console.log("绑定实验室到中心", centerUuid);
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-button + .el-button {
    margin-left: 10px;
  }

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.center-detail-tabs {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 0;

  .tab-content {
    min-height: 300px;
    padding: 20px;

    &.intro-content {
      line-height: 1.6;

      /* 富文本样式 */
      :deep(.w-e-text-container) {
        height: auto !important;
      }

      :deep(.w-e-text) {
        padding: 0;
        overflow: visible;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        margin: 10px auto;
        display: block;
      }

      :deep(p) {
        margin-bottom: 15px;
      }

      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;

        th,
        td {
          border: 1px solid #ddd;
          padding: 8px 12px;
        }

        th {
          background-color: #f6f6f6;
        }
      }
    }

    &.labs-tab-content {
      padding: 0;

      /* 确保实验室列表样式与centerManage保持一致 */
      :deep(.layout-center) {
        .search-section {
          padding: 20px;
          border-bottom: 1px solid #ebeef5;
          background-color: #fff;
        }

        .center-list {
          padding: 20px;
          // background-color: #f5f7fa;
          min-height: 400px;

          .card-container {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
          }
        }

        .pagination-section {
          background-color: #fff;
          margin: 0;
          border-top: 1px solid #ebeef5;
        }
      }
    }
  }
}
::v-deep .el-tabs__header {
  padding: 0 20px !important;
}
</style>
