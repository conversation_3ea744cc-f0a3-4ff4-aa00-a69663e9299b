<template>
  <div class="center-equipment-list">
    <!-- 搜索过滤区域 -->
    <div class="filter-style">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="仪器名称:">
          <el-input v-model="searchForm.devName" placeholder="请输入仪器名称" clearable />
        </el-form-item>
        <el-form-item label="仪器类型:">
          <el-select v-model="searchForm.devKindId" filterable placeholder="请选择仪器类型" clearable>
            <el-option
              v-for="item in deviceTypeOptions"
              :key="item.devKindId"
              :label="item.devKindName"
              :value="item.devKindId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="现状码:">
          <el-select v-model="searchForm.presentSituationCode" filterable placeholder="请选择现状码" clearable>
            <el-option v-for="item in presentSituationOptions" :key="item.codeValue" :label="item.name" :value="item.codeValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button @click="handleResetSearch" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <!-- 仪器设备网格展示 -->
    <div v-else-if="equipmentList && equipmentList.length > 0" class="equipment-container">
      <!-- 设备卡片网格 -->
      <div class="equipment-grid">
        <lab-center-card
          v-for="(item, index) in equipmentList"
          :key="item.uuid || index"
          :item="transformDeviceData(item)"
          :imageSrc="imageSrc"
          :showIcon="true"
          :useCustomInfoItems="true"
          :infoItems="getDeviceInfoItems(item)"
          :actions="false"
          :viewDeptName="false"
          class="equipment-card"
          @click.native="handleViewDetail(item)"
        >
          <template #status-text>
            <div class="card-icon">
              <i class="iconfont icon-bianyuan" :class="item.device.presentSituationCode == 1 ? 'icon-success' : 'icon-warning'" />
              <div class="text">
                {{ getStatusText(item.device.presentSituationCode) }}
              </div>
            </div>
          </template>
        </lab-center-card>
      </div>

      <!-- 分页组件 -->
      <div class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[12, 24, 36, 48]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
    <el-empty v-else description="暂无仪器设备信息" />

    <!-- 设备详情弹窗 -->
    <device-detail-dialog :visible.sync="showDetailDialog" :device-uuid="currentDeviceUuid" api-type="detail" :mode="mode" />
  </div>
</template>

<script>
import { labCenterDevicePage, getApiDevKinAll, getDictAll } from "@laboratory/api/laboratory/basicMessage";
import { labDevicePage } from "@laboratory/api/laboratory/laboratory";
import DeviceDetailDialog from "@centerManage/centerAddCom/CenterDeviceInfoCom/DeviceDetailDialog.vue";
import LabCenterCard from "@laboratory/components/LabCenterCard.vue";

export default {
  name: "CenterEquipmentList",
  components: {
    DeviceDetailDialog,
    LabCenterCard,
  },
  props: {
    centerUuid: {
      type: String,
      required: false,
    },
    labUuid: {
      type: String,
      required: false,
    },
    mode: {
      type: String,
      default: "center", // 'center' 或 'lab'
      validator: (value) => ["center", "lab"].includes(value),
    },
  },
  data() {
    return {
      loading: false,
      equipmentList: [],
      pageSize: 12,
      currentPage: 1,
      total: 0,
      searchForm: {
        devName: "",
        devKindId: "",
        presentSituationCode: "",
      },
      deviceTypeOptions: [], // 设备类型选项
      presentSituationOptions: [], // 现状码选项
      showDetailDialog: false,
      currentDeviceUuid: "",
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    // 计算当前使用的UUID
    currentUuid() {
      return this.mode === "center" ? this.centerUuid : this.labUuid;
    },
    // 接口映射
    apiMap() {
      return {
        center: {
          devicePage: labCenterDevicePage,
        },
        lab: {
          devicePage: labDevicePage,
        },
      };
    },
  },
  created() {
    // 获取设备类型选项
    this.fetchDeviceTypes();
    // 获取现状码选项
    this.fetchPresentSituationOptions();

    // if (this.currentUuid) {
    //   this.fetchEquipmentData();
    // }
  },
  watch: {
    currentUuid: {
      handler(newVal) {
        if (newVal) {
          this.fetchEquipmentData();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取设备类型选项
    fetchDeviceTypes() {
      getApiDevKinAll()
        .then((res) => {
          if (res.code === 0) {
            this.deviceTypeOptions = res.data || [];
          } else {
            console.error("获取设备类型失败:", res.message);
          }
        })
        .catch((error) => {
          console.error("获取设备类型失败:", error);
        });
    },

    // 获取现状码选项
    fetchPresentSituationOptions() {
      getDictAll({ codeType: 1012 })
        .then((res) => {
          if (res.code === 0) {
            this.presentSituationOptions = res.data || [];
          } else {
            console.error("获取现状码字典失败:", res.message);
          }
        })
        .catch((error) => {
          console.error("获取现状码字典失败:", error);
        });
    },

    // 转换设备数据为LabCenterCard组件所需格式
    transformDeviceData(item) {
      return {
        roomKindName: item.device.devName || "未命名设备",
        roomKindImage: item.device.imgUrl || "",
        deptName: item.device.devModel || "型号未知",
      };
    },

    // 获取设备信息项
    getDeviceInfoItems(item) {
      return [
        {
          label: "型号",
          value: item.device.devModel || "-",
          icon: "iconfont icon-bianzu",
        },
        {
          label: "仪器类型",
          value: item.device.devKindName || "-",
          icon: "iconfont icon-xingzhuangjiehe",
        },
        {
          label: "存放地点",
          value: item.device.storageLocation || "-",
          icon: "iconfont icon-a-bianzu5",
        },
      ];
    },

    // 获取状态文本
    getStatusText(code) {
      if (!code) return "未知";

      // 从字典数据中查找对应的状态名称
      const statusOption = this.presentSituationOptions.find((item) => item.codeValue === code);
      if (statusOption) {
        return statusOption.name;
      }

      // 如果字典数据还未加载，使用默认逻辑
      return code == "1" ? "在用" : "停用";
    },
    // 搜索设备
    handleSearch() {
      this.currentPage = 1;
      this.fetchEquipmentData();
    },

    // 重置搜索
    handleResetSearch() {
      this.searchForm = {
        devName: "",
        devKindId: "",
        presentSituationCode: "",
      };
      this.currentPage = 1;
      this.fetchEquipmentData();
    },

    // 获取设备数据（支持实验中心和实验室两种模式）
    fetchEquipmentData() {
      this.loading = true;

      // 根据模式构建不同的参数
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        orderItems: "gmtCreate",
        orderRule: "desc",
        devName: this.searchForm.devName || undefined,
        presentSituationCode: this.searchForm.presentSituationCode || undefined,
      };

      // 根据模式设置不同的UUID参数名
      if (this.mode === "center") {
        params.labCenterUuid = this.currentUuid;
        params.devKindName = this.searchForm.devKindId ? this.getDevKindNameById(this.searchForm.devKindId) : undefined;
      } else {
        params.labUuid = this.currentUuid;
        params.devKindName = this.searchForm.devKindId ? this.getDevKindNameById(this.searchForm.devKindId) : undefined;
      }

      // 调用对应的接口
      this.apiMap[this.mode]
        .devicePage(params)
        .then((res) => {
          if (res.code === 0) {
            this.equipmentList = res.data || [];
            this.total = res.count || 0;
          } else {
            this.$message.error(res.message || "获取设备列表失败");
          }
        })
        .catch((error) => {
          console.error("获取设备列表失败:", error);
          this.$message.error("获取设备列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 查看设备详情
    handleViewDetail(row) {
      console.log(row, "row");
      this.currentDeviceUuid = row.device.uuid;
      this.showDetailDialog = true;
    },

    // 分页大小变化事件
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.fetchEquipmentData();
    },

    // 分页页码变化事件
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchEquipmentData();
    },

    // 获取指定状态的设备数量
    getStatusCount(statusCode) {
      if (!this.equipmentList || this.equipmentList.length === 0) return 0;
      return this.equipmentList.filter((item) => item.device && item.device.presentSituationCode === statusCode).length;
    },

    // 计算设备总价值（万元）
    getTotalValue() {
      if (!this.equipmentList || this.equipmentList.length === 0) return 0;
      const total = this.equipmentList.reduce((sum, item) => {
        const price = item.device && item.device.price ? parseInt(item.device.price) : 0;
        return sum + price;
      }, 0);
      // 转换为万元，保留2位小数
      return (total / 100 / 10000).toFixed(2);
    },

    // 查看设备位置信息
    handleViewLocation(item) {
      if (item.device && item.device.storageLocation) {
        this.$message.info(`设备位置: ${item.device.storageLocation}`);
      } else {
        this.$message.warning("该设备暂无位置信息");
      }
    },

    // 根据设备类型ID获取设备类型名称
    getDevKindNameById(devKindId) {
      const deviceType = this.deviceTypeOptions.find((item) => item.devKindId === devKindId);
      return deviceType ? deviceType.devKindName : undefined;
    },
  },
};
</script>

<style lang="scss" scoped>
.center-equipment-list {
  min-height: 200px;

  .loading-container {
    padding: 20px;
  }

  .equipment-container {
    .equipment-grid {
      display: flex;
      flex-wrap: wrap;
      margin: -10px;
    }

    .equipment-card {
      cursor: pointer;
      transition: all 0.3s;
      width: calc(33.333% - 20px);

      @media screen and (max-width: 1600px) {
        width: calc(50% - 20px);
      }

      @media screen and (max-width: 1200px) {
        width: calc(100% - 20px);
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .el-empty {
    padding: 60px 0;
  }

  .status-tag {
    display: inline-block;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 4px;
    margin-top: 8px;
  }
}
</style>
