<template>
  <div class="center-rules-list">
    <!-- 搜索过滤区域 -->
    <div class="filter-style">
      <el-form :model="searchForm" :inline="true">
        <el-form-item label="文件名称:">
          <el-input v-model="searchForm.fileName" placeholder="请输入文件名" clearable />
        </el-form-item>
        <el-form-item label="规章类型:" v-if="mode === 'center'">
          <el-select v-model="searchForm.regulationType" placeholder="请选择规章类型" clearable>
            <el-option v-for="item in regulationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="更新时间:">
          <el-date-picker
            v-model="searchForm.updateTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            clearable
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button @click="handleResetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <!-- 规章制度表格展示 -->
    <div v-else-if="rulesList && rulesList.length > 0" class="rules-container">
      <table-pagetion
        ref="rulesTable"
        :table-data="rulesList"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        :screenWidth="screenWidth"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
      >
        <!-- 文件名称列 -->
        <template #fileName="{ row }">
          <div class="file-info">
            <i :class="getFileIcon(row.fileType)" class="file-icon"></i>
            <span class="file-name">{{ row.fileName || row.title || "-" }}</span>
          </div>
        </template>

        <!-- 规章类型列 -->
        <template #regulationType="{ row }">
          <el-tag size="mini" :type="row.regulationType === 1 ? 'warning' : 'primary'">
            {{ getRegulationTypeName(row.regulationType) }}
          </el-tag>
        </template>

        <!-- 更新时间列 -->
        <template #updateTime="{ row }">
          {{ row.gmtModified ? $moment(row.gmtModified).format("YYYY-MM-DD") : "-" }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" size="mini" @click="handlePreviewFile(row)" v-if="canPreviewFile(row.fileName)">
            预览
          </el-button>
          <!-- <el-button type="text" size="mini"  @click="handleViewRule(row)" v-if="!canPreviewFile(row.fileName)">
            查看
          </el-button> -->
          <el-button type="text" size="mini" @click="handleDownloadRule(row)" v-if="row.attachmentUrl">
            下载
          </el-button>
        </template>
      </table-pagetion>
    </div>
    <el-empty v-else :description="`暂无${mode === 'center' ? '规章制度' : '技术资料'}信息`" />

    <!-- 规章制度详情弹窗 -->
    <el-dialog
      :title="`${mode === 'center' ? '规章制度' : '技术资料'}详情`"
      :visible.sync="detailDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      class="rule-detail-dialog"
    >
      <div v-if="selectedRule" class="rule-detail-content">
        <div class="detail-header">
          <h3 class="detail-title">{{ selectedRule.title || selectedRule.fileName || "无标题" }}</h3>
          <div class="detail-meta">
            <el-tag :type="selectedRule.regulationType === 1 ? 'warning' : 'primary'">
              {{ selectedRule.regulationType === 1 ? "校级文件" : "上级文件" }}
            </el-tag>
            <span class="detail-date">
              {{ selectedRule.gmtCreate ? $moment(selectedRule.gmtCreate).format("YYYY-MM-DD") : "-" }}
            </span>
          </div>
        </div>

        <div class="detail-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名称">{{ selectedRule.fileName || "-" }}</el-descriptions-item>
            <el-descriptions-item label="文件类型">{{ selectedRule.fileType || "-" }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(selectedRule.fileSize) || "-" }}</el-descriptions-item>
            <el-descriptions-item label="下载次数">{{ selectedRule.downloadCount || 0 }}</el-descriptions-item>
            <el-descriptions-item label="发布状态">
              <el-tag :type="selectedRule.publishStatus === 2 ? 'success' : 'info'" size="mini">
                {{ selectedRule.publishStatus === 2 ? "已发布" : "草稿" }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ selectedRule.gmtModified ? $moment(selectedRule.gmtModified).format("YYYY-MM-DD") : "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section" v-if="selectedRule.subtitle">
          <h4>副标题</h4>
          <p>{{ selectedRule.subtitle }}</p>
        </div>

        <div class="detail-section" v-if="selectedRule.tags">
          <h4>标签</h4>
          <div class="tags-list">
            <el-tag v-for="tag in selectedRule.tags.split(',')" :key="tag" class="tag-item">
              {{ tag.trim() }}
            </el-tag>
          </div>
        </div>

        <div class="detail-section" v-if="selectedRule.memo">
          <h4>备注说明</h4>
          <p>{{ selectedRule.memo }}</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleDownloadRule(selectedRule)" v-if="selectedRule && selectedRule.attachmentUrl">
          下载文件
        </el-button>
      </div>
    </el-dialog>

    <!-- 文件预览弹窗 -->
    <file-preview-dialog :visible.sync="previewDialogVisible" :file-info="selectedFileInfo" />

    <!-- 图片预览组件 -->
    <image-preview-viewer ref="imagePreviewViewer" />
  </div>
</template>

<script>
import { labCenterRegulationPage } from "@laboratory/api/laboratory/basicMessage";
import { labRegulationPage } from "@laboratory/api/laboratory/laboratory";
import { REGULATION_TYPE_OPTIONS, getLabelByValue } from "@laboratory/constants/options";
import { canPreview, getFileIcon, isPdfFile } from "@laboratory/utils/fileUtils";
import TablePagetion from "@laboratory/components/TablePagetion/index.vue";
import FilePreviewDialog from "@laboratory/components/FilePreviewDialog/index.vue";
import ImagePreviewViewer from "@laboratory/components/ImagePreviewViewer/index.vue";

export default {
  name: "CenterRulesList",
  components: {
    TablePagetion,
    FilePreviewDialog,
    ImagePreviewViewer,
  },
  props: {
    centerUuid: {
      type: String,
      required: false,
    },
    labUuid: {
      type: String,
      required: false,
    },
    mode: {
      type: String,
      default: "center", // 'center' 或 'lab'
      validator: (value) => ["center", "lab"].includes(value),
    },
    screenWidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: false,
      rulesList: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      searchForm: {
        fileName: "",
        regulationType: "",
        updateTimeRange: null,
      },
      detailDialogVisible: false,
      selectedRule: null,
      regulationTypeOptions: REGULATION_TYPE_OPTIONS,

      // 文件预览相关
      previewDialogVisible: false,
      selectedFileInfo: {},

      // 文件URL前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),

      // 基础表格列配置
      baseTableColumns: [
        { prop: "index", label: "序号", type: "index", width: "80" },
        { prop: "fileName", label: "文件名称", slotName: "fileName", width: "" },
        { prop: "updateTime", label: "更新时间", slotName: "updateTime", width: "180" },
        {
          prop: "operation",
          label: "操作",
          fixed: "right",
          width: "160",
          slotName: "operation",
        },
      ],
    };
  },
  computed: {
    // 分页配置
    paginationConfig() {
      return {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        total: this.total,
        pageSizes: [10, 20, 30, 50],
      };
    },
    // 计算当前使用的UUID
    currentUuid() {
      return this.mode === "center" ? this.centerUuid : this.labUuid;
    },
    // 接口映射
    apiMap() {
      return {
        center: {
          regulationPage: labCenterRegulationPage,
        },
        lab: {
          regulationPage: labRegulationPage,
        },
      };
    },
    // 动态表格列配置
    tableColumns() {
      const columns = [...this.baseTableColumns];

      // 如果是实验中心模式，在文件名称后插入规章类型列
      if (this.mode === "center") {
        columns.splice(2, 0, {
          prop: "regulationType",
          label: "规章类型",
          slotName: "regulationType",
          width: "120",
        });
      }

      return columns;
    },
  },
  created() {
    // if (this.currentUuid) {
    //   this.fetchRulesData();
    // }
  },
  watch: {
    currentUuid: {
      handler(newVal) {
        if (newVal) {
          this.fetchRulesData();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取规章制度数据（支持实验中心和实验室两种模式）
    fetchRulesData() {
      this.loading = true;

      // 处理日期范围
      let beginDate, endDate;
      if (this.searchForm.updateTimeRange && this.searchForm.updateTimeRange.length === 2) {
        beginDate = this.searchForm.updateTimeRange[0];
        endDate = this.searchForm.updateTimeRange[1];
      }

      // 根据模式构建不同的参数
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        orderItems: "gmtModified",
        orderRule: "desc",
        fileName: this.searchForm.fileName || undefined,
        beginDate: beginDate || undefined,
        endDate: endDate || undefined,
      };

      // 根据模式设置不同的UUID参数名和特有参数
      if (this.mode === "center") {
        params.labCenterUuid = this.currentUuid;
        params.regulationType = this.searchForm.regulationType || undefined; // 实验中心有规章类型
      } else {
        params.labUuid = this.currentUuid;
        // 实验室模式不需要regulationType参数
      }

      // 调用对应的接口
      this.apiMap[this.mode]
        .regulationPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.rulesList = res.data || [];
            this.total = res.count || 0;
          } else {
            this.$message.error(res.message || "获取规章制度列表失败");
          }
        })
        .catch((error) => {
          console.error("获取规章制度列表失败:", error);
          this.$message.error("获取规章制度列表失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 搜索规章制度
    handleSearch() {
      this.currentPage = 1;
      this.fetchRulesData();
    },

    // 重置搜索
    handleResetSearch() {
      this.searchForm = {
        fileName: "",
        regulationType: "",
        updateTimeRange: null,
      };
      this.currentPage = 1;
      this.fetchRulesData();
    },

    // 查看规章制度详情
    handleViewRule(rule) {
      this.selectedRule = rule;
      this.detailDialogVisible = true;
    },

    // 预览文件
    handlePreviewFile(rule) {
      if (!rule.attachmentUrl) {
        this.$message.warning("该文件暂无可预览的链接");
        return;
      }

      const fileUrl = this.imageSrc + rule.attachmentUrl;
      const fileName = rule.fileName || "";

      // 判断是否为图片类型
      if (this.$refs.imagePreviewViewer && this.$refs.imagePreviewViewer.isImageFile(fileName)) {
        // 图片类型使用el-image预览
        this.$refs.imagePreviewViewer.preview(fileUrl, fileName);
      } else if (this.isPdfFile(fileName)) {
        // PDF文件在默认浏览器中打开
        window.open(fileUrl, "_blank");
      } else {
        // 其他文件类型使用弹窗预览
        this.selectedFileInfo = {
          fileName: rule.fileName,
          attachmentUrl: fileUrl,
          fileSize: rule.fileSize,
          fileType: rule.fileType,
        };
        this.previewDialogVisible = true;
      }
    },

    // 判断文件是否可以预览
    canPreviewFile(fileName) {
      return canPreview(fileName);
    },

    // 下载规章制度附件
    handleDownloadRule(rule) {
      if (!rule.attachmentUrl) {
        this.$message.warning("该规章制度暂无可下载的文件");
        return;
      }

      try {
        // 创建一个临时链接来下载文件
        const link = document.createElement("a");
        link.href = this.imageSrc + rule.attachmentUrl;
        link.download = rule.fileName || "规章制度文件";
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.$message.success("开始下载文件");
      } catch (error) {
        console.error("下载文件失败:", error);
        this.$message.error("下载文件失败");
      }
    },

    // 分页大小变化事件
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.fetchRulesData();
    },

    // 分页页码变化事件
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchRulesData();
    },

    // 获取文件类型图标
    getFileIcon,

    // 判断是否为PDF文件
    isPdfFile,

    // 获取规章类型名称
    getRegulationTypeName(regulationType) {
      return getLabelByValue(REGULATION_TYPE_OPTIONS, regulationType);
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return "";

      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));

      return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
    },
  },
};
</script>

<style lang="scss" scoped>
.center-rules-list {
  min-height: 200px;

  .loading-container {
    padding: 20px;
  }

  .rules-container {
    .file-info {
      display: flex;
      align-items: center;
      justify-content: center;
      .file-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #2050d1;
      }

      .file-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .el-empty {
    padding: 60px 0;
  }
}

.rule-detail-dialog {
  .rule-detail-content {
    .detail-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ebeef5;

      .detail-title {
        margin: 0 0 12px 0;
        font-size: 20px;
        font-weight: 600;
        color: #303133;
      }

      .detail-meta {
        display: flex;
        align-items: center;
        gap: 16px;

        .detail-date {
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .detail-info {
      margin-bottom: 24px;
    }

    .detail-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        display: flex;
        align-items: center;

        &:before {
          content: "";
          display: inline-block;
          width: 3px;
          height: 16px;
          background-color: #2050d1;
          margin-right: 8px;
        }
      }

      p {
        margin: 0;
        color: #606266;
        line-height: 1.6;
        padding-left: 11px;
      }

      .tags-list {
        padding-left: 11px;

        .tag-item {
          margin-right: 8px;
          margin-bottom: 6px;
        }
      }
    }
  }
}
</style>
