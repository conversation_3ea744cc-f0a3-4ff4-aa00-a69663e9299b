<template>
  <div class="center-staff-info">
    <div class="filter-style">
      <el-form :inline="true" :model="filterForm" class="demo-form-inline">
        <el-form-item label="姓名/学工号：">
          <el-input v-model="filterForm.nameOrId" placeholder="请输入姓名或学工号" clearable></el-input>
        </el-form-item>
        <el-form-item label="系统角色">
          <el-select v-model="filterForm.sysRole" placeholder="请选择" clearable>
            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button plain type="primary" icon="el-icon-plus" @click="openAddStaffDialog">绑定人员</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="staffList && staffList.length > 0" class="staff-container">
      <div class="staff-grid">
        <div class="staff-card" v-for="(item, index) in staffList" :key="index">
          <div class="staff-avatar">
            <img
              :src="imageSrc + (item.labPersonInfo && item.labPersonInfo.personPhoto ? item.labPersonInfo.personPhoto : '')"
              alt="人员头像"
              v-if="item.labPersonInfo && item.labPersonInfo.personPhoto"
            />
            <img src="@laboratory/assets/image/memberIcon.png" alt="默认头像" v-else />
            <!-- <div v-else class="default-avatar">
              <i :class="getGenderIcon(item.labPersonInfo && item.labPersonInfo.sex)"></i>
            </div> -->
          </div>
          <div class="staff-info">
            <div class="staff-header">
              <div class="staff-name">{{ item.labPersonInfo ? item.labPersonInfo.trueName : "-" }}</div>
              <div class="staff-role">{{ item.sysRoleName || "-" }}</div>
            </div>
            <div class="staff-detail">
              <div class="detail-item">
                <i class="iconfont icon-gerenzhongxin_0"></i>
                <span>学工号：{{ item.labPersonInfo ? item.labPersonInfo.logonName : "-" }}</span>
              </div>
              <div class="detail-item">
                <i class="iconfont icon-call"></i>
                <span>联系方式：{{ item.labPersonInfo && item.labPersonInfo.phone ? item.labPersonInfo.phone : "-" }}</span>
              </div>
            </div>
          </div>
          <!-- <div class="staff-actions">
            <el-tooltip content="编辑" placement="top">
              <el-button type="text" icon="el-icon-edit" @click.stop="openEditStaffDialog(item)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="text" icon="el-icon-delete" @click.stop="handleDeleteStaff(item)"></el-button>
            </el-tooltip>
          </div> -->
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[8, 16, 24, 32]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-empty v-else description="暂无人员信息"></el-empty>

    <!-- 绑定人员弹窗 -->
    <staff-binding-dialog
      :visible.sync="staffDialogVisible"
      :currentUuid="currentUuid"
      :resourceType="mode === 'center' ? 1 : 2"
      :is-edit="isEditMode"
      :edit-data="currentEditStaff"
      @success="handleStaffBindingSuccess"
      @closed="handleStaffBindingClosed"
      @roleOptions="getRoleOptions"
    />

    <!-- 删除确认弹窗 -->
    <el-dialog title="删除确认" :visible.sync="deleteDialogVisible" width="400px" :close-on-click-modal="false">
      <div class="delete-confirm-content">
        确定要删除
        <span class="delete-name">{{ deleteStaffName }}</span>
        吗？此操作不可恢复。
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDeleteStaff">确认删除</el-button>
      </div>
    </el-dialog>

    <!-- 人员详情弹窗 -->
    <staff-detail-dialog :visible.sync="staffDetailVisible" :staffData="selectedStaff" :allowEdit="false" @edit="handleEditFromDetail" />
  </div>
</template>

<script>
import { labCenterPersonPage, labCenterPersonBindDelete } from "@laboratory/api/laboratory/basicMessage";
import { labPersonBindPage, labPersonBindDelete } from "@laboratory/api/laboratory/laboratory";
import StaffBindingDialog from "@laboratory/components/StaffBindingDialog.vue";
import StaffDetailDialog from "./StaffDetailDialog.vue";

export default {
  name: "CenterStaffInfo",
  components: {
    StaffBindingDialog,
    StaffDetailDialog,
  },
  props: {
    centerUuid: {
      type: String,
      required: false,
    },
    labUuid: {
      type: String,
      required: false,
    },
    mode: {
      type: String,
      default: "center", // 'center' 或 'lab'
      validator: (value) => ["center", "lab"].includes(value),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      roleOptions: [],
      staffList: [],
      pageSize: 8, // 调整为8，更适合网格布局
      currentPage: 1,
      total: 0,
      innerLoading: false,
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      filterForm: {
        nameOrId: "", // 新增姓名或学工号搜索
        sysRole: "",
        personnelType: "",
      },
      staffDialogVisible: false, // 控制绑定人员弹窗显示
      isEditMode: false, // 是否为编辑模式
      currentEditStaff: {}, // 当前编辑的人员数据
      deleteDialogVisible: false, // 控制删除确认弹窗显示
      staffToDelete: null, // 待删除的人员
      staffDetailVisible: false, // 控制人员详情弹窗显示
      selectedStaff: null, // 选中的人员
    };
  },
  computed: {
    // 计算实际的loading状态，优先使用父组件传递的loading状态
    isLoading() {
      return this.loading || this.innerLoading;
    },
    // 获取待删除人员的姓名
    deleteStaffName() {
      if (this.staffToDelete && this.staffToDelete.labPersonInfo) {
        return this.staffToDelete.labPersonInfo.trueName || "该人员";
      }
      return "该人员";
    },
    // 计算当前使用的UUID
    currentUuid() {
      return this.mode === "center" ? this.centerUuid : this.labUuid;
    },
    // 接口映射
    apiMap() {
      return {
        center: {
          personPage: labCenterPersonPage,
          personBindDelete: labCenterPersonBindDelete,
        },
        lab: {
          personPage: labPersonBindPage,
          personBindDelete: labPersonBindDelete,
        },
      };
    },
  },
  created() {
    // 组件创建时初始化数据
    this.fetchStaffData();
  },
  methods: {
    // 获取人员数据（支持实验中心和实验室两种模式）
    fetchStaffData() {
      this.innerLoading = true;

      // 根据模式构建不同的参数
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        orderItems: "gmtCreate",
        orderRule: "desc",
      };

      // 根据模式设置不同的UUID参数名和搜索参数
      if (this.mode === "center") {
        params.labCenterUuid = this.currentUuid;
      } else {
        params.labUuid = this.currentUuid;
      }
      params.personKeyword = this.filterForm.nameOrId || undefined; // 实验中心使用personKeyword
      params.sysRole = this.filterForm.sysRole || undefined;
      // 调用对应的接口
      this.apiMap[this.mode]
        .personPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.staffList = res.data || [];
            this.total = res.count || 0;
          }
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },

    // 搜索按钮点击事件
    handleSearch() {
      this.currentPage = 1; // 重置页码
      console.log(this.filterForm, 'filterForm');
      this.fetchStaffData();
    },

    // 重置搜索条件
    handleReset() {
      this.filterForm = {
        nameOrId: "",
        sysRole: "",
      };
      this.currentPage = 1;
      this.fetchStaffData();
    },

    // 打开绑定人员弹窗
    openAddStaffDialog() {
      this.isEditMode = false;
      this.currentEditStaff = {};
      this.staffDialogVisible = true;
    },

    // 打开编辑人员弹窗
    openEditStaffDialog(staff) {
      this.isEditMode = true;
      this.currentEditStaff = staff;
      this.staffDialogVisible = true;
    },

    // 处理删除人员
    handleDeleteStaff(staff) {
      this.staffToDelete = staff;
      this.deleteDialogVisible = true;
    },

    // 确认删除人员
    confirmDeleteStaff() {
      if (!this.staffToDelete || !this.staffToDelete.uuid) {
        this.$message.error("删除失败：无效的人员信息");
        this.deleteDialogVisible = false;
        return;
      }

      // 调用删除API
      const params = {
        uuidSet: [this.staffToDelete.uuid],
      };

      // 根据模式调用对应的删除接口
      this.apiMap[this.mode]
        .personBindDelete(params)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("删除成功");
            this.fetchStaffData(); // 刷新列表
          }
        })
        .finally(() => {
          this.deleteDialogVisible = false;
        });
    },

    // 绑定人员成功回调
    handleStaffBindingSuccess() {
      this.fetchStaffData(); // 刷新人员列表
      // 刷新小红点列表
      this.$emit('fetchSummaryData')
    },

    // 绑定人员弹窗关闭回调
    handleStaffBindingClosed() {
      // 弹窗关闭后的处理，如果需要的话
    },
    getRoleOptions(roleOptions) {
      this.roleOptions = roleOptions;
    },

    // 查看人员详情
    handleViewStaffDetail(staff) {
      this.selectedStaff = staff;
      this.staffDetailVisible = true;
    },

    // 从详情弹窗触发编辑
    handleEditFromDetail(staff) {
      this.openEditStaffDialog(staff);
    },
    // 分页大小变化事件
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchStaffData();
    },

    // 分页页码变化事件
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchStaffData();
    },

    // 筛选条件变化事件
    handleFilterChange() {
      this.currentPage = 1; // 重置页码
      this.fetchStaffData();
    },
    // 获取人员类型文本
    getPersonnelType(type) {
      const typeMap = {
        1: "专任",
        2: "兼任",
      };
      return typeMap[type] || "-";
    },

    // 根据性别获取图标
    getGenderIcon(sex) {
      if (sex === 1) return "el-icon-male";
      if (sex === 2) return "el-icon-female";
      return "el-icon-user-solid";
    },
  },
};
</script>

<style lang="scss" scoped>
.center-staff-info {
  min-height: 200px;

  .loading-container {
    padding: 20px;
  }

  .staff-container {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .staff-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(354px, 1fr));
      gap: 20px;

      .staff-card {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 16px;
        display: flex;
        min-height: 124px;
        box-shadow: 0px 0px 24px 3px rgba(186, 186, 186, 0.17);
        transition: all 0.3s ease;
        position: relative;
        display: flex;
        align-items: center;
        &:hover {
          transform: translateY(-5px);
        }

        .staff-avatar {
          width: 56px;
          height: 56px;
          border-radius: 4px;
          overflow: hidden;
          margin-right: 16px;
          flex-shrink: 0;
          background-color: #f0f2f5;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .default-avatar {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e6f7ff;
            color: #1890ff;
            font-size: 24px;
          }
        }

        .staff-info {
          flex: 1;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          transition: all 0.2s ease;

          .staff-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .staff-name {
              font-size: 18px;
              color: #333333;
              margin-right: 8px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .staff-role {
              display: inline-block;
              padding: 2px 8px;
              background-color: #ecf2fe;
              color: #0052d9;
              font-size: 14px;
              border-radius: 4px;
              white-space: nowrap;
            }
          }

          .staff-detail {
            .detail-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              font-size: 14px;
              color: #333333;

              i {
                margin-right: 8px;
                font-size: 14px;
                flex-shrink: 0;
              }

              span {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        .staff-actions {
          position: absolute;
          top: 16px;
          right: 16px;
          display: flex;
          gap: 8px;

          .el-button {
            font-size: 16px;
            padding: 4px;
            color: #909399;

            &:hover {
              color: #2050d1;
            }

            &.el-icon-delete:hover {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .delete-confirm-content {
    text-align: center;
    font-size: 16px;
    padding: 20px 0;

    .delete-name {
      font-weight: bold;
      color: #f56c6c;
    }
  }

  @media screen and (max-width: 1400px) {
    .staff-grid {
      grid-template-columns: repeat(auto-fill, minmax(354px, 1fr)) !important;
    }
  }

  @media screen and (max-width: 768px) {
    .staff-grid {
      grid-template-columns: 1fr !important;
    }
  }
}
</style>
