<template>
  <el-dialog
    title="人员详情"
    :visible.sync="dialogVisible"
    width="70%"
    :close-on-click-modal="false"
    class="staff-detail-dialog"
    @close="handleClose"
  >
    <div v-if="staffData" class="staff-detail-content">
      <!-- 人员基本信息头部 -->
      <div class="staff-header">
        <div class="staff-avatar-section">
          <div class="staff-avatar">
            <img
              :src="imageSrc + (staffData.labPersonInfo && staffData.labPersonInfo.personPhoto ? staffData.labPersonInfo.personPhoto : '')"
              alt="人员头像"
              v-if="staffData.labPersonInfo && staffData.labPersonInfo.personPhoto"
            />
            <img src="@laboratory/assets/image/memberIcon.png" alt="默认头像" v-else />
            <!-- <div v-else class="default-avatar">
              <i :class="getGenderIcon(staffData.labPersonInfo && staffData.labPersonInfo.sex)"></i>
            </div> -->
          </div>
        </div>
        <div class="staff-basic-info">
          <h3 class="staff-name">{{ staffData.labPersonInfo ? staffData.labPersonInfo.trueName : "-" }}</h3>
          <div class="staff-role">
            <el-tag type="primary" size="medium">{{ staffData.sysRoleName || "-" }}</el-tag>
            <el-tag :type="staffData.personnelType === 1 ? 'success' : 'warning'" size="medium" class="personnel-type-tag">
              {{ getPersonnelType(staffData.personnelType) }}
            </el-tag>
          </div>
          <div class="staff-contact">
            <div class="contact-item">
              <i class="el-icon-user"></i>
              <span>学工号：{{ staffData.labPersonInfo ? staffData.labPersonInfo.logonName : "-" }}</span>
            </div>
            <div class="contact-item">
              <i class="el-icon-phone"></i>
              <span>联系电话：{{ staffData.labPersonInfo && staffData.labPersonInfo.phone ? staffData.labPersonInfo.phone : "-" }}</span>
            </div>
            <div class="contact-item">
              <i class="el-icon-message"></i>
              <span>邮箱：{{ staffData.labPersonInfo && staffData.labPersonInfo.email ? staffData.labPersonInfo.email : "-" }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="staff-details">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="姓名">
                {{ staffData.labPersonInfo ? staffData.labPersonInfo.trueName : "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="性别">
                {{ getGenderText(staffData.labPersonInfo && staffData.labPersonInfo.sex) }}
              </el-descriptions-item>
              <el-descriptions-item label="学工号">
                {{ staffData.labPersonInfo ? staffData.labPersonInfo.logonName : "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="出生日期">
                {{ formatBirthday(staffData.labPersonInfo && staffData.labPersonInfo.birthday) }}
              </el-descriptions-item>
              <el-descriptions-item label="所属部门">
                {{ staffData.labPersonInfo ? staffData.labPersonInfo.deptName : "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="系统角色">
                {{ staffData.sysRoleName || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="人员类型">
                {{ getPersonnelType(staffData.personnelType) }}
              </el-descriptions-item>
              <el-descriptions-item label="职位">
                {{ staffData.position || "-" }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="学术信息" name="academic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="所属学科">
                {{
                  staffData.labPersonInfo && staffData.labPersonInfo.personnelAcademicName
                    ? staffData.labPersonInfo.personnelAcademicName
                    : "-"
                }}
              </el-descriptions-item>
              <el-descriptions-item label="专业技术职务">
                {{
                  staffData.labPersonInfo && staffData.labPersonInfo.technicalPositionName
                    ? staffData.labPersonInfo.technicalPositionName
                    : "-"
                }}
              </el-descriptions-item>
              <el-descriptions-item label="文化程度">
                {{
                  staffData.labPersonInfo && staffData.labPersonInfo.educationalLevelName
                    ? staffData.labPersonInfo.educationalLevelName
                    : "-"
                }}
              </el-descriptions-item>
              <el-descriptions-item label="学位">
                {{ staffData.labPersonInfo && staffData.labPersonInfo.degreeName ? staffData.labPersonInfo.degreeName : "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="专家类别">
                {{
                  staffData.labPersonInfo && staffData.labPersonInfo.expertCategoryName ? staffData.labPersonInfo.expertCategoryName : "-"
                }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="任职信息" name="position">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="分配日期">
                {{ formatDate(staffData.assignedDate) }}
              </el-descriptions-item>
              <el-descriptions-item label="结束日期">
                {{ formatDate(staffData.endDate) || "无限期" }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDateTime(staffData.gmtCreate) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDateTime(staffData.gmtModified) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="联系方式" name="contact">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="联系电话">
                <span v-if="staffData.labPersonInfo && staffData.labPersonInfo.phone">
                  {{ staffData.labPersonInfo.phone }}
                  <el-button type="text" size="mini" icon="el-icon-phone" @click="makePhoneCall(staffData.labPersonInfo.phone)">
                    拨打
                  </el-button>
                </span>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="邮箱地址">
                <span v-if="staffData.labPersonInfo && staffData.labPersonInfo.email">
                  {{ staffData.labPersonInfo.email }}
                  <el-button type="text" size="mini" icon="el-icon-message" @click="sendEmail(staffData.labPersonInfo.email)">
                    发送邮件
                  </el-button>
                </span>
                <span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleEdit" v-if="allowEdit">编辑</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "StaffDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    staffData: {
      type: Object,
      default: () => ({}),
    },
    allowEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      activeTab: "basic",
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
      },
      immediate: true,
    },
    dialogVisible(newVal) {
      if (!newVal) {
        this.$emit("update:visible", false);
      }
    },
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.activeTab = "basic";
    },

    // 编辑人员
    handleEdit() {
      this.$emit("edit", this.staffData);
      this.handleClose();
    },

    // 获取性别图标
    getGenderIcon(sex) {
      if (sex === 1) return "el-icon-male";
      if (sex === 2) return "el-icon-female";
      return "el-icon-user-solid";
    },

    // 获取性别文本
    getGenderText(sex) {
      const genderMap = {
        0: "保密",
        1: "男",
        2: "女",
      };
      return genderMap[sex] || "未知";
    },

    // 获取人员类型文本
    getPersonnelType(type) {
      const typeMap = {
        1: "专任",
        2: "兼任",
      };
      return typeMap[type] || "-";
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        });
      } catch (e) {
        return dateStr;
      }
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      } catch (e) {
        return dateStr;
      }
    },

    // 格式化生日
    formatBirthday(birthday) {
      if (!birthday) return "-";
      // 如果是时间戳格式
      if (typeof birthday === "number") {
        try {
          const date = new Date(birthday);
          return date.toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          });
        } catch (e) {
          return "-";
        }
      }
      return birthday;
    },

    // 拨打电话
    makePhoneCall(phoneNumber) {
      if (phoneNumber) {
        window.location.href = `tel:${phoneNumber}`;
      }
    },

    // 发送邮件
    sendEmail(email) {
      if (email) {
        window.location.href = `mailto:${email}`;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.staff-detail-dialog {
  .staff-detail-content {
    .staff-header {
      display: flex;
      margin-bottom: 24px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .staff-avatar-section {
        margin-right: 24px;
        flex-shrink: 0;

        .staff-avatar {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          overflow: hidden;
          background-color: #f0f2f5;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid #e9ecef;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .default-avatar {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e6f7ff;
            color: #1890ff;
            font-size: 32px;
          }
        }
      }

      .staff-basic-info {
        flex: 1;

        .staff-name {
          margin: 0 0 12px 0;
          font-size: 24px;
          font-weight: 600;
          color: #303133;
        }

        .staff-role {
          margin-bottom: 16px;

          .personnel-type-tag {
            margin-left: 8px;
          }
        }

        .staff-contact {
          .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: #606266;
            font-size: 14px;

            i {
              margin-right: 8px;
              color: #2050d1;
              font-size: 16px;
              width: 16px;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .staff-details {
      .el-tabs {
        ::v-deep .el-tabs__header {
          margin-bottom: 20px;
        }

        ::v-deep .el-tabs__content {
          .el-descriptions {
            ::v-deep .el-descriptions__label {
              font-weight: 500;
              color: #303133;
              background-color: #fafafa;
            }

            ::v-deep .el-descriptions__content {
              color: #606266;
            }
          }
        }
      }
    }
  }
}
</style>
