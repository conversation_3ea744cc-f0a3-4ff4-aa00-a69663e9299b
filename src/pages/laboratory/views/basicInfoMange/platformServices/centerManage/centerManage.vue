<template>
  <layoutContent>
    <div class="search-form" slot="search">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="中心名称：">
          <el-input v-model="searchForm.roomKindName" placeholder="请输入中心名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="所属部门：">
          <el-select v-model="searchForm.deptId" placeholder="请选择所属部门" filterable clearable>
            <el-option v-for="item in deptOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="级别：">
          <el-select v-model="searchForm.roomKindLevel" placeholder="全部" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="校级" value="1"></el-option>
            <el-option label="院级" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button plain type="primary" icon="el-icon-plus" @click="handleAddCenter">实验中心新建</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 卡片列表区域 -->
    <div class="center-list2">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated :throttle="500" />
      </div>
      <el-empty v-else-if="centerList.length === 0" description="暂无数据"></el-empty>
      <div class="card-container" v-else>
        <!-- 使用LabCenterCard组件 -->
        <lab-center-card v-for="item in centerList" :key="item.uuid" :item="item" :image-src="location"
          :info-items="generateInfoItems(item)" :showIcon="false" :showIcon2="true" :use-custom-info-items="true" @action="handleCardAction">
          <!-- 使用具名插槽自定义按钮 -->
          <template #actions>
            <el-button plain type="delete" icon="el-icon-delete" @click="handleDelete(item)">删除</el-button>
            <el-button plain type="primary" icon="el-icon-edit" @click="handleEdit(item)">编辑</el-button>
            <el-button plain type="primary" @click="handleManageLab(item)">查看详情</el-button>
          </template>
        </lab-center-card>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog title="删除确认" :visible.sync="deleteDialogVisible" width="30%" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="delete-confirm">
        <i class="el-icon-warning-outline warning-icon"></i>
        <p>
          确定要删除实验中心
          <span class="highlight">{{ currentCenter.roomKindName }}</span>
          吗？
        </p>
        <p class="warning-text">删除后将无法恢复，请谨慎操作！</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" :loading="deleteLoading" @click="confirmDelete">确认删除</el-button>
      </div>
    </el-dialog>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import LabCenterCard from "@laboratory/components/LabCenterCard.vue";
import { labCenterPage, labCenterDelete, getDeptList } from "@laboratory/api/laboratory/basicMessage";

export default {
  name: "labManager",
  components: {
    layoutContent,
    LabCenterCard,
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      // 搜索表单数据
      searchForm: {
        roomKindName: "",
        deptId: "",
        roomKindLevel: "",
      },
      // 部门选项
      deptOptions: [],
      // 分页参数
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 列表数据
      centerList: [],
      // 加载状态
      loading: false,
      // 删除相关
      deleteDialogVisible: false,
      deleteLoading: false,
      currentCenter: {},
    };
  },
  computed: {
    // 计算总页数
    totalPages() {
      return Math.ceil(this.total / this.pageSize);
    },
    // 计算显示的页码
    displayPages() {
      const pages = [];
      // 简单实现，显示1到5页
      const maxDisplay = 5;
      const totalPages = this.totalPages;

      if (totalPages <= maxDisplay) {
        // 总页数小于等于最大显示页数，显示所有页码
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 总页数大于最大显示页数，需要处理页码显示逻辑
        let start = this.currentPage - Math.floor(maxDisplay / 2);
        start = Math.max(1, start);
        let end = start + maxDisplay - 1;

        if (end > totalPages) {
          end = totalPages;
          start = Math.max(1, end - maxDisplay + 1);
        }

        for (let i = start; i <= end; i++) {
          pages.push(i);
        }
      }

      return pages;
    },
  },
  created() {
    // 页面创建时获取数据
    this.fetchData();
    // 获取部门列表
    this.fetchDeptOptions();
  },
  methods: {
    // 获取实验中心列表数据
    fetchData() {
      this.loading = true;
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        roomKindName: this.searchForm.roomKindName || undefined,
        deptId: this.searchForm.deptId || undefined,
        roomKindLevel: this.searchForm.roomKindLevel || undefined,
      };

      labCenterPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.centerList = res.data || [];
            this.total = res.count || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 搜索按钮点击事件
    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },

    // 重置搜索条件
    handleReset() {
      this.searchForm = {
        roomKindName: "",
        deptId: "",
        roomKindLevel: "",
      };
      this.currentPage = 1;
      this.fetchData();
    },

    // 分页大小变化事件
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchData();
    },

    // 分页页码变化事件
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchData();
    },

    // 卡片操作统一处理函数
    handleCardAction(action, item) {
      switch (action) {
        case "delete":
          this.handleDelete(item);
          break;
        case "edit":
          this.handleEdit(item);
          break;
        case "detail":
          this.handleManageLab(item);
          break;
        default:
          console.log("未知操作:", action, item);
      }
    },

    // 实验中心新建
    handleAddCenter() {
      this.$router.push({ name: "centerAdd" });
    },

    // 删除实验中心
    handleDelete(item) {
      this.currentCenter = item;
      this.deleteDialogVisible = true;
    },

    // 确认删除实验中心
    confirmDelete() {
      if (!this.currentCenter || !this.currentCenter.uuid) {
        this.$message.error("删除失败：无效的实验中心信息");
        return;
      }

      this.deleteLoading = true;
      const data = {
        uuid: this.currentCenter.uuid,
      };

      labCenterDelete(data)
        .then((res) => {
          if (res.code === 0) {
            this.$message.success("删除成功");
            this.deleteDialogVisible = false;

            // 如果当前页只有一条数据且不是第一页，则删除后跳转到上一页
            if (this.centerList.length === 1 && this.currentPage > 1) {
              this.currentPage -= 1;
            }
            // 重新加载数据
            this.fetchData();
          }
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },

    // 实验中心编辑
    handleEdit(item) {
      if (item && item.uuid) {
        this.$router.push({ name: "centerEdit", params: { uuid: item.uuid } });
      } else {
        this.$message.error("编辑失败：无效的实验中心信息");
      }
    },

    // 查看详情
    handleManageLab(item) {
      if (item && item.uuid) {
        this.$router.push({ name: "centerDetail", params: { uuid: item.uuid } });
      } else {
        this.$message.error("查看详情失败：无效的实验中心信息");
      }
    },

    // 为每个实验中心生成信息项数组
    generateInfoItems(item) {
      const { deptName, responsiblePerson, contactInfo, roomKindKind, roomKindLevel, roomKindAddress, safetyOfficer } = item;

      // 基础信息项 - 始终显示这些项
      const baseInfoItems = [
        {
          label: "负责人",
          value: responsiblePerson || "-",
          icon: "iconfont icon-gerenzhongxin_0",
        },
        {
          label: "联系方式",
          value: contactInfo || "-",
          icon: "iconfont icon-call",
        },
        {
          label: "地址",
          value: roomKindAddress || "-",
          icon: "iconfont icon-a-bianzu5",
        },
      ];

      // 扩展信息项 - 根据数据可用性决定是否显示
      // 这里我们只获取基本信息，保持卡片简洁
      // 完整的详情会在详情页展示

      return baseInfoItems;
    },

    // 获取部门选项
    fetchDeptOptions() {
      getDeptList().then((res) => {
        if (res.code === 0) {
          this.deptOptions = (res.data || []).map((item) => ({
            value: item.deptId,
            label: item.deptName,
          }));
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .form-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;

    .label {
      white-space: nowrap;
      margin-right: 5px;
    }

    input,
    select {
      width: 180px;
      height: 32px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 0 10px;
      outline: none;

      &:focus {
        border-color: #2050d1;
      }
    }
  }

  .form-btns {
    display: flex;
    align-items: center;
    margin-left: auto;

    button {
      height: 32px;
      border-radius: 4px;
      padding: 0 15px;
      cursor: pointer;
      border: none;
      margin-left: 10px;

      &.search-btn {
        background-color: #2050d1;
        color: white;
      }

      &.add-btn {
        background-color: #67c23a;
        color: white;
      }
    }
  }
}

.center-list2 {
  position: relative;
  min-height: 200px;

  .loading-container {
    padding: 20px;
  }

  .card-container {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
  }
}

.pagination-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
}

/* 删除确认对话框样式 */
.delete-confirm {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;

  .warning-icon {
    font-size: 48px;
    color: #e6a23c;
    margin-bottom: 20px;
  }

  p {
    margin: 10px 0;
    font-size: 16px;
    text-align: center;

    .highlight {
      font-weight: bold;
      color: #f56c6c;
    }
  }

  .warning-text {
    color: #f56c6c;
    font-size: 14px;
  }
}
</style>
