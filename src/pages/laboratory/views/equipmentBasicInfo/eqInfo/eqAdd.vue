<template>
  <layoutContent>
    <!-- 顶部导航栏 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ isEditMode ? "资料补全" : "仪器新增" }}</span>
      </div>
      <el-button type="primary" @click="handleComplete">完成</el-button>
    </div>

    <!-- 主体内容区 -->
    <div class="equipment-add-container">
      <!-- 左侧步骤导航 -->
      <div class="steps-nav">
        <el-steps direction="vertical" :active="currentStep - 1" :space="80">
          <el-step v-for="(step, index) in steps" :key="index" :title="step" :class="{ clickable: true }">
            <div slot="title" @click="handleStepClick(index)" class="step-title">
              <span v-if="index === 0">*</span>
              {{ step }}
            </div>
          </el-step>
        </el-steps>
      </div>

      <!-- 右侧表单内容 -->
      <div class="form-content">
        <!-- 基本信息表单 -->
        <div v-if="currentStep === 1" class="step-form">
          <div class="form-title">基本信息</div>
          <equipment-basic-info ref="equipmentBasicInfo" :equipmentUuid="equipmentUuid" :isEditMode="isEditMode"
            :initialData="basicForm" @save-success="handleBasicInfoSaveSuccess" />
        </div>

        <!-- 扩展信息表单 -->
        <div v-if="currentStep === 2" class="step-form">
          <div class="form-title">扩展信息</div>
          <equipment-extended-info v-if="equipmentUuid" :equipmentUuid="equipmentUuid"
            :initialData="extendedForm" @save-success="handleExtendedNextStep" />
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取仪器ID后才能管理扩展信息
          </div>
        </div>

        <!-- 技术资料表单 -->
        <div v-if="currentStep === 3" class="step-form">
          <div class="form-title">技术资料</div>
          <equipment-technical-info v-if="equipmentUuid" :equipmentUuid="equipmentUuid" :equipmentData="equipmentData" />
          <div v-else class="empty-tip">
            请先完成基本信息的保存，获取仪器ID后才能管理技术资料
          </div>
        </div>
      </div>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import EquipmentBasicInfo from "./eqAddCom/EquipmentBasicInfo.vue";
import EquipmentExtendedInfo from "./eqAddCom/EquipmentExtendedInfo.vue";
import EquipmentTechnicalInfo from "./eqAddCom/EquipmentTechnicalInfo.vue";
import { deviceDetail } from '@laboratory/api/equipmentBasicInfo/eqBasicInfo';

export default {
  name: "eqAdd",
  components: {
    layoutContent,
    EquipmentBasicInfo,
    EquipmentExtendedInfo,
    EquipmentTechnicalInfo,
  },
  data () {
    return {
      // 步骤配置
      steps: ["基本信息", "扩展信息", "技术资料"],
      currentStep: 1,

      // 屏幕宽度
      screenWidth: 0,

      // 模式标识
      isEditMode: false,
      loading: false,

      // 基本信息表单
      basicForm: {},

      // 扩展信息表单
      extendedForm: {},

      // 保存状态
      saveLoading: false,
      equipmentUuid: "", // 创建成功后的仪器UUID
      equipmentData: {}, // 保存返回的完整仪器数据对象
    };
  },
  async created () {
    // 判断当前是否为编辑模式
    this.isEditMode = this.$route.name === "eqEdit";

    // 如果是编辑模式，则获取 UUID 并调用详情接口
    if (this.isEditMode) {
      const uuid = this.$route.params.uuid;
      if (uuid) {
        this.equipmentUuid = uuid;
        this.fetchEquipmentDetail();
      } else {
        this.$message.warning("缺少必要的参数");
        this.goBack();
      }
    }


    // 初始化屏幕宽度
    this.handleResize();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy () {
    // 移除事件监听
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 处理窗口大小变化
    handleResize () {
      if (window.innerWidth > 1640) {
        this.screenWidth = window.innerWidth - 620;
      } else {
        this.screenWidth = window.innerWidth - 580;
      }
    },

    // 处理step查询参数
    handleStepQuery() {
      const stepParam = this.$route.query.step;
      if (stepParam) {
        const step = parseInt(stepParam);
        // 验证step参数是否有效（1-3之间）
        if (step >= 1 && step <= 3) {
          // 在编辑模式下，可以直接跳转到指定步骤
          if (this.isEditMode) {
            this.currentStep = step;
          } else {
            // 在新增模式下，只有第一步可以直接访问
            if (step === 1) {
              this.currentStep = step;
            } else {
              this.$message.warning("请先完成基本信息的保存");
              this.currentStep = 1;
            }
          }
        }
      }
    },

    // 获取仪器详情
    fetchEquipmentDetail () {
      if (!this.equipmentUuid) return;
      this.loading = true;
      deviceDetail({ uuid: this.equipmentUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 保存完整的仪器数据
            this.equipmentData = res.data;

            // 解构赋值，优雅地提取数据
            const {
              devSn, devName, assetSn, classNumCode, devModel, devSpecification,
              price, devSource, countryCode, presentSituationCode, purchaseDate,
              directionOfUse, kindId, technicalParameters,
              deptId, usingAccNo, roomId, storageLocation, factoryName,
              factoryNumber, factoryDate, factoryContact, warrantyDate,
              serviceLife, usingDeptId, searchTag, tagId, latitude, longitude
            } = res.data;

            // 回填基本信息（分转元显示）
            this.basicForm = {
              devSn, devName, assetSn, classNumCode, devModel, devSpecification,
              price: price ? (price / 100).toFixed(2) : '',
              devSource, countryCode, presentSituationCode, purchaseDate,
              directionOfUse, kindId, technicalParameters,
            };

            // 回填扩展信息
            this.extendedForm = {
              deptId, usingAccNo, roomId, storageLocation, factoryName,
              factoryNumber, factoryDate, factoryContact, warrantyDate,
              serviceLife, usingDeptId, searchTag, tagId, latitude, longitude,
            };
            // 处理step查询参数，自动跳转到指定步骤
            this.handleStepQuery();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理步骤点击事件
    handleStepClick (index) {
      // 在编辑模式下，允许自由切换步骤
      if (this.isEditMode) {
        this.switchStep(index + 1);
        return;
      }

      // 在新增模式下，检查是否已有equipmentUuid
      if (!this.equipmentUuid) {
        this.$message.warning("请先完成基本信息的保存");
        this.switchStep(1);
        return;
      }
      // 直接使用 index + 1 作为步骤值，因为 currentStep 是从 1 开始的
      this.switchStep(index + 1);
    },

    // 切换步骤 - 简化逻辑，允许自由切换
    switchStep (step) {
      this.currentStep = step;
    },

    // 返回列表页或详情页
    goBack () {
      if (this.isEditMode && this.$route.query.from === "detail") {
        // 如果是从详情页进入的编辑页，则返回详情页
        this.$router.push({
          name: "eqDetail",
          params: { uuid: this.equipmentUuid },
        });
      } else {
        // 否则返回列表页
        this.$router.push({ name: "eqInfo" });
      }
    },

    // 处理基本信息保存成功
    handleBasicInfoSaveSuccess (data) {
      // 更新UUID
      this.equipmentUuid = data.uuid;
      // 更新表单数据
      this.basicForm = { ...this.basicForm, ...data };
      // 更新仪器数据
      this.equipmentData = { ...this.equipmentData, ...data };
      // 跳转到下一步
      this.switchStep(this.currentStep + 1);
    },

    // 处理扩展信息下一步事件
    handleExtendedNextStep (data) {
      // 更新表单数据
      this.extendedForm = { ...this.basicForm, ...data };
      // 切换到下一个步骤（技术资料）
      this.switchStep(3);
    },

    // 完成按钮点击事件
    handleComplete () {
      // 最终完成操作，可以进行最后的保存或跳转
      const confirmMessage = this.isEditMode ? "确认完成资料补全?" : "确认完成仪器创建?";

      this.$confirm(confirmMessage, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.goBack(); // 返回列表页
        })
        .catch(() => {
          // 取消操作
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.equipment-add-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 600px;

  .form-content {
    flex: 1;
    padding: 20px;

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;

      &::before {
        content: "";
        display: block;
        width: 4px;
        height: 16px;
        background-color: #2050d1;
        margin-right: 8px;
      }
    }

    .step-form {
      max-width: 100%;
    }

    .empty-tip {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}
</style>