<template>
  <div class="equipment-basic-info">
    <div class="form-actions">
      <el-button type="primary" @click="saveBasicInfo" :loading="saveLoading">保存</el-button>
    </div>
    <el-form :model="basicForm" :rules="basicRules" ref="basicForm" :inline="true" label-width="110px">
      <el-form-item label="仪器编号：" prop="devSn">
        <el-input v-model="basicForm.devSn" placeholder="请输入仪器编号" type="number"></el-input>
      </el-form-item>
      <el-form-item label="分类号：" prop="classNumCode">
        <dict-cascader v-model="basicForm.classNumCode" :code-type="1008" placeholder="请选择分类号"></dict-cascader>
      </el-form-item>
      <el-form-item label="资产编号：" prop="assetSn">
        <el-input v-model="basicForm.assetSn" placeholder="请输入资产编号"></el-input>
      </el-form-item>
      <el-form-item label="型号：" prop="devModel">
        <el-input v-model="basicForm.devModel" placeholder="请输入型号"></el-input>
      </el-form-item>
      <el-form-item label="仪器名称：" prop="devName">
        <el-input v-model="basicForm.devName" placeholder="请输入仪器名称"></el-input>
      </el-form-item>
      <el-form-item label="仪器来源：" prop="devSource">
        <dict-cascader v-model="basicForm.devSource" :code-type="1011" placeholder="请选择仪器来源"></dict-cascader>
      </el-form-item>
      <el-form-item label="规格：" prop="devSpecification">
        <el-input v-model="basicForm.devSpecification" placeholder="请输入规格"></el-input>
      </el-form-item>
      <el-form-item label="单价：" prop="price">
        <el-input v-model="basicForm.price" placeholder="请输入单价" type="number" step="0.01">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="国别码：" prop="countryCode">
        <el-input v-model="basicForm.countryCode" placeholder="请输入国别码"></el-input>
      </el-form-item>
      <el-form-item label="现状码：" prop="presentSituationCode">
        <dict-cascader v-model="basicForm.presentSituationCode" :code-type="1012" placeholder="请选择现状码"></dict-cascader>
      </el-form-item>
      <el-form-item label="购置日期：" prop="purchaseDate">
        <el-date-picker style="width: 177px" v-model="basicForm.purchaseDate" type="date" placeholder="选择购置日期"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="使用方向：" prop="directionOfUse">
        <dict-cascader v-model="basicForm.directionOfUse" :code-type="1013" placeholder="请选择使用方向"></dict-cascader>
      </el-form-item>
      <el-form-item label="仪器类型：" prop="kindId">
        <el-select v-model="basicForm.kindId" placeholder="请选择仪器类型">
          <el-option v-for="item in equipmentTypeOptions" :key="item.value" :label="item.label"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-row>
        <el-col>

          <el-form-item label="仪器照片：">
            <file-uploader v-model="basicForm.equipmentImage" type="image" :maxSize="2" tip="支持JPG、PNG格式，不超过2MB"
              class="photo-uploader" />
          </el-form-item>
          <el-form-item label="技术参数：" prop="technicalParameters" class="full-width-item">
            <el-input v-model="basicForm.technicalParameters" placeholder="请输入技术参数" type="textarea"
              :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import FileUploader from "@laboratory/components/FileUploader/index.vue";
import DictCascader from "@laboratory/components/DictCascader.vue";
import { deviceSaveForAcceptance, deviceUpdate } from '@laboratory/api/equipmentBasicInfo/eqBasicInfo';
import { devKindOptions } from '@laboratory/api/equipmentBasicInfo/eqType';
import { getDictAll } from '@laboratory/api/laboratory/basicMessage';

export default {
  name: "EquipmentBasicInfo",
  components: {
    FileUploader,
    DictCascader,
  },
  props: {
    equipmentUuid: {
      type: String,
      default: "",
    },
    isEditMode: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      saveLoading: false,
      basicForm: {
        devSn: "",                    // *设备编号（仪器编号）- Integer
        devName: "",                  // *仪器名称 - String
        assetSn: "",                  // *资产编号 - String
        classNumCode: "",             // *分类号 - String
        devModel: "",                 // *型号 - String
        devSpecification: "",         // *规格 - String
        price: "",                    // *单价（单位分，页面显示元需要*100）- Long
        devSource: "",                // *仪器来源 - String
        countryCode: "",              // *国别码 - String
        presentSituationCode: "",     // *现状码 - String
        purchaseDate: "",             // *购置日期 - LocalDate
        directionOfUse: "",           // *使用方向 - String
        kindId: "",                   // *设备类型ID - Integer
        equipmentImage: "",           // 仪器照片 - String（可选）
        technicalParameters: "",      // 技术参数 - String（可选）
      },
      basicRules: {
        devSn: [
          { required: true, message: "请输入仪器编号", trigger: "blur" },
          { pattern: /^\d+$/, message: "仪器编号必须为数字", trigger: "blur" }
        ],
        devName: [{ required: true, message: "请输入仪器名称", trigger: "blur" }],
        assetSn: [{ required: true, message: "请输入资产编号", trigger: "blur" }],
        classNumCode: [{ required: true, message: "请输入分类号", trigger: "blur" }],
        devModel: [{ required: true, message: "请输入型号", trigger: "blur" }],
        devSpecification: [{ required: true, message: "请输入规格", trigger: "blur" }],
        price: [
          { required: true, message: "请输入单价", trigger: "blur" },
          { pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
        ],
        devSource: [{ required: true, message: "请选择仪器来源", trigger: "blur" }],
        countryCode: [{ required: true, message: "请输入国别码", trigger: "blur" }],
        presentSituationCode: [{ required: true, message: "请输入现状码", trigger: "blur" }],
        purchaseDate: [{ required: true, message: "请选择购置日期", trigger: "change" }],
        directionOfUse: [{ required: true, message: "请输入使用方向", trigger: "blur" }],
        kindId: [{ required: true, message: "请选择仪器类型", trigger: "change" }],
      },
      // 下拉选项
      equipmentTypeOptions: [],
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.basicForm = { ...this.basicForm, ...newVal };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.loadEquipmentTypeOptions();
  },
  methods: {
    // 加载仪器类型选项
    async loadEquipmentTypeOptions() {
      try {
        const response = await devKindOptions();
        if (response.code === 0) {
          this.equipmentTypeOptions = response.data.map(item => ({
            value: item.devKindId,
            label: item.devKindName
          }));
        }
      } catch (error) {
        console.error("加载仪器类型选项失败:", error);
      }
    },
    // 保存基本信息
    saveBasicInfo() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          // 准备提交数据
          const submitData = this.prepareSubmitData();

          // 选择对应的API
          const apiMethod = this.isEditMode || this.equipmentUuid ? deviceUpdate : deviceSaveForAcceptance;

          apiMethod(submitData)
            .then((res) => {
              if (res.code === 0) {
                this.$message.success(this.isEditMode ? "更新成功" : "保存成功");
                // 返回数据给父组件，触发进入下一步
                const resultData = {
                  ...this.basicForm,
                  uuid: this.equipmentUuid || res.data, // 新增时返回uuid字符串
                };
                this.$emit("save-success", resultData);
              }
            })
            .finally(() => {
              this.saveLoading = false;
            });
        }
      });
    },

    // 准备提交数据 - 统一数据转换逻辑
    prepareSubmitData() {
      const data = {
        ...this.basicForm,
        price: Math.round(parseFloat(this.basicForm.price || 0) * 100), // 元转分
      };

      // 编辑模式添加UUID
      if (this.isEditMode && this.equipmentUuid) {
        data.uuid = this.equipmentUuid;
      }

      return data;
    },
  },
};
</script>

<style lang="scss" scoped>
.equipment-basic-info {
  .form-actions {
    display: flex;
    margin: 20px 0;
    justify-content: flex-end;
  }
  ::v-deep .el-input {
    width: 177px;
  }
  .photo-uploader {
    ::v-deep .el-upload {
      width: 100%;
      display: flex;
      justify-content: flex-start;
    }
  }
}
</style>