<template>
  <div class="equipment-basic-info-display">
    <div class="info-grid">
      <div class="info-item">
        <div class="info-label">仪器编号：</div>
        <div class="info-value">{{ displayData.devSn || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">分类号：</div>
        <div class="info-value">{{ displayData.classNumName || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">资产编号：</div>
        <div class="info-value">{{ displayData.assetSn || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">型号：</div>
        <div class="info-value">{{ displayData.devModel || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">仪器名称：</div>
        <div class="info-value">{{ displayData.devName || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">仪器来源：</div>
        <div class="info-value">{{ displayData.devSourceName || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">规格：</div>
        <div class="info-value">{{ displayData.devSpecification || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">单价：</div>
        <div class="info-value">{{ displayData.price || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">国别码：</div>
        <div class="info-value">{{ displayData.countryCode || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">现状码：</div>
        <div class="info-value">{{ displayData.presentSituationName || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">购置日期：</div>
        <div class="info-value">{{ displayData.purchaseDate || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">使用方向：</div>
        <div class="info-value">{{ displayData.directionOfUseName || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">仪器类型：</div>
        <div class="info-value">{{ getEquipmentTypeLabel(displayData.kindId) || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">仪器照片：</div>
        <div class="info-value">
          <div v-if="displayData.equipmentImage" class="equipment-image">
            <img :src="baseUrl + displayData.equipmentImage" alt="仪器照片" />
          </div>
          <span v-else>-</span>
        </div>
      </div>
      <div class="info-item">
        <div class="info-label">技术参数：</div>
        <div class="info-value">{{ displayData.technicalParameters || '-' }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">单位名称：</div>
        <div class="info-value">{{ displayData.deptName || '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EquipmentBasicInfoDisplay",
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      baseUrl: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      displayData: {},
      // 下拉选项映射
      equipmentSourceOptions: [
        { value: "采购", label: "采购" },
        { value: "捐赠", label: "捐赠" },
        { value: "自制", label: "自制" },
        { value: "调拨", label: "调拨" },
        { value: "其他", label: "其他" },
      ],
      equipmentTypeOptions: [
        { value: 1, label: "教学仪器" },
        { value: 2, label: "科研仪器" },
        { value: 3, label: "通用仪器" },
        { value: 4, label: "专用仪器" },
        { value: 5, label: "计量仪器" },
      ],
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        this.displayData = { ...newVal };
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 获取仪器来源标签
    getEquipmentSourceLabel(value) {
      if (!value) return '';
      const option = this.equipmentSourceOptions.find(item => item.value === value);
      return option ? option.label : value;
    },
    // 获取仪器类型标签
    getEquipmentTypeLabel(value) {
      if (!value) return '';
      const option = this.equipmentTypeOptions.find(item => item.value === value);
      return option ? option.label : value;
    },
  },
};
</script>

<style lang="scss" scoped>
.equipment-basic-info-display {
  .info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .info-item {
      display: flex;
      align-items: flex-start;
      min-height: 40px;
      font-size: 14px;

      &.full-width {
        grid-column: 1 / -1;
      }

      .info-label {
        min-width: 110px;
        font-weight: 500;
        color: #606266;
        line-height: 1.5;
        font-size: 14px;
      }

      .info-value {
        flex: 1;
        color: #303133;
        line-height: 1.5;
        word-break: break-word;
        font-size: 14px;

        .equipment-image {
          img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            object-fit: cover;
          }
        }
      }
    }
  }
}
</style>