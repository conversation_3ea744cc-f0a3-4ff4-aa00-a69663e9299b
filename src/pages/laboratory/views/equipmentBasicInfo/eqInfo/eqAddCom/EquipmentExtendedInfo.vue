<template>
  <div class="equipment-extended-info">
    <div class="form-actions">
      <el-button type="primary" @click="saveExtendedInfo" :loading="saveLoading">保存</el-button>
    </div>

    <el-form :model="extendedForm" :rules="extendedRules" ref="extendedForm" :inline="true" label-width="110px">
      <!-- 1. 设备归属信息 -->
      <div class="section-title">设备归属信息</div>
      <el-form-item label="单位名称：" prop="deptId">
        <el-select v-model="extendedForm.deptId" placeholder="请选择单位名称" filterable>
          <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属房间：" prop="roomId">
        <el-cascader :value="extendedForm.roomId" :options="roomTreeOptions" :props="cascaderProps"
          placeholder="请选择所属房间" clearable filterable style="width: 177px" :show-all-levels="false"
          @change="handleRoomChange"></el-cascader>
      </el-form-item>
      <el-form-item label="存放地点：" prop="storageLocation">
        <el-input type="textarea" :rows="2" class="full-width-item" v-model="extendedForm.storageLocation"
          placeholder="请输入存放地点"></el-input>
      </el-form-item>

      <!-- 2. 设备厂商信息 -->
      <div class="section-title">设备厂商信息</div>
      <el-form-item label="厂商名称：" prop="factoryName">
        <el-input v-model="extendedForm.factoryName" placeholder="请输入厂商名称"></el-input>
      </el-form-item>
      <el-form-item label="出厂号：" prop="factoryNumber">
        <el-input v-model="extendedForm.factoryNumber" placeholder="请输入出厂号"></el-input>
      </el-form-item>
      <el-form-item label="出厂日期：" prop="factoryDate">
        <el-date-picker style="width: 177px" v-model="extendedForm.factoryDate" type="datetime" placeholder="选择出厂日期"
          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
      </el-form-item>
      <el-form-item label="联系方式：" prop="factoryContact">
        <el-input v-model="extendedForm.factoryContact" placeholder="请输入联系方式"></el-input>
      </el-form-item>
      <el-form-item label="保修期限：" prop="warrantyDate">
        <el-date-picker style="width: 177px" v-model="extendedForm.warrantyDate" type="date" placeholder="选择保修期限"
          format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="使用年限：" prop="serviceLife">
        <el-input v-model="extendedForm.serviceLife" placeholder="使用年限" type="number">
          <template slot="append">年</template>
        </el-input>
      </el-form-item>

      <!-- 3. 使用单位信息 -->
      <div class="section-title">使用单位信息</div>
      <el-form-item label="使用人：" prop="usingAccNo">
        <el-select v-model="extendedForm.usingAccNo" placeholder="请选择使用人" filterable>
          <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="使用单位：" prop="usingDeptId">
        <el-select v-model="extendedForm.usingDeptId" placeholder="请选择使用单位" filterable>
          <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>

      <!-- 4. 其他 -->
      <div class="section-title">其他</div>
      <el-form-item label="设备标签：" prop="searchTag">
        <el-input v-model="extendedForm.searchTag" placeholder="请输入设备标签（逗号分隔）"></el-input>
      </el-form-item>
      <el-form-item label="RFID标签：" prop="tagId">
        <el-input v-model="extendedForm.tagId" placeholder="请输入RFID标签"></el-input>
      </el-form-item>
      <el-form-item label="GPS标签：" prop="latitude">
        <el-input v-model="extendedForm.latitude" placeholder="请输入纬度" type="number" step="0.000001"></el-input>
      </el-form-item>
      <el-form-item label="GPS标签：" prop="longitude">
        <el-input v-model="extendedForm.longitude" placeholder="请输入经度" type="number" step="0.000001"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { deviceUpdateExtension, getAccNo } from '@laboratory/api/equipmentBasicInfo/eqBasicInfo';
import { getDeptList } from '@laboratory/api/laboratory/basicMessage';
import { roomTree } from '@laboratory/api/laboratory/laboratory';

export default {
  name: "EquipmentExtendedInfo",
  props: {
    equipmentUuid: {
      type: String,
      required: true,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      saveLoading: false,
      extendedForm: {
        // 1. 设备归属信息
        deptId: "",             // 所属单位id - Integer（可选）
        roomId: "",             // 所属房间id - Integer（必填）
        storageLocation: "",    // 存放地点 - String（可选）

        // 2. 设备厂商信息
        factoryName: "",        // 厂商名称 - String（可选）
        factoryNumber: "",      // 出厂号 - String（可选）
        factoryDate: "",        // 出厂日期 - LocalDateTime（可选，格式：yyyy-MM-dd HH:mm:ss）
        factoryContact: "",     // 厂商联系方式 - String（可选）
        warrantyDate: "",       // 保修日期（保修期限）- LocalDate（必填，格式：yyyy-MM-dd）
        serviceLife: 5,         // 使用年限 - Integer（必填）

        // 3. 使用单位信息
        usingAccNo: "",         // 使用人ID - Integer（可选）
        usingDeptId: "",        // 使用单位ID - Integer（可选）

        // 4. 其他
        searchTag: "",          // 设备标签名称（逗号分隔）- String（可选）
        tagId: "",              // RFID标签 - String（可选）
        latitude: "",           // 纬度（GPS标签）- BigDecimal（可选）
        longitude: "",          // 经度（GPS标签）- BigDecimal（可选）
      },
      extendedRules: {
        // 所有字段都非必填，移除验证规则
      },
      // 下拉选项数据
      deptOptions: [], // 部门选项（单位名称和使用单位共用）
      userOptions: [], // 使用人选项
      // 房间级联选择器相关
      roomCascader: [],
      roomTreeOptions: [],
      cascaderProps: {
        value: "value",
        label: "name",
        children: "children",
        emitPath: false, // 返回完整路径
        checkStrictly: false, // 只能选择叶子节点（第4层房间）
      },
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.extendedForm = { ...this.extendedForm, ...newVal };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.fetchExtendedInfo();
    this.loadDeptOptions();
    // this.loadUserOptions();
    this.loadRoomTree();
  },
  methods: {
    // 获取扩展信息（已通过 initialData 传入，无需单独调用）
    fetchExtendedInfo() {
      // 扩展信息已通过父组件的 initialData 传入
      // 如果需要实时获取最新数据，可以在这里调用接口
    },

    // 加载部门选项
    async loadDeptOptions() {
      try {
        const response = await getDeptList();
        if (response.code === 0) {
          this.deptOptions = response.data.map(item => ({
            value: item.deptId,
            label: item.deptName
          }));
        }
      } catch (error) {
        console.error("加载部门选项失败:", error);
      }
    },

    // 加载使用人选项
    async loadUserOptions() {
      try {
        const response = await getAccNo({});
        if (response.code === 0) {
          this.userOptions = response.data.map(item => ({
            value: item.accNo,
            label: item.accNoTrueName
          }));
        }
      } catch (error) {
        console.error("加载使用人选项失败:", error);
      }
    },

    // 加载房间树数据
    async loadRoomTree() {
      try {
        const response = await roomTree();
        if (response.code === 0) {
          this.roomTreeOptions = response.data || [];
        }
      } catch (error) {
        console.error("加载房间树数据失败:", error);
      }
    },

    // 处理房间级联选择器变化
    handleRoomChange (value) {
      this.extendedForm.roomId = value;
    },
    // 保存扩展信息
    saveExtendedInfo() {
      this.$refs.extendedForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          // 准备提交数据
          const submitData = {
            uuid: this.equipmentUuid,
            ...this.extendedForm,
          };

          deviceUpdateExtension(submitData)
            .then((res) => {
              if (res.code === 0) {
                this.$message.success("扩展信息保存成功");
                // 返回数据给父组件，触发进入下一步
                const resultData = {
                  ...this.extendedForm,
                  uuid: this.equipmentUuid
                };
                this.$emit("save-success", resultData);
                // 保存成功后自动进入下一步
                // this.$emit("next-step");
              }
            })
            .finally(() => {
              this.saveLoading = false;
            });
        }
      });
    },

  },
};
</script>

<style lang="scss" scoped>
.equipment-extended-info {
  .form-actions {
    display: flex;
    margin: 20px 0;
    justify-content: flex-end;
    gap: 10px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 30px 0 20px 0;

    &:first-of-type {
      margin-top: 0;
    }
  }



  ::v-deep .el-input {
    width: 177px;
  }

  .full-width-item {
    width: 500px;
  }
}
</style>