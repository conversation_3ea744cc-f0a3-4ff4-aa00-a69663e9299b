<template>
  <div class="equipment-extended-info-display">
    <!-- 1. 设备归属信息 -->
    <div class="info-section">
      <div class="section-title">设备归属信息</div>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">单位名称：</div>
          <div class="info-value">{{ getUnitLabel(displayData.deptId) || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">管理人：</div>
          <div class="info-value">{{ getManagerLabel(displayData.usingAccNo) || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">所属房间：</div>
          <div class="info-value">{{ getRoomLabel(displayData.roomId) || '-' }}</div>
        </div>
        <div class="info-item full-width">
          <div class="info-label">存放地点：</div>
          <div class="info-value">{{ displayData.storageLocation || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 2. 设备厂商信息 -->
    <div class="info-section">
      <div class="section-title">设备厂商信息</div>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">厂商名称：</div>
          <div class="info-value">{{ displayData.factoryName || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">出厂号：</div>
          <div class="info-value">{{ displayData.factoryNumber || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">出厂日期：</div>
          <div class="info-value">{{ displayData.factoryDate || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">联系方式：</div>
          <div class="info-value">{{ displayData.factoryContact || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">保修期限：</div>
          <div class="info-value">{{ displayData.warrantyDate || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">使用年限：</div>
          <div class="info-value">{{ displayData.serviceLife ? displayData.serviceLife + ' 年' : '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 3. 使用单位信息 -->
    <div class="info-section">
      <div class="section-title">使用单位信息</div>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">使用人：</div>
          <div class="info-value">{{ getUserLabel(displayData.usingAccNo) || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">使用单位：</div>
          <div class="info-value">{{ getUsageUnitLabel(displayData.usingDeptId) || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 4. 其他 -->
    <div class="info-section">
      <div class="section-title">其他</div>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">设备标签：</div>
          <div class="info-value">{{ displayData.searchTag || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">RFID标签：</div>
          <div class="info-value">{{ displayData.tagId || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">GPS标签：</div>
          <div class="info-value">{{ displayData.latitude || '-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">GPS标签：</div>
          <div class="info-value">{{ displayData.longitude || '-' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EquipmentExtendedInfoDisplay",
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      displayData: {},
      // 下拉选项数据
      unitOptions: [
        { value: 1001, label: "化学实验室" },
        { value: 1002, label: "物理实验室" },
        { value: 1003, label: "生物实验室" },
        { value: 1004, label: "材料实验室" },
      ],
      roomOptions: [
        { value: 101, label: "A101" },
        { value: 102, label: "A102" },
        { value: 201, label: "B201" },
        { value: 202, label: "B202" },
      ],
      managerOptions: [
        { value: 10001, label: "张三" },
        { value: 10002, label: "李四" },
        { value: 10003, label: "王五" },
        { value: 10004, label: "赵六" },
      ],
      userOptions: [
        { value: 10001, label: "张三" },
        { value: 10002, label: "李四" },
        { value: 10003, label: "王五" },
        { value: 10004, label: "赵六" },
        { value: 10005, label: "钱七" },
      ],
      usageUnitOptions: [
        { value: 2001, label: "化学系" },
        { value: 2002, label: "物理系" },
        { value: 2003, label: "生物系" },
        { value: 2004, label: "材料系" },
      ],
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.displayData = { ...this.displayData, ...newVal };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 获取单位标签
    getUnitLabel(value) {
      if (!value) return '';
      const option = this.unitOptions.find(item => item.value == value);
      return option ? option.label : value;
    },
    // 获取房间标签
    getRoomLabel(value) {
      if (!value) return '';
      const option = this.roomOptions.find(item => item.value == value);
      return option ? option.label : value;
    },
    // 获取管理人标签
    getManagerLabel(value) {
      if (!value) return '';
      const option = this.managerOptions.find(item => item.value == value);
      return option ? option.label : value;
    },
    // 获取使用人标签
    getUserLabel(value) {
      if (!value) return '';
      const option = this.userOptions.find(item => item.value == value);
      return option ? option.label : value;
    },
    // 获取使用单位标签
    getUsageUnitLabel(value) {
      if (!value) return '';
      const option = this.usageUnitOptions.find(item => item.value == value);
      return option ? option.label : value;
    },
  },
};
</script>

<style lang="scss" scoped>
.equipment-extended-info-display {
  .info-section {
    margin-bottom: 30px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      
      &::before {
        content: "";
        display: inline-block;
        width: 4px;
        height: 16px;
        background-color: #2468f2;
        margin-right: 8px;
        vertical-align: middle;
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      
      .info-item {
        display: flex;
        align-items: flex-start;
        min-height: 40px;
        font-size: 14px;
        
        &.full-width {
          grid-column: 1 / -1;
        }
        
        .info-label {
          min-width: 110px;
          font-weight: 500;
          color: #606266;
          line-height: 1.5;
          font-size: 14px;
        }
        
        .info-value {
          flex: 1;
          color: #303133;
          line-height: 1.5;
          word-break: break-word;
          font-size: 14px;
        }
      }
    }
  }
}
</style>