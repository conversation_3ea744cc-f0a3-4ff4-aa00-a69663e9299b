<template>
  <div class="equipment-technical-info">
    <!-- 加载状态 -->
    <!-- <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div> -->
    <!-- 空状态 -->
    <div v-if="categorizedDocuments.length === 0" class="empty-container">
      <el-empty description="该设备类型暂无技术资料要求"></el-empty>
    </div>

    <!-- 技术资料内容 -->
    <div v-else class="content-container">
      <!-- 技术资料分类展示 -->
      <div v-for="category in categorizedDocuments" :key="category.categoryId" class="category-section">
        <div class="category-header">
          <span class="category-title">{{ category.categoryName }}</span>
        </div>

        <div class="documents-list">
          <div v-for="document in category.documents" :key="document.contentId" class="document-item">
            <span class="document-name">{{ document.contentName }}</span>

            <!-- 查看模式 -->
            <div v-if="isViewMode">
              <div v-if="document.isUploaded" class="view-info">
                <span class="file-name">{{ document.uploadedFile.fileName }}</span>
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  @click="downloadFile(document.uploadedFile)"
                  class="download-btn"
                >
                  下载
                </el-button>
              </div>
              <div v-else class="no-file">
                <span class="no-file-text">未上传</span>
              </div>
            </div>

            <!-- 编辑模式 -->
            <div v-else>
              <!-- 未上传状态：显示上传区域 -->
              <file-uploader
                v-if="!document.isUploaded"
                v-model="document.uploadUrl"
                type="file"
                tip=""
                :maxSize="500"
                :showFileList="false"
                clickName="上传"
                @upload-success="handleFileUploadSuccess($event, document)"
                @upload-error="handleFileUploadError($event, document)"
              />
              <!-- 已上传状态：显示文件信息 -->
              <div v-else class="uploaded-info">
                <span class="file-name">{{ document.uploadedFile.fileName }}</span>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="confirmDelete(document)"
                  class="delete-btn"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FileUploader from "@laboratory/components/FileUploader/index.vue";
import { devKindDocRelationList } from '@laboratory/api/equipmentBasicInfo/skDataType'
import { devTechDocList, devTechDocSave, devTechDocDelete, deviceDetail } from '@laboratory/api/equipmentBasicInfo/eqBasicInfo'
import { devKindOptions } from '@laboratory/api/equipmentBasicInfo/eqType'

export default {
  name: "EquipmentTechnicalInfo",
  components: {
    FileUploader,
  },
  props: {
    equipmentUuid: {
      type: String,
      required: true,
    },
    equipmentData: {
      type: Object,
      default: () => ({})
    },
    isViewMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      requiredDocuments: [], // 设备类型要求的技术资料
      uploadedDocuments: [], // 已上传的技术资料
      categorizedDocuments: [], // 按分类组织的技术资料
      deviceKindUuid: null // 设备类型UUID
    };
  },
  mounted() {
    this.initializeData()
  },
  methods: {
    // 初始化数据
    async initializeData() {
      this.loading = true
      try {
        await this.fetchRequiredDocuments()
        await this.fetchUploadedDocuments()
        this.processDocuments()
      } catch (error) {
        console.error('初始化数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取设备类型技术资料要求
    async fetchRequiredDocuments() {
      try {
        // 直接从父组件传入的设备数据获取kindId
        const kindId = this.equipmentData.kindId;

        if (kindId) {
          // 获取所有设备类型选项来找到对应的UUID
          const kindOptionsResponse = await devKindOptions()
          if (kindOptionsResponse.code === 0 && kindOptionsResponse.data) {
            const deviceKind = kindOptionsResponse.data.find(kind => kind.devKindId === kindId)
            if (deviceKind) {
              this.deviceKindUuid = deviceKind.uuid

              // 使用设备类型UUID获取技术资料要求
              const relationResponse = await devKindDocRelationList({
                devKindUuid: this.deviceKindUuid,
                pageNum: 1,
                pageSize: 1000 // 获取所有数据
              })

              if (relationResponse.code === 0 && relationResponse.data) {
                this.requiredDocuments = relationResponse.data.map(item => ({
                  contentId: item.techDocContent.contentId,
                  contentName: item.techDocContent.contentName,
                  categoryId: item.techDocContent.categoryId,
                  categoryName: item.techDocContent.categoryName,
                  orderNum: item.techDocContent.orderNum
                }))
              }
            }
          }
        } else {
          console.warn('设备类型ID不存在，无法获取技术资料要求');
          this.requiredDocuments = []
        }
      } catch (error) {
        console.error('获取设备类型技术资料要求失败:', error)
        this.requiredDocuments = []
      }
    },

    // 获取已上传技术资料
    async fetchUploadedDocuments() {
      try {
        const response = await devTechDocList({ uuid: this.equipmentUuid })
        if (response.code === 0 && response.data) {
          this.uploadedDocuments = response.data.map(item => ({
            id: item.id,
            uuid: item.uuid,
            contentId: item.techDocContentId,
            contentName: item.techDocContentName,
            fileName: item.fileName,
            attachmentUrl: item.attachmentUrl,
            fileSize: item.fileSize,
            fileType: item.fileType
          }))
        } else {
          this.uploadedDocuments = []
        }
      } catch (error) {
        console.error('获取已上传技术资料失败:', error)
        this.uploadedDocuments = []
      }
    },

    // 处理和合并数据
    processDocuments() {
      // 创建一个Map来快速查找已上传的文件
      const uploadedMap = new Map()
      this.uploadedDocuments.forEach(uploaded => {
        uploadedMap.set(uploaded.contentId, uploaded)
      })

      // 合并需要的技术资料和已上传的资料
      const mergedDocuments = this.requiredDocuments.map(required => {
        const uploaded = uploadedMap.get(required.contentId)
        return {
          ...required,
          isUploaded: !!uploaded,
          uploadedFile: uploaded || null,
          uploadUrl: '' // 用于file-uploader的v-model
        }
      })

      // 按分类组织数据
      const categoryMap = new Map()
      mergedDocuments.forEach(doc => {
        if (!categoryMap.has(doc.categoryId)) {
          categoryMap.set(doc.categoryId, {
            categoryId: doc.categoryId,
            categoryName: doc.categoryName,
            documents: []
          })
        }
        categoryMap.get(doc.categoryId).documents.push(doc)
      })

      // 转换为数组并按orderNum排序
      this.categorizedDocuments = Array.from(categoryMap.values()).map(category => ({
        ...category,
        documents: category.documents.sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))
      }))

      // 按分类名称排序（可以根据需要调整排序逻辑）
      this.categorizedDocuments.sort((a, b) => a.categoryName.localeCompare(b.categoryName))
    },

    // 处理文件上传成功
    async handleFileUploadSuccess(fileUrl, document) {
      try {
        // 调用保存技术资料接口
        const response = await devTechDocSave({
          devUuid: this.equipmentUuid,
          attachmentUrl: fileUrl.url,
          techDocContentId: document.contentId
        })

        if (response.code === 0) {
          this.$message.success('技术资料上传成功');
          // 上传成功，刷新数据
          await this.refreshData()
        } else {
          // 保存失败时清空上传的URL
          document.uploadUrl = '';
        }
      } catch (error) {
        console.error('技术资料保存失败:', error);
        // 保存失败时清空上传的URL
        document.uploadUrl = '';
      }
    },

    // 处理文件上传失败
    handleFileUploadError(fileInfo, document) {
      console.error('文件上传失败:', fileInfo);
      // 清空上传的URL
      document.uploadUrl = '';
      // 错误信息已经在file-uploader组件中显示了
    },

    // 确认删除文件
    confirmDelete(document) {
      this.$confirm(
        `确定要删除技术资料"${document.contentName}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.handleFileDelete(document);
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 处理文件删除
    async handleFileDelete(document) {
      try {
        // 调用删除技术资料接口，参数为数组形式
        const response = await devTechDocDelete({
          uuidSet: [document.uploadedFile.uuid]
        })

        if (response.code === 0) {
          this.$message.success('技术资料删除成功');
          // 删除成功，刷新数据
          await this.refreshData()
        }
      } catch (error) {
        console.error('文件删除失败:', error);
      }
    },

    // 预览文件
    previewFile(file) {
      if (file.attachmentUrl) {
        window.open(file.attachmentUrl, '_blank');
      } else {
        this.$message.warning('文件预览链接不可用');
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 下载文件
    downloadFile(file) {
      if (file.attachmentUrl) {
        // 创建一个临时的a标签来触发下载
        const link = document.createElement('a');
        link.href = file.attachmentUrl;
        link.download = file.fileName || '技术资料';
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        this.$message.warning('文件下载链接不可用');
      }
    },

    // 刷新数据
    async refreshData() {
      await this.initializeData()
    }
  },
};
</script>

<style lang="scss" scoped>
.equipment-technical-info {
  padding: 0;

  .loading-container {
    padding: 40px;
  }

  .empty-container {
    padding: 60px 20px;
    text-align: center;
  }

  .content-container {
    .category-section {
      margin-bottom: 10px;

      .category-header {

        .category-title {
          position: relative;
          font-size: 18px;
          color: #333;
          padding-left: 20px;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #2050d1;
            border-radius: 2px;
          }
        }
      }

      .documents-list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        .document-item {
          display: flex;
          align-items: center;
          padding: 15px 0;

          &:last-child {
            border-bottom: none;
          }

          .document-name {
            font-size: 16px;
            color: #333333;
            margin-right: 20px;
          }
          .uploaded-info,
          .view-info{
            flex: 1;
            display: flex;
            align-items: center;

            .file-name {
              font-size: 14px;
              color: #666666;
              margin-right: 10px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .delete-btn {
              color: #f56c6c;
              padding: 5px 10px;

              &:hover {
                color: #f78989;
                background: #fef0f0;
              }
            }
          }
        }
      }
    }
  }
}
</style>