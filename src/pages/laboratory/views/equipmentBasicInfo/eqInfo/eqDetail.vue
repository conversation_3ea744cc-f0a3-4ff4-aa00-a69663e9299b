<template>
  <layoutContent>
    <!-- 返回按钮放在search插槽中 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ equipmentData?.devName || "仪器详情" }}</span>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="equipment-detail-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic" lazy>
          <div class="tab-content" v-loading="loading" v-if="activeTab === 'basic'">
            <equipment-basic-info-display
              :initialData="basicForm"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="扩展信息" name="extended" lazy>
          <div class="tab-content" v-loading="loading" v-if="activeTab === 'extended'">
            <equipment-extended-info-display
              :initialData="extendedForm"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="技术资料" name="technical" lazy>
          <div class="tab-content" v-loading="loading" v-if="activeTab === 'technical'">
            <equipment-technical-info
              v-if="equipmentUuid"
              :isViewMode="true"
              :equipmentUuid="equipmentUuid"
              :equipmentData="equipmentData"
            />
            <div v-else class="empty-tip">
              请先完成基本信息的保存，获取仪器ID后才能管理技术资料
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import EquipmentBasicInfoDisplay from "./eqAddCom/EquipmentBasicInfoDisplay.vue";
import EquipmentExtendedInfoDisplay from "./eqAddCom/EquipmentExtendedInfoDisplay.vue";
import EquipmentTechnicalInfo from "./eqAddCom/EquipmentTechnicalInfo.vue";
import { deviceDetail } from '@laboratory/api/equipmentBasicInfo/eqBasicInfo';

export default {
  name: "eqDetail",
  components: {
    layoutContent,
    EquipmentBasicInfoDisplay,
    EquipmentExtendedInfoDisplay,
    EquipmentTechnicalInfo,
  },
  data() {
    return {
      // 当前激活的标签页
      activeTab: "basic",



      // 基本信息表单
      basicForm: {},

      // 扩展信息表单
      extendedForm: {},

      // 保存状态
      loading: false,
      equipmentUuid: "", // 仪器UUID
      equipmentData: {}, // 保存返回的完整仪器数据对象
    };
  },
  created() {
    this.equipmentUuid = this.$route.params.uuid;
    if (this.equipmentUuid) {
      this.fetchEquipmentDetail();
    } else {
      this.$message.warning("缺少必要的参数");
      this.goBack();
    }


  },

  methods: {


    // 获取仪器详情
    fetchEquipmentDetail() {
      if (!this.equipmentUuid) return;

      this.loading = true;
      deviceDetail({ uuid: this.equipmentUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            // 保存完整的仪器数据
            this.equipmentData = res.data;

            // 解构赋值，优雅地提取数据
            const {
              devSn, devName, assetSn, classNumCode, devModel, devSpecification,
              price, devSource, countryCode, presentSituationCode, purchaseDate,
              directionOfUse, kindId, equipmentImage, technicalParameters,
              deptId, usingAccNo, roomId, storageLocation, factoryName,
              factoryNumber, factoryDate, factoryContact, warrantyDate,
              serviceLife, usingDeptId, searchTag, tagId, latitude, longitude
            } = res.data;

            // 回填基本信息（分转元显示）
            this.basicForm = {
              devSn, devName, assetSn, classNumCode, devModel, devSpecification,
              price: price ? (price / 100).toFixed(2) : '',
              devSource, countryCode, presentSituationCode, purchaseDate,
              directionOfUse, kindId, equipmentImage, technicalParameters,
            };

            // 回填扩展信息
            this.extendedForm = {
              deptId, usingAccNo, roomId, storageLocation, factoryName,
              factoryNumber, factoryDate, factoryContact, warrantyDate,
              serviceLife, usingDeptId, searchTag, tagId, latitude, longitude,
            };
          }
        })
        .catch((error) => {
          console.error('获取仪器详情失败:', error);
          this.$message.error('获取仪器详情失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 返回列表页
    goBack() {
      this.$router.push({ name: "eqInfo" });
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.equipment-detail-tabs {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 0;

  .tab-content {
    min-height: 300px;
    padding: 20px;

    .empty-tip {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}

::v-deep .el-tabs__header {
  padding: 0 20px !important;
}
</style>