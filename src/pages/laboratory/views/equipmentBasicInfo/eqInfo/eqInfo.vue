<template>
  <div class="eq-info-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="待验收" name="pending">
        <equipment-list v-if="activeName === 'pending'" type="pending" />
      </el-tab-pane>
      <el-tab-pane label="基本信息" name="basicInfo">
        <equipment-list v-if="activeName === 'basicInfo'" type="basic" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import EquipmentList from "./eqInfoCom/EquipmentList.vue";

export default {
  name: "EqInfo",
  components: {
    EquipmentList,
  },
  data() {
    return {
      activeName: "pending",
    };
  },
  methods: {
    handleTabClick(tab) {
      console.log("切换到标签页:", tab.name);
    },
  },
};
</script>

<style lang="scss" scoped>
.eq-info-container {
  padding: 20px;
}
</style>