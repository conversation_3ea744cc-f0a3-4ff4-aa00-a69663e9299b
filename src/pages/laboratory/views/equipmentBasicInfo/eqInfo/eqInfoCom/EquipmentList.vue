<template>
  <div class="equipment-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" label-width="100px" class="demo-form-inline">
        <el-form-item label="仪器编号：">
          <el-input v-model="searchForm.devSn" placeholder="请输入仪器编号" clearable></el-input>
        </el-form-item>
        <el-form-item label="资产编号：">
          <el-input v-model="searchForm.assetSn" placeholder="请输入资产编号" clearable></el-input>
        </el-form-item>
        <el-form-item label="仪器名称：">
          <el-input v-model="searchForm.devName" placeholder="请输入仪器名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="国别码：">
          <el-input v-model="searchForm.countryCode" placeholder="请输入国别码" clearable></el-input>
        </el-form-item>
        <el-form-item label="单价：">
          <el-input v-model="searchForm.unitPrice" placeholder="请输入单价" clearable></el-input>
        </el-form-item>
        <el-form-item label="型号：">
          <el-input v-model="searchForm.devModel" placeholder="请输入型号" clearable></el-input>
        </el-form-item>
        <el-form-item label="规格：">
          <el-input v-model="searchForm.devSpecification" placeholder="请输入规格" clearable></el-input>
        </el-form-item>
        <el-form-item label="仪器来源：">
          <dict-cascader v-model="searchForm.devSource" :code-type="1011" placeholder="请选择仪器来源"
            clearable></dict-cascader>
        </el-form-item>
        <el-form-item label="仪器类型：">
          <el-select v-model="searchForm.devKindUuid" placeholder="请选择仪器类型" filterable clearable>
            <el-option v-for="item in equipmentTypeOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用方向：">
          <dict-cascader v-model="searchForm.directionOfUse" :code-type="1013" placeholder="请选择使用方向"
            clearable></dict-cascader>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <!-- <import-export-buttons
          :exportFilePrefix="exportPrefix"
          :show-import="false"
          :show-template="false"
          :exportApi="handleExportApi"
        /> -->
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion ref="equipmentTable" :loading="loading" :table-data="equipmentList" :columns="tableColumns"
        :pagination="paginationConfig" :show-pagination="true" @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange" @handleSelectionChange="handleSelectionChange">
        <!-- 单价列 -->
        <template #price="{ row }">
          ¥{{ (row.price / 100).toFixed(2) }}
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <el-tag :type="getStatusTagType(row.acceptanceStatus)">
            {{ getStatusText(row.acceptanceStatus) }}
          </el-tag>
        </template>

        <!-- 照片列 -->
        <template #image="{ row }">
          <el-image v-if="row.imgUrl" :src="row.imgUrl" :preview-src-list="[row.imgUrl]"
            style="width: 50px; height: 50px" fit="cover">
          </el-image>
          <span v-else>-</span>
        </template>

        <!-- GPS标签列 -->
        <template #gps="{ row }">
          <span v-if="row.latitude && row.longitude">
            {{ row.latitude }}, {{ row.longitude }}
          </span>
          <span v-else>-</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <template v-if="type === 'pending'">
            <el-button v-if="row.acceptanceStatus === 1" type="text" @click="handleComplete(row)">资料补全</el-button>
            <el-button v-if="row.acceptanceStatus === 1" type="text" @click="handleApprove(row)">验收通过</el-button>
            <el-button v-if="row.acceptanceStatus === 4" type="text" @click="handleDetail(row)">查看详情</el-button>
          </template>
          <template v-if="type === 'basic'">
            <el-button type="text" @click="handleDetail(row)">查看详情</el-button>
            <el-button type="text" @click="handleQRCode(row)">二维码</el-button>
          </template>
        </template>
      </table-pagetion>
    </div>

    <!-- 验收确认对话框 -->
    <el-dialog title="验收确认" :visible.sync="acceptanceDialogVisible" width="30%" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="delete-confirm">
        <i class="el-icon-warning-outline warning-icon"></i>
        <p>
          确定要进行验收通过吗？
        </p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="acceptanceDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="acceptanceLoading" @click="confirmAcceptance">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 验收失败对话框 -->
    <el-dialog title="验收失败" :visible.sync="failureDialogVisible" width="40%" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="delete-confirm">
        <i class="el-icon-warning-outline warning-icon error-icon"></i>
        <p class="warning-text">{{ failureMessage }}</p>
        <p>以上文件未上传，请上传后再提交</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="failureDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleGoToUpload">去上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import DictCascader from "@laboratory/components/DictCascader.vue";
import {
  deviceAcceptanceList,
  deviceBasicList,
  devTechDocAcceptanceDevice
} from '@laboratory/api/equipmentBasicInfo/eqBasicInfo'
import { devKindOptions } from '@laboratory/api/equipmentBasicInfo/eqType'

export default {
  name: 'EquipmentList',
  components: {
    TablePagetion,
    ImportExportButtons,
    DictCascader
  },
  props: {
    // 类型：pending-待验收，basic-基本信息
    type: {
      type: String,
      required: true,
      validator: value => ['pending', 'basic'].includes(value)
    }
  },
  data() {
    return {
      loading: false,
      selectedItems: [],
      equipmentList: [],
      equipmentTypeOptions: [],
      searchForm: {
        devSn: '',
        assetSn: '',
        devName: '',
        countryCode: '',
        unitPrice: '',
        devModel: '',
        devSpecification: '',
        devSource: '',
        devKindUuid: '',
        directionOfUse: '',
        orderRule: "asc",
        orderItems: "devSn",
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      // 表格列配置 - 根据类型动态生成
      tableColumns: [],
      // 验收相关
      acceptanceDialogVisible: false,
      acceptanceLoading: false,
      failureDialogVisible: false,
      failureMessage: '',
      currentAcceptanceItem: null,
    };
  },
  computed: {
    // 导出文件前缀
    exportPrefix() {
      return this.type === 'pending' ? '待验收仪器列表' : '仪器基本信息列表';
    },
    // 获取对应的API方法
    listApi() {
      return this.type === 'pending' ? deviceAcceptanceList : deviceBasicList;
    }
  },
  mounted() {
    this.initTableColumns();
    this.loadEquipmentTypeOptions();
    this.loadEquipmentList();
  },
  methods: {
    // 加载仪器类型选项
    async loadEquipmentTypeOptions() {
      try {
        const response = await devKindOptions();
        if (response.code === 0) {
          this.equipmentTypeOptions = response.data.map(item => ({
            value: item.uuid,
            label: item.devKindName
          }));
        }
      } catch (error) {
        console.error("加载仪器类型选项失败:", error);
      }
    },

    // 初始化表格列配置
    initTableColumns() {
      this.tableColumns = this.type === 'pending'
        ? this.getPendingColumns()
        : this.getBasicColumns();
    },

    // 待验收列表列配置
    getPendingColumns() {
      return [
        { prop: "devSn", label: "仪器编号", minWidth: 120 },
        { prop: "assetSn", label: "资产编号", minWidth: 120 },
        { prop: "classNumName", label: "分类号", minWidth: 100 },
        { prop: "devName", label: "仪器名称", minWidth: 150 },
        { prop: "devModel", label: "型号", minWidth: 120 },
        { prop: "devSpecification", label: "规格", minWidth: 120 },
        { prop: "devSourceName", label: "仪器来源", minWidth: 120 },
        { prop: "countryCode", label: "国别码", minWidth: 100 },
        { prop: "price", label: "单价", minWidth: 100, slotName: "price" },
        { prop: "purchaseDate", label: "购置日期", minWidth: 120 },
        { prop: "presentSituationName", label: "现状码", minWidth: 100 },
        { prop: "directionOfUseName", label: "使用方向", minWidth: 120 },
        { prop: "deptName", label: "单位名称", minWidth: 150 },
        { prop: "acceptanceStatus", label: "状态", minWidth: 100, slotName: "status" },
        { prop: "operation", label: "操作", fixed: "right", width: "200", slotName: "operation" },
      ];
    },

    // 基本信息列表列配置
    getBasicColumns() {
      return [
        ...this.getPendingColumns().slice(0, -2), // 复用基础列，移除状态和操作列
        { prop: "roomName", label: "所属房间", minWidth: 120 },
        { prop: "presentSituationName", label: "设备状态", minWidth: 100 },
        { prop: "factoryDate", label: "出厂日期", minWidth: 120 },
        { prop: "factoryNumber", label: "出厂号", minWidth: 120 },
        { prop: "usingPersonName", label: "管理人", minWidth: 100 },
        { prop: "usingDeptName", label: "使用单位", minWidth: 120 },
        { prop: "usingTrueName", label: "使用人", minWidth: 100 },
        { prop: "imgUrl", label: "照片", minWidth: 80, slotName: "image" },
        { prop: "storageLocation", label: "存放地点", minWidth: 150 },
        { prop: "devKindName", label: "仪器类型", minWidth: 120 },
        { prop: "tagId", label: "设备RFID标签", minWidth: 120 },
        { prop: "searchTag", label: "设备标签", minWidth: 120 },
        { prop: "gpsTag", label: "GPS标签", minWidth: 120, slotName: "gps" },
        { prop: "serviceLife", label: "使用年限", minWidth: 100 },
        { prop: "operation", label: "操作", fixed: "right", width: "200", slotName: "operation" },
      ];
    },
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        1: 'info',      // 草稿
        2: 'warning',   // 待验收
        3: 'primary',   // 验收中
        4: 'success',   // 已验收
        5: 'danger',    // 验收不通过
      };
      return statusMap[status] || 'info';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '草稿',
        2: '待验收',
        3: '验收中',
        4: '已验收',
        5: '验收不通过',
      };
      return statusMap[status] || '未知';
    },

    // 加载设备列表
    async loadEquipmentList() {
      this.loading = true;
      try {
        const params = {
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
          ...this.searchForm
        };

        const res = await this.listApi(params);
        if (res.code === 0) {
          this.equipmentList = res.data || [];
          this.paginationConfig.total = res.count || 0;
        } else {
          this.$message.error(res.message || '获取设备列表失败');
          this.equipmentList = [];
          this.paginationConfig.total = 0;
        }
      } catch (error) {
        console.error('获取设备列表失败:', error);
        this.equipmentList = [];
        this.paginationConfig.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadEquipmentList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        devSn: '',
        assetSn: '',
        devName: '',
        countryCode: '',
        unitPrice: '',
        devModel: '',
        devSpecification: '',
        devSource: '',
        devKindUuid: '',
        directionOfUse: '',
        orderRule: "asc",
        orderItems: "devSn",
      };
      this.paginationConfig.currentPage = 1;
      this.loadEquipmentList();
    },

    // 新增
    handleAdd() {
      this.$router.push({ name: 'eqAdd' });
    },

    async handleExportApi(params) {
      params = {
        ...params,
        ...this.searchForm,
      };
      console.log(`导出${this.exportPrefix}`, params);
      this.$message.info('导出功能开发中');
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadEquipmentList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadEquipmentList();
    },

    // 资料补全
    async handleComplete(row) {
      this.$router.push({
        name: 'eqEdit',
        params: { uuid: row.uuid }
      });
    },

    // 去上传（验收失败时）
    handleGoToUpload() {
      // 关闭失败对话框
      this.failureDialogVisible = false;

      // 跳转到编辑页面，并默认为第三步技术资料模块
      this.$router.push({
        name: 'eqEdit',
        params: { uuid: this.currentAcceptanceItem.uuid },
        query: { step: 3 } // 默认为第三步技术资料模块
      });
    },

    // 验收通过
    handleApprove(row) {
      this.currentAcceptanceItem = row;
      this.acceptanceDialogVisible = true;
    },

    // 确认验收
    async confirmAcceptance() {
      if (!this.currentAcceptanceItem || !this.currentAcceptanceItem.uuid) {
        this.$message.error('验收失败：无效的设备信息');
        return;
      }

      this.acceptanceLoading = true;
      try {
        const data = {
          uuid: this.currentAcceptanceItem.uuid
        };

        const res = await devTechDocAcceptanceDevice(data);

        if (res.code === 0) {
          // 验收成功
          this.$message.success('验收通过成功');
          this.acceptanceDialogVisible = false;
          this.loadEquipmentList();
        } else if (res.code === 1) {
          // 验收失败，显示失败信息
          this.acceptanceDialogVisible = false;
          this.failureMessage = res.message || '验收失败';
          this.failureDialogVisible = true;
        } else {
          this.$message.error(res.message || '验收失败');
        }
      } catch (error) {
        console.error('验收通过失败:', error);
        this.$message.error('验收失败，请稍后重试');
      } finally {
        this.acceptanceLoading = false;
      }
    },

    // 查看详情
    handleDetail(row) {
      this.$router.push({
        name: 'eqDetail',
        params: { uuid: row.uuid }
      });
    },

    // 二维码
    handleQRCode(row) {
      this.$message.info('二维码功能开发中');
    }
  }
}
</script>

<style lang="scss" scoped>
.equipment-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 确认对话框样式 */
.delete-confirm {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  font-size: 16px;
  color: #333333;
  .warning-icon {
    font-size: 48px;
    color: #e6a23c;
    margin-bottom: 20px;
  }
  .error-icon {
    color: #D2585D;
  }
  p {
    margin: 10px 0;
    text-align: center;

    .highlight {
      font-weight: bold;
      color: #f56c6c;
    }
  }

  .warning-text {
    color: #f56c6c;
  }
}
</style>