<template>
  <layoutContent>
    <div class="eq-type-list">
      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="仪器设备类型名称：">
            <el-input v-model="searchForm.devKindName" placeholder="请输入仪器设备类型名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="header-actions">
        <div class="left-actions">
          <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <table-pagetion ref="eqTypeTable" :loading="loading" :table-data="eqTypeList" :columns="tableColumns"
        :pagination="paginationConfig" :show-pagination="true" @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange" @sort-change="handleSortChange"
        @handleSelectionChange="handleSelectionChange">
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (paginationConfig.currentPage - 1) * paginationConfig.pageSize + eqTypeList.indexOf(row) + 1 }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleTechDoc(row)">技术资料</el-button>
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
      <!-- 新增/编辑弹窗 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false"
        @closed="handleDialogClosed">
        <el-form ref="eqTypeForm" :model="eqTypeForm" :rules="rules" label-width="140px">
          <el-form-item label="仪器设备类型名称" prop="devKindName">
            <el-input v-model="eqTypeForm.devKindName" placeholder="请输入仪器设备类型名称"></el-input>
          </el-form-item>
          <el-form-item label="排序序号" prop="orderNum">
            <el-input-number v-model="eqTypeForm.orderNum" :min="1" placeholder="请输入排序序号"></el-input-number>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
        </div>
      </el-dialog>

      <!-- 技术资料关联弹窗 -->
      <el-dialog title="技术资料关联" :visible.sync="techDocDialogVisible" width="1200px" :close-on-click-modal="false" @closed="handleTechDocDialogClosed">
        <div class="tech-doc-content">
          <!-- 全选功能 -->
          <div class="select-all-section">
            <el-checkbox v-model="selectAll" @change="handleSelectAllChange">全选</el-checkbox>
          </div>
          
          <div class="tech-doc-options" v-loading="techDocLoading">
            <div v-for="group in groupedTechDocs" :key="group.categoryName" class="tech-doc-group">
              <div class="group-header">
                <i class="iconfont icon-layers"></i>
                <span class="group-name">{{ group.categoryName }}</span>
              </div>
              <el-checkbox-group class="group-items" v-model="selectedTechDocs">
                <el-checkbox v-for="item in group.items" :key="item.contentId" class="checkbox-item"
                  :label="item.contentId" :value="item.contentId">
                  <div class="item-content">
                    <div class="item-name">{{ item.contentName }}</div>
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <el-empty v-if="!techDocLoading && groupedTechDocs.length === 0" description="暂无技术资料数据"></el-empty>
          </div>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="techDocDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleTechDocSave" :loading="techDocSaveLoading">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from '@laboratory/components/layoutContent'
import TablePagetion from "@laboratory/components/TablePagetion"
import {
  devKindList,
  devKindSave,
  devKindUpdate,
  devKindDelete
} from "@laboratory/api/equipmentBasicInfo/eqType"
import {
  devTechDocContentList,
  devKindDocRelationSave,
  devKindDocRelationTechDocContentIds
} from "@laboratory/api/equipmentBasicInfo/skDataType"

export default {
  name: 'EqType',
  components: {
    layoutContent,
    TablePagetion
  },
  data () {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增仪器设备类型",
      selectedItems: [],
      eqTypeList: [],
      // 技术资料关联相关
      techDocDialogVisible: false,
      techDocLoading: false,
      techDocSaveLoading: false,
      currentDevKind: null,
      techDocList: [],
      selectedTechDocs: [],
      selectAll: false,
      searchForm: {
        devKindName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      },
      eqTypeForm: {
        uuid: "",
        devKindName: "",
        orderNum: null,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        devKindName: [{ required: true, message: "请输入仪器设备类型名称", trigger: "blur" }],
      },
      // 表格列配置
      tableColumns: [
        { prop: "index", label: "序号", slotName: "index" },
        { prop: "orderNum", label: "排序", sortable: "custom" },
        { prop: "devKindName", label: "仪器设备类型名称", },
        { prop: "operation", label: "操作", fixed: "right", slotName: "operation" },
      ],
    }
  },
  computed: {
    // 处理技术资料分类及内容列表的嵌套结构
    groupedTechDocs() {
      if (!this.techDocList || this.techDocList.length === 0) {
        return [];
      }

      // 直接使用接口返回的分类结构，每个分类包含contentList
      return this.techDocList.map(category => ({
        categoryName: category.categoryName || "其他",
        items: category.contentList || []
      })).filter(group => group.items.length > 0); // 过滤掉没有内容的分类
    },
    
    // 获取所有技术资料内容的ID列表
    allTechDocIds() {
      const ids = [];
      this.groupedTechDocs.forEach(group => {
        group.items.forEach(item => {
          ids.push(item.contentId);
        });
      });
      return ids;
    },
  },
  mounted () {
    this.loadEqTypeList()
  },
  methods: {
    // 加载仪器设备类型列表
    async loadEqTypeList () {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        }
        const response = await devKindList(params)
        if (response.code === 0) {
          this.eqTypeList = response.data || []
          this.paginationConfig.total = response.count || 0
        }
      } catch (error) {
        console.error("加载仪器设备类型列表失败:", error)
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch () {
      this.paginationConfig.currentPage = 1
      this.loadEqTypeList()
    },

    // 重置
    handleReset () {
      this.searchForm = {
        devKindName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      }
      this.paginationConfig.currentPage = 1
      this.loadEqTypeList()
    },

    // 新增
    handleAdd () {
      this.dialogTitle = "新增仪器设备类型"
      this.eqTypeForm = {
        uuid: "",
        devKindName: "",
        orderNum: null,
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit (row) {
      this.dialogTitle = "编辑仪器设备类型"
      this.eqTypeForm = {
        uuid: row.uuid,
        devKindName: row.devKindName,
        orderNum: row.orderNum,
      }
      this.dialogVisible = true
    },

    // 删除
    handleDelete (row) {
      this.$confirm("确定要删除该仪器设备类型吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await devKindDelete({ uuid: row.uuid })
          if (response.code === 0) {
            this.$message.success("删除成功")
            this.loadEqTypeList()
          }
        } catch (error) {
          console.error("删除失败:", error)
        }
      })
    },

    // 提交表单
    handleSubmit () {
      this.$refs.eqTypeForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const isEdit = !!this.eqTypeForm.uuid
            const api = isEdit ? devKindUpdate : devKindSave
            const response = await api(this.eqTypeForm)

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功")
              this.dialogVisible = false
              this.loadEqTypeList()
            }
          } catch (error) {
            console.error("提交失败:", error)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 表格选择变化
    handleSelectionChange (selection) {
      this.selectedItems = selection
    },

    // 分页大小变化
    handleSizeChange (size) {
      this.paginationConfig.pageSize = size
      this.paginationConfig.currentPage = 1
      this.loadEqTypeList()
    },

    // 当前页变化
    handleCurrentChange (page) {
      this.paginationConfig.currentPage = page
      this.loadEqTypeList()
    },

    // 排序变化
    handleSortChange ({ prop, order }) {
      if (prop === 'orderNum') {
        this.searchForm.orderItems = 'orderNum'
        this.searchForm.orderRule = order === 'ascending' ? 'asc' : 'desc'
        this.paginationConfig.currentPage = 1
        this.loadEqTypeList()
      }
    },

    // 弹窗关闭
    handleDialogClosed () {
      this.$refs.eqTypeForm.resetFields()
    },

    // 技术资料关联
    async handleTechDoc(row) {
      this.currentDevKind = row
      this.techDocDialogVisible = true
      await this.loadTechDocData()
    },

    // 加载技术资料数据
    async loadTechDocData() {
      this.techDocLoading = true
      try {
        // 并行获取技术资料列表和已关联的技术资料ID
        const [techDocRes, relationRes] = await Promise.all([
          devTechDocContentList(), // 获取技术资料分类及内容列表，无需参数
          devKindDocRelationTechDocContentIds({ devKindId: this.currentDevKind.devKindId })
        ])

        // 处理技术资料分类及内容列表
        if (techDocRes.code === 0) {
          this.techDocList = techDocRes.data || []
        }

        // 处理已关联的技术资料ID，用于回显
        if (relationRes.code === 0) {
          this.selectedTechDocs = relationRes.data || []
        }
        
        // 默认取消全选状态
        this.selectAll = false
      } catch (error) {
        console.error('获取技术资料数据失败:', error)
      } finally {
        this.techDocLoading = false
      }
    },

    // 保存技术资料关联
    async handleTechDocSave() {
      this.techDocSaveLoading = true
      try {
        const data = {
          devKindUuid: this.currentDevKind.uuid,
          techDocContentIds: this.selectedTechDocs
        }

        const response = await devKindDocRelationSave(data)
        if (response.code === 0) {
          this.$message.success("技术资料关联保存成功")
          this.techDocDialogVisible = false
        }
      } catch (error) {
        console.error('保存技术资料关联失败:', error)
      } finally {
        this.techDocSaveLoading = false
      }
    },

    // 全选/取消全选
    handleSelectAllChange(checked) {
      if (checked) {
        // 全选：选中所有技术资料
        this.selectedTechDocs = [...this.allTechDocIds]
      } else {
        // 取消全选：清空所有选择
        this.selectedTechDocs = []
      }
    },

    // 技术资料弹窗关闭
    handleTechDocDialogClosed() {
      this.currentDevKind = null
      this.techDocList = []
      this.selectedTechDocs = []
      this.selectAll = false
    },
  }
}
</script>

<style lang="scss" scoped>
.eq-type-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  // 技术资料关联弹窗样式
  .tech-doc-content {
    max-height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
    
    .select-all-section {
      padding: 8px 0 12px 0;
      border-bottom: 1px solid #e8e8e8;
      margin-bottom: 16px;
      
      .el-checkbox {
        font-weight: 500;
        color: #333333;
      }
    }

    .tech-doc-options {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      width: 100%;
      box-sizing: border-box;

      .tech-doc-group {
        margin-bottom: 12px;
        min-width: 0; // 防止grid项目溢出

        .group-header {
          padding: 4px 0;
          margin-bottom: 8px;

          i {
            color: #2050D1;
          }
          .group-name {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            margin-left: 8px;
          }
        }

        .group-items {
          width: 100%;
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 6px;
          color: #333333;

          .checkbox-item {
            min-width: 0; // 防止checkbox溢出
            display: flex;
            align-items: center;

            ::v-deep .el-checkbox {
              margin-right: 6px;
            }

            ::v-deep .el-checkbox__label {
              max-width: 250px;
              box-sizing: border-box;
              padding-left: 0;
              font-size: 14px;
            }

            .item-content {
              font-size: 14px;
              width: 100%;
              max-width: 250px;

              .item-name {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.4;
                padding-left: 10px;
              }
            }
          }
        }
      }
    }
  }
}
</style>