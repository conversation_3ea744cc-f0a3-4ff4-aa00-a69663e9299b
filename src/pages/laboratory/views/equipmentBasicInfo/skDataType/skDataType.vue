<template>
  <div class="sk-data-type-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="技术类型" name="techType">
        <tech-type-list v-if="activeName === 'techType'" />
      </el-tab-pane>
      <el-tab-pane label="技术内容" name="techContent">
        <tech-content-list v-if="activeName === 'techContent'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TechTypeList from "./skDataTypeCom/TechTypeList.vue"
import TechContentList from "./skDataTypeCom/TechContentList.vue"

export default {
  name: 'SkDataType',
  components: {
    TechTypeList,
    TechContentList
  },
  data() {
    return {
      activeName: "techType"
    }
  },
  methods: {
    handleTabClick(tab) {
      console.log("切换到标签页:", tab.name)
    }
  }
}
</script>

<style lang="scss" scoped>
.sk-data-type-container {
  padding: 20px 16px;
}
</style>