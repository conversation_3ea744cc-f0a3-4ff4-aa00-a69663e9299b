<template>
  <div class="tech-content-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="技术资料类型：">
          <el-select v-model="searchForm.categoryUuid" placeholder="请选择技术资料类型" filterable clearable>
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资料内容：">
          <el-input v-model="searchForm.contentName" placeholder="请输入资料内容" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion ref="techContentTable" :loading="loading" :table-data="techContentList" :columns="tableColumns"
        :pagination="paginationConfig" :show-pagination="true" @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange" @sort-change="handleSortChange"
        @handleSelectionChange="handleSelectionChange">
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (paginationConfig.currentPage - 1) * paginationConfig.pageSize + techContentList.indexOf(row) + 1 }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false"
      @closed="handleDialogClosed">
      <el-form ref="techContentForm" :model="techContentForm" :rules="rules" label-width="120px">
        <el-form-item label="资料内容名称" prop="contentName">
          <el-input v-model="techContentForm.contentName" placeholder="请输入资料内容名称"></el-input>
        </el-form-item>
        <el-form-item label="所属类型" prop="categoryId">
          <el-select v-model="techContentForm.categoryId" placeholder="请选择所属类型" style="width: 100%">
            <el-option v-for="item in categoryOptions" :key="item.categoryId" :label="item.categoryName"
              :value="item.categoryId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序序号" prop="orderNum">
          <el-input-number v-model="techContentForm.orderNum" :min="1" placeholder="请输入排序序号"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion"
import {
  devTechDocContentPage,
  devTechDocContentSave,
  devTechDocContentUpdate,
  devTechDocContentDelete,
  devTechDocCategoryOptions
} from "@laboratory/api/equipmentBasicInfo/skDataType"

export default {
  name: "TechContentList",
  components: {
    TablePagetion
  },
  data () {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增技术资料内容",
      selectedItems: [],
      techContentList: [],
      categoryOptions: [],
      searchForm: {
        contentName: "",
        categoryUuid: "",
        orderRule: "asc",
        orderItems: "orderNum",
      },
      techContentForm: {
        uuid: "",
        contentName: "",
        categoryId: "",
        orderNum: null,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        contentName: [{ required: true, message: "请输入资料内容名称", trigger: "blur" }],
        categoryId: [{ required: true, message: "请选择所属类型", trigger: "change" }],
      },
      // 表格列配置
      tableColumns: [
        { prop: "index", label: "序号", slotName: "index" },
        { prop: "orderNum", label: "排序", sortable: "custom" },
        { prop: "contentName", label: "资料内容名称", },
        { prop: "categoryName", label: "所属类型", },
        { prop: "operation", label: "操作", fixed: "right", slotName: "operation" },
      ],
    }
  },
  mounted () {
    this.loadCategoryOptions()
    this.loadTechContentList()
  },
  methods: {
    // 加载技术资料类型选项
    async loadCategoryOptions () {
      try {
        const response = await devTechDocCategoryOptions()
        if (response.code === 0) {
          this.categoryOptions = response.data.map((item) => ({
            value: item.uuid,
            label: item.categoryName,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
          }))
        }
      } catch (error) {
        console.error("加载技术资料类型选项失败:", error)
      }
    },

    // 加载技术资料内容列表
    async loadTechContentList () {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        }
        const response = await devTechDocContentPage(params)
        if (response.code === 0) {
          this.techContentList = response.data || []
          this.paginationConfig.total = response.count || 0
        }
      } catch (error) {
        console.error("加载技术资料内容列表失败:", error)
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch () {
      this.paginationConfig.currentPage = 1
      this.loadTechContentList()
    },

    // 重置
    handleReset () {
      this.searchForm = {
        contentName: "",
        categoryUuid: "",
        orderRule: "asc",
        orderItems: "orderNum",
      }
      this.paginationConfig.currentPage = 1
      this.loadTechContentList()
    },

    // 新增
    handleAdd () {
      this.dialogTitle = "新增技术资料内容"
      this.techContentForm = {
        uuid: "",
        contentName: "",
        categoryId: "",
        orderNum: null,
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit (row) {
      this.dialogTitle = "编辑技术资料内容"
      this.techContentForm = {
        uuid: row.uuid,
        contentName: row.contentName,
        categoryId: row.categoryId,
        orderNum: row.orderNum,
      }
      this.dialogVisible = true
    },

    // 删除
    handleDelete (row) {
      this.$confirm("确定要删除该技术资料内容吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await devTechDocContentDelete({ uuid: row.uuid })
          if (response.code === 0) {
            this.$message.success("删除成功")
            this.loadTechContentList()
          }
        } catch (error) {
          console.error("删除失败:", error)
        }
      })
    },

    // 提交表单
    handleSubmit () {
      this.$refs.techContentForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const isEdit = !!this.techContentForm.uuid
            const api = isEdit ? devTechDocContentUpdate : devTechDocContentSave
            const response = await api(this.techContentForm)

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功")
              this.dialogVisible = false
              this.loadTechContentList()
            }
          } catch (error) {
            console.error("提交失败:", error)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 表格选择变化
    handleSelectionChange (selection) {
      this.selectedItems = selection
    },

    // 分页大小变化
    handleSizeChange (size) {
      this.paginationConfig.pageSize = size
      this.paginationConfig.currentPage = 1
      this.loadTechContentList()
    },

    // 当前页变化
    handleCurrentChange (page) {
      this.paginationConfig.currentPage = page
      this.loadTechContentList()
    },

    // 排序变化
    handleSortChange ({ prop, order }) {
      if (prop === 'orderNum') {
        this.searchForm.orderItems = 'orderNum'
        this.searchForm.orderRule = order === 'ascending' ? 'asc' : 'desc'
        this.paginationConfig.currentPage = 1
        this.loadTechContentList()
      }
    },

    // 弹窗关闭
    handleDialogClosed () {
      this.$refs.techContentForm.resetFields()
    },
  }
}
</script>

<style lang="scss" scoped>
.tech-content-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>