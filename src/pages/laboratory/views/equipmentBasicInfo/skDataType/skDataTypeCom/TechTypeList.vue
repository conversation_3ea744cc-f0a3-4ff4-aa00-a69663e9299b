<template>
  <div class="tech-type-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="技术资料类型名称：">
          <el-input v-model="searchForm.categoryName" placeholder="请输入技术资料类型名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="techTypeTable"
        :loading="loading"
        :table-data="techTypeList"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @sort-change="handleSortChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (paginationConfig.currentPage - 1) * paginationConfig.pageSize + techTypeList.indexOf(row) + 1 }}
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false" @closed="handleDialogClosed">
      <el-form ref="techTypeForm" :model="techTypeForm" :rules="rules" label-width="140px">
        <el-form-item label="技术资料类型名称" prop="categoryName">
          <el-input v-model="techTypeForm.categoryName" placeholder="请输入技术资料类型名称"></el-input>
        </el-form-item>
        <el-form-item label="排序序号" prop="orderNum">
          <el-input-number v-model="techTypeForm.orderNum" :min="1" placeholder="请输入排序序号" ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion"
import {
  devTechDocCategoryList,
  devTechDocCategorySave,
  devTechDocCategoryUpdate,
  devTechDocCategoryDelete
} from "@laboratory/api/equipmentBasicInfo/skDataType"

export default {
  name: "TechTypeList",
  components: {
    TablePagetion
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增技术资料类型",
      selectedItems: [],
      techTypeList: [],
      searchForm: {
        categoryName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      },
      techTypeForm: {
        uuid: "",
        categoryName: "",
        orderNum: null,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        categoryName: [{ required: true, message: "请输入技术资料类型名称", trigger: "blur" }],
      },
      // 表格列配置
      tableColumns: [
        { prop: "index", label: "序号",  slotName: "index" },
        { prop: "orderNum", label: "排序",  sortable: "custom" },
        { prop: "categoryName", label: "技术资料类型名称", },
        { prop: "operation", label: "操作", fixed: "right", slotName: "operation" },
      ],
    }
  },
  mounted() {
    this.loadTechTypeList()
  },
  methods: {
    // 加载技术资料类型列表
    async loadTechTypeList() {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        }
        const response = await devTechDocCategoryList(params)
        if (response.code === 0) {
          this.techTypeList = response.data || []
          this.paginationConfig.total = response.count || 0
        }
      } catch (error) {
        console.error("加载技术资料类型列表失败:", error)
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1
      this.loadTechTypeList()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        categoryName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      }
      this.paginationConfig.currentPage = 1
      this.loadTechTypeList()
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增技术资料类型"
      this.techTypeForm = {
        uuid: "",
        categoryName: "",
        orderNum: null,
      }
      this.dialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑技术资料类型"
      this.techTypeForm = {
        uuid: row.uuid,
        categoryName: row.categoryName,
        orderNum: row.orderNum,
      }
      this.dialogVisible = true
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该技术资料类型吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await devTechDocCategoryDelete({ uuid: row.uuid })
          if (response.code === 0) {
            this.$message.success("删除成功")
            this.loadTechTypeList()
          }
        } catch (error) {
          console.error("删除失败:", error)
        }
      })
    },

    // 提交表单
    handleSubmit() {
      this.$refs.techTypeForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const isEdit = !!this.techTypeForm.uuid
            const api = isEdit ? devTechDocCategoryUpdate : devTechDocCategorySave
            const response = await api(this.techTypeForm)

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功")
              this.dialogVisible = false
              this.loadTechTypeList()
            }
          } catch (error) {
            console.error("提交失败:", error)
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size
      this.paginationConfig.currentPage = 1
      this.loadTechTypeList()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page
      this.loadTechTypeList()
    },

    // 排序变化
    handleSortChange({ prop, order }) {
      if (prop === 'orderNum') {
        this.searchForm.orderItems = 'orderNum'
        this.searchForm.orderRule = order === 'ascending' ? 'asc' : 'desc'
        this.paginationConfig.currentPage = 1
        this.loadTechTypeList()
      }
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.techTypeForm.resetFields()
    },
  }
}
</script>

<style lang="scss" scoped>
.tech-type-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>