<template>
  <layoutContent>
    <div class="purchase-application-container">
      <h2>采购申请</h2>
      <p>采购申请页面内容待开发...</p>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from '@laboratory/components/layoutContent'

export default {
  name: 'PurchaseApplication',
  components: {
    layoutContent
  },
  data() {
    return {
      // 页面数据
    }
  },
  created() {
    // 初始化逻辑
  },
  methods: {
    // 页面方法
  }
}
</script>

<style lang="scss" scoped>
.purchase-application-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}
</style>