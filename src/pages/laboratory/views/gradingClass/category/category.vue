<template>
  <div class="category">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="分类名称：">
          <el-input v-model="searchForm.categoryName" placeholder="请输入分类名称" clearable></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <!-- <el-button
          type="danger"

          icon="el-icon-delete"
          @click="handleBatchDelete"
          :disabled="selectedItems.length === 0"
        >
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <import-export-buttons exportFilePrefix="实验室分类列表" :show-import="false" :show-template="false" :exportApi="handleExportApi" />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="categoryTable"
        :loading="loading"
        :table-data="categoryList"
        :columns="columns"
        :pagination="paginationConfig"
        :selection="true"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: #D2585D" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false" @closed="handleDialogClosed">
      <el-form ref="categoryForm" :model="categoryForm" :rules="rules" label-width="100px">
        <el-form-item label="实验室分类" prop="categoryName">
          <el-input v-model="categoryForm.categoryName" style="width: 50%" placeholder="请输入实验室分类"></el-input>
        </el-form-item>
        <el-form-item label="说明" prop="detailedExplanation">
          <el-input v-model="categoryForm.detailedExplanation" type="textarea" :rows="4" placeholder="请输入说明"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number
            v-model="categoryForm.orderNum"
            :min="0"
            :max="9999"
            placeholder="请输入排序数字"
            style="width: 30%"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import {
  labSafetyCategoryList,
  labSafetyCategorySave,
  labSafetyCategoryUpdate,
  labSafetyCategoryDelete,
  labSafetyCategoryExport,
} from "@laboratory/api/gradingClass/category";
export default {
  name: "category",
  components: {
    ImportExportButtons,
    TablePagetion,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增分类",
      selectedItems: [],
      categoryList: [],
      classificationList: [],
      scoreList: [],
      searchForm: {
        categoryName: "",
      },
      categoryForm: {
        uuid: "",
        categoryName: "",
        detailedExplanation: "",
        orderNum: 0,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        categoryName: [{ required: true, message: "请输入实验室分类", trigger: "blur" }],
        detailedExplanation: [{ required: true, message: "请输入说明", trigger: "blur" }],
        orderNum: [{ required: true, message: "请输入排序数字", trigger: "blur" }],
      },
      columns: [
        {
          prop: "orderNum",
          label: "排序",
          minWidth: 100,
        },
        {
          prop: "categoryName",
          label: "实验室分类",
          minWidth: 150,
        },
        {
          prop: "labCount",
          label: "实验室个数",
          minWidth: 100,
        },
        {
          prop: "detailedExplanation",
          label: "说明",
          minWidth: 300,
        },
        {
          prop: "updateByName",
          label: "操作人",
          minWidth: 120,
        },
        {
          prop: "gmtModified",
          label: "操作时间",
          minWidth: 150,
        },
        {
          prop: "operation",
          label: "操作",
          width: 150,
          fixed: "right",
          slotName: "operation",
        },
      ],
    };
  },
  mounted() {
    // 页面初始化
    this.loadCategoryList();
  },
  methods: {
    // 页面方法
    async loadCategoryList() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          orderItems: "orderNum",
          orderRule: "asc",
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        };
        const response = await labSafetyCategoryList(params);
        if (response.code === 0) {
          this.categoryList = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
        // eslint-disable-next-line no-empty
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadCategoryList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        categoryName: "",
      };
      this.paginationConfig.currentPage = 1;
      this.loadCategoryList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增实验室安全分类";
      this.categoryForm = {
        uuid: "",
        categoryName: "",
        detailedExplanation: "",
        orderNum: 0,
      };
      this.dialogVisible = true;
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = "实验室编辑安全分类";
      this.categoryForm = {
        uuid: row.uuid,
        categoryName: row.categoryName,
        detailedExplanation: row.detailedExplanation,
        orderNum: row.orderNum,
      };
      this.dialogVisible = true;
    },
    //提交表单
    handleSubmit() {
      this.$refs.categoryForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            const isEdit = !!this.categoryForm.uuid;

            const submitData = {
              ...this.categoryForm,
            };
            const api = isEdit ? labSafetyCategoryUpdate : labSafetyCategorySave;
            const response = await api(submitData);

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功");
              this.dialogVisible = false;
              this.loadCategoryList();
            }
            // eslint-disable-next-line no-empty
          } catch (error) {
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedItems.length} 条数据吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const uuids = this.selectedItems.map((item) => item.uuid);
          const response = await labSafetyCategoryDelete({ uuids });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.selectedItems = [];
            this.loadCategoryList();
          }
          // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该实验室安全分类吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetyCategoryDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.loadCategoryList();
          }
          // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
    // 导出API - 使用选项列表接口
    handleExportApi() {
      const params = {
        orderItems: "orderNum",
        orderRule: "asc",
      };
      return labSafetyCategoryExport(params);
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadCategoryList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadCategoryList();
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.categoryForm.resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.category {
  padding: 20px;
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

}
</style>
