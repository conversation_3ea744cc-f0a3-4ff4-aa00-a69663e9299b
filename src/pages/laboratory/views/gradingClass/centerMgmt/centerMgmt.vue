<template>
  <layoutContent>
    <!-- 卡片列表区域 -->
    <div class="centers-list">
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <el-empty v-else-if="centersList.length === 0" description="暂无数据"></el-empty>
      <div class="card-container" v-else>
        <lab-center-card
          v-for="(item, index) in centersList"
          :key="item.uuid || index"
          :item="adaptCenterDataForCard(item)"
          :image-src="imageSrc"
          :viewDeptName="false"
          :ellipsis="1"
        >
          <!-- 使用具名插槽自定义状态文本 -->
          <template #status-text>
            <div class="card-icon">
              <i class="iconfont icon-bianyuan" :class="item.roomKindLevel == 1 ? 'icon-warning' : 'icon-success'" />
              <div class="text">
                <span>{{ item.roomKindLevel === 1 ? "校级" : "学院级" }}</span>
              </div>
            </div>
          </template>

          <!-- 额外信息插槽 - 展示实验室分级统计（两列） -->
          <template #extra-info>
            <div class="lab-level-stats-row">
              <div class="level-row" v-for="(row, rowIdx) in getLevelRows(item.labSafetyRiskLevelSummaryList)" :key="rowIdx">
                <div class="level-col" v-for="levelItem in row" :key="levelItem.scoreLevelId">
                  <div class="level-left">
                    <div class="level-value-container">
                      <i class="iconfont icon-anquandengji" :style="{ color: levelItem.displayColors }"></i>
                      <span class="level-value">{{ levelItem.displayValue }}</span>
                    </div>
                    <div class="level-label ellipsis">{{ levelItem.scoreLevelName }}</div>
                  </div>
                  <div class="level-right">{{ levelItem.labCount }}</div>
                </div>
              </div>
            </div>
          </template>

          <!-- 中间层插槽，分级统计摘要 -->
          <template #middle-layer>
            <div class="stats-summary">
              <div class="summary-item">
                <span class="summary-dot graded"></span>
                <span class="summary-text">已分级实验室</span>
                <span class="summary-count">{{ item.gradedLabCount || 0 }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-dot graded"></span>
                <span class="summary-text">未分级实验室</span>
                <span class="summary-count">{{ item.ungradedLabCount || 0 }}</span>
              </div>
            </div>
          </template>

          <!-- 操作按钮 -->
          <template #actions>
            <el-button type="primary" @click="handleViewDetail(item)">查看详情</el-button>
          </template>
        </lab-center-card>
      </div>
    </div>
  </layoutContent>
</template>

<script>
import LabCenterCard from "@laboratory/components/LabCenterCard.vue";
import { labSafetyLabCenterList } from "@laboratory/api/gradingClass/classifyLaboratory";
import layoutContent from "@laboratory/components/layoutContent";

export default {
  name: "CenterMgmt",
  components: {
    LabCenterCard,
    layoutContent,
  },
  data() {
    return {
      centersList: [],
      pageSize: 999, // 默认每页显示10个卡片
      currentPage: 1,
      total: 0,
      innerLoading: false,
      // 图片路径前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    isLoading() {
      return this.innerLoading;
    },
  },
  created() {
    // 组件创建时初始化数据
    this.fetchCentersData();
  },
  methods: {
    // 将实验中心数据适配为卡片组件所需的格式
    adaptCenterDataForCard(item) {
      // 创建一个新对象，避免修改原对象
      const adaptedItem = { ...item };

      // 确保必要的字段存在
      adaptedItem.roomKindName = item.roomKindName || item.roomKindName || "";
      adaptedItem.roomKindImage = item.roomKindImage || "";
      adaptedItem.deptName = item.deptName || "";
      adaptedItem.responsiblePerson = item.responsiblePerson || "";
      adaptedItem.contactInfo = item.contactInfo || "";
      adaptedItem.roomKindAddress = item.roomKindAddress || "";

      return adaptedItem;
    },

    // 分级统计卡片两列分组
    getLevelRows(list) {
      const rows = [];
      for (let i = 0; i < list.length; i += 2) {
        rows.push(list.slice(i, i + 2));
      }
      return rows;
    },

    // 获取实验中心分级列表数据
    fetchCentersData() {
      this.innerLoading = true;
      let params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };

      labSafetyLabCenterList(params)
        .then((res) => {
          if (res.code === 0) {
            // 展开labCenterInfo层级到一级
            this.centersList = (res.data || []).map((item) => ({
              ...item.labCenterInfo,
              // 保留其他可能的字段
              ...item,
            }));
            this.total = res.count || 0;
          }
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },

    // 查看详情 - 跳转到实验中心管理员权限页面
    handleViewDetail(item) {
      if (!item.uuid) {
        this.$message.error("缺少实验中心标识");
        return;
      }

      // 跳转到 centerLabMgmt 路由，携带实验中心的 uuid 作为路径参数
      this.$router.push({
        name: 'centerLabMgmt',
        params: {
          centerUuid: item.uuid // 实验中心的 uuid
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.centers-list {
  position: relative;
  min-height: 400px;

  .loading-container {
    padding: 20px;
  }

  .card-container {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
  }
}

.lab-level-stats-row {
  .level-row {
    height: 60px;
    display: flex;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
    .level-col {
      flex: 1;
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 4px;
      margin-right: 8px;
      padding: 8px 12px;
      &:last-child {
        margin-right: 0;
      }
      .level-left {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        flex: 1;
        .level-value-container {
          position: relative;
        }
        .icon-anquandengji {
          font-size: 24px;
        }
        .level-value {
          font-size: 13px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        .level-label {
          font-size: 14px;
          color: #333333;
        }
      }
      .level-right {
        margin-right: 10px;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

.pagination-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
}

/* 实验室分级统计样式 */
.stats-summary {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 8px 22px;

  .summary-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-right: 50px;
    .summary-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;

      &.graded {
        background-color: #2050d1;
      }

      &.ungraded {
        background-color: #666;
      }
    }

    .summary-text {
      margin-right: 4px;
    }

    .summary-count {
      font-size: 18px;
      color: #333;
    }
  }
}
</style>
