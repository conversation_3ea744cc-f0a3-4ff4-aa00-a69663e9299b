<template>
  <div class="lab-grading">
     <el-tabs v-model="activeName" type="card" >
        <el-tab-pane label="实验室安全分级类目" name="hierarchicalCategory"><hierarchicalCategory v-if="activeName === 'hierarchicalCategory'"/></el-tab-pane>
        <el-tab-pane label="实验室安全分级分值" name="gradingScore"><gradingScore v-if="activeName === 'gradingScore'"/></el-tab-pane>
      </el-tabs>
  </div>
</template>

<script>
import hierarchicalCategory from './labGradingCom/hierarchical-category.vue'
import gradingScore from './labGradingCom/grading-score.vue'
export default {
  components:{
    hierarchicalCategory,
    gradingScore
  },
  name: 'LabGrading',
  data() {
    return {
      // 页面数据
      activeName:'hierarchicalCategory'
    }
  },
  mounted() {
    // 页面初始化
  },
  methods: {
    // 页面方法
  }
}
</script>

<style scoped>
.lab-grading {
  padding: 20px;
}
</style>