<template>
  <div class="grading-score">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="分值名称：">
          <el-input
            v-model="searchForm.scoreLevelName"

            placeholder="请输入分值名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"

            icon="el-icon-search"
            @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="handleReset"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button
          type="warning"

          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <!-- <el-button
          type="danger"

          icon="el-icon-delete"
          @click="handleBatchDelete"
          :disabled="selectedItems.length === 0"
        >
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <import-export-buttons
          exportFilePrefix="安全分级分值列表"
          :show-import="false"
          :show-template="false"
          :exportApi="handleExportApi"
        />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="categoryTable"
        :loading="loading"
        :table-data="gradingScoreList"
        :columns="columns"
        :pagination="paginationConfig"
        :selection="true"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      <!-- 区间 -->
      <template #numScore="{ row }">
          {{ row.minScore   }}{{row.maxScore == -1?'': '~'+ row.maxScore }}
        </template>
          <!-- 操作列 -->
      <template #operation="{ row }">
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-button type="text" style="color: #D2585D" @click="handleDelete(row)">删除</el-button>
      </template>
    </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @closed="handleDialogClosed"
    >
      <el-form
        ref="gradingScoreForm"
        :model="gradingScoreForm"
        :rules="rules"
        label-width="80px"

      >
        <el-form-item label="分值名称" prop="scoreLevelName">
          <el-input
            v-model="gradingScoreForm.scoreLevelName"
            style="width: 55%"
            placeholder="请输入分值名称"
          ></el-input>
        </el-form-item>
         <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="展示值" prop="displayValue">
              <el-input v-model="gradingScoreForm.displayValue" placeholder="请输入展示值"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="展示颜色" prop="displayColors">
             <el-color-picker v-model="gradingScoreForm.displayColors"></el-color-picker>
            </el-form-item>
          </el-col>
        </el-row>
         <el-form-item label="分值区间" prop="minScore">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-input v-model="gradingScoreForm.minScore" type="number" placeholder="请输入最小值"></el-input>
            </el-col>
            <el-col :span="1">
                ~
            </el-col>
            <el-col :span="8">
            <!-- <template slot="append"><p style="cursor: pointer;">无穷大</p> </template> -->
              <el-input v-model="gradingScoreForm.maxScore" type="number" placeholder="请输入最大值">
              </el-input>
            </el-col>
          </el-row>
          <p style="padding-left: 20px;font-size: 12px;margin: 0;color: rgb(139 138 138);">最大值未输入时为无穷大</p>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number
            v-model="gradingScoreForm.orderNum"
            :min="0"
            :max="9999"
            placeholder="请输入排序数字"
            style="width: 30%"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import {
  labSafetyScoreLevelList,
  labSafetyScoreLevelSave,
  labSafetyScoreLevelUpdate,
  labSafetyScoreLevelDelete,
  labSafetyScoreLevelExport
} from "@laboratory/api/gradingClass/grading";
export default {
  name: "grading-score",
  components: {
    ImportExportButtons,
    TablePagetion,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增安全分级类目",
      selectedItems: [],
      gradingScoreList: [],
      searchForm: {
        scoreLevelName: "",
      },
      gradingScoreForm: {
        uuid: "",
        scoreLevelName: "",
        maxScore: '',
        minScore:'',
        displayValue:'',
        displayColors:'',
        orderNum:0
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
       rules: {
        scoreLevelName: [{ required: true, message: "请输入分值名称", trigger: "blur" }],
        minScore: [{ required: true, message: "请输入最小值", trigger: "blur" }],
        displayValue: [{ required: true, message: "请输入展示值", trigger: "blur" }],
        displayColors: [{ required: true, message: "请输入展示颜色", trigger: "blur" }],
        orderNum: [{ required: true, message: "请输入排序", trigger: "blur" }],
      },
      columns: [
        {
          prop: "orderNum",
          label: "排序",
          minWidth: 100,
        },
        {
          prop: "scoreLevelName",
          label: "分值名称",
          minWidth: 200,
        },
        {
          prop: "labCount",
          label: "实验室个数",
          minWidth: 100,
        },
        {
          prop: "numScore",
          label: "区间",
          minWidth: 200,
          slotName: "numScore",
        },

        {
          prop: "operation",
          label: "操作",
          width: 150,
          fixed: "right",
          slotName: "operation",
        },
      ],
    };
  },
  mounted() {
    // 页面初始化
    this.loadgradingScoreList()
  },
  methods: {
    // 页面方法
    async loadgradingScoreList() {
       this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          orderItems:'orderNum',
          orderRule:'asc',
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        };
        const response = await labSafetyScoreLevelList(params);
        if (response.code === 0) {
          this.gradingScoreList = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
        // eslint-disable-next-line no-empty
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadgradingScoreList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        categoryName: "",
      };
      this.paginationConfig.currentPage = 1;
      this.loadgradingScoreList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增安全分级分值";
      this.gradingScoreForm = {
        uuid: "",
        scoreLevelName: "",
        maxScore: '',
        minScore:'',
        displayValue:'',
        displayColors:'',
        orderNum:0
      };
      this.dialogVisible = true;
    },
    //提交表单
    handleSubmit(){
       this.$refs.gradingScoreForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            const isEdit = !!this.gradingScoreForm.uuid;
            if(!this.gradingScoreForm.maxScore){
              this.gradingScoreForm.maxScore = -1
            }

            const submitData = {
              ...this.gradingScoreForm,
            };
            const api = isEdit
              ? labSafetyScoreLevelUpdate
              : labSafetyScoreLevelSave;
            const response = await api(submitData);

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功");
              this.dialogVisible = false;
              this.loadgradingScoreList();
            }
          // eslint-disable-next-line no-empty
          } catch (error) {
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedItems.length} 条数据吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const uuids = this.selectedItems.map((item) => item.uuid);
          const response = await labSafetyScoreLevelDelete({ uuids });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.selectedItems = [];
            this.loadgradingScoreList();
          }
        // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
     // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑安全分级分值";
      this.gradingScoreForm = {
        uuid: row.uuid,
        scoreLevelName: row.scoreLevelName,
        maxScore: row.maxScore == -1?'':row.maxScore,
        minScore:row.minScore,
        displayValue:row.displayValue,
        displayColors:row.displayColors,
        orderNum: row.orderNum,
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该安全分级分值吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetyScoreLevelDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.loadgradingScoreList();
          }
        // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
    // 导出API - 使用选项列表接口
    async handleExportApi() {
      const params = {
          orderItems:'orderNum',
          orderRule:'asc',
        };
      return await labSafetyScoreLevelExport(params);
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadgradingScoreList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadgradingScoreList();
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.gradingScoreForm.resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.grading-score {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
