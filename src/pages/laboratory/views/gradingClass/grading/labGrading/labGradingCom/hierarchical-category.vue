<template>
  <div class="hierarchical-category">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="级别名称：">
          <el-input
            v-model="searchForm.scoreLevelName"

            placeholder="请输入级别名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"

            icon="el-icon-search"
            @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="handleReset"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button
          type="warning"

          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <!-- <el-button
          type="danger"

          icon="el-icon-delete"
          @click="handleBatchDelete"
          :disabled="selectedItems.length === 0"
        >
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <import-export-buttons
          exportFilePrefix="安全分级类目列表"
          :show-import="false"
          :show-template="false"
          :exportApi="handleExportApi"
        />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="categoryTable"
        :loading="loading"
        :table-data="hierarchicalCategoryList"
        :columns="columns"
        :pagination="paginationConfig"
        :selection="true"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <!-- 操作列 -->
      <template #operation="{ row }">
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-button type="text" style="color: #D2585D" @click="handleDelete(row)">删除</el-button>
      </template>
      </table-pagetion>

    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @closed="handleDialogClosed"
    >
      <el-form
        ref="hierarchicalCategoryForm"
        :model="hierarchicalCategoryForm"
        :rules="rules"
        label-width="80px"

      >
        <el-form-item label="级别名称" prop="scoreLevelId">
          <el-select
            v-model="hierarchicalCategoryForm.scoreLevelId"
            clearable
            placeholder="请选择级别"
          >
            <el-option
              v-for="item in scoreLeveList"
              :key="item.scoreLevelId"
              :label="item.scoreLevelName"
              :value="item.scoreLevelId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分级依据" prop="categoryLevelBasis">
          <el-input
            v-model="hierarchicalCategoryForm.categoryLevelBasis"
            type="textarea"
            :rows="4"
            placeholder="请输入分级依据"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number
            v-model="hierarchicalCategoryForm.orderNum"
            :min="0"
            :max="9999"
            placeholder="请输入排序数字"
            style="width: 30%"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import {
  labSafetyCategoryLevelDelete,
  labSafetyCategoryLevelList,
  labSafetyCategoryLevelUpdate,
  labSafetyCategoryLevelSave,
  labSafetyScoreLevelOptionsList,
  labSafetyCategoryLevelExport
} from "@laboratory/api/gradingClass/grading";
export default {
  name: "category",
  components: {
    ImportExportButtons,
    TablePagetion,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增安全分级类目",
      selectedItems: [],
      hierarchicalCategoryList: [],
      scoreLeveList: [],
      searchForm: {
        scoreLevelName: "",
      },
      hierarchicalCategoryForm: {
        uuid: "",
        categoryLevelBasis: "",
        scoreLevelId: "",
        orderNum: 0,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        scoreLevelId: [
          { required: true, message: "请选择级别名称", trigger: "blur" },
        ],
        categoryLevelBasis: [
          { required: true, message: "请输入分级依据	", trigger: "blur" },
        ],
        orderNum: [
          { required: true, message: "请输入排序数字", trigger: "blur" },
        ],
      },
      columns: [
        {
          prop: "orderNum",
          label: "排序",
          minWidth: 100,
        },
        {
          prop: "scoreLevelName",
          label: "级别名称",
          minWidth: 150,
        },
        {
          prop: "categoryLevelBasis",
          label: "分级依据",
          minWidth: 200,
        },
        {
          prop: "updateByName",
          label: "操作人",
          minWidth: 150,
        },
        {
          prop: "operation",
          label: "操作",
          width: 150,
          fixed: "right",
          slotName: "operation",
        },
      ],
    };
  },
  mounted() {
    // 页面初始化
    this.loadhierarchicalCategoryList();
  },
  methods: {
    // 页面方法
    // 加载分类列表
    async loadhierarchicalCategoryList() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          orderItems:'orderNum',
          orderRule:'asc',
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        };
        const response = await labSafetyCategoryLevelList(params);
        if (response.code === 0) {
          this.hierarchicalCategoryList = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
        // eslint-disable-next-line no-empty
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadhierarchicalCategoryList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        scoreLevelName: "",
      };
      this.paginationConfig.currentPage = 1;
      this.loadhierarchicalCategoryList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增安全分级类目";
      this.hierarchicalCategoryForm = {
        uuid: "",
        categoryLevelBasis: "",
        scoreLevelId: "",
        orderNum: 0,
      };
      this.getScoreLeveList();
      this.dialogVisible = true;
    },
    //获取分值列表
    async getScoreLeveList() {
      const response = await labSafetyScoreLevelOptionsList();
      this.scoreLeveList = response.data;
    },
    //提交表单
    handleSubmit() {
      this.$refs.hierarchicalCategoryForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            const isEdit = !!this.hierarchicalCategoryForm.uuid;
            const submitData = {
              ...this.hierarchicalCategoryForm,
            };
            const api = isEdit
              ? labSafetyCategoryLevelUpdate
              : labSafetyCategoryLevelSave;
            const response = await api(submitData);

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功");
              this.dialogVisible = false;
              this.loadhierarchicalCategoryList();
            }
          // eslint-disable-next-line no-empty
          } catch (error) {
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑安全分级类目";
      this.hierarchicalCategoryForm = {
        uuid: row.uuid,
        categoryLevelBasis:row.categoryLevelBasis,
        scoreLevelId: row.scoreLevelId,
        orderNum: row.orderNum,
      };
      this.getScoreLeveList();
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该分级类目吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetyCategoryLevelDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.loadhierarchicalCategoryList();
          }
        // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedItems.length} 条数据吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        try {
          const uuids = this.selectedItems.map((item) => item.uuid);
          const response = await labSafetyCategoryLevelDelete({ uuids });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.selectedItems = [];
            this.loadhierarchicalCategoryList();
          }
          // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
    // 导出API - 使用选项列表接口
    async handleExportApi() {
      const params = {
          orderItems:'orderNum',
          orderRule:'asc',
        };
      return await labSafetyCategoryLevelExport(params);
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadhierarchicalCategoryList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadhierarchicalCategoryList();
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.hierarchicalCategoryForm.resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.hierarchical-category {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
