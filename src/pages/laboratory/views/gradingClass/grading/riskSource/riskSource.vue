<template>
  <div class="risk-source">
    <el-tabs v-model="activeName" type="card" >
            <el-tab-pane label="风险源分类" name="ClassificationRiskSources"><ClassificationRiskSources v-if="activeName === 'ClassificationRiskSources'"/></el-tab-pane>
            <el-tab-pane label="风险源" name="riskSources"><riskSources v-if="activeName === 'riskSources'"/></el-tab-pane>
          </el-tabs>
  </div>
</template>

<script>
import ClassificationRiskSources from './riskSourceCom/Classification-riskSources.vue'
import riskSources from './riskSourceCom/risk-source.vue'
export default {
  name: 'RiskSource',
  components:{
    ClassificationRiskSources,
    riskSources
  },
  data() {
    return {
      // 页面数据
       activeName:'ClassificationRiskSources'
    }
  },
  mounted() {
    // 页面初始化
  },
  methods: {
    // 页面方法
  }
}
</script>

<style scoped>
.risk-source {
  padding: 20px;
}
</style>