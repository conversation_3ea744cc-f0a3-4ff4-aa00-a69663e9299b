<template>
  <div class="Classification-riskSources">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="风险源分类名称：">
          <el-input
            v-model="searchForm.hazardCategoryName"

            placeholder="请输入风险源分类名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"

            icon="el-icon-search"
            @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="handleReset"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button
          type="warning"

          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <!-- <el-button
          type="danger"

          icon="el-icon-delete"
          @click="handleBatchDelete"
          :disabled="selectedItems.length === 0"
        >
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <import-export-buttons
          exportFilePrefix="风险源分类列表"
          :show-import="false"
          :show-template="false"
          :exportApi="handleExportApi"
        />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="categoryTable"
        :loading="loading"
        :table-data="riskSourcesClassList"
        :columns="columns"
        :pagination="paginationConfig"
        :selection="true"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
     <!-- 操作列 -->
      <template #operation="{ row }">
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-button type="text" style="color: #D2585D" @click="handleDelete(row)">删除</el-button>
      </template>
    </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @closed="handleDialogClosed"
    >
      <el-form
        ref="riskSourcesClassForm"
        :model="riskSourcesClassForm"
        :rules="rules"
        label-width="100px"

      >
        <el-form-item label="风险源类型" prop="hazardCategoryName">
          <el-input
            v-model="riskSourcesClassForm.hazardCategoryName"
            style="width: 55%"
            placeholder="请输入风险源类型"
          ></el-input>
        </el-form-item>

        <el-form-item label="排序" prop="orderNum">
          <el-input-number
            v-model="riskSourcesClassForm.orderNum"
            :min="0"
            :max="9999"
            placeholder="请输入排序数字"
            style="width: 30%"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import {
  labSafetyHazardCategoryList,
  labSafetyHazardCategorySave,
  labSafetyHazardCategoryUpdate,
  labSafetyHazardCategoryDelete,
  labSafetyHazardCategoryExport
} from "@laboratory/api/gradingClass/grading";
export default {
  name: "Classification-riskSources",
  components: {
    ImportExportButtons,
    TablePagetion,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增风险源分类",
      selectedItems: [],
      riskSourcesClassList: [],
      searchForm: {
        hazardCategoryName: "",
      },
      riskSourcesClassForm: {
        uuid: "",
        hazardCategoryName: "",
        orderNum: 0,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        hazardCategoryName: [{ required: true, message: "请输入风险源分类名称", trigger: "blur" }],
        orderNum: [{ required: true, message: "请输入排序数字", trigger: "blur" }],
      },
      columns: [
        {
          prop: "orderNum",
          label: "排序",
          minWidth: 100,
        },
        {
          prop: "hazardCategoryName",
          label: "风险源分类名称",
          minWidth: 200,
        },
        {
          prop: "operation",
          label: "操作",
          width: 150,
          fixed: "right",
         slotName: "operation",
        },
      ],
    };
  },
  mounted() {
    // 页面初始化
    this.loadriskSourcesClassList()
  },
  methods: {
     // 页面方法
    async loadriskSourcesClassList() {
       this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          orderItems:'orderNum',
          orderRule:'asc',
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        };
        const response = await labSafetyHazardCategoryList(params);
        if (response.code === 0) {
          this.riskSourcesClassList = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
        // eslint-disable-next-line no-empty
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadriskSourcesClassList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        hazardCategoryName: "",
      };
      this.paginationConfig.currentPage = 1;
      this.loadriskSourcesClassList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增风险源分类";
      this.riskSourcesClassForm = {
        uuid: "",
        hazardCategoryName: "",
        orderNum:0
      };
      this.dialogVisible = true;
    },
    //提交表单
    handleSubmit(){
       this.$refs.riskSourcesClassForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            const isEdit = !!this.riskSourcesClassForm.uuid;
            const submitData = {
              ...this.riskSourcesClassForm,
            };
            const api = isEdit
              ? labSafetyHazardCategoryUpdate
              : labSafetyHazardCategorySave;
            const response = await api(submitData);

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功");
              this.dialogVisible = false;
              this.loadriskSourcesClassList();
            }
          // eslint-disable-next-line no-empty
          } catch (error) {
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedItems.length} 条数据吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const uuids = this.selectedItems.map((item) => item.uuid);
          const response = await labSafetyHazardCategoryDelete({ uuids });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.selectedItems = [];
            this.loadriskSourcesClassList();
          }
        // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
     // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑风险源分类";
      this.riskSourcesClassForm = {
        uuid: row.uuid,
        hazardCategoryName: row.hazardCategoryName,
        orderNum: row.orderNum,
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该风险源分类吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetyHazardCategoryDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.loadriskSourcesClassList();
          }
        // eslint-disable-next-line no-empty
        } catch (error) {}
      });
    },
    // 导出API - 使用选项列表接口
    async handleExportApi() {
      const params = {
          orderItems:'orderNum',
          orderRule:'asc',
        };
      return await labSafetyHazardCategoryExport(params);
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadriskSourcesClassList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadriskSourcesClassList();
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.riskSourcesClassForm.resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.Classification-riskSources {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
