<template>
  <div class="lab-category-form">
    <div class="form-section">
      <div class="section-title">
        请选择实验室分类 (
        <span style="color: #0052D9;">{{ selectedCategory ? 1 : 0 }}</span>
        / 1)
      </div>
      <div class="category-options" v-loading="loading">
        <el-radio v-for="item in categoryOptions" :key="item.categoryId" v-model="selectedCategory" :label="item.categoryId">
          {{ item.categoryName }}
        </el-radio>
        <el-empty v-if="!loading && categoryOptions.length === 0" description="暂无分类选项"></el-empty>
      </div>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="handleSave" :loading="saveLoading" :disabled="!selectedCategory">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { labSafetyCategoryOptions } from "@laboratory/api/gradingClass/category";
import { labSafetyCategoryRateSave, labSafetyLabDetail } from "@laboratory/api/gradingClass/classifyLaboratory";

export default {
  name: "LabCategoryForm",
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      categoryOptions: [],
      selectedCategory: "",
    };
  },
  created() {
    this.fetchAllData();
  },
  methods: {
    // 并行获取所有数据
    async fetchAllData() {
      this.loading = true;

      try {
        // 使用 Promise.all 并行调用两个接口
        const [optionsRes, currentRes] = await Promise.all([
          labSafetyCategoryOptions(),
          labSafetyLabDetail({ uuid: this.labUuid })
        ]);

        // 处理分类选项数据
        if (optionsRes.code === 0) {
          this.categoryOptions = optionsRes.data || [];
        }

        // 处理当前分类数据并进行有效性检查
        if (currentRes.code === 0 && currentRes.data) {
          const currentCategoryId = currentRes.data.categoryId || "";

          // 检查当前分类是否在选项列表中存在
          const categoryExists = this.categoryOptions.some(option => option.categoryId === currentCategoryId);

          // 如果存在则回显，否则赋空
          this.selectedCategory = categoryExists ? currentCategoryId : "";
        }
      } catch (error) {
        console.error('获取分类数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 单独获取分类选项（保留用于其他场景）
    fetchCategoryOptions() {
      this.loading = true;
      labSafetyCategoryOptions()
        .then((res) => {
          if (res.code === 0) {
            this.categoryOptions = res.data || [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 单独获取当前实验室的分类信息（保留用于其他场景）
    fetchCurrentCategory() {
      labSafetyLabDetail({ uuid: this.labUuid }).then((res) => {
        if (res.code === 0 && res.data) {
          const currentCategoryId = res.data.categoryId || "";

          // 检查当前分类是否在选项列表中存在
          const categoryExists = this.categoryOptions.some(option => option.categoryId === currentCategoryId);

          // 如果存在则回显，否则赋空
          this.selectedCategory = categoryExists ? currentCategoryId : "";
        }
      });
    },

    // 分类选择变化
    handleCategoryChange(value) {
      console.log("选择的分类:", value);
    },

    // 保存分类
    handleSave() {
      if (!this.selectedCategory) {
        this.$message.warning("请选择实验室分类");
        return;
      }

      this.saveLoading = true;
      const data = {
        labUuid: this.labUuid,
        categoryId: this.selectedCategory,
      };

      labSafetyCategoryRateSave(data)
        .then((res) => {
          if (res.code === 0) {
            this.$emit("save-success");
          }
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-category-form {
  position: relative;
  .form-section {
    margin-bottom: 30px;
    position: relative;
    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #999999;
      margin-bottom: 20px;

      &::before {
        content: "*";
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .category-options {
      .radio-item {
        margin-bottom: 16px;
        padding: 16px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          border-color: #2050d1;
          background-color: #f0f9ff;
        }

        ::v-deep .el-radio {
          width: 100%;

          .el-radio__label {
            width: 100%;
            padding-left: 10px;
          }
        }

        .radio-content {
          .category-name {
            font-size: 14px;
            font-weight: 500;
            color: #333333;
            margin-bottom: 4px;
          }

          .category-desc {
            font-size: 12px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }

      ::v-deep .el-radio.is-checked {
        .radio-item {
          border-color: #2050d1;
          background-color: #f0f9ff;
        }
      }
    }
  }

  .form-actions {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
