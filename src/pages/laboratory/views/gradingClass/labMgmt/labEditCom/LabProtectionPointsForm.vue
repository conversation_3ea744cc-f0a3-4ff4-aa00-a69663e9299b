<template>
  <div class="lab-protection-points-form">
    <div class="protection-content">
      <div class="protection-options" v-loading="loading">
        <div v-for="group in groupedProtectionPoints" :key="group.pointType" class="protection-group">
          <div class="group-header">
            <i class="iconfont icon-layers"></i>
            <span class="group-name">{{ group.name }}</span>
          </div>
          <el-checkbox-group class="group-items" v-model="selectedProtectionPoints">
            <el-checkbox v-for="item in group.items" :key="item.protectionPointId" class="checkbox-item"
              :label="item.protectionPointId" :value="item.protectionPointId">
              <div class="item-content">
                <div class="item-name">{{ item.protectionPointName }}</div>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>

        <el-empty v-if="!loading && groupedProtectionPoints.length === 0" description="暂无防护要点数据"></el-empty>
      </div>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="handleSave" :loading="saveLoading">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { labSafetyProtectionPointOptionsList } from "@laboratory/api/gradingClass/protectionPoints";
import { labProtectionPointSave, labProtectionPointList } from "@laboratory/api/gradingClass/classifyLaboratory";
import { filterArrayByField } from "@laboratory/utils/dataFilter";

export default {
  name: "LabProtectionPointsForm",
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      protectionPointOptions: [],
      selectedProtectionPoints: [],
    };
  },
  computed: {
    // 按防护类型对防护要点进行分组
    groupedProtectionPoints() {
      if (!this.protectionPointOptions || this.protectionPointOptions.length === 0) {
        return [];
      }

      const groups = {};
      const typeMapping = {
        "personal_protection": "个人防护",
        "fire_extinguishing": "灭火要点",
        "hazard_source": "危险源",
      };

      this.protectionPointOptions.forEach((item) => {
        const pointType = item.pointType || "其他";
        const typeName = typeMapping[pointType] || pointType;

        if (!groups[pointType]) {
          groups[pointType] = {
            pointType,
            name: typeName,
            items: [],
          };
        }

        groups[pointType].items.push(item);
      });

      return Object.values(groups);
    },
  },
  created() {
    this.fetchAllData();
  },
  methods: {
    // 并行获取所有数据
    async fetchAllData() {
      this.loading = true;

      try {
        // 使用 Promise.all 并行调用两个接口
        const [optionsRes, currentRes] = await Promise.all([
          labSafetyProtectionPointOptionsList(),
          labProtectionPointList({ uuid: this.labUuid })
        ]);

        // 处理防护要点选项数据
        if (optionsRes.code === 0) {
          this.protectionPointOptions = optionsRes.data || [];
        }

        // 处理当前防护要点数据并进行筛选
        if (currentRes.code === 0 && currentRes.data) {
          // 筛选出在protectionPointOptions中存在的防护要点
          const filteredData = filterArrayByField(currentRes.data, this.protectionPointOptions, 'protectionPointId');
          this.selectedProtectionPoints = filteredData.map((item) => item.protectionPointId);
        }
      } catch (error) {
        console.error('获取防护要点数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 单独获取防护要点选项（保留用于其他场景）
    fetchProtectionPointOptions() {
      this.loading = true;
      labSafetyProtectionPointOptionsList()
        .then((res) => {
          if (res.code === 0) {
            this.protectionPointOptions = res.data || [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 单独获取当前实验室的防护要点数据（保留用于其他场景）
    fetchCurrentProtectionPoints() {
      labProtectionPointList({ uuid: this.labUuid }).then((res) => {
        if (res.code === 0 && res.data) {
          // 筛选出在protectionPointOptions中存在的防护要点
          const filteredData = filterArrayByField(res.data, this.protectionPointOptions, 'protectionPointId');
          this.selectedProtectionPoints = filteredData.map((item) => item.protectionPointId);
        }
      });
    },

    // 保存防护要点
    handleSave() {
      if (this.selectedProtectionPoints.length === 0) {
        this.$message.warning("请勾选防护要点");
        return;
      }

      this.saveLoading = true;
      const data = {
        uuid: this.labUuid,
        protectionPointId: this.selectedProtectionPoints,
      };

      labProtectionPointSave(data)
        .then((res) => {
          if (res.code === 0) {
            this.$emit("save-success");
          }
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-protection-points-form {
  position: relative;
  width: 100%;
  .protection-content {
    .protection-options {
      .protection-group {
        margin-bottom: 24px;

        .group-header {
            padding: 12px 0;
            i {
              color: #2050D1;
            }
            .group-name {
              font-size: 14px;
              font-weight: 500;
              color: #333333;
              margin-left: 8px;
            }
          }

        .group-items {
          width: 100%;
          display: grid;
          grid-template-columns: repeat(auto-fill, 400px);
          gap: 16px;
          color: #333333;

          .checkbox-item {
            transition: all 0.3s;

            .item-content {
              font-size: 14px;

              .item-name {
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }
  }

  .form-actions {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
