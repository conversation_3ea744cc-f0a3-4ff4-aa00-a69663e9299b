<template>
  <div class="lab-risk-points-form">
    <!-- 切换按钮 -->
    <div class="risk-btn-tabs">
      <el-button :class="['risk-tab-btn', { active: activeTab === 'safetyGrading' }]" @click="activeTab = 'safetyGrading'">
        安全分级
      </el-button>
      <el-button :class="['risk-tab-btn', { active: activeTab === 'riskAssessment' }]" @click="activeTab = 'riskAssessment'">
        安全风险评分
      </el-button>
    </div>

    <div class="risk-tab-content">
      <!-- 安全分级 -->
      <div v-if="activeTab === 'safetyGrading'" class="safety-grading-content">
        <div class="grading-options" v-loading="gradingLoading">
          <div v-for="group in groupedCategoryLevels" :key="group.scoreLevelId" class="grading-group">
            <div class="group-header">
              <div class="level-info">
                <i class="iconfont icon-anquandengji" :style="{ color: group.displayColors }">
                  <span class="level-value">{{ group.displayValue }}</span>
                </i>
                <span class="level-name">{{ group.scoreLevelName }}</span>
              </div>
            </div>
            <el-checkbox-group class="group-items" v-model="selectedCategoryLevels">
              <el-checkbox
                v-for="item in group.items"
                :key="item.categoryLevelId"
                class="checkbox-item"
                :label="item.categoryLevelId"
                :value="item.categoryLevelId"
              >
                <div class="item-content">
                  <div class="item-name">{{ item.categoryLevelBasis }}</div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <el-empty v-if="!gradingLoading && groupedCategoryLevels.length === 0" description="暂无安全分级数据"></el-empty>
        </div>
      </div>

      <!-- 安全风险评分 -->
      <div v-else class="risk-assessment-content">
        <div class="assessment-options" v-loading="assessmentLoading">
          <div class="total-score-opt">
            总分：
            <span class="score-value-opt">{{ totalScore }}</span>
            <span class="score-unit">分</span>
          </div>

          <!-- "以下都不满足"复选框 -->
          <div class="none-applicable-section">
            <el-checkbox v-model="noneApplicable" @change="handleNoneApplicableChange">
              以下都不满足
            </el-checkbox>
          </div>
          <div v-for="group in groupedHazardCategories" :key="group.hazardCategoryId" class="assessment-group">
            <div class="group-header">
              <i class="iconfont icon-sheshileixing" style="color: #0052D9;"></i>
              <span class="category-name">{{ group.hazardCategoryName }}</span>
            </div>
            <el-checkbox-group class="group-items" v-model="selectedHazards" :disabled="noneApplicable">
              <el-checkbox
                v-for="item in group.items"
                :key="item.hazardId"
                class="checkbox-item"
                :label="item.hazardId"
                :value="item.hazardId"
              >
                <div class="item-content">
                  <div class="item-name">
                    <span class="score-value">( {{ item.hazardScore }} )</span>
                    {{ item.hazardName }}
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <el-empty v-if="!assessmentLoading && groupedHazardCategories.length === 0" description="暂无风险评分数据"></el-empty>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="handleSave" :loading="saveLoading">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { labSafetyCategoryLevelOptionsList, labSafetyHazardOptionsList } from "@laboratory/api/gradingClass/grading";
import { labSafetyAssessmentSave, labSafetyCurrentLevelDetail } from "@laboratory/api/gradingClass/classifyLaboratory";
import { filterArrayByField } from "@laboratory/utils/dataFilter";

export default {
  name: "LabRiskPointsForm",
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      activeTab: "safetyGrading",
      gradingLoading: false,
      assessmentLoading: false,
      saveLoading: false,
      categoryLevelOptions: [],
      hazardCategoryOptions: [],
      selectedCategoryLevels: [],
      selectedHazards: [],
      noneApplicable: false, // "以下都不满足"复选框状态
    };
  },
  computed: {
    // 按分级ID对安全分级数据进行分组
    groupedCategoryLevels() {
      if (!this.categoryLevelOptions || this.categoryLevelOptions.length === 0) {
        return [];
      }

      const groups = {};
      this.categoryLevelOptions.forEach((item) => {
        const scoreLevelId = item.scoreLevelId;

        if (!groups[scoreLevelId]) {
          groups[scoreLevelId] = {
            scoreLevelId,
            scoreLevelName: item.scoreLevelName,
            displayValue: item.displayValue,
            displayColors: item.displayColors,
            scoreLevelOrderNum: item.scoreLevelOrderNum || 0,
            items: [],
          };
        }

        groups[scoreLevelId].items.push(item);
      });

      return Object.values(groups);
    },

    // 按分类ID对风险点数据进行分组
    groupedHazardCategories() {
      if (!this.hazardCategoryOptions || this.hazardCategoryOptions.length === 0) {
        return [];
      }

      const groups = {};
      this.hazardCategoryOptions.forEach((item) => {
        const categoryId = item.hazardCategoryId;

        if (!groups[categoryId]) {
          groups[categoryId] = {
            hazardCategoryId: categoryId,
            hazardCategoryName: item.hazardCategoryName || "未分类",
            categoryOrderNum: item.categoryOrderNum || 0,
            items: [],
          };
        }

        groups[categoryId].items.push(item);
      });
      return Object.values(groups);
    },

    // 计算当前安全风险评分已勾选的数值总分
    totalScore() {
      if (!this.selectedHazards.length || !this.hazardCategoryOptions.length) {
        return 0;
      }

      let total = 0;
      this.selectedHazards.forEach((hazardId) => {
        const hazard = this.hazardCategoryOptions.find((item) => item.hazardId === hazardId);
        if (hazard && hazard.hazardScore) {
          total += hazard.hazardScore;
        }
      });

      return total;
    },
  },
  created() {
    this.fetchAllData();
  },
  watch: {
    // 监听选中的风险点变化，如果有选中项则取消"以下都不满足"
    selectedHazards(newVal) {
      if (newVal.length > 0 && this.noneApplicable) {
        this.noneApplicable = false;
      }
    },
  },
  methods: {
    // 并行获取所有数据
    async fetchAllData() {
      this.gradingLoading = true;
      this.assessmentLoading = true;

      try {
        // 使用 Promise.all 并行调用三个接口
        const [categoryRes, hazardRes, currentRes] = await Promise.all([
          labSafetyCategoryLevelOptionsList(),
          labSafetyHazardOptionsList(),
          labSafetyCurrentLevelDetail({ uuid: this.labUuid }),
        ]);

        // 处理安全分级选项数据
        if (categoryRes.code === 0) {
          this.categoryLevelOptions = categoryRes.data || [];
        }

        // 处理风险评分选项数据
        if (hazardRes.code === 0) {
          this.hazardCategoryOptions = hazardRes.data || [];
        }

        // 处理当前风险点数据并进行筛选
        if (currentRes.code === 0 && currentRes.data) {
          // 回显安全分级选择（筛选出在categoryLevelOptions中存在的）
          if (currentRes.data.safetyCategoryLevelJson) {
            const filteredCategoryLevels = filterArrayByField(
              currentRes.data.safetyCategoryLevelJson,
              this.categoryLevelOptions,
              "categoryLevelId",
            );
            this.selectedCategoryLevels = filteredCategoryLevels.map((item) => item.categoryLevelId);
          }

          // 回显风险评分选择（筛选出在hazardCategoryOptions中存在的）
          if (currentRes.data.hazardJson) {
            const filteredHazards = filterArrayByField(currentRes.data.hazardJson, this.hazardCategoryOptions, "hazardId");
            this.selectedHazards = filteredHazards.map((item) => item.hazardId);
          }
        }
      } catch (error) {
        console.error("获取风险点数据失败:", error);
      } finally {
        this.gradingLoading = false;
        this.assessmentLoading = false;
      }
    },

    // 单独获取安全分级选项（保留用于其他场景）
    fetchCategoryLevelOptions() {
      this.gradingLoading = true;
      labSafetyCategoryLevelOptionsList()
        .then((res) => {
          if (res.code === 0) {
            this.categoryLevelOptions = res.data || [];
          }
        })
        .finally(() => {
          this.gradingLoading = false;
        });
    },

    // 单独获取风险评分选项（保留用于其他场景）
    fetchHazardCategoryOptions() {
      this.assessmentLoading = true;
      labSafetyHazardOptionsList()
        .then((res) => {
          if (res.code === 0) {
            this.hazardCategoryOptions = res.data || [];
          }
        })
        .finally(() => {
          this.assessmentLoading = false;
        });
    },

    // 单独获取当前实验室的风险点数据（保留用于其他场景）
    fetchCurrentRiskPoints() {
      labSafetyCurrentLevelDetail({ uuid: this.labUuid }).then((res) => {
        if (res.code === 0 && res.data) {
          // 回显安全分级选择（筛选出在categoryLevelOptions中存在的）
          if (res.data.safetyCategoryLevelJson) {
            const filteredCategoryLevels = filterArrayByField(
              res.data.safetyCategoryLevelJson,
              this.categoryLevelOptions,
              "categoryLevelId",
            );
            this.selectedCategoryLevels = filteredCategoryLevels.map((item) => item.categoryLevelId);
          }

          // 回显风险评分选择（筛选出在hazardCategoryOptions中存在的）
          if (res.data.hazardJson) {
            const filteredHazards = filterArrayByField(res.data.hazardJson, this.hazardCategoryOptions, "hazardId");
            this.selectedHazards = filteredHazards.map((item) => item.hazardId);
          }
        }
      });
    },

    // 处理"以下都不满足"复选框变化
    handleNoneApplicableChange(checked) {
      if (checked) {
        // 如果勾选"以下都不满足"，清空所有已选中的风险点
        this.selectedHazards = [];
      }
    },

    // 保存风险点
    handleSave() {
      // 检查安全分级/安全风险评分至少选其1
      if (this.selectedCategoryLevels.length === 0 && this.selectedHazards.length === 0) {
        this.$message.warning("请勾选安全分级或安全风险评分");
        return;
      }

      this.saveLoading = true;
      const data = {
        uuid: this.labUuid,
        categoryLevelIds: this.selectedCategoryLevels,
        hazardIds: this.selectedHazards,
      };

      labSafetyAssessmentSave(data)
        .then((res) => {
          if (res.code === 0) {
            this.$emit("save-success");
          }
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-risk-points-form {
  position: relative;
  width: 100%;
  .risk-btn-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    .risk-tab-btn {
      background: #e5e6eb;
      color: #1d2129;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      min-width: 90px;

      &.active {
        background: #2050d1;
        color: #fff;
      }
    }
  }

  .risk-tab-content {
    // 总分样式
    .total-score-opt {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 18px;

      .score-value-opt {
        color: #d2585d;
        font-style: italic;
        font-size: 20px;
        font-weight: bold;
        margin: 0 2px 0 6px;
        text-decoration: underline;
      }

      .score-unit {
        color: #d2585d;
        font-style: italic;
        font-size: 16px;
      }
    }

    .none-applicable-section {
      margin-bottom: 15px;
    }
    .grading-group,
    .assessment-group {
      margin-bottom: 24px;

      .group-header {
        padding: 12px 0;

        .level-info {
          display: flex;
          align-items: center;
          gap: 8px;
          position: relative;
          .iconfont {
            font-size: 20px;
            position: relative;
          }

          .level-value {
            font-size: 13px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            color: #333333;
          }

          .level-name {
            font-size: 14px;
            font-weight: 500;
            color: #333333;
          }
        }

        .category-name {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          margin-left: 8px;
        }
      }

      .group-items {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(auto-fill, 400px);
        gap: 16px;
        color: #333333;
        .checkbox-item {
          transition: all 0.3s;
          .item-content {
            font-size: 14px;
            .item-name {
              margin-bottom: 4px;
            }
            .score-value {
              color: #d2585d;
            }
          }
        }
      }
    }
  }

  .form-actions {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
