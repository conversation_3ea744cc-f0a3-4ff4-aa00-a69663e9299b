<template>
  <div class="lab-safety-signs-form">
    <div class="form-section">
      <!-- <div class="section-title">请选择警示标识</div> -->
      <div class="safety-signs-options" v-loading="loading">
        <div v-for="group in groupedSafetySigns" :key="group.categoryId" class="signs-group">
          <div class="group-header">
            <span class="group-name">{{ group.categoryName }}</span>
          </div>
          <div class="sign-grid">
            <div
              v-for="item in group.items"
              :key="item.signId"
              class="sign-item"
              :class="{ 'selected': selectedSafetySigns.includes(item.signId) }"
              @click="handleSignToggle(item.signId)"
            >
              <div class="sign-image">
                <el-image :src="imageSrc + item.signImagePath" fit="cover">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div class="sign-name ellipsis">{{ item.signName }}</div>
              <div class="sign-check" v-if="selectedSafetySigns.includes(item.signId)">
                <i class="el-icon-check"></i>
              </div>
            </div>
          </div>
        </div>

        <el-empty v-if="!loading && groupedSafetySigns.length === 0" description="暂无警示标识选项"></el-empty>
      </div>
    </div>

    <div class="form-actions">
      <el-button type="primary" @click="handleSave" :loading="saveLoading">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { labSafetySignOptionsList } from "@laboratory/api/gradingClass/safetySign";
import { labWarningSignSave, labSafetyWarningSignList } from "@laboratory/api/gradingClass/classifyLaboratory";
import { filterArrayByField } from "@laboratory/utils/dataFilter";

export default {
  name: "LabSafetySignsForm",
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      safetySignOptions: [],
      selectedSafetySigns: [],
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    // 按分类对警示标识进行分组
    groupedSafetySigns() {
      if (!this.safetySignOptions || this.safetySignOptions.length === 0) {
        return [];
      }

      const groups = {};
      this.safetySignOptions.forEach((item) => {
        const categoryId = item.categoryId || "other";
        const categoryName = item.categoryName || "其他";

        if (!groups[categoryId]) {
          groups[categoryId] = {
            categoryId,
            categoryName,
            items: [],
          };
        }

        groups[categoryId].items.push(item);
      });

      return Object.values(groups);
    },
  },
  created() {
    this.fetchAllData();
  },
  methods: {
    // 并行获取所有数据
    async fetchAllData() {
      this.loading = true;

      try {
        // 使用 Promise.all 并行调用两个接口
        const [optionsRes, currentRes] = await Promise.all([
          labSafetySignOptionsList(),
          labSafetyWarningSignList({ uuid: this.labUuid })
        ]);

        // 处理警示标识选项数据
        if (optionsRes.code === 0) {
          this.safetySignOptions = optionsRes.data || [];
        }

        // 处理当前警示标识数据并进行筛选
        if (currentRes.code === 0 && currentRes.data) {
          // 筛选出在safetySignOptions中存在的警示标识
          const filteredData = filterArrayByField(currentRes.data, this.safetySignOptions, 'signId');
          this.selectedSafetySigns = filteredData.map((item) => item.signId);
        }
      } catch (error) {
        console.error('获取警示标识数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 单独获取警示标识选项（保留用于其他场景）
    fetchSafetySignOptions() {
      this.loading = true;
      labSafetySignOptionsList()
        .then((res) => {
          if (res.code === 0) {
            this.safetySignOptions = res.data || [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 单独获取当前实验室的警示标识（保留用于其他场景）
    fetchCurrentSafetySigns() {
      labSafetyWarningSignList({ uuid: this.labUuid }).then((res) => {
        if (res.code === 0 && res.data) {
          // 筛选出在safetySignOptions中存在的警示标识
          const filteredData = filterArrayByField(res.data, this.safetySignOptions, 'signId');
          this.selectedSafetySigns = filteredData.map((item) => item.signId);
        }
      });
    },

    // 保存警示标识
    handleSave() {
      this.saveLoading = true;
      const data = {
        uuid: this.labUuid,
        signId: this.selectedSafetySigns,
      };

      labWarningSignSave(data)
        .then((res) => {
          if (res.code === 0) {
            this.$emit("save-success");
          }
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },

    // 切换标识选择状态
    handleSignToggle(signId) {
      const index = this.selectedSafetySigns.indexOf(signId);
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedSafetySigns.splice(index, 1);
      } else {
        // 如果未选中，则添加选择
        this.selectedSafetySigns.push(signId);
      }
    },

    // 获取图片完整URL
    getImageUrl(path) {
      if (!path) return "";
      if (path.startsWith("http")) return path;
      return this.imageSrc + path;
    },
  },
};
</script>

<style lang="scss" scoped>
.lab-safety-signs-form {
  position: relative;
  width: 100%;
  .form-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 20px;
    }

    .safety-signs-options {
      min-height: 200px;

      .signs-group {
        margin-bottom: 10px;

        .group-header {
          padding: 12px 16px;
          border-radius: 4px;
          .group-name {
            font-size: 14px;
            font-weight: 500;
            color: #333333;
          }
        }

        .sign-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 15px;
          max-height: 400px;
          overflow-y: auto;

          .sign-item {
            position: relative;
            border: 2px solid #e4e7ed;
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;

            &:hover {
              border-color: #c0c4cc;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            &.selected {
              border-color: #2050d1;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
            }

            .sign-image {
              width: 80px;
              height: 80px;
              margin: 0 auto 8px;
              border-radius: 4px;
              overflow: hidden;

              .el-image {
                width: 100%;
                height: 100%;
              }

              .image-slot {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                background: #f5f7fa;
                color: #909399;
                font-size: 24px;
              }
            }

            .sign-name {
              text-align: center;
              font-size: 14px;
              color: #333333;
              line-height: 1.4;
            }

            .sign-check {
              position: absolute;
              top: 5px;
              right: 5px;
              width: 20px;
              height: 20px;
              background: #2050d1;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
  .form-actions {
    position: absolute;
    right: 0;
    top: 0;
  }
}
</style>
