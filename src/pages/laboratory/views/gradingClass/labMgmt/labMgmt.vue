<template>
  <div class="labMgmt">
    <div class="tree" :class="{ 'tree-disabled': centerUuid }">
      <dept-tree ref="deptTree" @node-click="handleTreeNodeClick" />
    </div>
    <labs-list
      class="labs-list"
      ref="labsList"
      mode="manager"
      :searchParams="searchParams"
      :centerUuid="centerUuid"
      @reset="handleReset"
      @editLab="handleEditLab"
      @viewLab="handleViewLab"
      @safetyCard="handleSafetyCard"
    ></labs-list>
  </div>
</template>

<script>
import LabsList from "./labMgmtCom/MgmtLabsList.vue";
import DeptTree from "./labMgmtCom/MgmtDeptTree.vue";

export default {
  name: "labMgmt",
  components: {
    LabsList,
    DeptTree,
  },
  data() {
    return {
      searchParams: {}, // 传递给LabsList的搜索参数
      centerUuid: "", // 实验中心UUID（从路由参数获取）
    };
  },
  created() {
    // 获取路由参数中的实验中心UUID
    this.centerUuid = this.$route.params.centerUuid || "";

    // 如果有实验中心UUID，初始化搜索参数
    if (this.centerUuid) {
      this.searchParams = {
        centerUuid: this.centerUuid,
      };
    }
  },
  methods: {
    // 处理来自树组件的节点点击事件
    handleTreeNodeClick(params) {
      // 如果是从实验中心进入的，禁止树的点击事件
      if (this.centerUuid) {
        return;
      }

      // 更新搜索参数并触发LabsList搜索，确保永远携带centerUuid
      this.searchParams = {
        ...params,
        // 如果有实验中心UUID，永远携带它
        ...(this.centerUuid ? { centerUuid: this.centerUuid } : {}),
      };

      // 通知LabsList组件进行搜索
      if (this.$refs.labsList && this.$refs.labsList.handleTreeSearch) {
        this.$refs.labsList.handleTreeSearch(this.searchParams);
      }
    },
    handleReset () {
      this.$refs.deptTree.$refs.deptTree.setCurrentKey(null)
    },
    // 处理实验室编辑事件
    handleEditLab(item) {
      // 根据是否从实验中心进入来决定路由名称
      const routeName = this.centerUuid ? "centerMgmtLabEdit" : "mgmtLabEdit";

      this.$router.push({
        name: routeName,
        params: {
          uuid: item.uuid,
          ...(this.centerUuid ? { centerUuid: this.centerUuid } : {}),
        },
        query: { from: "list" },
      });
    },

    // 查看实验室详情
    handleViewLab(item) {
      // 根据是否从实验中心进入来决定路由名称
      const routeName = this.centerUuid ? "centerMgmtLabDetail" : "mgmtLabDetail";

      this.$router.push({
        name: routeName,
        params: {
          uuid: item.uuid,
          ...(this.centerUuid ? { centerUuid: this.centerUuid } : {}),
        },
      });
    },
    handleSafetyCard(item) {
      // 安全信息牌功能
      this.$message.info("功能灰度测试中");
    },
  },
};
</script>
<style lang="scss" scoped>
.labMgmt {
  width: 100%;
  display: flex;
  background-color: #f8f8f8;
  height: calc(100vh - 265px);
  overflow-y: hidden;
}

.tree {
  // 树的禁用状态样式
  &.tree-disabled {
    pointer-events: none; // 禁止所有点击事件
    opacity: 0.6; // 置灰效果

    ::v-deep .el-tree {
      .el-tree-node {
        cursor: not-allowed !important;

        .el-tree-node__content {
          cursor: not-allowed !important;

          &:hover {
            background-color: transparent !important;
          }
        }
      }
    }
  }
}

.labs-list {
  overflow-y: auto;
}
</style>
