<template>
  <div class="dept-tree">
    <div class="tree-header">
      <el-input v-model="filterText" placeholder="请输入内容" clearable prefix-icon="el-icon-search" />
    </div>
    <div class="tree-container">
      <div class="tree-title" @click="btnClickAll()">全部部门</div>
      <el-tree
        ref="deptTree"
        :data="treeData"
        :props="treeProps"
        :default-expand-all="false"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        node-key="nodeKey"
        :highlight-current="true"
        @node-click="handleNodeClick"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <!-- <i :class="getNodeIcon(data)" class="node-icon"></i> -->
          <span class="node-label ">{{ node.label }}</span>
        </span>
      </el-tree>

      <div v-if="loading" class="tree-loading">
        <el-skeleton :rows="3" animated />
      </div>

      <el-empty v-if="!loading && (!treeData || treeData.length === 0)" description="暂无数据" :image-size="80" />
    </div>
  </div>
</template>

<script>
import { deptTree } from "@laboratory/api/laboratory/laboratory";

export default {
  name: "DeptTree",
  data() {
    return {
      loading: false,
      treeData: [],
      filterText: "",
      treeProps: {
        children: "children",
        label: "name",
        value: "nodeKey",
      },
    };
  },
  watch: {
    filterText(val) {
      this.$refs.deptTree.filter(val);
    },
  },
  created() {
    this.fetchDeptTree();
  },
  methods: {
    // 获取部门中心树数据
    fetchDeptTree() {
      this.loading = true;
      deptTree()
        .then((res) => {
          if (res.code === 0) {
            this.treeData = res.data || [];
          } else {
            this.$message.error(res.message || "获取组织架构失败");
            this.treeData = [];
          }
        })
        .catch((error) => {
          console.error("获取组织架构失败:", error);
          this.$message.error("获取组织架构失败");
          this.treeData = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    btnClickAll () {
      // 去除tree的高亮状态
      this.$refs.deptTree.setCurrentKey(null);
      this.$emit("node-click", {});
    },
    // 处理树节点点击事件
    handleNodeClick(data, node) {
      // 根据节点类型设置不同的搜索参数
      const params = {};

      if (data.type === 1) {
        // 部门节点
        params.deptUuid = data.uuid;
        params.deptName = data.name;
      } else if (data.type === 2) {
        // 实验中心节点
        params.labCenterUuid = data.uuid;
        params.labCenterName = data.name;
      }

      // 发出事件，通知父组件
      this.$emit("node-click", params);
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 根据节点类型获取图标
    // getNodeIcon(data) {
    //   switch (data.type) {
    //     case 1:
    //       return "el-icon-office-building"; // 部门图标
    //     case 2:
    //       return "el-icon-school"; // 实验中心图标
    //     default:
    //       return "el-icon-folder";
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>
.dept-tree {
  width: 230px;
  height: calc(100vh - 260px);
  overflow-x: hidden;
  background: #ffffff;
  box-shadow: 0px 0px 3px 0px rgba(102, 102, 102, 0.15);
  border-radius: 3px;
  margin-right: 6px;
  display: flex;
  flex-direction: column;
  position: relative;
  .tree-header {
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
    position: sticky;
    top: 0;
    left: 0;
    background-color: #ffffff;
    z-index: 9;
  }

  .tree-container {
    width: 100%;
    padding: 10px;
    .tree-title {
      width: 195px;
      height: 36px;
      font-size: 14px;
      color: #606266;
      line-height: 36px;
      padding-left: 24px;
      border-radius: 3px;
      cursor: pointer;
      margin-bottom: 2px;
    }
    .tree-title:hover {
      background: #f5f7fa;
    }
    .el-tree {
      background: transparent;

      ::v-deep .el-tree-node {
        .el-tree-node__content {
          height: 36px;
          padding: 0 10px;
          border-radius: 4px;
          margin-bottom: 2px;

          &:hover {
            background-color: #f5f7fa;
          }

          &.is-current {
            background-color: #ecf5ff;
            color: #2050d1;
          }
        }

        .el-tree-node__expand-icon {
          color: #c0c4cc;

          &.is-leaf {
            color: transparent;
          }
        }
      }
    }

    .custom-tree-node {
      display: flex;
      align-items: center;
      width: 100%;

      .node-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #909399;
      }

      .node-label {
        font-size: 14px;
        color: #606266;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .tree-loading {
      padding: 20px;
    }

    .el-empty {
      padding: 40px 20px;
    }
  }
}
</style>
