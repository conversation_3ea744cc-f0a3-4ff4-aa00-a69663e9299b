<template>
  <layoutContent>
    <div class="search-form" slot="search">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="分级实验室：">
          <el-select v-model="searchForm.scoreLevelId" placeholder="全部" clearable style="width: 180px">
            <el-option :value="''" label="全部" />
            <el-option v-for="item in scoreLevelOptions" :key="item.scoreLevelId" :label="item.scoreLevelName" :value="item.scoreLevelId" />
          </el-select>
        </el-form-item>
        <el-form-item label="实验室名称：">
          <el-input v-model="searchForm.roomLabName" placeholder="请输入实验室名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="实验室管理员：">
          <el-input v-model="searchForm.responsiblePerson" placeholder="请输入管理员姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="实验室安全分类：">
          <el-select v-model="searchForm.categoryId" placeholder="全部" clearable style="width: 180px">
            <el-option :value="''" label="全部" />
            <el-option v-for="item in categoryOptions" :key="item.categoryId" :label="item.categoryName" :value="item.categoryId" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"  icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button  icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 卡片列表区域 -->
    <div class="labs-list">
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <el-empty v-else-if="labsList.length === 0" description="暂无数据"></el-empty>
      <div class="card-container" v-else>
        <lab-center-card
          v-for="(item, index) in labsList"
          :key="item.uuid || index"
          :item="adaptLabDataForCard(item)"
          :image-src="imageSrc"
          :info-items="generateInfoItems(item)"
          :use-custom-info-items="true"
          :viewDeptName="false"
        >
          <!-- :actions="mode === 'manager'" -->
          <!-- 使用具名插槽自定义状态文本 -->
          <template #status-text>
            <div class="card-icon">
              <i class="iconfont icon-jiaobiao" :style="{ color: item.displayColors || '#DCDCDC' }" />
              <div class="text">
                {{ item.displayValue || "暂无" }}
              </div>
            </div>
          </template>
          <template #actions>
            <el-button type="success"  @click="handleSafetyCard(item)">安全信息牌</el-button>
            <!-- assessStatus 评估状态false 未评估则显示分级按钮-->
            <el-button v-if="!item.assessStatus" plain type="warning" @click="handleClassify(item)">分级</el-button>
            <el-button v-else plain type="primary" icon="el-icon-edit" @click="handleEditLab(item)">编辑</el-button>
            <el-button type="primary" @click="handleViewLab(item)">查看详情</el-button>
          </template>
        </lab-center-card>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </layoutContent>
</template>

<script>
import LabCenterCard from "@laboratory/components/LabCenterCard.vue";
import { labSafetyLabList } from "@laboratory/api/gradingClass/classifyLaboratory";
import { labSafetyCategoryOptions } from "@laboratory/api/gradingClass/category";
import { labSafetyScoreLevelOptionsList } from "@laboratory/api/gradingClass/grading";
import layoutContent from "@laboratory/components/layoutContent";

export default {
  name: "LabsList",
  components: {
    LabCenterCard,
    layoutContent,
  },
  props: {
    centerUuid: {
      type: String,
      required: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      labsList: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      searchForm: {
        roomLabName: "",
        categoryId: "",
        responsiblePerson: "",
        scoreLevelId: "",
      },
      // 来自树组件的搜索参数
      treeSearchParams: {},
      innerLoading: false,
      // 分类选项
      categoryOptions: [],
      // 分级实验室选项
      scoreLevelOptions: [],
      // 图片路径前缀
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    isLoading() {
      return this.loading || this.innerLoading;
    },
  },
  created() {
    this.getCategoryOptions();
    this.getScoreLevelOptions();
    this.fetchLabsData();
  },
  methods: {
    // 获取实验室安全分类选项
    getCategoryOptions() {
      labSafetyCategoryOptions().then((res) => {
        if (res.code === 0) {
          this.categoryOptions = res.data || [];
        }
      });
    },

    // 获取安全分级分值选项
    getScoreLevelOptions() {
      labSafetyScoreLevelOptionsList().then((res) => {
        if (res.code === 0) {
          this.scoreLevelOptions = res.data || [];
        }
      });
    },
    // 将实验室数据适配为卡片组件所需的格式
    adaptLabDataForCard(item) {
      const adaptedItem = { ...item };
      adaptedItem.roomKindName = item.roomLabName;
      adaptedItem.roomKindImage = item.roomLabImage || "";
      return adaptedItem;
    },
    // 为每个实验室生成信息项数组
    generateInfoItems(item) {
      return [
        {
          label: "实验室管理员",
          value: item.responsiblePerson || "-",
          icon: "iconfont icon-gerenzhongxin_0",
        },
        {
          label: "实验室分类",
          value: item.categoryName || "-",
          icon: "iconfont icon-bianzu",
        },
        {
          label: "地址",
          value: item.labAddress || "-",
          icon: "iconfont icon-a-bianzu5",
        },
      ];
    },
    // 获取实验中心下的实验室数据
    fetchLabsData() {
      this.innerLoading = true;
      let params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        // orderItems: "gmtCreate",
        // orderRule: "desc",
      };
      if (this.centerUuid) {
        params.labCenterUuid = this.centerUuid;
      }
      if (this.treeSearchParams.deptUuid) {
        params.deptUuid = this.treeSearchParams.deptUuid;
      }
      if (this.treeSearchParams.labCenterUuid) {
        params.labCenterUuid = this.treeSearchParams.labCenterUuid;
      }
      params = {
        ...params,
        ...this.searchForm,
      };
      labSafetyLabList(params)
        .then((res) => {
          if (res.code === 0) {
            // 展开labInfo层级到一级
            this.labsList = (res.data || []).map((item) => ({
              ...item.labInfo,
              // 保留其他可能的字段
              ...item,
            }));
            this.total = res.count || 0;
          }
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },
    // 获取图片完整URL
    getImageUrl(path) {
      if (!path) return "";
      if (path.startsWith("http")) return path;
      return this.imageSrc + path;
    },
    // 获取古罗马数字
    getRomanNumber(level) {
      const romanNumerals = { 1: "I", 2: "II", 3: "III", 4: "IV", 5: "V" };
      return romanNumerals[level] || level;
    },
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchLabsData();
    },
    // 重置搜索条件
    handleReset() {
      this.treeSearchParams = {};
      this.searchForm = {
        roomLabName: "",
        categoryId: "",
        responsiblePerson: "",
        scoreLevelId: "",
      };
      this.currentPage = 1;
      this.fetchLabsData();
      this.$emit('reset')
    },
    // 分页大小变化事件
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchLabsData();
    },
    // 分页页码变化事件
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchLabsData();
    },
    // 分级
    handleClassify(item) {
      // this.$emit("handleClassify", item);
      this.$emit("editLab", item);
    },
    // 安全信息牌
    handleSafetyCard(item) {
      this.$emit("safetyCard", item);
    },
    // 实验室编辑
    handleEditLab(item) {
      this.$emit("editLab", item);
    },
    // 查看实验室详情
    handleViewLab(item) {
      this.$emit("viewLab", item);
    },
    // 处理来自树组件的搜索
    handleTreeSearch(params) {
      this.treeSearchParams = { ...params };
      this.currentPage = 1; // 重置到第一页
      this.fetchLabsData();
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.labs-list {
  position: relative;
  min-height: 400px;

  .loading-container {
    padding: 20px;
  }

  .card-container {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
  }
}

.pagination-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 4px;
}

.card-icon {
  display: flex;
  align-items: center;
  gap: 8px;

  .iconfont {
    left: 6px !important;
    font-size: 24px !important;
  }
  .text {
    font-size: 15px;
    color: #333333 !important;
    margin-top: -12px;
    margin-left: 2px !important;
  }
}
</style>
