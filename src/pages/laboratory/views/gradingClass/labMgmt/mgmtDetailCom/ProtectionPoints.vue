<template>
  <div class="protection-points-container">
    <div v-if="innerLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else class="protection-points-content">
      <grouped-data-display
        :grouped-data="groupedProtectionPoints"
        :loading="false"
        :empty-text="'暂无防护要点信息'"
        :config="{
          categoryTitleField: 'name',
          itemTextField: 'protectionPointName',
          iconFont: 'iconfont icon-layers'
        }"
      />
    </div>
  </div>
</template>

<script>
import { labProtectionPointList } from "@laboratory/api/gradingClass/classifyLaboratory";
import GroupedDataDisplay from "./common/GroupedDataDisplay.vue";

export default {
  name: "ProtectionPoints",
  components: {
    GroupedDataDisplay,
  },
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      innerLoading: false,
      protectionPoints: [],
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    // 按防护类型对防护要点进行分组
    groupedProtectionPoints() {
      if (!this.protectionPoints || this.protectionPoints.length === 0) {
        return [];
      }

      const groups = {};
      const typeMapping = {
        "personal_protection": "个人防护",
        "fire_extinguishing": "灭火要点",
        "hazard_source": "危险源",
      };

      this.protectionPoints.forEach((item) => {
        const pointType = item.pointType || "其他";
        const typeName = typeMapping[pointType] || pointType;

        if (!groups[pointType]) {
          groups[pointType] = {
            type: pointType,
            name: typeName,
            items: [],
          };
        }

        groups[pointType].items.push(item);
      });
      return Object.values(groups);
    },
  },
  created() {
    this.fetchProtectionPoints();
  },
  methods: {
    // 获取防护要点数据
    fetchProtectionPoints() {
      if (!this.labUuid) return;

      this.innerLoading = true;

      labProtectionPointList({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0) {
            this.protectionPoints = res.data || [];
          }
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.protection-points-container {
  .protection-points-content {
    background-color: #fff;
    border-radius: 4px;
    // padding: 20px;
  }
}
</style>
