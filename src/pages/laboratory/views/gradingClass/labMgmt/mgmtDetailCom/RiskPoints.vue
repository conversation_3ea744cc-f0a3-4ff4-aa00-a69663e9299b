<template>
  <div class="risk-points-container">
    <div v-if="innerLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else>
      <!-- 切换按钮 -->
      <div class="risk-btn-tabs">
        <el-button :class="['risk-tab-btn', { active: activeTab === 'safetyGrading' }]" @click="activeTab = 'safetyGrading'">
          安全分级
        </el-button>
        <el-button :class="['risk-tab-btn', { active: activeTab === 'riskAssessment' }]" @click="activeTab = 'riskAssessment'">
          安全风险评分
        </el-button>
      </div>
      <div class="risk-tab-content">
        <div v-if="!safetyLevelData.assessmentStatus" style="display: flex;flex-direction: column;align-items: center;">
          <grouped-data-display :grouped-data="[]" :loading="false" :empty-text="'暂未分级，尽快去分级吧！'" />
          <el-button type="primary" style="margin: auto" @click="handleEdit">
            去分级
          </el-button>
        </div>
        <!-- 安全分级样式 -->
        <div v-else-if="activeTab === 'safetyGrading'">
          <grouped-data-display
            :grouped-data="groupedCategoryLevels"
            :loading="false"
            :empty-text="'暂无安全分级信息'"
            :config="{
              categoryTitleField: 'scoreLevelName',
              itemTextField: 'categoryLevelBasis',
            }"
          />
        </div>
        <!-- 安全风险评分样式 -->
        <div v-else>
          <grouped-data-display
            :grouped-data="groupedRiskPoints"
            :loading="false"
            :show-total-score="true"
            :total-score="safetyLevelData.hazardScore || 0"
            :empty-text="'暂无风险点信息'"
            :show-item-score="true"
            :config="{
              categoryTitleField: 'name',
              itemTextField: 'hazardName',
              itemScoreField: 'hazardScore',
            }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 导入安全分级和风险点相关的API
import { labSafetyCurrentLevelDetail } from "@laboratory/api/gradingClass/classifyLaboratory";
import GroupedDataDisplay from "./common/GroupedDataDisplay.vue";

export default {
  name: "RiskPoints",
  components: {
    GroupedDataDisplay,
  },
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      innerLoading: false,
      safetyCategoryLevels: [],
      activeTab: "safetyGrading", // 默认显示安全分级标签页
      safetyLevelData: {}, // 存储从API获取的安全等级数据
    };
  },
  computed: {
    // 按分级ID对安全分级数据进行分组
    groupedCategoryLevels() {
      if (!this.safetyLevelData || !this.safetyLevelData.safetyCategoryLevelJson) {
        return [];
      }

      const groups = {};

      this.safetyLevelData.safetyCategoryLevelJson.forEach((item) => {
        const scoreLevelId = item.scoreLevelId;

        if (!groups[scoreLevelId]) {
          groups[scoreLevelId] = {
            scoreLevelId,
            scoreLevelName: item.scoreLevelName,
            displayValue: item.displayValue,
            displayColors: item.displayColors,
            items: [],
          };
        }

        groups[scoreLevelId].items.push(item);
      });
      return Object.values(groups);
    },

    // 按分类ID对风险点数据进行分组
    groupedRiskPoints() {
      if (!this.safetyLevelData || !this.safetyLevelData.hazardJson) {
        return [];
      }

      const groups = {};

      this.safetyLevelData.hazardJson.forEach((item) => {
        const categoryId = item.hazardCategoryId;
        const categoryName = item.hazardCategoryName || "未分类";

        if (!groups[categoryId]) {
          groups[categoryId] = {
            id: categoryId,
            name: categoryName,
            orderNum: item.categoryOrderNum || 0,
            items: [],
          };
        }

        groups[categoryId].items.push(item);
      });
      return Object.values(groups);
    },
  },
  created() {
    this.fetchRiskPoints();
  },
  methods: {
    handleEdit() {
      this.$emit("handleEdit");
    },
    // 获取风险点数据
    fetchRiskPoints() {
      if (!this.labUuid) return;

      this.innerLoading = true;

      // 调用API获取安全等级数据
      labSafetyCurrentLevelDetail({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0 && res.data) {
            this.safetyLevelData = res.data;
          }
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.risk-points-container {
  .risk-btn-tabs {
    display: flex;
    gap: 10px;

    .risk-tab-btn {
      background: #e5e6eb;
      color: #1d2129;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      min-width: 90px;

      &.active {
        background: #2050d1;
        color: #fff;
      }
    }
  }

  .risk-tab-content {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px 0;
  }
}
</style>
