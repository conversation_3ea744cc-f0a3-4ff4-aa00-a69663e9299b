<template>
  <div class="safety-sign-container">
    <div v-if="innerLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else class="safety-sign-content">
      <el-empty v-if="!safetySigns || safetySigns.length === 0" description="暂无警示标志信息"></el-empty>
      <div v-else class="signs-list">
        <div class="sign-grid">
          <div v-for="(sign, signIndex) in safetySigns" :key="signIndex" class="sign-item">
            <div class="sign-image">
              <el-image :src="imageSrc + sign.signImagePath" fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
            <div class="sign-name ellipsis">{{ sign.signName }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { labSafetyWarningSignList } from "@laboratory/api/gradingClass/classifyLaboratory";

export default {
  name: "SafetySign",
  props: {
    labUuid: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      innerLoading: false,
      safetySigns: [],
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {},
  created() {
    this.fetchSafetySigns();
  },
  methods: {
    // 获取警示标志数据
    fetchSafetySigns() {
      if (!this.labUuid) return;

      this.innerLoading = true;

      labSafetyWarningSignList({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0) {
            this.safetySigns = res.data || [];
          }
        })
        .finally(() => {
          this.innerLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-sign-container {
  .signs-list {
    .sign-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 15px;
      max-height: 400px;
      overflow-y: auto;

      .sign-item {
        position: relative;
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fff;
        .sign-image {
          width: 80px;
          height: 80px;
          margin: 0 auto 8px;
          border-radius: 4px;
          overflow: hidden;

          .el-image {
            width: 100%;
            height: 100%;
          }

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 24px;
          }
        }

        .sign-name {
          text-align: center;
          font-size: 14px;
          color: #333333;
          line-height: 1.4;
        }

        .sign-check {
          position: absolute;
          top: 5px;
          right: 5px;
          width: 20px;
          height: 20px;
          background: #2050d1;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
