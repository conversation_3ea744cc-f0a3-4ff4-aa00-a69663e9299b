<template>
  <div class="grouped-data-container" v-loading="loading">
    <!-- 可选的总分显示 -->
    <div v-if="showTotalScore && totalScore" class="total-score-opt">
      总分：
      <span class="score-value-opt">{{ totalScore }}</span>
      <span class="score-unit">分</span>
    </div>

    <!-- 空数据提示 -->
    <el-empty v-if="isEmpty" :description="emptyText"></el-empty>

    <!-- 分组数据展示 -->
    <div v-else class="categories-opt">
      <div v-for="(category, categoryKey) in groupedData" :key="categoryKey" class="category-opt">
        <div class="category-title-opt">
          <i v-if="category.displayValue" class="iconfont icon-anquandengji" :style="{ color: category.displayColors }">
            <span v-if="category.displayValue" class="level-value">{{ category.displayValue }}</span>
          </i>
          <i v-else :class="config.iconFont ? config.iconFont : 'iconfont icon-sheshileixing'"
            style="color: #0052D9; font-size: 16px;"></i>
          <span class="category-title-text">{{ getCategoryTitle(category) }}</span>
        </div>
        <div class="category-items-opt">
          <div v-for="(item, index) in category.items" :key="index" class="item-opt">
            <span v-if="showItemScore && getItemScore(item)" class="item-score-opt">( {{ getItemScore(item) }}分 )</span>
            <span class="item-content-opt ellipsis2">{{ getItemText(item) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GroupedDataDisplay",
  props: {
    // 分组后的数据
    groupedData: {
      type: Array,
      required: true,
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 是否显示总分
    showTotalScore: {
      type: Boolean,
      default: false,
    },
    // 总分值
    totalScore: {
      type: [Number, String],
      default: undefined,
    },
    // 空数据提示文本
    emptyText: {
      type: String,
      default: "暂无数据",
    },
    // 是否显示项目分数
    showItemScore: {
      type: Boolean,
      default: false,
    },
    // 配置对象：用于自定义字段映射
    config: {
      type: Object,
      default: () => ({
        // 分类标题字段
        categoryTitleField: "name",
        // 项目文本字段
        itemTextField: "name",
        // 项目分数字段
        itemScoreField: "score",
      }),
    },
  },
  computed: {
    // 判断数据是否为空
    isEmpty() {
      return !this.groupedData || this.groupedData.length === 0;
    },
  },
  methods: {
    // 获取分类标题
    getCategoryTitle(category) {
      return category[this.config.categoryTitleField] || "未命名分类";
    },

    // 获取项目文本
    getItemText(item) {
      // 支持多级嵌套路径，如 'data.name'
      if (this.config.itemTextField && this.config.itemTextField.includes(".")) {
        const parts = this.config.itemTextField.split(".");
        let value = item;
        for (const part of parts) {
          value = value && value[part];
        }
        return value || item.protectionPointName || "未命名项目";
      }

      // 特殊处理防护要点的情况，优先使用signName，如果没有则使用protectionPointName
      if (this.config.itemTextField === "signName" && !item.signName && item.protectionPointName) {
        return item.protectionPointName;
      }
      return item[this.config.itemTextField] || "未命名项目";
    },

    // 获取项目分数
    getItemScore(item) {
      // 支持多级嵌套路径，如 'data.score'
      if (this.config.itemScoreField.includes(".")) {
        const parts = this.config.itemScoreField.split(".");
        let value = item;
        for (const part of parts) {
          value = value && value[part];
        }
        return value;
      }
      return item[this.config.itemScoreField];
    },
  },
};
</script>

<style lang="scss" scoped>
.grouped-data-container {
  // 总分样式
  .total-score-opt {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 18px;

    .score-value-opt {
      color: #d2585d;
      font-style: italic;
      font-size: 20px;
      font-weight: bold;
      margin: 0 2px 0 6px;
      text-decoration: underline;
    }

    .score-unit {
      color: #d2585d;
      font-style: italic;
      font-size: 16px;
    }
  }

  // 分类卡片样式
  .categories-opt {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }

  .category-opt {
    background: #fafbfc;
    border-radius: 8px;
    padding: 18px 20px 12px 20px;
    box-shadow: none;
  }

  .category-title-opt {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 12px;
    position: relative;
    .iconfont {
      position: relative;
      font-size: 24px;
    }
    .level-value {
      font-size: 13px;
      color: #333333;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .category-icon {
      color: #2468f2;
      font-size: 18px;
      margin-right: 8px;
    }

    .category-title-text {
      margin-left: 6px;
      color: #333333;
    }
  }

  // 项目横向排列
  .category-items-opt {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 12px 32px;
    max-width: 100%;

    @media screen and (max-width: 1400px) {
      grid-template-columns: repeat(2, minmax(400px, 1fr));
    }

    @media screen and (max-width: 900px) {
      grid-template-columns: repeat(1, minmax(300px, 1fr));
    }
  }

  .item-opt {
    display: flex;
    margin-bottom: 8px;

    .item-score-opt {
      color: #d2585d;
      font-size: 15px;
      margin-right: 4px;
      white-space: nowrap;
    }

    .item-content-opt {
      color: #333333;
    }
  }
}
</style>
