<template>
  <layoutContent>
    <!-- 返回按钮和编辑按钮放在search插槽中 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">{{ labDetail.roomLabName || "加载中..." }}</span>
      </div>
      <el-button type="primary" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
    </div>

    <!-- 使用封装的顶部信息卡片组件 -->
    <info-detail-header
      :centerDetail="labDetail"
      :title="labDetail.roomLabName || '加载中...'"
      :image-path="labDetail.roomLabImage"
      :image-prefix="imageSrc"
      :info-items="headerInfoItems"
      :loading="loading"
    >
      <div slot="title-style" class="card-icon">
        <i class="iconfont icon-jiaobiao" :style="{ color: labDetail.displayColors || '#DCDCDC' }" />
        <div class="text">
          {{ labDetail.displayValue || "暂无" }}
        </div>
      </div>
      <div slot="dept" style="position: relative;">
        <div class="center-dept on-line" v-if="labDetail.usageStatus === 1">
          在用
        </div>
        <div class="center-dept un-line" v-else-if="labDetail.usageStatus === 2">停用</div>
        <div class="center-dept un-konw" v-else>未知</div>
      </div>
    </info-detail-header>

    <!-- 标签页导航 -->
    <div class="lab-detail-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane name="riskPoints" lazy>
          <span slot="label">
            风险点
            <el-badge v-if="summaryData.riskPointsCount > 0" :value="summaryData.riskPointsCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'riskPoints'">
            <risk-points :labUuid="labUuid" @handleEdit="handleEdit"></risk-points>
          </div>
        </el-tab-pane>
        <el-tab-pane name="protectionPoints" lazy>
          <span slot="label">
            防护要点
            <el-badge v-if="summaryData.protectionPointsCount > 0" :value="summaryData.protectionPointsCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'protectionPoints'">
            <protection-points :labUuid="labUuid"></protection-points>
          </div>
        </el-tab-pane>
        <el-tab-pane name="safetySign" lazy>
          <span slot="label">
            警示标志
            <el-badge v-if="summaryData.safetySignCount > 0" :value="summaryData.safetySignCount" :max="99" class="tab-badge" />
          </span>
          <div class="tab-content" v-if="activeTab === 'safetySign'">
            <safety-sign :labUuid="labUuid"></safety-sign>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import InfoDetailHeader from "@laboratory/components/InfoDetailHeader.vue";
import { labSafetyLabDetail } from "@laboratory/api/gradingClass/classifyLaboratory";
import RiskPoints from "@gradingClass/labMgmt/mgmtDetailCom/RiskPoints.vue";
import ProtectionPoints from "@gradingClass/labMgmt/mgmtDetailCom/ProtectionPoints.vue";
import SafetySign from "@gradingClass/labMgmt/mgmtDetailCom/SafetySign.vue";

export default {
  name: "mgmtLabDetail",
  components: {
    layoutContent,
    InfoDetailHeader,
    RiskPoints,
    ProtectionPoints,
    SafetySign,
  },
  data() {
    return {
      imageSrc: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      labUuid: "",
      labDetail: {},
      loading: false,
      activeTab: "riskPoints", // 默认显示风险点标签页
      // 概览数据
      summaryData: {
        riskPointsCount: 0,
        protectionPointsCount: 0,
        safetySignCount: 0,
      },
    };
  },
  computed: {
    // 计算顶部信息卡片的数据
    headerInfoItems() {
      const {
        roomLabSn,
        establishmentDate,
        contactInfo,
        roomArea,
        roomLabTypeName,
        roomLabCategoryName,
        categoryName,
        labAcademicName,
        deptName,
        safetyOfficer,
        labAddress,
      } = this.labDetail;
      console.log(establishmentDate, "establishmentDate");
      return [
        { label: "实验室编号", value: roomLabSn || "-" },
        { label: "建设年份", value: establishmentDate || "-" },
        { label: "实验室联系方式", value: contactInfo || "-" },
        { label: "房屋使用面积", value: roomArea ? roomArea / 100 + "m²" : "-" },
        { label: "实验室类别", value: roomLabCategoryName || "-" },
        { label: "实验室类型", value: roomLabTypeName || "-" },
        { label: "实验室分类", value: categoryName || "-" },
        { label: "所属学科", value: labAcademicName || "-" },
        { label: "所属部门", value: deptName || "-" },
        { label: "安全管理员", value: safetyOfficer || "-" },
        { label: "地址", value: labAddress || "-" },
      ];
    },
  },
  created() {
    // 从路由参数中获取实验室UUID
    this.labUuid = this.$route.params.uuid;
    if (!this.labUuid) {
      this.$message.error("缺少必要的实验室标识");
      this.goBack();
      return;
    }

    // 获取实验室详情
    this.fetchLabDetail();
  },
  methods: {
    // 获取实验室详情
    fetchLabDetail() {
      if (!this.labUuid) return;

      this.loading = true;
      labSafetyLabDetail({ uuid: this.labUuid })
        .then((res) => {
          if (res.code === 0) {
            // 处理返回的数据结构，将labInfo展开到顶层
            if (res.data && res.data.labInfo) {
              this.labDetail = {
                ...res.data.labInfo,
                ...res.data,
              };
            } else {
              this.labDetail = res.data || {};
            }

            // 更新概览数据
            this.updateSummaryData();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 更新概览数据
    updateSummaryData() {
      // 这里可以根据实际情况更新各个标签页的计数
      // 如果API没有直接返回计数，可以在各组件内部处理
      this.summaryData = {
        riskPointsCount: 0, // 暂时设为0，后续可以通过API获取或在子组件中更新
        protectionPointsCount: 0,
        safetySignCount: 0,
      };
    },
    // 返回列表页
    goBack() {
      // 检查是否从实验中心进入
      const centerUuid = this.$route.params.centerUuid;

      if (centerUuid) {
        // 从实验中心进入，返回实验中心管理员权限的实验室列表
        this.$router.push({
          name: "centerLabMgmt",
          params: { centerUuid },
        });
      } else {
        // 普通实验室管理，返回普通列表页
        this.$router.push({ name: "labMgmt" });
      }
    },

    // 实验室编辑
    handleEdit() {
      if (this.labUuid) {
        // 检查是否从实验中心进入
        const centerUuid = this.$route.params.centerUuid;
        const routeName = centerUuid ? "centerMgmtLabEdit" : "mgmtLabEdit";

        this.$router.push({
          name: routeName,
          params: {
            uuid: this.labUuid,
            ...(centerUuid ? { centerUuid } : {}),
          },
          query: { from: "mgmtDetail" }, // 标记来源为详情页
        });
      } else {
        this.$message.error("编辑失败：无效的实验室信息");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-button + .el-button {
    margin-left: 10px;
  }

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #303133;
      font-weight: 500;
    }
  }
}

.lab-detail-tabs {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px 0;

  .tab-content {
    min-height: 300px;
    padding: 0 20px;

    &.intro-content {
      line-height: 1.6;

      /* 富文本样式 */
      :deep(.w-e-text-container) {
        height: auto !important;
      }

      :deep(.w-e-text) {
        padding: 0;
        overflow: visible;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        margin: 10px auto;
        display: block;
      }

      :deep(p) {
        margin-bottom: 15px;
      }

      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;

        th,
        td {
          border: 1px solid #ddd;
          padding: 8px 12px;
        }

        th {
          background-color: #f6f6f6;
        }
      }
    }
  }
}

::v-deep .el-tabs__header {
  padding: 0 20px !important;
}
.on-line {
  background-color: #cff2e6 !important;
  color: #09ab75 !important;
}
.un-line {
  background-color: #fae5e5 !important;
  color: #d2585d !important;
}
.un-konw {
  background-color: #cdcaca !important;
  color: #625e5e !important;
}
.card-icon {
  display: inline-block;
  position: relative;
  .iconfont {
    font-size: 24px !important;
  }
  .text {
    position: absolute;
    font-size: 15px;
    color: #333333 !important;
    left: 50%;
    top: 45%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
  }
}
</style>
