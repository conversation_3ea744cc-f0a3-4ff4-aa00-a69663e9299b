<template>
  <layoutContent>
    <!-- 顶部导航栏 -->
    <div class="header-actions" slot="search">
      <div class="breadcrumb-nav">
        <span class="prev-level" @click="goBack">上一级</span>
        <i class="el-icon-arrow-right separator"></i>
        <span class="current-name">实验室编辑</span>
      </div>
      <el-button type="primary" @click="handleComplete">完成</el-button>
    </div>

    <!-- 主体内容区 -->
    <div class="lab-edit-container">
      <!-- 左侧步骤导航 -->
      <div class="steps-nav">
        <el-steps direction="vertical" :active="currentStep - 1" :space="80">
          <el-step v-for="(step, index) in steps" :key="index" :title="step" :class="{ clickable: true }">
            <div slot="title" @click="handleStepClick(index)" class="step-title">
              <span v-if="index < 3">*</span>
              {{ step }}
            </div>
          </el-step>
        </el-steps>
      </div>

      <!-- 右侧表单内容 -->
      <div class="form-content">
        <!-- 实验室分类 -->
        <div v-if="currentStep === 1" class="step-form">
          <lab-category-form ref="labCategoryForm" :labUuid="labUuid" @save-success="handleCategorySaveSuccess" />
        </div>
        <!-- 风险点 -->
        <div v-if="currentStep === 2" class="step-form">
          <lab-risk-points-form ref="labRiskPointsForm" :labUuid="labUuid" @save-success="handleRiskPointsSaveSuccess" />
        </div>
        <!-- 防护要点 -->
        <div v-if="currentStep === 3" class="step-form">
          <lab-protection-points-form ref="labProtectionPointsForm" :labUuid="labUuid" @save-success="handleProtectionPointsSaveSuccess" />
        </div>
        <!-- 警示标识 -->
        <div v-if="currentStep === 4" class="step-form">
          <lab-safety-signs-form ref="labSafetySignsForm" :labUuid="labUuid" @save-success="handleSafetySignsSaveSuccess" />
        </div>
      </div>
    </div>
  </layoutContent>
</template>

<script>
import layoutContent from "@laboratory/components/layoutContent";
import LabCategoryForm from "./labEditCom/LabCategoryForm.vue";
import LabRiskPointsForm from "./labEditCom/LabRiskPointsForm.vue";
import LabProtectionPointsForm from "./labEditCom/LabProtectionPointsForm.vue";
import LabSafetySignsForm from "./labEditCom/LabSafetySignsForm.vue";

export default {
  name: "labEdit",
  components: {
    layoutContent,
    LabCategoryForm,
    LabRiskPointsForm,
    LabProtectionPointsForm,
    LabSafetySignsForm,
  },
  data() {
    return {
      labUuid: "",
      currentStep: 1,
      steps: ["实验室分类", "风险点", "防护要点", "警示标识"],
    };
  },
  created() {
    // 从路由参数中获取实验室UUID
    this.labUuid = this.$route.params.uuid;
    if (!this.labUuid) {
      this.$message.error("缺少必要的实验室标识");
      this.goBack();
      return;
    }
  },
  methods: {
    // 步骤点击
    handleStepClick(index) {
      this.currentStep = index + 1;
    },

    // 返回
    goBack() {
      const from = this.$route.query.from;
      const centerUuid = this.$route.params.centerUuid;

      if (from === "mgmtDetail") {
        // 从详情页来的，返回详情页
        const detailRouteName = centerUuid ? "centerMgmtLabDetail" : "mgmtLabDetail";

        this.$router.push({
          name: detailRouteName,
          params: {
            uuid: this.labUuid,
            ...(centerUuid ? { centerUuid } : {}),
          },
        });
      } else {
        // 其他情况返回列表页
        if (centerUuid) {
          // 从实验中心进入，返回实验中心管理员权限的实验室列表
          this.$router.push({
            name: "centerLabMgmt",
            params: { centerUuid },
          });
        } else {
          // 普通实验室管理，返回普通列表页
          this.$router.push({ name: "labMgmt" });
        }
      }
    },

    // 完成编辑
    handleComplete() {
      this.$confirm("确认完成实验室编辑？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message.success("实验室编辑完成");
          this.goBack();
        })
        .catch(() => {
          // 用户取消操作
        });
    },

    // 实验室分类保存成功
    handleCategorySaveSuccess() {
      this.$message.success("实验室分类保存成功");
      this.switchStep(this.currentStep + 1);
    },

    // 风险点保存成功
    handleRiskPointsSaveSuccess() {
      this.$message.success("风险点保存成功");
      this.switchStep(this.currentStep + 1);
    },

    // 防护要点保存成功
    handleProtectionPointsSaveSuccess() {
      this.$message.success("防护要点保存成功");
      this.switchStep(this.currentStep + 1);
    },

    // 警示标识保存成功
    handleSafetySignsSaveSuccess() {
      this.$message.success("警示标识保存成功");
      this.handleComplete()
    },
    // 切换步骤 - 简化逻辑，允许自由切换
    switchStep(step) {
      this.currentStep = step;
    },
  },
};
</script>

<style lang="scss" scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    font-size: 14px;

    .prev-level {
      color: #0052d9;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }

    .separator {
      margin: 0 8px;
      color: #909399;
      font-size: 12px;
    }

    .current-name {
      color: #333333;
      font-weight: 500;
    }
  }
}

.lab-edit-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-height: 600px;

  .form-content {
    flex: 1;
    padding: 20px;

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: block;
        width: 4px;
        height: 16px;
        background-color: #2050d1;
        margin-right: 8px;
      }
    }

    .step-form {
      max-width: 100%;
    }

    .intro-form {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 10px;
    }

    .form-actions {
      display: flex;
      margin: 20px 0;

      .el-button {
        margin-left: auto;
      }
    }

    .placeholder {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }

    .empty-tip {
      padding: 40px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
  }
}
</style>
