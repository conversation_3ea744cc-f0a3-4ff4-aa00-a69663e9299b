<template>
  <div class="protection-points-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="个人防护" name="personal_protection">
        <protection-points-list 
          v-if="activeName === 'personal_protection'" 
          :point-type="activeName"
          :type-label="'个人防护要点'"
        />
      </el-tab-pane>
      <el-tab-pane label="灭火要点" name="fire_extinguishing">
        <protection-points-list 
          v-if="activeName === 'fire_extinguishing'" 
          :point-type="activeName"
          :type-label="'灭火要点'"
        />
      </el-tab-pane>
      <el-tab-pane label="危险源" name="hazard_source">
        <protection-points-list 
          v-if="activeName === 'hazard_source'" 
          :point-type="activeName"
          :type-label="'危险源'"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ProtectionPointsList from "./protectionCom/ProtectionPointsList.vue";

export default {
  name: "ProtectionPoints",
  components: {
    ProtectionPointsList,
  },
  data() {
    return {
      activeName: "personal_protection",
    };
  },
  methods: {
    handleTabClick(tab) {
      console.log("切换到标签页:", tab.name);
    },
  },
};
</script>

<style lang="less" scoped>
.protection-points-container {
  padding: 20px 16px;
}
</style>