<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="811px" :close-on-click-modal="false" @closed="handleDialogClosed">
    <el-form ref="protectionForm" :model="localFormData" :rules="rules" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="typeLabel + '名称'" label-width="150px" prop="protectionPointName">
            <el-input v-model="localFormData.protectionPointName" :placeholder="'请输入' + typeLabel + '名称'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="orderNum">
            <el-input-number
              v-model="localFormData.orderNum"
              :min="1"
              :max="9999"
              placeholder="请输入排序数字"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "ProtectionPointDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    pointType: {
      type: String,
      required: true,
    },
    typeLabel: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      submitLoading: false,
      localFormData: {},
      rules: {
        protectionPointName: [
          { required: true, message: "请输入防护要点名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" },
        ],
        orderNum: [
          { required: true, message: "请输入排序", trigger: "blur" },
          { type: "number", message: "排序必须为数字", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    dialogTitle() {
      return this.formData.uuid ? `编辑${this.typeLabel}` : `新增${this.typeLabel}`;
    },
  },
  watch: {
    formData: {
      handler(newVal) {
        this.localFormData = { ...newVal };
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 提交表单
    handleSubmit() {
      this.$refs.protectionForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true;

          // 准备提交的数据
          const submitData = {
            ...this.localFormData,
            pointType: this.pointType,
          };

          // 触发父组件的确认事件
          this.$emit("confirm", submitData);

          // 延迟重置loading状态，让父组件处理完成后再重置
          setTimeout(() => {
            this.submitLoading = false;
          }, 1000);
        } else {
          this.$message.error("请检查表单填写是否正确");
        }
      });
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },

    // 对话框关闭时重置表单
    handleDialogClosed() {
      this.$refs.protectionForm.resetFields();
      this.submitLoading = false;
    },
  },
};
</script>

<style lang="less" scoped>
</style>
