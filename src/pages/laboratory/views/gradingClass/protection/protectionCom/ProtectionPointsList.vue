<template>
  <div class="protection-points-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item :label="searchLabel">
          <el-input v-model="searchForm.protectionPointName" :placeholder="searchPlaceholder" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <!-- <el-button type="danger" icon="el-icon-delete" @click="handleBatchDelete" :disabled="selectedItems.length === 0">
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <import-export-buttons
          :exportFilePrefix="exportFilePrefix"
          :show-import="false"
          :show-template="false"
          :exportApi="handleExportApi"
        />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="protectionTable"
        :loading="loading"
        :table-data="tableData"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 标识图片列 -->
        <template #signImagePath="{ row }" v-if="pointType !== 'fire_extinguishing'">
          <el-image
            v-if="row.signImagePath"
            :src="location + row.signImagePath"
            style="width: 50px; height: 50px; border-radius: 4px"
            :preview-src-list="[location + row.signImagePath]"
            fit="cover"
          ></el-image>
          <span v-else>-</span>
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button
            v-if="pointType !== 'fire_extinguishing'"

            type="text"
            @click="handleRelateSign(row)"
            style="color: #00A870"
          >
            关联标识
          </el-button>
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 新增/编辑对话框 -->
    <protection-point-dialog
      :visible.sync="dialogVisible"
      :form-data="dialogFormData"
      :point-type="pointType"
      :type-label="typeLabel"
      @confirm="handleDialogConfirm"
    />

    <!-- 关联标识对话框 -->
    <sign-relation-dialog
      :visible.sync="signRelationDialogVisible"
      :protection-point-data="currentProtectionPoint"
      @success="handleSignRelationSuccess"
    />
  </div>
</template>

<script>
import {
  labSafetyprotectionPointList,
  labSafetySignCategorySave,
  labSafetySignCategoryUpdate,
  labSafetySignCategoryDelete,
  labSafetyProtectionPointExport,
} from "@laboratory/api/gradingClass/protectionPoints";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import TablePagetion from "@laboratory/components/TablePagetion";
import ProtectionPointDialog from "./ProtectionPointDialog.vue";
import SignRelationDialog from "./SignRelationDialog.vue";

export default {
  name: "ProtectionPointsList",
  components: {
    ImportExportButtons,
    TablePagetion,
    ProtectionPointDialog,
    SignRelationDialog,
  },
  props: {
    pointType: {
      type: String,
      required: true,
    },
    typeLabel: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      loading: false,
      tableData: [],
      selectedItems: [],
      searchForm: {
        protectionPointName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      dialogVisible: false,
      dialogFormData: {},
      signRelationDialogVisible: false,
      currentProtectionPoint: {},
      tableColumns: [],
    };
  },
  computed: {
    searchLabel() {
      return `${this.typeLabel}名称：`;
    },
    searchPlaceholder() {
      return `请输入${this.typeLabel}`;
    },
    exportFilePrefix() {
      return `${this.typeLabel}列表`;
    },
  },
  mounted() {
    this.initTableColumns();
    this.getList();
  },
  methods: {
    // 初始化表格列配置
    initTableColumns() {
      const baseColumns = [
        // { prop: "selection", type: "selection", width: 55, align: "center" },
        { prop: "orderNum", label: "排序", width: 80, align: "center" },
        { prop: "protectionPointName", label: this.typeLabel, minWidth: 200 },
      ];

      // 根据不同类型决定是否显示标识列
      if (this.pointType !== "fire_extinguishing") {
        baseColumns.push({ prop: "signImagePath", label: "标识", width: 100, align: "center", slotName: "signImagePath" });
      }

      baseColumns.push(
        { prop: "updateByName", label: "操作人", minWidth: 120, align: "center" },
        { prop: "gmtModified", label: "操作时间", minWidth: 180, align: "center", slot: true },
      );

      // 根据不同类型决定操作列宽度
      const operationWidth = this.pointType === "fire_extinguishing" ? 150 : 200;
      baseColumns.push({ prop: "operation", label: "操作", width: operationWidth, align: "center", fixed: "right", slotName: "operation" });

      this.tableColumns = baseColumns;
    },

    // 获取列表数据
    async getList() {
      this.loading = true;
      try {
        const params = {
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
          pointType: this.pointType,
          protectionPointName: this.searchForm.protectionPointName,
          orderRule: this.searchForm.orderRule,
          orderItems: this.searchForm.orderItems,
        };
        const response = await labSafetyprotectionPointList(params);
        if (response.code === 0) {
          this.tableData = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.getList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        protectionPointName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      };
      this.paginationConfig.currentPage = 1;
      this.getList();
    },

    // 新增
    handleAdd() {
      this.dialogFormData = {
        protectionPointName: "",
        orderNum: "",
      };
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.dialogFormData = { ...row };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除${this.typeLabel}"${row.protectionPointName}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetySignCategoryDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.getList();
          }
        } catch (error) {}
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(`确定要删除选中的${this.selectedItems.length}条${this.typeLabel}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const promises = this.selectedItems.map((item) => labSafetySignCategoryDelete({ uuid: item.uuid }));
          await Promise.all(promises);
          this.selectedItems.length = 0;
          this.$message.success("批量删除成功");
          this.getList();
        } catch (error) {}
      });
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.getList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.getList();
    },

    // 关联标识
    handleRelateSign(row) {
      this.currentProtectionPoint = { ...row };
      this.signRelationDialogVisible = true;
    },

    // 关联标识成功回调
    handleSignRelationSuccess() {
      this.signRelationDialogVisible = false;
      this.getList(); // 刷新列表以显示最新的关联标识
    },

    // 对话框确认
    async handleDialogConfirm(formData) {
      try {
        const data = {
          ...formData,
          pointType: this.pointType,
        };

        let response;
        if (data.uuid) {
          // 编辑
          response = await labSafetySignCategoryUpdate(data);
        } else {
          // 新增
          response = await labSafetySignCategorySave(data);
        }

        if (response.code === 0) {
          this.$message.success(data.uuid ? "编辑成功" : "新增成功");
          this.dialogVisible = false;
          this.getList();
        }
      } catch (error) {
        console.error("保存失败:", error);
        this.$message.error("保存失败");
      }
    },

    // 导出API
    async handleExportApi(params) {
      params = {
        ...params,
        ...this.searchForm,
        pointType: this.pointType,
      };
      return labSafetyProtectionPointExport(params);
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "-";
      return new Date(date).toLocaleString("zh-CN");
    },
  },
};
</script>

<style lang="scss" scoped>
.protection-points-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
