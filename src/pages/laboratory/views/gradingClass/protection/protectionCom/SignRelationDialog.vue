<template>
  <el-dialog
    title="关联标识"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
    custom-class="sign-relation-dialog"
  >
    <div class="sign-relation-content">
      <!-- 选择标识分类 -->
      <div class="category-section">
        <div class="section-title">标识分类：</div>
        <div class="category-radio-group">
          <el-radio-group v-model="selectedCategoryId" @change="handleCategoryChange">
            <el-radio v-for="category in categoryOptions" :key="category.categoryId" :label="category.categoryId" class="category-radio">
              {{ category.categoryName }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 选择标识 -->
      <div class="sign-section" v-if="selectedCategoryId">
        <div class="section-title">选择标识：</div>
        <div class="sign-grid" v-loading="signLoading">
          <div
            v-for="sign in filteredSigns"
            :key="sign.signId"
            class="sign-item"
            :class="{ 'selected': selectedSignId === sign.signId }"
            @click="handleSignSelect(sign)"
          >
            <div class="sign-image">
              <el-image :src="location + sign.signImagePath" fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
            <div class="sign-name">{{ sign.signName }}</div>
            <div class="sign-check" v-if="selectedSignId === sign.signId">
              <i class="el-icon-check"></i>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredSigns.length === 0 && !signLoading" class="empty-state">
          <i class="el-icon-info"></i>
          <span>该分类下暂无标识</span>
        </div>
      </div>

      <!-- 未选择分类提示 -->
      <div v-else class="no-category-tip">
        <i class="el-icon-info"></i>
        <span>请先选择标识分类</span>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitLoading" :disabled="!selectedSignId">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { labSafetySignOptionsList } from "@laboratory/api/gradingClass/safetySign";
import { labSafetyProtectionPointSignRelationSave } from "@laboratory/api/gradingClass/protectionPoints";

export default {
  name: "SignRelationDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    protectionPointData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      signLoading: false,
      submitLoading: false,
      selectedCategoryId: "",
      selectedSignId: null,
      selectedSignData: null,
      allSigns: [],
      categoryOptions: [],
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    filteredSigns() {
      if (!this.selectedCategoryId) return [];
      return this.allSigns.filter((sign) => sign.categoryId === this.selectedCategoryId);
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadSignOptions();
      }
    },
  },
  methods: {
    // 加载标识选项列表
    async loadSignOptions() {
      this.signLoading = true;
      try {
        const response = await labSafetySignOptionsList();
        if (response.code === 0) {
          this.allSigns = response.data || [];
          this.processCategoryOptions();
        }
      } catch (error) {
        console.error("加载标识列表失败:", error);
        this.$message.error("加载标识列表失败");
      } finally {
        this.signLoading = false;
      }
    },

    // 处理分类选项
    processCategoryOptions() {
      const categoryMap = new Map();

      this.allSigns.forEach((sign) => {
        if (sign.categoryId && sign.categoryName) {
          categoryMap.set(sign.categoryId, {
            categoryId: sign.categoryId,
            categoryName: sign.categoryName,
          });
        }
      });

      this.categoryOptions = Array.from(categoryMap.values());
      
      // 如果有分类数据，默认选择第一条
      if (this.categoryOptions.length > 0) {
        this.selectedCategoryId = this.categoryOptions[0].categoryId;
      }
    },

    // 分类变化
    handleCategoryChange(categoryId) {
      this.selectedSignId = null;
      this.selectedSignData = null;
      console.log("选择分类:", categoryId);
    },

    // 选择标识
    handleSignSelect(sign) {
      this.selectedSignId = sign.signId;
      this.selectedSignData = sign;
    },

    // 确认关联
    async handleConfirm() {
      if (!this.selectedSignId) {
        this.$message.warning("请选择要关联的标识");
        return;
      }

      this.submitLoading = true;
      try {
        const data = {
          uuid: this.protectionPointData.uuid,
          signId: this.selectedSignId,
        };

        const response = await labSafetyProtectionPointSignRelationSave(data);
        if (response.code === 0) {
          this.$message.success("关联标识成功");
          this.$emit("success");
          this.dialogVisible = false;
        }
      } catch (error) {
      } finally {
        this.submitLoading = false;
      }
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },

    // 对话框关闭时重置数据
    handleDialogClosed() {
      this.selectedCategoryId = "";
      this.selectedSignId = null;
      this.selectedSignData = null;
      this.submitLoading = false;
      this.allSigns = [];
      this.categoryOptions = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.sign-relation-content {
  .category-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
    }

    .category-radio-group {
      .category-radio {
        margin-right: 20px;
        margin-bottom: 10px;

        :deep(.el-radio__label) {
          font-size: 14px;
          color: #606266;
        }

        &:hover {
          :deep(.el-radio__label) {
            color: #2050d1;
          }
        }
      }
    }
  }

  .sign-section {
    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
    }

    .sign-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 15px;
      max-height: 400px;
      overflow-y: auto;

      .sign-item {
        position: relative;
        border: 2px solid #e4e7ed;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fff;

        &:hover {
          border-color: #c0c4cc;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border-color: #2050d1;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        .sign-image {
          width: 80px;
          height: 80px;
          margin: 0 auto 8px;
          border-radius: 4px;
          overflow: hidden;

          .el-image {
            width: 100%;
            height: 100%;
          }

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 24px;
          }
        }

        .sign-name {
          text-align: center;
          font-size: 12px;
          color: #606266;
          line-height: 1.4;
          word-break: break-all;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .sign-check {
          position: absolute;
          top: 5px;
          right: 5px;
          width: 20px;
          height: 20px;
          background: #2050d1;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12px;
        }
      }
    }
  }

  .empty-state,
  .no-category-tip {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    font-size: 14px;

    i {
      font-size: 24px;
      margin-bottom: 10px;
      display: block;
    }
  }
}
</style>
