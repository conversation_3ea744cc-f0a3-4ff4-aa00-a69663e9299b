<template>
  <div class="safety-sign-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="安全标识库" name="safetySign">
        <safety-sign-list v-if="activeName === 'safetySign'" />
      </el-tab-pane>
      <el-tab-pane label="标识分类" name="signCategory">
        <sign-category-list v-if="activeName === 'signCategory'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SafetySignList from "./safetySignCom/SafetySignList.vue";
import SignCategoryList from "./safetySignCom/SignCategoryList.vue";

export default {
  name: "SafetySign",
  components: {
    SafetySignList,
    SignCategoryList,
  },
  data() {
    return {
      activeName: "safetySign",
    };
  },
  methods: {
    handleTabClick(tab) {
      console.log("切换到标签页:", tab.name);
    },
  },
};
</script>

<style lang="less" scoped>
.safety-sign-container {
  padding: 20px 16px;
}
</style>
