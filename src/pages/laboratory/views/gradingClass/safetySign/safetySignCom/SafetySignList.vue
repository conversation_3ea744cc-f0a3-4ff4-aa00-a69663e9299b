<template>
  <div class="safety-sign-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="标识名称：">
          <el-input v-model="searchForm.signName" placeholder="请输入标识名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="标识分类：">
          <el-select v-model="searchForm.categoryId" placeholder="请选择标识分类" filterable clearable>
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <!-- <el-button type="danger" icon="el-icon-delete" @click="handleBatchDelete" :disabled="selectedItems.length === 0">
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <!-- :uuid="labUuid"
          uuidName="labUuid" -->
        <import-export-buttons exportFilePrefix="标识库列表" :show-import="false" :show-template="false" :exportApi="handleExportApi" />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="signTable"
        :loading="loading"
        :table-data="signList"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 标识图片列 -->
        <template #signImagePath="{ row }">
          <el-image
            v-if="row.signImagePath"
            :src="location + row.signImagePath"
            style="width: 50px; height: 50px; border-radius: 4px"
            :preview-src-list="[location + row.signImagePath]"
            fit="cover"
          ></el-image>
          <span v-else>-</span>
        </template>

        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="811px" :close-on-click-modal="false" @closed="handleDialogClosed">
      <el-form ref="signForm" :model="signForm" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标识名称" prop="signName">
              <el-input v-model="signForm.signName" placeholder="请输入标识名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标识分类" prop="categoryId">
              <el-select v-model="signForm.categoryId" placeholder="请选择标识分类" style="width: 100%">
                <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="上传图片" prop="signImagePath">
              <file-uploader
                v-model="signForm.signImagePath"
                type="image"
                :maxSize="2"
                tip="只能上传jpg/png文件，且不超过2MB"
                class="photo-uploader"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import FileUploader from "@laboratory/components/FileUploader";
import {
  labSafetySignList,
  labSafetySignSave,
  labSafetySignUpdate,
  labSafetySignDelete,
  labSafetySignCategoryOptionsList,
  labSafetySignExport,
} from "@laboratory/api/gradingClass/safetySign";

export default {
  name: "SafetySignList",
  components: {
    TablePagetion,
    ImportExportButtons,
    FileUploader,
  },
  data() {
    return {
      location: document.location.protocol + "//" + document.location.host + (window.g?.ApiUrl || ""),
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增安全标识",
      selectedItems: [],
      signList: [],
      categoryOptions: [],
      searchForm: {
        signName: "",
        categoryId: "",
        orderRule: "asc",
        orderItems: "orderNum",
      },
      signForm: {
        uuid: "",
        signName: "",
        categoryId: "",
        signImagePath: "",
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        signName: [{ required: true, message: "请输入标识名称", trigger: "blur" }],
        categoryId: [{ required: true, message: "请选择标识分类", trigger: "change" }],
        signImagePath: [{ required: true, message: "请上传标识图片", trigger: "change" }],
      },
      // 表格列配置
      tableColumns: [
        // { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "signName", label: "标识名称", minWidth: 150 },
        { prop: "categoryName", label: "标识分类", minWidth: 120 },
        { prop: "signImagePath", label: "标识图片", minWidth: 100, slotName: "signImagePath" },
        { prop: "updateByName", label: "操作人", minWidth: 120 },
        { prop: "gmtModified", label: "操作时间", minWidth: 150 },
        { prop: "operation", label: "操作", fixed: "right", width: "150", slotName: "operation" },
      ],
    };
  },
  mounted() {
    this.loadCategoryOptions();
    this.loadSignList();
  },
  methods: {
    // 加载标识分类选项
    async loadCategoryOptions() {
      try {
        const response = await labSafetySignCategoryOptionsList();
        if (response.code === 0) {
          this.categoryOptions = response.data.map((item) => ({
            value: item.categoryId,
            label: item.categoryName,
          }));
        }
      } catch (error) {
        console.error("加载标识分类失败:", error);
      }
    },

    // 加载标识列表
    async loadSignList() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        };
        const response = await labSafetySignList(params);
        if (response.code === 0) {
          this.signList = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadSignList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        signName: "",
        categoryId: "",
        orderRule: "asc",
        orderItems: "orderNum",
      };
      this.paginationConfig.currentPage = 1;
      this.loadSignList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增安全标识";
      this.signForm = {
        uuid: "",
        signName: "",
        categoryId: "",
        signImagePath: "",
      };
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑安全标识";
      this.signForm = {
        uuid: row.uuid,
        signName: row.signName,
        categoryId: row.categoryId,
        signImagePath: row.signImagePath,
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该安全标识吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetySignDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.loadSignList();
          }
        } catch (error) {}
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedItems.length} 条数据吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const promises = this.selectedItems.map((item) => labSafetySignDelete({ uuid: item.uuid }));
          await Promise.all(promises);
          this.selectedItems.length = 0;
          this.$message.success("批量删除成功");
          this.loadSignList();
        } catch (error) {}
      });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.signForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            const isEdit = !!this.signForm.uuid;
            const api = isEdit ? labSafetySignUpdate : labSafetySignSave;
            const response = await api(this.signForm);

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功");
              this.dialogVisible = false;
              this.loadSignList();
            }
          } catch (error) {
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },

    async handleExportApi(params) {
      params = {
        ...params,
        ...this.searchForm,
      };
      return labSafetySignExport(params);
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadSignList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadSignList();
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.signForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-sign-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
