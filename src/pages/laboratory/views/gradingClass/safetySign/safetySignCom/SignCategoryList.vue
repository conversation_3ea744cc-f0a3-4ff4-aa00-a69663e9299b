<template>
  <div class="sign-category-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="分类名称：">
          <el-input v-model="searchForm.categoryName" placeholder="请输入分类名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="header-actions">
      <div class="left-actions">
        <el-button type="warning" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <!-- <el-button type="danger" icon="el-icon-delete" @click="handleBatchDelete" :disabled="selectedItems.length === 0">
          删除
        </el-button> -->
        <!-- 导出功能 -->
        <import-export-buttons exportFilePrefix="标识分类列表" :show-import="false" :show-template="false" :exportApi="handleExportApi" />
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table-pagetion
        ref="categoryTable"
        :loading="loading"
        :table-data="categoryList"
        :columns="tableColumns"
        :pagination="paginationConfig"
        :show-pagination="true"
        @page-size-change="handleSizeChange"
        @current-page-change="handleCurrentChange"
        @handleSelectionChange="handleSelectionChange"
      >
        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: #f56c6c" @click="handleDelete(row)">删除</el-button>
        </template>
      </table-pagetion>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false" @closed="handleDialogClosed">
      <el-form ref="categoryForm" :model="categoryForm" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input v-model="categoryForm.categoryName" placeholder="请输入分类名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="orderNum">
              <el-input-number
                v-model="categoryForm.orderNum"
                :min="1"
                :max="9999"
                placeholder="请输入排序数字"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TablePagetion from "@laboratory/components/TablePagetion";
import ImportExportButtons from "@laboratory/components/ImportExportButtons";
import {
  labSafetySignCategoryList,
  labSafetySignCategorySave,
  labSafetySignCategoryUpdate,
  labSafetySignCategoryDelete,
  labSafetySignCategoryExport,
} from "@laboratory/api/gradingClass/safetySign";

export default {
  name: "SignCategoryList",
  components: {
    TablePagetion,
    ImportExportButtons,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      dialogTitle: "新增标识分类",
      selectedItems: [],
      categoryList: [],
      searchForm: {
        categoryName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      },
      categoryForm: {
        uuid: "",
        categoryName: "",
        orderNum: 0,
      },
      paginationConfig: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      rules: {
        categoryName: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
        orderNum: [{ required: true, message: "请输入排序数字", trigger: "blur" }],
      },
      // 表格列配置
      tableColumns: [
        // { prop: "selection", label: "", type: "selection", width: "55" },
        { prop: "orderNum", label: "排序", width: "80" },
        { prop: "categoryName", label: "分类名称", minWidth: 200 },
        { prop: "updateByName", label: "操作人", minWidth: 150 },
        { prop: "gmtModified", label: "操作时间", minWidth: 150 },
        { prop: "operation", label: "操作", fixed: "right", width: "150", slotName: "operation" },
      ],
    };
  },
  mounted() {
    this.loadCategoryList();
  },
  methods: {
    // 加载分类列表
    async loadCategoryList() {
      this.loading = true;
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.paginationConfig.currentPage,
          pageSize: this.paginationConfig.pageSize,
        };
        const response = await labSafetySignCategoryList(params);
        console.log(response, "response");
        if (response.code === 0) {
          this.categoryList = response.data || [];
          this.paginationConfig.total = response.count || 0;
        }
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.paginationConfig.currentPage = 1;
      this.loadCategoryList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        categoryName: "",
        orderRule: "asc",
        orderItems: "orderNum",
      };
      this.paginationConfig.currentPage = 1;
      this.loadCategoryList();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增标识分类";
      this.categoryForm = {
        uuid: "",
        categoryName: "",
        orderNum: 0,
      };
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑标识分类";
      this.categoryForm = {
        uuid: row.uuid,
        categoryName: row.categoryName,
        orderNum: row.orderNum,
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确定要删除该标识分类吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const response = await labSafetySignCategoryDelete({ uuid: row.uuid });
          if (response.code === 0) {
            this.$message.success("删除成功");
            this.loadCategoryList();
          }
        } catch (error) {}
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedItems.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      this.$confirm(`确定要删除选中的 ${this.selectedItems.length} 条数据吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          const promises = this.selectedItems.map((item) => labSafetySignCategoryDelete({ uuid: item.uuid }));
          await Promise.all(promises);
          this.selectedItems.length = 0;
          this.$message.success("批量删除成功");
          this.loadCategoryList();
        } catch (error) {}
      });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.categoryForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            const isEdit = !!this.categoryForm.uuid;
            // 直接使用表单数据
            const submitData = {
              ...this.categoryForm,
            };

            const api = isEdit ? labSafetySignCategoryUpdate : labSafetySignCategorySave;
            const response = await api(submitData);

            if (response.code === 0) {
              this.$message.success(isEdit ? "编辑成功" : "新增成功");
              this.dialogVisible = false;
              this.loadCategoryList();
            }
          } catch (error) {
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },

    async handleExportApi(params) {
      params = {
        ...params,
        ...this.searchForm,
      };
      return labSafetySignCategoryExport(params);
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedItems = selection;
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.paginationConfig.pageSize = size;
      this.paginationConfig.currentPage = 1;
      this.loadCategoryList();
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.paginationConfig.currentPage = page;
      this.loadCategoryList();
    },

    // 弹窗关闭
    handleDialogClosed() {
      this.$refs.categoryForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.sign-category-list {
  .header-actions {
    margin-top: 20px;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
