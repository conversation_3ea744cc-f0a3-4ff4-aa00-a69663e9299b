<template>
  <div class="dashboard-container">
    <div class="dashboard-text">欢迎来到{{ title }}</div>
  </div>
</template>

<script>
export default {
  name: "Dashboard",
  data() {
    return {
      title: window.g.title,
    };
  },
  computed: {},
  created() {},
};
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
