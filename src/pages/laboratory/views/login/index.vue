<template>
  <div class="login-container">
    <div class="login-form">
      <div class="title-container">
        <h3 class="title">{{ sysName }}</h3>
      </div>
      <div class="formbox">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" auto-complete="on" label-position="left">
          <el-form-item prop="username" class="user">
            <span class="svg-container">
              <i class="el-icon-user" style="font-size: 22px" />
            </span>
            <el-input ref="username" v-model="loginForm.username" placeholder="请输入账号" name="username" type="text" />
          </el-form-item>

          <el-form-item prop="password" class="pwd">
            <span class="svg-container">
              <i class="el-icon-lock" style="font-size: 22px" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="请输入密码"
              name="password"
              @keyup.enter.native="handleLogin"
            />
            <span class="show-pwd" @click="showPwd"><svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" /></span>
          </el-form-item>
          <el-button :loading="loading" type="primary" @click.native.prevent="handleLogin" class="login">登录</el-button>
        </el-form>
      </div>
      <Verify
        @success="success"
        :mode="'pop'"
        :captchaType="'blockPuzzle'"
        :imgSize="{ width: '330px', height: '155px' }"
        ref="verify"
      ></Verify>
    </div>
  </div>
</template>

<script>
import { getsysconfig } from "@laboratory/api/Settings/DictTable.js";
import { getPublicKey } from "@laboratory/api/Settings/Laboratory/videorecorder.js";
import Verify from "@laboratory/components/verifition/Verify";
import { JSEncrypt } from "jsencrypt";
export default {
  name: "systemlogin",
  data() {
    //用户名和密码输入框的校验
    const validateUsername = (rule, value, callback) => {
      // if (!validUsername(value)) {
      //   callback(new Error('Please enter the correct user name'))
      // } else {
      //   callback()
      // }
    };
    const validatePassword = (rule, value, callback) => {
      // if (value.length < 6) {
      //   callback(new Error('The password can not be less than 6 digits'))
      // } else {
      //   callback()
      // }
    };
    return {
      key: "",
      loginForm: {
        username: "",
        password: "",
        captcha: "",
      },
      loginRules: {
        username: [{ required: true, trigger: "blur", validator: validateUsername }],
        password: [{ required: true, trigger: "blur", validator: validatePassword }],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
      sysName: "", //系统名称配置项
      enableLoginCaptcha: "",
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  methods: {
    //获取公钥
    getkey() {
      getPublicKey().then((res) => {
        if (res.code == 0) {
          this.key = res.data;
        }
      });
    },
    success(params) {
      this.loginForm.captcha = params.captchaVerification;
      this.loading = true;
      let encryptor = new JSEncrypt(); // 实例化一个 jsEncrypt 对象
      encryptor.setPublicKey(this.key); //配置公钥
      let password = "";
      password = encryptor.encrypt(this.loginForm.password);
      let json = {
        username: this.loginForm.username,
        password: password,
        captcha: this.loginForm.captcha,
      };
      this.$store
        .dispatch("user/login", json)
        .then(() => {
          // this.$router.replace({ path: this.redirect || "/" });
          this.$router.replace({ path: "/home" });
          // console.log(222)
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      if (this.enableLoginCaptcha == "0") {
        this.loading = true;
        let encryptor = new JSEncrypt(); // 实例化一个 jsEncrypt 对象
        encryptor.setPublicKey(this.key); //配置公钥
        let password = "";
        password = encryptor.encrypt(this.loginForm.password);
        let json = {
          username: this.loginForm.username,
          password: password,
          captcha: this.loginForm.captcha,
        };
        this.$store
          .dispatch("user/login", json)
          .then(() => {
            localStorage.setItem('frequency', 0)
            this.$router.push({ path: this.redirect || "/" });
            this.loading = false;
            // setTimeout(() => {
            //   this.$router.go(0);
            // }, 50);
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        if (this.loginForm.username == "" || this.loginForm.password == "") {
          this.$message({
            message: "请填写用户名和密码",
            type: "error",
            duration: "5000",
          });
        } else {
          this.$refs.verify.show();
        }
      }
    },
    judgelogin() {
      //加载后管登录页面时直接判断是否统一身份认证登录，是的话，跳转到统一身份认证
      if (window.g.casLoginFlag) {
        this.$store
          .dispatch("user/thirdlogin")
          .then(() => {
            this.$router.push({ path: this.redirect || "/" });
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    getsysyName() {
      getsysconfig().then((res) => {
        if (res.code == 0) {
          res.data.map((item, index) => {
            if (item.sysKey == "sysName") {
              if (item.sysValue == "") {
                this.sysName = "实验室管理系统";
              } else {
                this.sysName = item.sysValue;
              }
            }
            if (item.sysKey == "enableLoginCaptcha") {
              this.enableLoginCaptcha = item.sysValue;
            }
          });
        }
      });
    },
  },
  created() {
    this.getsysyName();
    this.getkey();
    //测试二进制图片转换显示
    // const imageUrl = "http://localhost:8080/img/bg.5233d37f.jpg";

    // convertImageToBlob(imageUrl)
    //   .then((blob) => {
    //     // 使用 blob 对象进行后续操作
    //     console.log(blob);
    //     var img = this.$refs.imgs;
    //     img.onload = function (e) {
    //       window.URL.revokeObjectURL(img.src);
    //     };
    //     img.src = window.URL.createObjectURL(blob);
    //     // document.body.appendChild(img);
    //   })
    //   .catch((error) => {
    //     // 错误处理
    //     console.error(error);
    //   });
  },
  components: {
    Verify,
  },
};
</script>
<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    // input {
    // 	background: transparent;
    // 	border: 0px;
    // 	-webkit-appearance: none;
    // 	border-radius: 0px;
    // 	padding: 12px 5px 12px 15px;
    // 	color: $light_gray;
    // 	height: 47px;
    // 	caret-color: $cursor;

    // 	&:-webkit-autofill {
    // 		box-shadow: 0 0 0px 1000px $bg inset !important;
    // 		-webkit-text-fill-color: $cursor !important;
    // 	}
    // }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }

  // .el-input {
  // 	height: 44px !important;
  // 	// border: 1px solid #00000029;
  // 	// border-top-right-radius: 4px;
  // 	// border-bottom-right-radius: 4px;
  // }
  .el-input input {
    color: #999999;
    height: 44px !important;
    font-size: 18px;
  }

  .svg-container {
    background: #44a36e;
    width: 50px !important;
    color: white !important;
    height: 44px;
    line-height: 50px;
    text-align: center;
    vertical-align: top !important;
    padding: 0px !important;
  }

  .show-pwd .svg-icon {
    margin-right: 10px;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background: url("../../assets/bg.jpg") no-repeat;
  background-size: 100% 100%;
  overflow: hidden;

  .login-form {
    position: relative;
    width: 500px;
    max-width: 100%;
    /* padding: 160px 35px 0; */
    margin: 14% auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 30px;
      color: $light_gray;
      letter-spacing: 10px;
      text-shadow: 0px 2px 6px rgba(46, 104, 61, 0.63);
      text-align: center;
      font-weight: bold;
    }
  }

  .formbox {
    width: 100%;
    padding: 40px;
    background: white;
    box-shadow: 0px 3px 8px 0px rgba(30, 111, 72, 0.35);
    border-radius: 10px;

    .user {
      background: white;
      // box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.3);
    }

    .pwd {
      background: white;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .login {
    height: 40px;
    background: #44a36e;
    opacity: 0.91;
    border-radius: 4px;
    color: #ffffff;
    margin-top: 4%;
    width: 97%;
    border: none;
    font-size: 20px;
    letter-spacing: 12px;
  }
}
</style>
