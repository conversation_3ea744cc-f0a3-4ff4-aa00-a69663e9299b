'use strict'
const path = require('path')
const defaultSettings = require('./src/pages/admin/settings.js');

function resolve (dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'vue Admin Template' // page title

const Timestamp = new Date().getTime();

const port = process.env.port || process.env.npm_config_port || 9528 // dev port

const pages = {
  laboratory: {
    entry: "./src/pages/laboratory/main.js",
    template: 'public/laboratory.html',
    filename: 'laboratory.html',
  },
  admin: {
    entry: "./src/pages/admin/main.js",
    template: 'public/admin.html',
    filename: 'admin.html',
  },
  index: {
    entry: "./src/pages/index/main.js",
    template: 'public/index.html',
    filename: 'index.html',
  },
}
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  publicPath: './',
  outputDir: 'dist',
  assetsDir: 'static',
  // lintOnSave: process.env.NODE_ENV === 'development',
  lintOnSave: false,
  css: {
    loaderOptions: {
      postcss: {
        plugins: [
          require('postcss-plugin-px2rem')({
            // rootValue: 100, //换算基数， 默认100  ，这样的话把根标签的字体规定为1rem为50px,这样就可以从设计稿上量出多少个px直接在代码中写多上px了。
            // unitPrecision: 5, //允许REM单位增长到的十进制数字。
            //propWhiteList: [],  //默认值是一个空数组，这意味着禁用白名单并启用所有属性。
            // propBlackList: [], //黑名单
            exclude: /(node_module)|(admin)|(laboratory)/, //默认false，可以（reg）利用正则表达式排除某些文件夹的方法，例如/(node_module)/ 。如果想把前端UI框架内的px也转换成rem，请把此属性设为默认值
            // |(FooterTypeTwo)
            // 排除所有包含el-的类名（Element UI组件类名前缀）
            // selectorBlackList: [], //要忽略并保留为px的选择器
            // ignoreIdentifier: false,  //（boolean/string）忽略单个属性的方法，启用ignoreidentifier后，replace将自动设置为true。
            // replace: true, // （布尔值）替换包含REM的规则，而不是添加回退。
            mediaQuery: false, //（布尔值）允许在媒体查询中转换px。
            minPixelValue: 1 //设置要替换的最小像素值(3px会被转rem)。 默认 0
          }),
        ]
      }
    }
  },
  productionSourceMap: false,
  // devServer: {
  //   port: port,
  //   open: true,
  //   overlay: {
  //     warnings: false,
  //     errors: true
  //   },
  //   // before: require('./mock/mock-server.js')
  // },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@laboratory': resolve('src/pages/laboratory'),
        '@basicInfoMange': resolve('src/pages/laboratory/views/basicInfoMange'),
        '@centerManage': resolve('src/pages/laboratory/views/basicInfoMange/platformServices/centerManage'),
        '@labManager': resolve('src/pages/laboratory/views/basicInfoMange/basicInfo/labManager'),
        '@gradingClass': resolve('src/pages/laboratory/views/gradingClass'),
        '@equipmentPurchase': resolve('src/pages/laboratory/views/equipmentPurchase'),
        '@equipmentBasicInfo': resolve('src/pages/laboratory/views/equipmentBasicInfo'),
        '@admin': resolve('src/pages/admin'),
        '@index': resolve('src/pages/index'),
      }
    }
  },
  pages,
  chainWebpack (config) {
    config.output.filename(`js/${Timestamp}.[name].js`).end();
    config.output.chunkFilename(`js/${Timestamp}.[name].js`).end();
    config.plugins.delete("prefetch");
    config.plugins.delete("preload");
  },
  devServer: {
    proxy: {
      '/lab': {
        // target: "http://***********:8083/",
        target: "http://dc.unifound.net:980/lab/", //.9后台外网-通用版
        changeOrigin: true, //是否跨域
        pathRewrite: {
          "^/lab": '' //
        }
      }
    }
  },

}
